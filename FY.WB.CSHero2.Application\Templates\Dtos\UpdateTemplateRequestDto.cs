using System;
using System.Collections.Generic;
using System.Text.Json; // Required for JsonDocument if used directly in DTO
using FY.WB.CSHero2.Domain.Entities; // Required for TemplateSection and TemplateField

namespace FY.WB.CSHero2.Application.Templates.Dtos
{
    public class UpdateTemplateRequestDto
    {
        public Guid Id { get; set; } // Id of the Template to update
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;
        public IEnumerable<string>? Tags { get; set; }
        public IEnumerable<TemplateSection>? Sections { get; set; }
        public IEnumerable<TemplateField>? Fields { get; set; }
    }
}
