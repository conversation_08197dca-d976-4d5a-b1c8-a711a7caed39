# Azure Resources Setup Guide for Report Rendering Engine

## Overview
This guide will help you create the necessary Azure resources for the Report Rendering Engine project, including Cosmos DB and Blob Storage.

## Prerequisites

### Required Information
Before running the setup scripts, you'll need:

1. **Azure Subscription Details**
   - Azure Subscription ID (optional, will use default if not provided)
   - Azure account with permissions to create resources

2. **Resource Naming**
   - Resource Group Name (e.g., `rg-reportengine-dev`)
   - Azure Region/Location (e.g., `East US`, `West US 2`, `Central US`)
   - Cosmos DB Account Name (must be globally unique, e.g., `cosmos-reportengine-dev-12345`)
   - Storage Account Name (must be globally unique, lowercase, e.g., `streportenginedev12345`)

### Azure PowerShell Modules
You already have the required AzureRM modules installed. If you need to update them:
```powershell
Install-Module AzureRM -Force -AllowClobber
```

## Setup Process

### Step 1: Create Azure Resources

Run the PowerShell script to create the Azure resources:

```powershell
.\Setup-AzureResources.ps1 `
    -ResourceGroupName "rg-reportengine-dev" `
    -Location "Central US" `
    -CosmosAccountName "cosmos-reportengine-dev-12345" `
    -StorageAccountName "streportenginedev12345" `
    -SubscriptionId "your-subscription-id"  # Optional
```

**Example with actual values:**
```powershell
.\Setup-AzureResources.ps1 `
    -ResourceGroupName "rg-reportengine-dev" `
    -Location "Central US" `
    -CosmosAccountName "cosmos-reportengine-dev-********" `
    -StorageAccountName "streportenginedev********"
```

This script will:
- Log you into Azure
- Create a Resource Group
- Create a Storage Account with blob containers
- Create a Cosmos DB account (serverless tier for cost efficiency)
- Output connection strings and save them to `azure-connection-details.txt`

### Step 2: Update CosmosDbSetup.cs

After the Azure resources are created, update the `CosmosDbSetup.cs` file with the connection details:

```csharp
private static readonly string CosmosEndpoint = "https://your-actual-cosmos-account.documents.azure.com:443/";
private static readonly string CosmosKey = "your-actual-cosmos-primary-key";
private static readonly string BlobConnectionString = "your-actual-blob-connection-string";
```

### Step 3: Build and Run Database Setup

Build the setup application:
```bash
dotnet build CosmosDbSetup.csproj
```

Run the setup to create databases and containers:
```bash
dotnet run --project CosmosDbSetup.csproj
```

This will create:
- **Database:** `ReportRenderingEngine`
- **Containers:**
  - `ReportStyles` (partitioned by `/tenantId`)
  - `ReportTemplates` (partitioned by `/tenantId`)
  - `ReportData` (partitioned by `/tenantId`)
  - `ReportMetadata` (partitioned by `/tenantId`)
- **Blob Containers:**
  - `report-templates`
  - `report-data`
  - `report-outputs`
  - `report-assets`

### Step 4: Update Application Configuration

Update your `appsettings.json` with the connection details:

```json
{
  "CosmosDb": {
    "Endpoint": "https://your-cosmos-account.documents.azure.com:443/",
    "Key": "your-cosmos-primary-key",
    "DatabaseName": "ReportRenderingEngine"
  },
  "BlobStorage": {
    "ConnectionString": "your-blob-connection-string",
    "ContainerNames": {
      "Templates": "report-templates",
      "Data": "report-data",
      "Outputs": "report-outputs",
      "Assets": "report-assets"
    }
  }
}
```

## Resource Architecture

### Cosmos DB Structure
```
ReportRenderingEngine (Database)
├── ReportStyles (Container)
│   ├── Partition Key: /tenantId
│   └── Throughput: 400 RU/s
├── ReportTemplates (Container)
│   ├── Partition Key: /tenantId
│   └── Throughput: 400 RU/s
├── ReportData (Container)
│   ├── Partition Key: /tenantId
│   └── Throughput: 400 RU/s
└── ReportMetadata (Container)
    ├── Partition Key: /tenantId
    └── Throughput: 400 RU/s
```

### Blob Storage Structure
```
Storage Account
├── report-templates (Container)
├── report-data (Container)
├── report-outputs (Container)
└── report-assets (Container)
```

## Cost Considerations

### Cosmos DB (Serverless)
- No minimum charges
- Pay per request unit (RU) consumed
- Ideal for development and variable workloads
- Automatic scaling

### Blob Storage (Standard LRS)
- Low-cost storage option
- Locally redundant storage
- Pay for storage used and operations

## Security Notes

1. **Connection Strings**: Store securely, never commit to source control
2. **Access Keys**: Rotate regularly
3. **Network Access**: Consider restricting to specific IP ranges in production
4. **Managed Identity**: Consider using Managed Identity instead of connection strings in production

## Troubleshooting

### Common Issues

1. **Resource Name Conflicts**
   - Cosmos DB and Storage Account names must be globally unique
   - Try adding date/random numbers to names

2. **Permission Issues**
   - Ensure your Azure account has Contributor role on the subscription
   - May need Owner role to create certain resources

3. **Region Availability**
   - Some regions may not support all services
   - Try different regions if deployment fails

4. **PowerShell Module Issues**
   ```powershell
   # Update modules if needed
   Update-Module AzureRM
   Import-Module AzureRM.Profile -Force
   ```

## Next Steps

After completing the setup:

1. Test the connection using your existing test projects
2. Run integration tests to verify functionality
3. Update any existing connection strings in your application
4. Consider setting up monitoring and alerts for the resources

## Support

If you encounter issues:
1. Check the Azure portal for resource creation status
2. Review the PowerShell script output for error messages
3. Verify your Azure permissions
4. Check the `azure-connection-details.txt` file for saved connection information
