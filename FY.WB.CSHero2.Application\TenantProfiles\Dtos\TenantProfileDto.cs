using System;
using System.Collections.Generic;

namespace FY.WB.CSHero2.Application.TenantProfiles.Dtos
{
    public class PaymentMethodInfoDto
    {
        public string CardType { get; set; } = string.Empty;
        public string LastFourDigits { get; set; } = string.Empty;
        public string ExpirationDate { get; set; } = string.Empty;
        public string SecurityMethod { get; set; } = string.Empty;
    }

    public class BillingAddressInfoDto
    {
        public string Street { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
    }

    public class TenantProfileDto
    {
        public Guid Id { get; set; } // This is the TenantProfile's own ID
        public Guid? TenantId { get; set; } // This is the link to AppTenantInfo.Id (Finbuckle's tenant ID)
        
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Subscription { get; set; } = string.Empty;
        public DateTime LastLoginTime { get; set; }
        public string BillingCycle { get; set; } = string.Empty;
        public DateTime NextBillingDate { get; set; }
        public string SubscriptionStatus { get; set; } = string.Empty;

        public PaymentMethodInfoDto? PaymentMethod { get; set; }
        public BillingAddressInfoDto? BillingAddress { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Potentially include counts or brief summaries of related entities if needed for list views
        // public int ClientCount { get; set; }
        // public int InvoiceCount { get; set; }
    }
}
