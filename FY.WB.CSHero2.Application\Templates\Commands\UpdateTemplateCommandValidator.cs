using FluentValidation;
using FY.WB.CSHero2.Application.Templates.Dtos;
using FY.WB.CSHero2.Domain.Entities; // For TemplateSection, TemplateField
using System;
using System.Linq;

namespace FY.WB.CSHero2.Application.Templates.Commands
{
    public class UpdateTemplateCommandValidator : AbstractValidator<UpdateTemplateCommand>
    {
        public UpdateTemplateCommandValidator()
        {
            RuleFor(v => v.Dto.Id)
                .NotEmpty().WithMessage("Id is required.");

            RuleFor(v => v.Dto.Name)
                .NotEmpty().WithMessage("Name is required.")
                .MaximumLength(200).WithMessage("Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Description)
                .MaximumLength(1000).WithMessage("Description must not exceed 1000 characters.");

            RuleFor(v => v.Dto.Category)
                .MaximumLength(100).WithMessage("Category must not exceed 100 characters.");

            RuleFor(v => v.Dto.ThumbnailUrl)
                .MaximumLength(500).WithMessage("Thumbnail URL must not exceed 500 characters.");

            RuleForEach(v => v.Dto.Tags)
                .NotEmpty().WithMessage("Tag cannot be empty.")
                .MaximumLength(50).WithMessage("Tag must not exceed 50 characters.");
            
            RuleForEach(v => v.Dto.Sections).ChildRules(sections =>
            {
                sections.RuleFor(s => s.Id).NotEmpty().MaximumLength(50);
                sections.RuleFor(s => s.Title).NotEmpty().MaximumLength(200);
                sections.RuleFor(s => s.Type).NotEmpty().MaximumLength(50);
            });
            
            RuleForEach(v => v.Dto.Fields).ChildRules(fields =>
            {
                fields.RuleFor(f => f.Id).NotEmpty().MaximumLength(50);
                fields.RuleFor(f => f.Name).NotEmpty().MaximumLength(100);
                fields.RuleFor(f => f.Type).NotEmpty().MaximumLength(50);
            });
        }
    }
}
