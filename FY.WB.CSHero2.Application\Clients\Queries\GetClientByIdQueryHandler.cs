using FY.WB.CSHero2.Application.Clients.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Clients.Queries
{
    public class GetClientByIdQueryHandler : IRequestHandler<GetClientByIdQuery, ClientDto?>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetClientByIdQueryHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<ClientDto?> Handle(GetClientByIdQuery request, CancellationToken cancellationToken)
        {
            if (!Guid.TryParse(request.Id, out var clientId))
            {
                return null; // Or throw an ArgumentException if ID format is strictly GUID
            }

            var query = _context.Clients.AsNoTracking(); // Read-only query

            // Filter by tenant ID if available
            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(c => c.TenantId == _currentUserService.TenantId);
            }

            var client = await query
                .FirstOrDefaultAsync(c => c.Id == clientId, cancellationToken);

            if (client is null)
            {
                return null;
            }

            return new ClientDto
            {
                Id = client.Id.ToString(),
                Name = client.Name,
                Email = client.Email,
                Status = client.Status,
                CompanyName = client.CompanyName,
                CreatedAt = client.CreationTime,
                UpdatedAt = client.LastModificationTime ?? client.CreationTime, // Fallback to CreationTime if LastModificationTime is null
                Phone = client.Phone,
                Address = client.Address,
                CompanySize = client.CompanySize,
                Industry = client.Industry
            };
        }
    }
}
