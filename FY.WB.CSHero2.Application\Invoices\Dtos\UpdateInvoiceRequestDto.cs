using System;

namespace FY.WB.CSHero2.Application.Invoices.Dtos
{
    public class UpdateInvoiceRequestDto
    {
        public Guid Id { get; set; } // Id of the Invoice to update
        // OrderNumber and Type are typically not updatable for an existing invoice.
        // Date might also be fixed post-creation.
        public string Plans { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
