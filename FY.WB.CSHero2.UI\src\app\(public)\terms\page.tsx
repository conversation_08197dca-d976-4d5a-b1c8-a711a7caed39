"use client";

import { motion } from "framer-motion";
import { 
  Scale,
  FileText,
  AlertCircle,
  Ban,
  Key,
  Clock,
  Wallet,
  ScrollText
} from "lucide-react";

export default function TermsPage() {
  return (
    <div>
      <div className="gradient-primary-secondary py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold mb-4 text-white">Terms of Service</h1>
            <p className="text-xl text-gray-300">
              Please read these terms carefully before using our service.
            </p>
          </motion.div>
        </div>
      </div>

      <div className="bg-white">
        <div className="container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="prose max-w-4xl mx-auto text-gray-600">
              <p className="text-lg mb-8">Last updated: February 18, 2024</p>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                    >
                      <Scale className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">1. Agreement to Terms</h2>
                </div>
                <p className="mb-4">
                  By accessing or using CS-Hero, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this service.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >
                      <FileText className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">2. Use License</h2>
                </div>
                <p className="mb-4">Permission is granted to temporarily access and use CS-Hero for:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Personal or business use</li>
                  <li>Viewing and interacting with available content</li>
                  <li>Generating and managing reports</li>
                </ul>
                <p className="mb-4">This license shall automatically terminate if you violate any of these restrictions.</p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.4 }}
                    >
                      <AlertCircle className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">3. Disclaimer</h2>
                </div>
                <p className="mb-4">
                  The materials on CS-Hero are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.5 }}
                    >
                      <Ban className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">4. Limitations</h2>
                </div>
                <p className="mb-4">
                  In no event shall CS-Hero or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use our service.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                    >
                      <Key className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">5. Account Security</h2>
                </div>
                <p className="mb-4">You are responsible for:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Maintaining account confidentiality</li>
                  <li>Restricting access to your account</li>
                  <li>All activities under your account</li>
                </ul>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.7 }}
                    >
                      <Clock className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">6. Service Availability</h2>
                </div>
                <p className="mb-4">
                  We strive to provide uninterrupted service but may need to temporarily suspend access for maintenance or updates. We are not liable for any service interruptions or data loss during such periods.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.8 }}
                    >
                      <Wallet className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">7. Payment Terms</h2>
                </div>
                <p className="mb-4">
                  Subscription fees are billed in advance on a monthly or annual basis. You agree to pay all charges at the prices then in effect for your subscription. Automatic renewal may be disabled by you at any time in your account settings.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.9 }}
                    >
                      <ScrollText className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">8. Modifications</h2>
                </div>
                <p className="mb-4">
                  We reserve the right to modify or replace these Terms at any time. We will provide notice of any changes by posting the new Terms on this page and updating the "Last updated" date.
                </p>
              </section>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
