using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Mapping configuration for report styles to actual tenants
    /// </summary>
    public class ReportStyleMapping
    {
        public string CompanyName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Theme { get; set; } = string.Empty;
        public string PrimaryColor { get; set; } = string.Empty;
        public string SecondaryColor { get; set; } = string.Empty;
        public string ReportId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Seeds CosmosDB with sample report style documents using actual tenant data
    /// </summary>
    public class CosmosDbSeeder
    {
        /// <summary>
        /// Report style mappings to actual tenant companies
        /// </summary>
        private readonly List<ReportStyleMapping> _reportStyleMappings = new()
        {
            new ReportStyleMapping
            {
                CompanyName = "TechCorp Solutions", // Maps to actual tenant
                Category = "Satisfaction Survey",
                Theme = "satisfaction-analysis",
                PrimaryColor = "#10b981",
                SecondaryColor = "#059669",
                ReportId = "CSR-2025-001"
            },
            new ReportStyleMapping
            {
                CompanyName = "Health Plus", // Maps to actual tenant
                Category = "Performance Metrics",
                Theme = "performance-dashboard",
                PrimaryColor = "#3b82f6",
                SecondaryColor = "#1e40af",
                ReportId = "CSR-2025-002"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechFusion Inc.", // Maps to actual tenant
                Category = "Action Plan",
                Theme = "action-planning",
                PrimaryColor = "#f59e0b",
                SecondaryColor = "#d97706",
                ReportId = "CSR-2025-003"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechCorp Solutions", // Second style for same tenant
                Category = "Channel Analysis",
                Theme = "channel-effectiveness",
                PrimaryColor = "#8b5cf6",
                SecondaryColor = "#7c3aed",
                ReportId = "CSR-2025-004"
            },
            new ReportStyleMapping
            {
                CompanyName = "Health Plus", // Second style for same tenant
                Category = "User Experience",
                Theme = "ux-analysis",
                PrimaryColor = "#ef4444",
                SecondaryColor = "#dc2626",
                ReportId = "CSR-2025-005"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechFusion Inc.", // Second style for same tenant
                Category = "Product Improvement",
                Theme = "product-analysis",
                PrimaryColor = "#06b6d4",
                SecondaryColor = "#0891b2",
                ReportId = "CSR-2025-006"
            }
        };

        /// <summary>
        /// Seeds CosmosDB with sample report style documents using actual tenant IDs
        /// </summary>
        public async Task<Dictionary<string, string>> SeedReportStylesAsync(
            IReportStyleService styleService,
            ITenantResolutionService tenantResolutionService,
            ILogger logger)
        {
            logger.LogInformation("Starting CosmosDB seeding for report styles with real tenant IDs...");
            
            var styleDocumentIds = new Dictionary<string, string>();
            
            try
            {
                // Validate that we have tenant data
                var tenants = await tenantResolutionService.GetSeededTenantsAsync();
                if (!tenants.Any())
                {
                    logger.LogWarning("No tenants found in SQL database, skipping CosmosDB seeding");
                    return styleDocumentIds;
                }

                logger.LogInformation("Found {TenantCount} tenants, proceeding with seeding", tenants.Count);

                // Create template styles first (system-level)
                var templateStyles = await CreateTemplateStylesAsync(styleService, logger);
                foreach (var kvp in templateStyles)
                {
                    styleDocumentIds[kvp.Key] = kvp.Value;
                }
                
                // Create report styles with real tenant IDs
                var reportStyles = await CreateReportStylesWithRealTenantsAsync(
                    styleService, tenantResolutionService, logger);
                foreach (var kvp in reportStyles)
                {
                    styleDocumentIds[kvp.Key] = kvp.Value;
                }
                
                logger.LogInformation("CosmosDB seeding completed successfully. Created {Count} style documents",
                    styleDocumentIds.Count);
                
                return styleDocumentIds;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during CosmosDB seeding");
                throw;
            }
        }
        
        /// <summary>
        /// Creates template style documents (system-level templates)
        /// </summary>
        private async Task<Dictionary<string, string>> CreateTemplateStylesAsync(
            IReportStyleService styleService,
            ILogger logger)
        {
            logger.LogInformation("Creating template style documents...");
            
            var styleDocumentIds = new Dictionary<string, string>();
            
            // Template style for the "Example Template" - system level
            var exampleTemplateId = Guid.Parse("00000000-0000-0000-0000-000000000001");
            var systemTenantId = Guid.Parse("00000000-0000-0000-0000-000000000000"); // System template
            
            var templateStyle = new ReportStyleDocument
            {
                Id = ReportStyleDocument.CreateTemplateStyleId(exampleTemplateId),
                PartitionKey = systemTenantId.ToString(), // Use system TenantId as partition key
                TemplateId = exampleTemplateId,
                TenantId = systemTenantId, // System template
                StyleType = "template",
                HtmlContent = @"
<div class='template-container bg-white shadow-lg rounded-lg overflow-hidden'>
    <div class='template-header bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6'>
        <h1 class='text-3xl font-bold'>{{title}}</h1>
        <p class='text-blue-100 mt-2'>{{subtitle}}</p>
    </div>
    <div class='template-content p-6'>
        <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div class='content-section'>
                <h2 class='text-xl font-semibold text-gray-800 mb-4'>{{sectionTitle}}</h2>
                <div class='content-body text-gray-600'>{{content}}</div>
            </div>
            <div class='data-section'>
                <div class='chart-container bg-gray-50 rounded-lg p-4'>
                    {{chartPlaceholder}}
                </div>
            </div>
        </div>
    </div>
    <div class='template-footer bg-gray-50 p-4 text-center text-sm text-gray-500'>
        Generated by CSHero Report Engine
    </div>
</div>",
                CssStyles = @"
.template-container {
    @apply max-w-4xl mx-auto;
    font-family: 'Inter', sans-serif;
}

.template-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.content-section {
    @apply space-y-4;
}

.data-section {
    @apply flex flex-col justify-center;
}

.chart-container {
    @apply min-h-64 flex items-center justify-center;
    border: 2px dashed #d1d5db;
}

@media (max-width: 768px) {
    .template-container {
        @apply mx-4;
    }
    
    .template-header {
        @apply p-4;
    }
    
    .template-content {
        @apply p-4;
    }
}",
                ComponentStyles = new Dictionary<string, ComponentStyle>
                {
                    ["header"] = new ComponentStyle
                    {
                        ComponentId = "header",
                        ComponentType = "header",
                        CssClasses = new List<string> { "template-header", "bg-gradient-to-r", "from-blue-600", "to-blue-800" },
                        Layout = new ComponentLayout
                        {
                            Width = "100%",
                            Height = "auto",
                            Padding = "1.5rem",
                            Display = "block"
                        }
                    },
                    ["content"] = new ComponentStyle
                    {
                        ComponentId = "content",
                        ComponentType = "content",
                        CssClasses = new List<string> { "template-content", "p-6" },
                        Layout = new ComponentLayout
                        {
                            Width = "100%",
                            Height = "auto",
                            Padding = "1.5rem",
                            Display = "block"
                        }
                    }
                },
                Metadata = new StyleMetadata
                {
                    Framework = "TailwindCSS",
                    FrameworkVersion = "3.0",
                    Theme = "corporate-blue",
                    ColorScheme = "light",
                    Tags = new List<string> { "template", "corporate", "finance", "professional" },
                    CustomProperties = new Dictionary<string, object>
                    {
                        ["primaryColor"] = "#3b82f6",
                        ["secondaryColor"] = "#1e40af",
                        ["accentColor"] = "#60a5fa"
                    }
                },
                CreatedBy = Guid.Parse("00000000-0000-0000-0000-000000000000"), // System
                LastModifiedBy = Guid.Parse("00000000-0000-0000-0000-000000000000"),
                Version = 1,
                IsActive = true
            };
            
            var styleId = await styleService.SaveStyleAsync(templateStyle);
            styleDocumentIds[$"template-{exampleTemplateId}"] = styleId;
            
            logger.LogInformation("Created template style: {StyleId}", styleId);
            
            return styleDocumentIds;
        }
        
        /// <summary>
        /// Creates report style documents using real tenant IDs from SQL database
        /// </summary>
        private async Task<Dictionary<string, string>> CreateReportStylesWithRealTenantsAsync(
            IReportStyleService styleService,
            ITenantResolutionService tenantResolutionService,
            ILogger logger)
        {
            logger.LogInformation("Creating report styles with real tenant IDs...");
            
            var styleDocumentIds = new Dictionary<string, string>();
            var tenantMapping = await tenantResolutionService.GetCompanyToTenantMappingAsync();
            
            logger.LogInformation("Retrieved {TenantCount} tenant mappings", tenantMapping.Count);
            
            foreach (var mapping in _reportStyleMappings)
            {
                var companyKey = mapping.CompanyName.ToLowerInvariant().Trim();
                
                if (!tenantMapping.TryGetValue(companyKey, out var tenantId))
                {
                    logger.LogWarning("No tenant found for company: {CompanyName}, skipping style creation", mapping.CompanyName);
                    continue;
                }
                
                logger.LogInformation("Creating report style for {Category} - {CompanyName} (TenantId: {TenantId})",
                    mapping.Category, mapping.CompanyName, tenantId);
                
                var reportVersionId = Guid.NewGuid();
                var reportStyle = new ReportStyleDocument
                {
                    Id = ReportStyleDocument.CreateReportStyleId(reportVersionId),
                    PartitionKey = tenantId.ToString(), // Use real TenantId as partition key
                    ReportVersionId = reportVersionId,
                    TenantId = tenantId, // Use real TenantId
                    StyleType = "report",
                    HtmlContent = GenerateReportHtml(mapping.Category, mapping.CompanyName, mapping.PrimaryColor),
                    CssStyles = GenerateReportCss(mapping.Theme, mapping.PrimaryColor, mapping.SecondaryColor),
                    ComponentStyles = GenerateComponentStyles(mapping.Category, mapping.PrimaryColor),
                    Metadata = new StyleMetadata
                    {
                        Framework = "TailwindCSS",
                        FrameworkVersion = "3.0",
                        Theme = mapping.Theme,
                        ColorScheme = "light",
                        Tags = new List<string> { mapping.Category.ToLower().Replace(" ", "-"), "report", mapping.CompanyName.ToLower().Replace(" ", "-") },
                        CustomProperties = new Dictionary<string, object>
                        {
                            ["reportId"] = mapping.ReportId,
                            ["companyName"] = mapping.CompanyName,
                            ["category"] = mapping.Category,
                            ["primaryColor"] = mapping.PrimaryColor,
                            ["secondaryColor"] = mapping.SecondaryColor,
                            ["tenantId"] = tenantId.ToString()
                        }
                    },
                    CreatedBy = Guid.Parse("00000000-0000-0000-0000-000000000000"), // System
                    LastModifiedBy = Guid.Parse("00000000-0000-0000-0000-000000000000"),
                    Version = 1,
                    IsActive = true
                };
                
                var styleId = await styleService.SaveStyleAsync(reportStyle);
                styleDocumentIds[$"report-{mapping.ReportId}"] = styleId;
                
                logger.LogInformation("Created report style {StyleId} for tenant {TenantId} ({CompanyName})",
                    styleId, tenantId, mapping.CompanyName);
            }
            
            return styleDocumentIds;
        }
        
        /// <summary>
        /// Generates HTML content for a report based on category
        /// </summary>
        private string GenerateReportHtml(string category, string clientName, string primaryColor)
        {
            return $@"
<div class='report-container bg-white shadow-xl rounded-lg overflow-hidden'>
    <div class='report-header text-white p-8' style='background: linear-gradient(135deg, {primaryColor} 0%, {primaryColor}dd 100%)'>
        <div class='flex items-center justify-between'>
            <div>
                <h1 class='text-4xl font-bold mb-2'>{category} Report</h1>
                <p class='text-lg opacity-90'>Client: {clientName}</p>
                <p class='text-sm opacity-75 mt-1'>Generated on {{{{date}}}}</p>
            </div>
            <div class='report-logo'>
                <div class='w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center'>
                    <span class='text-2xl font-bold'>{{{{logo}}}}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class='report-content p-8'>
        <div class='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8'>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 1</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric1}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric1Description}}}}</p>
            </div>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 2</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric2}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric2Description}}}}</p>
            </div>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 3</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric3}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric3Description}}}}</p>
            </div>
        </div>
        
        <div class='grid grid-cols-1 lg:grid-cols-2 gap-8'>
            <div class='chart-section'>
                <h2 class='text-2xl font-bold text-gray-800 mb-4'>Trend Analysis</h2>
                <div class='chart-container bg-gray-50 rounded-lg p-6 min-h-80'>
                    <div class='chart-placeholder flex items-center justify-center h-full text-gray-400'>
                        {{{{trendChart}}}}
                    </div>
                </div>
            </div>
            <div class='insights-section'>
                <h2 class='text-2xl font-bold text-gray-800 mb-4'>Key Insights</h2>
                <div class='insights-content space-y-4'>
                    <div class='insight-item p-4 bg-blue-50 rounded-lg border-l-4' style='border-color: {primaryColor}'>
                        <h4 class='font-semibold text-gray-800'>{{{{insight1Title}}}}</h4>
                        <p class='text-gray-600 mt-1'>{{{{insight1Content}}}}</p>
                    </div>
                    <div class='insight-item p-4 bg-blue-50 rounded-lg border-l-4' style='border-color: {primaryColor}'>
                        <h4 class='font-semibold text-gray-800'>{{{{insight2Title}}}}</h4>
                        <p class='text-gray-600 mt-1'>{{{{insight2Content}}}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class='report-footer bg-gray-50 p-6 text-center'>
        <p class='text-sm text-gray-500'>
            This report was generated by CSHero Report Engine • 
            <span class='font-medium'>Confidential & Proprietary</span>
        </p>
    </div>
</div>";
        }
        
        /// <summary>
        /// Generates CSS styles for a report theme
        /// </summary>
        private string GenerateReportCss(string theme, string primaryColor, string secondaryColor)
        {
            return $@"
.report-container {{
    @apply max-w-6xl mx-auto;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
}}

.report-header {{
    background: linear-gradient(135deg, {primaryColor} 0%, {secondaryColor} 100%);
    position: relative;
}}

.report-header::before {{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns=""http://www.w3.org/2000/svg"" viewBox=""0 0 100 100""><defs><pattern id=""grain"" width=""100"" height=""100"" patternUnits=""userSpaceOnUse""><circle cx=""50"" cy=""50"" r=""1"" fill=""white"" opacity=""0.1""/></pattern></defs><rect width=""100"" height=""100"" fill=""url(%23grain)""/></svg>');
    opacity: 0.3;
}}

.metric-card {{
    @apply transition-all duration-300 hover:shadow-lg;
    border: 1px solid rgba(0,0,0,0.05);
}}

.metric-card:hover {{
    transform: translateY(-2px);
}}

.chart-container {{
    @apply border-2 border-dashed border-gray-200;
    background: linear-gradient(45deg, #f9fafb 25%, transparent 25%), 
                linear-gradient(-45deg, #f9fafb 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f9fafb 75%), 
                linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}}

.insight-item {{
    @apply transition-all duration-200 hover:shadow-md;
}}

.insight-item:hover {{
    transform: translateX(4px);
}}

/* Theme-specific styles for {theme} */
.theme-{theme.Replace("-", "_")} {{
    --primary-color: {primaryColor};
    --secondary-color: {secondaryColor};
}}

/* Responsive design */
@media (max-width: 1024px) {{
    .report-container {{
        @apply mx-4;
    }}
    
    .report-header {{
        @apply p-6;
    }}
    
    .report-content {{
        @apply p-6;
    }}
}}

@media (max-width: 768px) {{
    .report-header h1 {{
        @apply text-2xl;
    }}
    
    .metric-card {{
        @apply text-center;
    }}
    
    .chart-container {{
        @apply min-h-64;
    }}
}}

/* Print styles */
@media print {{
    .report-container {{
        @apply shadow-none max-w-none mx-0;
    }}
    
    .report-header {{
        background: {primaryColor} !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }}
}}";
        }
        
        /// <summary>
        /// Generates component styles for a report category
        /// </summary>
        private Dictionary<string, ComponentStyle> GenerateComponentStyles(string category, string primaryColor)
        {
            return new Dictionary<string, ComponentStyle>
            {
                ["header"] = new ComponentStyle
                {
                    ComponentId = "header",
                    ComponentType = "header",
                    CssClasses = new List<string> { "report-header", "text-white", "p-8" },
                    InlineStyles = new Dictionary<string, string>
                    {
                        ["background"] = $"linear-gradient(135deg, {primaryColor} 0%, {primaryColor}dd 100%)"
                    },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Padding = "2rem",
                        Display = "block"
                    }
                },
                ["metrics"] = new ComponentStyle
                {
                    ComponentId = "metrics",
                    ComponentType = "metrics-grid",
                    CssClasses = new List<string> { "grid", "grid-cols-1", "lg:grid-cols-3", "gap-8", "mb-8" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Display = "grid"
                    }
                },
                ["chart"] = new ComponentStyle
                {
                    ComponentId = "chart",
                    ComponentType = "chart",
                    CssClasses = new List<string> { "chart-container", "bg-gray-50", "rounded-lg", "p-6", "min-h-80" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "320px",
                        Padding = "1.5rem",
                        Display = "flex"
                    }
                },
                ["insights"] = new ComponentStyle
                {
                    ComponentId = "insights",
                    ComponentType = "insights",
                    CssClasses = new List<string> { "insights-content", "space-y-4" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Display = "block"
                    }
                },
                ["footer"] = new ComponentStyle
                {
                    ComponentId = "footer",
                    ComponentType = "footer",
                    CssClasses = new List<string> { "report-footer", "bg-gray-50", "p-6", "text-center" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Padding = "1.5rem",
                        Display = "block"
                    }
                }
            };
        }
    }
}
