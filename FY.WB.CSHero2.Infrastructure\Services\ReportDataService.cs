using FY.WB.CSHero2.Infrastructure.Models;
using FY.WB.CSHero2.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Infrastructure.Services;

public interface IReportDataService
{
    Task<string> SaveReportDataAsync(Guid reportId, object reportData, string tenantId);
    Task<ReportDataDocument?> GetReportDataAsync(Guid reportId, string tenantId);
    Task DeleteReportDataAsync(Guid reportId, string tenantId);
    Task<ReportDataDocument> UpdateReportDataAsync(Guid reportId, object reportData, string tenantId);
}

public class ReportDataService : IReportDataService
{
    private readonly ICosmosDbService _cosmosDbService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<ReportDataService> _logger;

    public ReportDataService(
        ICosmosDbService cosmosDbService,
        ApplicationDbContext dbContext,
        ILogger<ReportDataService> logger)
    {
        _cosmosDbService = cosmosDbService;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<string> SaveReportDataAsync(Guid reportId, object reportData, string tenantId)
    {
        try
        {
            _logger.LogInformation("Saving report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            // Convert the frontend report data to our Cosmos DB document format
            var document = ConvertToReportDataDocument(reportId, reportData, tenantId);
            
            // Save to Cosmos DB
            var savedDocument = await _cosmosDbService.UpsertItemAsync(document, tenantId);
            
            _logger.LogInformation("Successfully saved report data to Cosmos DB for ReportId: {ReportId}", reportId);
            
            return savedDocument.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task<ReportDataDocument?> GetReportDataAsync(Guid reportId, string tenantId)
    {
        try
        {
            _logger.LogInformation("Getting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            var document = await _cosmosDbService.GetItemAsync<ReportDataDocument>(reportId.ToString(), tenantId);
            
            if (document != null)
            {
                _logger.LogInformation("Successfully retrieved report data from Cosmos DB for ReportId: {ReportId}", reportId);
            }
            else
            {
                _logger.LogWarning("No report data found in Cosmos DB for ReportId: {ReportId}", reportId);
            }
            
            return document;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task DeleteReportDataAsync(Guid reportId, string tenantId)
    {
        try
        {
            _logger.LogInformation("Deleting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            await _cosmosDbService.DeleteItemAsync(reportId.ToString(), tenantId);
            
            _logger.LogInformation("Successfully deleted report data from Cosmos DB for ReportId: {ReportId}", reportId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task<ReportDataDocument> UpdateReportDataAsync(Guid reportId, object reportData, string tenantId)
    {
        try
        {
            _logger.LogInformation("Updating report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            // Get existing document to preserve version and creation date
            var existingDocument = await GetReportDataAsync(reportId, tenantId);
            
            var document = ConvertToReportDataDocument(reportId, reportData, tenantId);
            
            if (existingDocument != null)
            {
                document.CreatedAt = existingDocument.CreatedAt;
                document.Version = existingDocument.Version + 1;
            }
            
            document.UpdatedAt = DateTime.UtcNow;
            
            var savedDocument = await _cosmosDbService.UpsertItemAsync(document, tenantId);
            
            _logger.LogInformation("Successfully updated report data in Cosmos DB for ReportId: {ReportId}", reportId);
            
            return savedDocument;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    private ReportDataDocument ConvertToReportDataDocument(Guid reportId, object reportData, string tenantId)
    {
        // This method converts the frontend report data format to our Cosmos DB document format
        // The reportData object comes from the frontend ReportEditor component
        
        var document = new ReportDataDocument
        {
            Id = reportId.ToString(),
            ReportId = reportId.ToString(),
            TenantId = tenantId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Version = 1
        };

        // Handle dynamic object conversion
        if (reportData is System.Text.Json.JsonElement jsonElement)
        {
            // Convert from System.Text.Json to our model
            ConvertFromJsonElement(document, jsonElement);
        }
        else if (reportData != null)
        {
            // Try to convert using reflection or serialization
            var json = System.Text.Json.JsonSerializer.Serialize(reportData);
            var jsonDoc = System.Text.Json.JsonDocument.Parse(json);
            ConvertFromJsonElement(document, jsonDoc.RootElement);
        }

        return document;
    }

    private void ConvertFromJsonElement(ReportDataDocument document, System.Text.Json.JsonElement element)
    {
        if (element.TryGetProperty("reportName", out var reportName))
        {
            document.ReportInfo.Title = reportName.GetString() ?? "";
        }
        
        if (element.TryGetProperty("description", out var description))
        {
            document.ReportInfo.Description = description.GetString() ?? "";
        }
        
        if (element.TryGetProperty("clientId", out var clientId))
        {
            document.ReportInfo.ClientId = clientId.GetString();
        }
        
        if (element.TryGetProperty("clientName", out var clientName))
        {
            document.ReportInfo.ClientName = clientName.GetString();
        }
        
        if (element.TryGetProperty("startDate", out var startDate))
        {
            document.ReportInfo.StartDate = startDate.GetString() ?? "";
        }
        
        if (element.TryGetProperty("endDate", out var endDate))
        {
            document.ReportInfo.EndDate = endDate.GetString() ?? "";
        }

        if (element.TryGetProperty("content", out var content) && 
            content.TryGetProperty("sections", out var sections))
        {
            document.Sections = ConvertSections(sections);
        }

        if (element.TryGetProperty("style", out var style))
        {
            document.Styles = ConvertToObject(style);
        }

        if (element.TryGetProperty("content", out var contentObj) && 
            contentObj.TryGetProperty("template", out var template))
        {
            document.Template = template.GetString();
        }
    }

    private List<SectionData> ConvertSections(System.Text.Json.JsonElement sectionsElement)
    {
        var sections = new List<SectionData>();
        
        if (sectionsElement.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var sectionElement in sectionsElement.EnumerateArray())
            {
                var section = new SectionData();
                
                if (sectionElement.TryGetProperty("id", out var id))
                {
                    section.Id = id.GetString() ?? "";
                }
                
                if (sectionElement.TryGetProperty("section-title", out var title))
                {
                    section.Title = title.GetString() ?? "";
                }
                else if (sectionElement.TryGetProperty("title", out var titleAlt))
                {
                    section.Title = titleAlt.GetString() ?? "";
                }
                
                if (sectionElement.TryGetProperty("type", out var type))
                {
                    section.Type = type.GetString() ?? "";
                }
                
                if (sectionElement.TryGetProperty("content", out var content))
                {
                    section.Content = ConvertToStringObjectDictionary(content);
                }
                
                sections.Add(section);
            }
        }
        
        return sections;
    }

    private Dictionary<string, object> ConvertToStringObjectDictionary(System.Text.Json.JsonElement element)
    {
        var dict = new Dictionary<string, object>();
        
        if (element.ValueKind == System.Text.Json.JsonValueKind.Object)
        {
            foreach (var property in element.EnumerateObject())
            {
                dict[property.Name] = ConvertToObject(property.Value);
            }
        }
        
        return dict;
    }

    private object ConvertToObject(System.Text.Json.JsonElement element)
    {
        return element.ValueKind switch
        {
            System.Text.Json.JsonValueKind.String => element.GetString() ?? "",
            System.Text.Json.JsonValueKind.Number => element.TryGetInt32(out var intVal) ? intVal : element.GetDouble(),
            System.Text.Json.JsonValueKind.True => true,
            System.Text.Json.JsonValueKind.False => false,
            System.Text.Json.JsonValueKind.Array => element.EnumerateArray().Select(ConvertToObject).ToArray(),
            System.Text.Json.JsonValueKind.Object => ConvertToStringObjectDictionary(element),
            System.Text.Json.JsonValueKind.Null => null!,
            _ => element.GetRawText()
        };
    }
}
