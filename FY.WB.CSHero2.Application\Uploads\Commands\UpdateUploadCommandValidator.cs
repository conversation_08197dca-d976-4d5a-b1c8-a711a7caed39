using FluentValidation;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using System;

namespace FY.WB.CSHero2.Application.Uploads.Commands
{
    public class UpdateUploadCommandValidator : AbstractValidator<UpdateUploadCommand>
    {
        public UpdateUploadCommandValidator()
        {
            RuleFor(v => v.Dto.Id)
                .NotEmpty().WithMessage("Id is required.");

            // Filename, ContentType, etc. are optional for update, so only validate if provided
            When(v => v.Dto.Filename != null, () => {
                RuleFor(v => v.Dto.Filename)
                    .NotEmpty().WithMessage("Filename cannot be empty if provided.")
                    .MaximumLength(255).WithMessage("Filename must not exceed 255 characters.");
            });

            When(v => v.Dto.ContentType != null, () => {
                RuleFor(v => v.Dto.ContentType)
                    .NotEmpty().WithMessage("Content Type cannot be empty if provided.")
                    .MaximumLength(100).WithMessage("Content Type must not exceed 100 characters.");
            });
            
            When(v => v.Dto.StoragePath != null, () => {
                RuleFor(v => v.Dto.StoragePath)
                    .NotEmpty().WithMessage("Storage Path cannot be empty if provided.")
                    .MaximumLength(1024).WithMessage("Storage Path must not exceed 1024 characters.");
            });

            RuleFor(v => v.Dto.StorageProvider)
                .MaximumLength(100).WithMessage("Storage Provider must not exceed 100 characters.");

            RuleFor(v => v.Dto.ExternalUrl)
                .MaximumLength(2048).WithMessage("External URL must not exceed 2048 characters.")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
                .When(v => !string.IsNullOrEmpty(v.Dto.ExternalUrl))
                .WithMessage("External URL must be a valid URL.");
                
            RuleFor(v => v.Dto.Checksum)
                .MaximumLength(128).WithMessage("Checksum must not exceed 128 characters.");
        }
    }
}
