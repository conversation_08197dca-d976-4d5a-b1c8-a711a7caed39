'use client';

import { useState, useEffect } from 'react';
import { MotionDiv } from '@/components/ui/motion';
import Link from 'next/link';
import { getCompanyProfile } from '@/lib/api'; // Changed API import
import { CompanyProfile } from '@/types/company'; // Changed type import
import { useToast } from '@/components/providers/toast-provider';
import { Button } from '@/components/ui/button';
import { PageHeader } from '@/components/layouts/page-header';
import { AdminBillingDetailsCard, AdminInvoiceTable, PastDueNotificationCard } from '@/components/features/admin/billing';

export default function AdminBillingPage() {
  const [companyProfileData, setCompanyProfileData] = useState<CompanyProfile | null>(null); // Changed state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchCompanyProfileData = async () => {
      try {
        setLoading(true);
        const profileData = await getCompanyProfile();
        setCompanyProfileData(profileData);
        setError(null);
      } catch (err) {
        console.error('Error fetching company profile:', err);
        setError('Failed to fetch company profile data');
        toast({
          title: 'Error',
          description: 'Failed to fetch company profile data. Ensure you are logged in as Admin.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyProfileData();
  }, [toast]);

  const handleRefreshProfile = async () => {
    try {
      setLoading(true);
      const refreshedProfile = await getCompanyProfile();
      setCompanyProfileData(refreshedProfile);
      toast({
        title: 'Success',
        description: 'Company profile refreshed.',
      });
    } catch (err) {
      console.error('Error refreshing company profile:', err);
      toast({
        title: 'Error',
        description: 'Failed to refresh company profile data.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <PageHeader 
        title="Billing"
        description="View and manage billing information and transactions"
      />
      
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      ) : error ? (
        <div className="bg-card rounded-lg border p-6">
          <p className="text-red-600">{error}</p>
        </div>
      ) : companyProfileData ? (
        <>
          <div className="mb-8 border rounded-xl shadow-lg p-8 gradient-primary-secondary">
            {/* Pass companyProfileData and the refresh handler */}
            <AdminBillingDetailsCard companyProfile={companyProfileData} onUpdate={handleRefreshProfile} />
          </div>
          <PastDueNotificationCard />
          <AdminInvoiceTable />
        </>
      ) : (
        <div className="bg-card rounded-lg border p-6">
          <p className="text-gray-600">Company profile data not found. Ensure it is configured in appsettings.json.</p>
        </div>
      )}
    </div>
  );
}
