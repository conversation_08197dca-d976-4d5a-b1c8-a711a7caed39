# AI-Driven Report Builder

The AI-Driven Report Builder is a next-generation tool for creating professional, customizable reports in CS-Hero. It uses AI to generate high-quality report content based on structured prompts constructed from user inputs.

## Key Features

- **Modern UI**: Clean, intuitive interface with sidebar navigation and contextual panels
- **AI Prompt Construction**: Builds structured prompts from user inputs following the example-prompt/ format
- **Multiple Document Templates**: Support for various document structures and design briefs
- **Render Button**: Initiates the report generation process and stores prompt data
- **Preview Area**: Displays the AI-generated HTML output with all sections in scroll format
- **Chat Interface**: Allows for section-specific refinements through text instructions
- **Template System**: Start with pre-designed templates or create your own
- **Style Customization**: Control typography, colors, and spacing through design brief options

## Component Structure

- **ReportEditor**: Main component that orchestrates the entire editor
- **Sidebar**: Navigation sidebar with tabs for different editor functions
- **PanelContent**: Content panels for each sidebar tab
- **SectionDetailPanel**: Detailed editing panel for individual sections
- **PreviewArea**: Live preview of the current section
- **Preview Components**: Specialized components for rendering different section types
  - CoverPagePreview
  - TextPagePreview
  - ThreeColumnPreview
  - TimelinePreview

## Usage

```tsx
// Basic usage
<ReportEditor />

// With template
<ReportEditor templateId="professional" />

// Edit existing report
<ReportEditor reportId="CSR-2025-001" />

// With callbacks
<ReportEditor 
  onSave={(reportData) => console.log('Saving report', reportData)}
  onCancel={() => console.log('Cancelled editing')}
/>
```

## Data Flow

1. User selects document structure and design brief templates
2. User inputs data through structured forms and fields
3. System constructs an XML-based prompt following the example-prompt/ format
4. When user clicks "Render Report" button, the prompt data is stored in the mock-server in JSON format
5. The preview area displays the AI-generated HTML output (for development, using pre-generated examples)
6. User can refine sections through the chat interface by providing text instructions
7. These refinement instructions are added to the prompt data and stored in the mock-server
8. The updated report can be exported in various formats

**Note:** For development purposes, actual API calls to an AI service are not implemented. Instead, the system prepares and stores the prompt data in the mock-server, ready for future integration with an AI API.

## Future Enhancements

- Integration with actual AI API services
- Advanced prompt engineering capabilities
- Real-time collaborative editing
- Version history and comparison
- Enhanced chat interface with AI suggestions
- Additional document structure templates
- More design brief customization options
- PDF and other export format improvements
