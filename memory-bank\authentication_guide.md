# Authentication & Authorization Implementation Guide

**Version:** 1.1
**Date:** 2025-05-12
**Source Project:** `FY.WB.CSHero2` (ASP.NET Core API) & `FY.WB.CSHero2.UI` (Next.js Frontend)

This guide outlines the steps to implement user authentication (JWT-based) and authorization in a new frontend project, mirroring the system used in the `FY.WB.CSHero2` solution.

## I. Backend Setup (ASP.NET Core API)

This assumes you have an existing ASP.NET Core API project or are setting one up.

### A. Core Dependencies

Ensure your `.csproj` file includes:
```xml
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.x.x" />
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.x.x" />
<!-- Other necessary packages like EF Core, SQL Server provider -->
```

### B. User Model (`ApplicationUser.cs`)

Create a custom user class inheriting from `IdentityUser`:
```csharp
// Data/ApplicationUser.cs
using Microsoft.AspNetCore.Identity;

namespace YourProject.Data
{
    public class ApplicationUser : IdentityUser
    {
        public string? CompanyName { get; set; } // Example custom field
        public string? CompanyUrl { get; set; }  // Example custom field
        // Add other custom fields as needed
    }
}
```

### C. Database Context (`ApplicationDbContext.cs`)

Configure your DbContext to use `ApplicationUser`:
```csharp
// Data/ApplicationDbContext.cs
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace YourProject.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser> // Use your custom ApplicationUser
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }
        // DbSet for other entities if any
    }
}
```

### D. Configuration (`Program.cs`)

1.  **Register DbContext:**
    ```csharp
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    builder.Services.AddDbContext<ApplicationDbContext>(options =>
        options.UseSqlServer(connectionString));
    ```

2.  **Register Identity Services:**
    ```csharp
    builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
    {
        options.SignIn.RequireConfirmedAccount = false; // Adjust as needed
        options.User.RequireUniqueEmail = true;
        // Configure other password/user options
    })
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();
    ```

3.  **Configure JWT Authentication:**
    ```csharp
    builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? string.Empty))
        };
    });
    ```

4.  **Add CORS Policy:** (Allow your new frontend's development and production URLs)
    ```csharp
    const string MyFrontendCorsPolicy = "_myFrontendCorsPolicy";
    builder.Services.AddCors(options =>
    {
        options.AddPolicy(name: MyFrontendCorsPolicy,
                          policyBuilder =>
                          {
                              policyBuilder.WithOrigins("http://localhost:YOUR_FRONTEND_PORT", "https://your-production-frontend.com")
                                           .AllowAnyHeader()
                                           .AllowAnyMethod()
                                           .AllowCredentials(); // Important if frontend sends cookies
                          });
    });
    ```

5.  **Middleware Pipeline:** (Order is important)
    ```csharp
    // ...
    app.UseCors(MyFrontendCorsPolicy); // Apply CORS
    app.UseHttpsRedirection();
    app.UseAuthentication(); // Before UseAuthorization
    app.UseAuthorization();
    // ...
    app.MapControllers();
    ```

### E. Authentication Controller (`AuthController.cs`)

```csharp
// Controllers/AuthController.cs
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging; // Added for logging
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq; // Added for LINQ
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using YourProject.Data; // For ApplicationUser
using Finbuckle.MultiTenant; // Added for multi-tenancy
using Finbuckle.MultiTenant.Abstractions; // Added for multi-tenancy
using Microsoft.AspNetCore.Authorization; // Added for Authorize attribute

namespace YourProject.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager; // Added for roles
        private readonly SignInManager<ApplicationUser> _signInManager; // Added for sign-in checks
        private readonly IConfiguration _configuration;
        private readonly IMultiTenantStore<AppTenantInfo> _tenantStore; // Added for tenant creation
        private readonly ILogger<AuthController> _logger; // Added for logging

        public AuthController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration,
            IMultiTenantStore<AppTenantInfo> tenantStore,
            ILogger<AuthController> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _signInManager = signInManager;
            _configuration = configuration;
            _tenantStore = tenantStore;
            _logger = logger;
        }

        public class RegisterModel
        {
            public string? Email { get; set; }
            public string? Password { get; set; }
            public string? CompanyName { get; set; }
            public string? CompanyUrl { get; set; }
        }
        public class LoginModel { /* ... Email, Password ... */ }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterModel model)
        {
            _logger.LogInformation("Register endpoint called for {Email}", model?.Email);
            // ... (Input validation: Check for null Email, Password, CompanyName)

            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                return BadRequest(new { message = "Email is already registered." });
            }

            // --- Tenant Creation ---
            var tenantId = Guid.NewGuid();
            var identifier = $"{model.CompanyName.ToLower().Replace(" ", "-")}-{Guid.NewGuid().ToString()[..8]}";
            var newTenant = new AppTenantInfo
            {
                Id = tenantId.ToString(),
                Identifier = identifier,
                Name = model.CompanyName,
                ConnectionString = _configuration.GetConnectionString("DefaultConnection") // Or tenant-specific logic
            };
            var addResult = await _tenantStore.TryAddAsync(newTenant);
            if (!addResult)
            {
                _logger.LogError("Failed to add tenant to store");
                return StatusCode(500, new { message = "Failed to create tenant." });
            }
            // --- End Tenant Creation ---

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                EmailConfirmed = true, // Confirm email by default
                CompanyName = model.CompanyName,
                CompanyUrl = model.CompanyUrl,
                TenantId = tenantId, // Assign tenant ID
                CreationTime = DateTime.UtcNow
            };
            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                // Assign default role (e.g., "User")
                string userRoleName = "User";
                if (!await _roleManager.RoleExistsAsync(userRoleName))
                {
                    await _roleManager.CreateAsync(new IdentityRole<Guid>(userRoleName));
                }
                await _userManager.AddToRoleAsync(user, userRoleName);

                _logger.LogInformation("User {Email} registered successfully with Tenant ID {TenantId}", model.Email, tenantId);
                return Ok(new {
                    message = "User and tenant registered successfully",
                    tenantId = tenantId.ToString(),
                    tenantIdentifier = identifier,
                    email = model.Email
                });
            }
            else
            {
                // Clean up tenant if user creation failed
                await _tenantStore.TryRemoveAsync(tenantId.ToString());
                return BadRequest(new { message = "User registration failed", errors = result.Errors.Select(e => e.Description) });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginModel model)
        {
            _logger.LogInformation("Login attempt for {Email}", model?.Email);
            // ... (Input validation)

            // Find user ignoring query filters to check even if TenantId doesn't match current context (if any)
            var user = await _userManager.Users
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(u => u.NormalizedEmail == _userManager.NormalizeEmail(model.Email));

            if (user == null)
            {
                return Unauthorized(new { message = "Invalid credentials" });
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, model.Password, false);
            if (result.Succeeded)
            {
                var authClaims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Email, user.Email ?? ""),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                    new Claim("IsAdmin", user.IsAdmin.ToString()), // Add IsAdmin claim
                };

                // Add tenant ID claim if available
                if (user.TenantId.HasValue)
                {
                    authClaims.Add(new Claim("tenant_id", user.TenantId.Value.ToString()));
                }

                // Add roles as claims
                var userRoles = await _userManager.GetRolesAsync(user);
                foreach (var userRole in userRoles)
                {
                    authClaims.Add(new Claim(ClaimTypes.Role, userRole));
                }

                var jwtKey = _configuration["Jwt:Key"];
                if (string.IsNullOrEmpty(jwtKey)) { /* Handle missing key */ }

                var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey!));

                var token = new JwtSecurityToken( /* ... (same as before) ... */ );

                return Ok(new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = token.ValidTo,
                    userId = user.Id.ToString(),
                    email = user.Email,
                    tenantId = user.TenantId?.ToString(),
                    isAdmin = user.IsAdmin // Return IsAdmin status
                });
            }
            return Unauthorized(new { message = "Invalid credentials" });
        }

        // Example: Endpoint to get current user details
        [HttpGet("me")]
        [Authorize] // Requires authentication
        public async Task<IActionResult> GetCurrentUser()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId)) return Unauthorized();

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) return NotFound();

            var roles = await _userManager.GetRolesAsync(user);

            return Ok(new {
                userId = user.Id.ToString(),
                email = user.Email,
                tenantId = user.TenantId?.ToString(),
                companyName = user.CompanyName,
                isAdmin = user.IsAdmin, // Use the property
                roles = roles,
                authenticated = true
            });
        }

        // Utility endpoint (Admin only)
        [HttpPost("confirm-all-emails")]
        [Authorize(Roles = "Admin")] // Requires Admin role
        public async Task<IActionResult> ConfirmAllEmails()
        {
            // ... (Implementation to find users with EmailConfirmed=false and update them)
            return Ok(new { message = "Email confirmation process completed." });
        }
    }
}
```

### F. Configuration (`appsettings.json`)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=YourProjectDb;Trusted_Connection=True;MultipleActiveResultSets=true"
  },
  "Jwt": {
    "Key": "YOUR_SUPER_SECRET_JWT_KEY_REPLACE_THIS_AND_KEEP_IT_SAFE", // Should be long and random
    "Issuer": "https://your-api-domain.com",
    "Audience": "https://your-frontend-domain.com" // Or a general audience claim
  },
  "Logging": { /* ... */ }
}
```
**Security Note:** For production, the `Jwt:Key` should be stored securely (e.g., Azure Key Vault, environment variables), not directly in `appsettings.json`.

## II. Frontend Setup (e.g., Next.js)

This section provides guidance for a Next.js frontend but can be adapted.

### A. Authentication Context (`AuthContext.tsx`)

This context will manage user state and provide login/logout functions.
```tsx
// src/context/AuthContext.tsx
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation'; // Or your router equivalent

interface AuthUser { id: string; email: string; /* other properties */ }
interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, pass: string) => Promise<void>;
  logout: () => Promise<void>;
  // checkSession: () => Promise<void>; // For session check on load
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Optional: Check session on initial load using a /api/auth/me endpoint
  useEffect(() => {
    const checkUserSession = async () => {
      try {
        const response = await fetch('/api/auth/me'); // BFF endpoint
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        } else {
          setUser(null);
        }
      } catch (error) {
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    checkUserSession();
  }, []);

  const login = async (emailInput: string, passwordInput: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/login', { // BFF endpoint
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: emailInput, password: passwordInput }),
      });
      if (!response.ok) throw new Error('Login failed');
      const data = await response.json(); // Expects { userId, email, ... }
      setUser({ id: data.userId, email: data.email /*, ... */ });
      router.push('/dashboard'); // Or desired protected route
    } catch (error) {
      console.error('Login error:', error);
      setUser(null); // Clear user on error
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await fetch('/api/auth/logout', { method: 'POST' }); // BFF endpoint
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsLoading(false);
      router.push('/login'); // Or home page
    }
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated: !!user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => { /* ... (standard useContext hook) ... */ };
```
**Note:** The `FY.WB.CSHero2.UI` project's `AuthContext` also uses `localStorage` to store the token and user details. This can be useful for immediate UI updates but means the token is accessible via JavaScript. Relying solely on the HttpOnly cookie for API calls and using a `/api/auth/me` endpoint to hydrate `AuthContext` on load is a more secure pattern for the token itself.

### B. BFF API Routes (e.g., Next.js `src/app/api/auth/...`)

1.  **Login (`/api/auth/login/route.ts`):**
    ```typescript
    // src/app/api/auth/login/route.ts
    import { NextResponse } from 'next/server';
    import type { NextRequest } from 'next/server';

    const ASPNET_API_URL = process.env.ASPNET_API_URL || 'http://localhost:YOUR_API_PORT';

    export async function POST(request: NextRequest) {
      try {
        const { email, password } = await request.json();
        // ... (validation)

        const apiResponse = await fetch(`${ASPNET_API_URL}/api/Auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password }),
        });
        const responseBody = await apiResponse.json();

        if (!apiResponse.ok) { /* Handle backend error */ }

        const { token, expiration, userId, email: userEmail } = responseBody;
        if (!token) { /* Handle missing token */ }

        const res = NextResponse.json({ userId, email: userEmail /*, other user data */ });
        res.cookies.set('authToken', token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          path: '/',
          sameSite: 'lax', // Or 'strict'
          expires: new Date(expiration),
        });
        return res;
      } catch (error) { /* Handle internal error */ }
    }
    ```

2.  **Register (`/api/auth/register/route.ts`):**
    Proxies to backend `/api/Auth/register`. Does not typically set cookies.

3.  **Logout (`/api/auth/logout/route.ts`):**
    ```typescript
    // src/app/api/auth/logout/route.ts
    import { NextResponse } from 'next/server';
    export async function POST(request: NextRequest) {
      const response = NextResponse.json({ message: 'Logged out' });
      response.cookies.set('authToken', '', { // Clear the cookie
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        sameSite: 'lax',
        expires: new Date(0), // Expire immediately
      });
      return response;
    }
    ```

4.  **Me / Check Session (`/api/auth/me/route.ts`):** (Recommended)
    ```typescript
    // src/app/api/auth/me/route.ts
    import { NextResponse } from 'next/server';
    import type { NextRequest } from 'next/server';
    import { jwtVerify } from 'jose'; // Or any JWT library to verify/decode

    const ASPNET_API_URL = process.env.ASPNET_API_URL || 'http://localhost:YOUR_API_PORT';
    const JWT_SECRET_KEY = new TextEncoder().encode(process.env.JWT_BFF_SECRET || "YOUR_BACKEND_JWT_KEY_IF_VERIFYING_HERE");


    export async function GET(request: NextRequest) {
      const tokenCookie = request.cookies.get('authToken');
      if (!tokenCookie) {
        return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
      }
      const token = tokenCookie.value;

      try {
        // Option 1: Verify JWT here in BFF (if you have the key and want to avoid backend call)
        // const { payload } = await jwtVerify(token, JWT_SECRET_KEY, {
        //   issuer: process.env.JWT_ISSUER, // From your backend config
        //   audience: process.env.JWT_AUDIENCE, // From your backend config
        // });
        // const user = { id: payload.nameid, email: payload.email /*, ... */ };
        // return NextResponse.json(user);

        // Option 2: Call a protected backend endpoint that returns user info based on the token
        // This is generally preferred as the backend is the source of truth for token validity.
        const backendResponse = await fetch(`${ASPNET_API_URL}/api/User/profile`, { // Example endpoint
            headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!backendResponse.ok) {
            return NextResponse.json({ message: 'Invalid session' }, { status: 401 });
        }
        const userData = await backendResponse.json();
        return NextResponse.json(userData);

      } catch (error) {
        console.error("Session check error:", error);
        return NextResponse.json({ message: 'Invalid token or session error' }, { status: 401 });
      }
    }
    ```
    The backend would need a corresponding `/api/User/profile` (or similar) endpoint protected by `[Authorize]` that returns user details.

### C. Middleware for Route Protection (e.g., Next.js `src/middleware.ts`)

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const protectedPaths = ['/dashboard', '/profile', '/admin']; // Define your protected routes

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const authToken = request.cookies.get('authToken')?.value;

  const isPathProtected = protectedPaths.some((path) => pathname.startsWith(path));

  if (isPathProtected && !authToken) {
    const loginUrl = new URL('/login', request.url);
    // loginUrl.searchParams.set('redirectedFrom', pathname); // Optional
    return NextResponse.redirect(loginUrl);
  }
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Match all paths except API, static files, images, favicon, login page
    '/((?!api|_next/static|_next/image|favicon.ico|login).*)',
  ],
};
```

### D. Making Authenticated API Calls

When making calls from your frontend (either directly or via BFF routes that subsequently call the backend), the `authToken` HttpOnly cookie will be automatically sent by the browser if the API domain matches the frontend domain (or is configured for `SameSite` and `credentials: 'include'` correctly). The backend's `[Authorize]` attribute will handle validation.

## III. Protected Pages/Routes

### A. Backend API Endpoints

Decorate controllers or specific action methods with the `[Authorize]` attribute:
```csharp
[Authorize] // Requires authentication for all actions in this controller
[ApiController]
[Route("api/[controller]")]
public class ProtectedDataController : ControllerBase
{
    [HttpGet]
    public IActionResult Get() { /* ... return sensitive data ... */ }

    [HttpGet("admin")]
    [Authorize(Roles = "Admin")] // Requires authentication AND Admin role
    public IActionResult GetAdminData() { /* ... */ }
}
```

### B. Frontend Pages

Route protection is primarily handled by the frontend middleware (as shown above). Inside components, you can use `useAuth().isAuthenticated` or `useAuth().user` to conditionally render UI elements or perform additional checks if needed.

This guide provides a comprehensive starting point. You'll need to adapt paths, URLs, and specific logic to your new project's structure and requirements.
