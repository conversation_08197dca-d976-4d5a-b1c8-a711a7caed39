using FluentValidation;
using FY.WB.CSHero2.Application.Clients.Dtos;

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class UpdateClientCommandValidator : AbstractValidator<UpdateClientCommand>
    {
        public UpdateClientCommandValidator()
        {
            RuleFor(v => v.Id)
                .NotEmpty().WithMessage("Client ID is required for an update.");

            RuleFor(v => v.ClientDto.Name)
                .MaximumLength(100).When(x => !string.IsNullOrEmpty(x.ClientDto.Name))
                .WithMessage("Name must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Email)
                .EmailAddress().When(x => !string.IsNullOrEmpty(x.ClientDto.Email))
                .WithMessage("A valid email is required.")
                .MaximumLength(100).When(x => !string.IsNullOrEmpty(x.ClientDto.Email))
                .WithMessage("Email must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Status)
                .MaximumLength(50).When(x => !string.IsNullOrEmpty(x.ClientDto.Status))
                .WithMessage("Status must not exceed 50 characters.");

            RuleFor(v => v.ClientDto.CompanyName)
                .MaximumLength(100).When(x => !string.IsNullOrEmpty(x.ClientDto.CompanyName))
                .WithMessage("Company name must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Phone)
                .MaximumLength(20).When(x => x.ClientDto.Phone != null) // Check for null as it's nullable
                .WithMessage("Phone must not exceed 20 characters.");

            RuleFor(v => v.ClientDto.Address)
                .MaximumLength(200).When(x => x.ClientDto.Address != null)
                .WithMessage("Address must not exceed 200 characters.");
            
            RuleFor(v => v.ClientDto.CompanySize)
                .MaximumLength(50).When(x => x.ClientDto.CompanySize != null)
                .WithMessage("Company size must not exceed 50 characters.");

            RuleFor(v => v.ClientDto.Industry)
                .MaximumLength(100).When(x => x.ClientDto.Industry != null)
                .WithMessage("Industry must not exceed 100 characters.");
        }
    }
}
