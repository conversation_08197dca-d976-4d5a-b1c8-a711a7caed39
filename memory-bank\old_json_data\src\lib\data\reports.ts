import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';

export interface Report {
  reportId: string;
  clientId: string;
  clientName: string;
  reportName: string;
  category: string;
  slideCount: number;
  createdAt: string;
  lastModified: string;
  status: string;
  author: string;
}

/**
 * Get all reports with optional filtering, sorting, and pagination
 * @param options Query options for filtering, sorting, and pagination
 * @returns Array of reports
 */
export async function getReports(options: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
} = {}): Promise<Report[]> {
  const { query, limit, page, sortBy, order } = options;
  
  const data = await readJsonFile<any>();
  
  return processArray(data.reports, {
    query,
    sortBy,
    order,
    page: page ? parseInt(page.toString()) : undefined,
    limit: limit ? parseInt(limit.toString()) : undefined
  });
}

/**
 * Get a report by ID
 * @param id Report ID
 * @returns Report object or null if not found
 */
export async function getReportById(id: string): Promise<Report | null> {
  const data = await readJsonFile<any>();
  const report = data.reports.find((r: Report) => r.reportId === id);
  return report || null;
}

/**
 * Create a new report
 * @param report Report data
 * @returns Created report
 */
export async function createReport(report: Omit<Report, 'reportId' | 'createdAt' | 'lastModified'>): Promise<Report> {
  const data = await readJsonFile<any>();
  
  // Generate a new report ID in the format CSR-YYYY-XXX
  const year = new Date().getFullYear();
  const existingReports = data.reports.filter((r: Report) => r.reportId.startsWith(`CSR-${year}`));
  
  let reportNumber = 1;
  if (existingReports.length > 0) {
    const lastReportId = existingReports[existingReports.length - 1].reportId;
    const match = lastReportId.match(/CSR-\d{4}-(\d{3})/);
    if (match) {
      reportNumber = parseInt(match[1], 10) + 1;
    }
  }
  
  const reportId = `CSR-${year}-${reportNumber.toString().padStart(3, '0')}`;
  const now = new Date().toISOString();
  
  const newReport: Report = {
    ...report,
    reportId,
    createdAt: now,
    lastModified: now
  };
  
  data.reports.push(newReport);
  
  await writeJsonFile('db.json', data);
  
  return newReport;
}

/**
 * Update a report
 * @param id Report ID
 * @param updates Report data to update
 * @returns Updated report or null if not found
 */
export async function updateReport(
  id: string,
  updates: Partial<Omit<Report, 'reportId' | 'createdAt'>>
): Promise<Report | null> {
  const data = await readJsonFile<any>();
  
  const reportIndex = data.reports.findIndex((r: Report) => r.reportId === id);
  if (reportIndex === -1) return null;
  
  const updatedReport: Report = {
    ...data.reports[reportIndex],
    ...updates,
    lastModified: new Date().toISOString()
  };
  
  data.reports[reportIndex] = updatedReport;
  
  await writeJsonFile('db.json', data);
  
  return updatedReport;
}

/**
 * Delete a report
 * @param id Report ID
 * @returns True if report was deleted, false if not found
 */
export async function deleteReport(id: string): Promise<boolean> {
  const data = await readJsonFile<any>();
  
  const reportIndex = data.reports.findIndex((r: Report) => r.reportId === id);
  if (reportIndex === -1) return false;
  
  data.reports.splice(reportIndex, 1);
  
  await writeJsonFile('db.json', data);
  
  return true;
}
