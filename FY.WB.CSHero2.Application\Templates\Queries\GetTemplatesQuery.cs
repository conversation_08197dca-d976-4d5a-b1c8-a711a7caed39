using MediatR;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Templates.Dtos;

namespace FY.WB.CSHero2.Application.Templates.Queries
{
    public class GetTemplatesQuery : IRequest<PagedResult<TemplateDto>>
    {
        public TemplateQueryParametersDto Parameters { get; }

        public GetTemplatesQuery(TemplateQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
