using MediatR;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Invoices.Dtos;

namespace FY.WB.CSHero2.Application.Invoices.Queries
{
    public class GetInvoicesQuery : IRequest<PagedResult<InvoiceDto>>
    {
        public InvoiceQueryParametersDto Parameters { get; }

        public GetInvoicesQuery(InvoiceQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
