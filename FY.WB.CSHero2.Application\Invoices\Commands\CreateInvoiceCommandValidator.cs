using FluentValidation;
using FY.WB.CSHero2.Application.Invoices.Dtos;
using System;
using System.Linq;

namespace FY.WB.CSHero2.Application.Invoices.Commands
{
    public class CreateInvoiceCommandValidator : AbstractValidator<CreateInvoiceCommand>
    {
        private readonly string[] _validTypes = { "Invoice", "Payment", "Refund" };
        private readonly string[] _validStatuses = { "paid", "overdue", "pending", "cancelled", "refunded" }; // Added more statuses

        public CreateInvoiceCommandValidator()
        {
            RuleFor(v => v.Dto.Type)
                .NotEmpty().WithMessage("Type is required.")
                .Must(type => _validTypes.Contains(type, StringComparer.OrdinalIgnoreCase))
                .WithMessage($"Type must be one of the following: {string.Join(", ", _validTypes)}.")
                .MaximumLength(50).WithMessage("Type must not exceed 50 characters.");

            RuleFor(v => v.Dto.Plans)
                .NotEmpty().WithMessage("Plans are required.")
                .MaximumLength(200).WithMessage("Plans must not exceed 200 characters.");

            RuleFor(v => v.Dto.Amount)
                .GreaterThan(0).WithMessage("Amount must be greater than 0.");

            RuleFor(v => v.Dto.Status)
                .NotEmpty().WithMessage("Status is required.")
                .Must(status => _validStatuses.Contains(status, StringComparer.OrdinalIgnoreCase))
                .WithMessage($"Status must be one of the following: {string.Join(", ", _validStatuses)}.")
                .MaximumLength(50).WithMessage("Status must not exceed 50 characters.");

            RuleFor(v => v.Dto.Date)
                .NotEmpty().WithMessage("Date is required.");
        }
    }
}
