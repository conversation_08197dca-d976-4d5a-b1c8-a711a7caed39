using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Templates.Commands
{
    public class UpdateTemplateCommandHandler : IRequestHandler<UpdateTemplateCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateTemplateCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateTemplateCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Templates
                .FirstOrDefaultAsync(t => t.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Template), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this template.");
            }

            entity.UpdateDetails(
                request.Dto.Name,
                request.Dto.Description,
                request.Dto.Category,
                request.Dto.ThumbnailUrl
            );

            if (request.Dto.Tags != null) // Allow clearing tags by passing empty or null
            {
                entity.SetTags(request.Dto.Tags);
            }

            if (request.Dto.Sections != null) // Allow clearing sections
            {
                entity.SetSections(request.Dto.Sections);
            }

            if (request.Dto.Fields != null) // Allow clearing fields
            {
                entity.SetFields(request.Dto.Fields);
            }

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
