using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Interface for HTML validation
    /// </summary>
    public interface IHtmlValidator
    {
        /// <summary>
        /// Validates the HTML content
        /// </summary>
        /// <param name="html">The HTML content to validate</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The validation result</returns>
        Task<ValidationResult> ValidateAsync(string html, CancellationToken cancellationToken = default);
    }
}
