'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { 
  MoreHorizontal, 
  ArrowUpDown,
  FileEdit,
  Archive,
  Trash2,
  Users
} from 'lucide-react';
import { Client } from '@/types/client';
import { getClients, deleteClient, archiveClient } from '@/lib/api';
import { useToast } from '@/components/providers/toast-provider';

interface ClientTableProps {
  searchQuery?: string;
}

export function ClientTable({ searchQuery = '' }: ClientTableProps) {
  const [sortBy, setSortBy] = useState<keyof Client>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0); // Added for total count
  const [limit] = useState(10);
  const { toast } = useToast();

  const fetchClients = useCallback(async () => {
    try {
      setLoading(true);
      // getClients returns { data: PagedResult, headers }
      // PagedResult from backend has { items, page, pageSize, totalCount, totalPages }
      const response = await getClients({
        search: searchQuery || undefined,
        sortBy: sortBy,
        sortOrder: sortOrder,
        page,
        limit,
      });
      // response.data is the PagedResult object
      const pagedData = response.data; 
      setClients(pagedData.items || []);
      setTotalPages(pagedData.totalPages || 1);
      setTotalCount(pagedData.totalCount || 0);
      setPage(pagedData.page || 1); // Ensure page state is in sync
      setError(null);
    } catch (err) {
      setError('Failed to fetch clients');
      toast({
        title: 'Error',
        description: 'Failed to fetch clients',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [searchQuery, sortBy, sortOrder, page, limit, toast]);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  const handleSort = (column: keyof Client) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteClient(id);
      toast({
        title: 'Success',
        description: 'Client deleted successfully',
      });
      fetchClients();
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to delete client',
        variant: 'destructive',
      });
    }
  };

  const handleArchive = async (id: string) => {
    try {
      await archiveClient(id);
      toast({
        title: 'Success',
        description: 'Client archived successfully',
      });
      fetchClients();
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to archive client',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="rounded-lg border bg-gradient-to-b from-card to-card/95 shadow-md transition-all duration-200">
      <div className="relative">
        <Table>
          <TableHeader className="bg-secondary/25 rounded-t-lg">
            <TableRow className="hover:bg-muted/5 transition-colors">
              <TableHead className="w-[250px]">
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('companyName')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Company
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[200px]">
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('name')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Contact
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[250px]">
                <div className="flex items-center gap-1">Email</div>
              </TableHead>
              <TableHead className="w-[150px]">
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('status')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Status
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-red-600">
                  {error}
                </TableCell>
              </TableRow>
            ) : clients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <p className="text-muted-foreground">No clients found</p>
                    <p className="text-sm text-muted-foreground">Add your first client to get started</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              clients.map((client) => (
                <TableRow 
                  key={client.id} 
                  className="hover:bg-primary/5 hover:shadow-sm cursor-pointer transition-all duration-200"
                >
                  <TableCell>
                    <Link href={`/client/clients/${client.id}`} className="block group">
                      <div className="font-medium text-primary/90 group-hover:text-primary transition-colors">{client.companyName}</div>
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link href={`/client/clients/${client.id}`} className="block group">
                      <div className="font-medium text-foreground/90 group-hover:text-primary transition-colors">{client.name}</div>
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link href={`/client/clients/${client.id}`} className="block group">
                      <div className="text-sm text-muted-foreground group-hover:text-primary/80 transition-colors">{client.email}</div>
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link href={`/client/clients/${client.id}`} className="block group">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                        client.status?.toLowerCase() === 'active' 
                          ? 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20 group-hover:bg-emerald-100 group-hover:ring-emerald-600/30' 
                          : client.status?.toLowerCase() === 'inactive'
                          ? 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20 group-hover:bg-amber-100 group-hover:ring-amber-600/30'
                          : client.status?.toLowerCase() === 'deleted'
                          ? 'bg-gradient-to-r from-red-50 to-red-100/50 text-red-700 ring-1 ring-red-600/20 group-hover:bg-red-100 group-hover:ring-red-600/30'
                          : 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20 group-hover:bg-slate-100 group-hover:ring-slate-600/30'
                      }`}>
                        {client.status}
                      </span>
                    </Link>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-primary/5 hover:text-primary transition-colors">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem asChild className="flex items-center gap-2 cursor-pointer">
                          <Link href={`/client/clients/${client.id}`}>
                            <Users className="h-4 w-4 mr-2" />
                            View Profile
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                          <FileEdit className="h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={() => handleArchive(client.id)}
                        >
                          <Archive className="h-4 w-4" />
                          Archive
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 text-red-600 cursor-pointer"
                          onClick={() => handleDelete(client.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-4 py-4 border-t bg-muted/5">
        <p className="text-sm text-muted-foreground">
          Page {page} of {totalPages}
        </p>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1 || loading}
            className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages || loading}
            className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
