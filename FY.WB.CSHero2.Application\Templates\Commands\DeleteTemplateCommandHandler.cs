using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Templates.Commands
{
    public class DeleteTemplateCommandHandler : IRequestHandler<DeleteTemplateCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public DeleteTemplateCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(DeleteTemplateCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Templates
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Template), request.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to delete this template.");
            }

            _context.Templates.Remove(entity);
            // For soft delete, you would do:
            // entity.IsDeleted = true;
            // entity.DeletionTime = DateTime.UtcNow;
            // entity.DeleterId = _currentUserService.UserId; // Assuming UserId is available
            // _context.Templates.Update(entity);

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
