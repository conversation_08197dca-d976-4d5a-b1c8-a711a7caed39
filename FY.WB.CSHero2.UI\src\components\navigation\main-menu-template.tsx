"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";
import { MenuProps } from "./types";

export function MainMenuTemplate({ items, pathname }: MenuProps) {
  const isActive = (href: string) => pathname.startsWith(href);

  return (
    <div className="flex flex-col items-center space-y-6">
      {items.map((item) => (
        <div key={item.href} className="w-full flex flex-col items-center">
          <Link
            href={item.href}
            className={cn(
              "nav-transition flex flex-col items-center justify-center px-2 py-2 text-white/80 hover:bg-white/10 hover:text-white rounded-lg w-full",
              isActive(item.href) && "bg-white/10 text-white"
            )}
          >
            <item.icon className="h-6 w-6 mb-1" />
            <span className="text-xs text-center whitespace-nowrap">
              {item.label}
            </span>
          </Link>
        </div>
      ))}
    </div>
  );
}
