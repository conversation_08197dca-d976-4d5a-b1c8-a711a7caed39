using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using FY.WB.CSHero2.Infrastructure.Persistence;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;

namespace FY.WB.CSHero2.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IConfiguration _configuration;
        private readonly IMultiTenantStore<AppTenantInfo> _tenantStore;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration,
            IMultiTenantStore<AppTenantInfo> tenantStore,
            ILogger<AuthController> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _signInManager = signInManager;
            _configuration = configuration;
            _tenantStore = tenantStore;
            _logger = logger;
        }

        [HttpGet("test")]
        public IActionResult Test()
        {
            _logger.LogCritical("AUTH TEST ENDPOINT CALLED");
            return Ok(new { message = "Auth controller test works!" });
        }

        public class RegisterModel
        {
            public string? Email { get; set; }
            public string? Password { get; set; }
            public string? CompanyName { get; set; }
            public string? CompanyUrl { get; set; }
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterModel model)
        {
            try
            {
                // Add CRITICAL log at the very beginning to help diagnose if we're hitting this endpoint
                _logger.LogCritical("AUTH REGISTER ENDPOINT CALLED: Request received from {RequestIP}",
                    HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown");

                // Detailed debugging info about the request
                _logger.LogInformation("Request method: {Method}, Content-Type: {ContentType}, Content-Length: {ContentLength}",
                    Request.Method,
                    Request.ContentType,
                    Request.ContentLength);

                // Log the received model data (exclude sensitive info like password)
                _logger.LogInformation("Register model received: Email={Email}, CompanyName={CompanyName}",
                    model?.Email, model?.CompanyName);

                // Explicitly log if any of the required fields are null to help debugging
                if (model?.Email == null)
                    _logger.LogWarning("Email is null");
                if (model?.Password == null)
                    _logger.LogWarning("Password is null");
                if (string.IsNullOrEmpty(model?.CompanyName))
                    _logger.LogWarning("CompanyName is null or empty");

                if (model?.Email == null || model?.Password == null || string.IsNullOrEmpty(model?.CompanyName))
                {
                    _logger.LogWarning("Register validation failed: missing required fields");
                    return BadRequest(new { message = "Email, Password, and Company Name are required." });
                }

                // Check if user already exists
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    _logger.LogWarning("Registration failed: Email {Email} is already registered", model.Email);
                    return BadRequest(new { message = "Email is already registered." });
                }

                // Log DB access attempt
                _logger.LogInformation("Attempting database operations for registration");

                // Create a new tenant based on the company name
                var tenantId = Guid.NewGuid();

                // Generate a unique identifier from the company name (lowercase, no spaces)
                var identifier = model.CompanyName.ToLower().Replace(" ", "-");

                // Ensure identifier is unique by appending a random suffix
                identifier = $"{identifier}-{Guid.NewGuid().ToString()[..8]}";

                // Create the new tenant
                var newTenant = new AppTenantInfo
                {
                    Id = tenantId.ToString(),
                    Identifier = identifier,
                    Name = model.CompanyName,
                    // Use the default connection string for now
                    ConnectionString = _configuration.GetConnectionString("DefaultConnection")
                };

                // Log connection string to verify it's properly loaded
                _logger.LogInformation("Using connection string: {ConnectionString}",
                    _configuration.GetConnectionString("DefaultConnection"));

                // Add the tenant to the store
                _logger.LogInformation("Attempting to add tenant with ID {TenantId} and identifier {Identifier}",
                    tenantId, identifier);

                var addResult = await _tenantStore.TryAddAsync(newTenant);
                if (!addResult)
                {
                    _logger.LogError("Failed to add tenant to store");
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        new { message = "Failed to add tenant to store" });
                }

                _logger.LogInformation("Successfully added tenant with ID {TenantId}", tenantId);

                // Create the user and associate with the new tenant
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    EmailConfirmed = true, // Set EmailConfirmed to true for all new users
                    CompanyName = model.CompanyName,
                    CompanyUrl = model.CompanyUrl,
                    TenantId = tenantId, // Assign the newly created tenant ID to the user
                    CreationTime = DateTime.UtcNow
                };

                _logger.LogInformation("Attempting to create user with email {Email}", model.Email);
                var result = await _userManager.CreateAsync(user, model.Password);

                if (result.Succeeded)
                {
                    // Ensure the "User" role exists
                    string userRoleName = "User";
                    if (!await _roleManager.RoleExistsAsync(userRoleName))
                    {
                        await _roleManager.CreateAsync(new IdentityRole<Guid>(userRoleName));
                        _logger.LogInformation("Created 'User' role as it didn't exist");
                    }

                    // Assign the user to the "User" role (not Admin)
                    await _userManager.AddToRoleAsync(user, userRoleName);
                    _logger.LogInformation("User {Email} assigned to 'User' role", model.Email);

                    _logger.LogInformation("User registration successful for {Email}", model.Email);
                    return Ok(new {
                        message = "User and tenant registered successfully",
                        tenantId = tenantId.ToString(),
                        tenantIdentifier = identifier,
                        email = model.Email
                    });
                }
                else
                {
                    _logger.LogError("Failed to create user: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));

                    // Try to clean up the tenant if user creation failed
                    await _tenantStore.TryRemoveAsync(tenantId.ToString());

                    return BadRequest(new {
                        message = "User registration failed",
                        errors = result.Errors.Select(e => e.Description)
                    });
                }
            }
            catch (Exception ex)
            {
                // Log the exception with full stack trace
                _logger.LogError(ex, "Unhandled exception in Register endpoint: {ErrorMessage}", ex.Message);

                // Log any inner exceptions as well
                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "Inner exception: {ErrorMessage}",
                        ex.InnerException.Message);
                }

                return StatusCode(500,
                    new {
                        message = $"Unhandled exception: {ex.Message}",
                        stackTrace = ex.StackTrace,
                        innerException = ex.InnerException?.Message
                    });
            }
        }

        public class LoginModel
        {
            public string? Email { get; set; }
            public string? Password { get; set; }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginModel model)
        {
            try
            {
                _logger.LogInformation("AUTH LOGIN ENDPOINT CALLED"); // Changed from LogCritical to LogInformation

                if (model?.Email == null || model?.Password == null)
                {
                    return BadRequest(new { message = "Email and Password are required." });
                }

                // Find the user by email - we need to handle multi-tenancy
                // First, try to find the user directly without tenant filtering
                var normalizedEmail = _userManager.NormalizeEmail(model.Email);

                // Log the normalized email for debugging
                _logger.LogInformation("Looking for user with normalized email: {NormalizedEmail}", normalizedEmail);

                // Use a direct query to bypass tenant filtering and check for soft-deleted users
                var user = await _userManager.Users
                    .Where(u => u.NormalizedEmail == normalizedEmail)
                    .IgnoreQueryFilters() // This bypasses global query filters like IsDeleted and TenantId
                    .FirstOrDefaultAsync();

                // Log the SQL query for debugging (if possible)
                _logger.LogInformation("Executed query to find user by email");

                if (user == null)
                {
                    _logger.LogWarning("Login failed: User with email {Email} not found", model.Email);
                    return Unauthorized(new { message = "Invalid credentials" });
                }

                // Log found user details for debugging
                _logger.LogInformation("Found user: ID={UserId}, Email={Email}, TenantId={TenantId}, EmailConfirmed={EmailConfirmed}",
                    user.Id, user.Email, user.TenantId, user.EmailConfirmed);

                // Check if email is confirmed
                if (!user.EmailConfirmed)
                {
                    _logger.LogWarning("Login attempt with unconfirmed email: {Email}", user.Email);
                    // We'll continue with the login process, but log the warning
                }

                // Check if the password is correct
                var result = await _signInManager.CheckPasswordSignInAsync(user, model.Password, false);
                if (result.Succeeded)
                {
                    _logger.LogInformation("User {Email} logged in successfully", user.Email);

                    // Create claims for the token
                    var authClaims = new List<Claim>
                    {
                        new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                        new Claim(ClaimTypes.Email, user.Email ?? ""),
                        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                        // Add IsAdmin claim
                        new Claim("IsAdmin", user.IsAdmin.ToString()),
                    };

                    // Add tenant ID claim if available
                    if (user.TenantId.HasValue)
                    {
                        authClaims.Add(new Claim("tenant_id", user.TenantId.Value.ToString()));
                    }

                    // Add roles as claims
                    var userRoles = await _userManager.GetRolesAsync(user);
                    foreach (var userRole in userRoles)
                    {
                        authClaims.Add(new Claim(ClaimTypes.Role, userRole));
                    }

                    // Check if JWT key is configured
                    var jwtKey = _configuration["Jwt:Key"];
                    if (string.IsNullOrEmpty(jwtKey))
                    {
                        _logger.LogError("JWT Key is not configured");
                        return StatusCode(StatusCodes.Status500InternalServerError,
                            new { message = "JWT Key is not configured." });
                    }

                    // Create and sign the token
                    var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
                    var token = new JwtSecurityToken(
                        issuer: _configuration["Jwt:Issuer"],
                        audience: _configuration["Jwt:Audience"],
                        expires: DateTime.Now.AddHours(3),
                        claims: authClaims,
                        signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                    );

                    // Return the token and user information
                    return Ok(new
                    {
                        token = new JwtSecurityTokenHandler().WriteToken(token),
                        expiration = token.ValidTo,
                        userId = user.Id.ToString(),
                        email = user.Email,
                        tenantId = user.TenantId?.ToString(),
                        isAdmin = user.IsAdmin // Use the IsAdmin property directly
                    });
                }
                else
                {
                    _logger.LogWarning("Login failed: Invalid password for user {Email}", model.Email);
                    return Unauthorized(new { message = "Invalid credentials" });
                }
            }
            catch (Exception ex)
            {
                // Log the exception with full stack trace
                _logger.LogError(ex, "Unhandled exception in Login endpoint: {ErrorMessage}", ex.Message);

                // Log any inner exceptions as well
                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "Inner exception: {ErrorMessage}",
                        ex.InnerException.Message);
                }

                return StatusCode(500,
                    new {
                        message = $"Unhandled exception: {ex.Message}",
                        stackTrace = ex.StackTrace,
                        innerException = ex.InnerException?.Message
                    });
            }
        }

        [HttpGet("me")]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                // Get the user ID from the claims
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "Not authenticated" });
                }

                // Find the user by ID
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "User not found" });
                }

                // Get user roles
                var roles = await _userManager.GetRolesAsync(user);
                var isAdmin = roles.Contains("Admin");

                // Return user information
                return Ok(new
                {
                    userId = user.Id.ToString(),
                    email = user.Email,
                    tenantId = user.TenantId?.ToString(),
                    companyName = user.CompanyName,
                    isAdmin,
                    authenticated = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCurrentUser: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while fetching user information" });
            }
        }

        // Utility endpoint to confirm all user emails (for development/admin use only)
        [HttpPost("confirm-all-emails")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ConfirmAllEmails()
        {
            try
            {
                _logger.LogInformation("Starting to confirm all user emails");

                // Get all users with unconfirmed emails
                var unconfirmedUsers = await _userManager.Users
                    .Where(u => !u.EmailConfirmed)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} users with unconfirmed emails", unconfirmedUsers.Count);

                int successCount = 0;
                List<string> errors = new List<string>();

                // Confirm each user's email
                foreach (var user in unconfirmedUsers)
                {
                    user.EmailConfirmed = true;
                    var result = await _userManager.UpdateAsync(user);

                    if (result.Succeeded)
                    {
                        successCount++;
                        _logger.LogInformation("Confirmed email for user {Email}", user.Email);
                    }
                    else
                    {
                        string errorMessage = string.Join(", ", result.Errors.Select(e => e.Description));
                        errors.Add($"Failed to confirm email for {user.Email}: {errorMessage}");
                        _logger.LogError("Failed to confirm email for user {Email}: {ErrorMessage}", user.Email, errorMessage);
                    }
                }

                return Ok(new {
                    message = $"Email confirmation process completed. {successCount} users updated successfully.",
                    totalProcessed = unconfirmedUsers.Count,
                    successCount = successCount,
                    errors = errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ConfirmAllEmails: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while confirming emails" });
            }
        }
    }
}
