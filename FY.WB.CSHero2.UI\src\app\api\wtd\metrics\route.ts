import { NextResponse } from 'next/server';
import { getWTDMetrics } from '@/lib/data/metrics';

/**
 * GET /api/wtd/metrics
 * Returns week-to-date metrics data
 */
export async function GET() {
  try {
    const metrics = await getWTDMetrics();
    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching week-to-date metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}
