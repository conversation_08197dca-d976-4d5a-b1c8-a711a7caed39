using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ApplicationUserConfiguration : IEntityTypeConfiguration<ApplicationUser>
    {
        public void Configure(EntityTypeBuilder<ApplicationUser> builder)
        {
            // Properties
            builder.Property(u => u.CompanyName)
                .HasMaxLength(100);

            builder.Property(u => u.CompanyUrl)
                .HasMaxLength(100);

            // Enforce foreign key relationship to TenantProfile
            builder.HasOne<TenantProfile>()
                .WithMany()
                .HasForeignKey(u => u.TenantId)
                .OnDelete(DeleteBehavior.Restrict) // Prevent accidental deletion of users if a tenant is deleted
                .IsRequired(false); // Allow admin users with no tenant

            // Audit properties
            builder.Property(u => u.CreationTime)
                .IsRequired();

            // Additional indexes
            builder.HasIndex(u => u.TenantId);
            builder.HasIndex(u => u.Email);
        }
    }
}
