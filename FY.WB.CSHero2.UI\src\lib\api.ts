import { Client } from '@/types/client';
import { CompanyProfile } from '@/types/company';
import { Report, PaginatedResponse } from '@/types/report';
import { Subscription } from '@/types/subscription';
import { Tenant } from '@/types/tenant';
import { NEXT_API_BASE_URL } from '@/lib/constants';

// Auth API functions
export async function logout() {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/auth/logout`, {
    method: 'POST',
    credentials: 'include', // Important for handling cookies
  });
  if (!response.ok) {
    throw new Error('Failed to logout');
  }
  // Clear any local storage/session storage if needed
  localStorage.clear();
  sessionStorage.clear();
  // Redirect to login page
  window.location.href = '/';
}

export async function getCurrentUser() {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/auth/me`, {
    credentials: 'include'
  });
  if (!response.ok) {
    throw new Error('Failed to fetch user');
  }
  return response.json();
}

export async function sendPasswordReset(email: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/auth/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email }),
  });

  if (!response.ok) {
    throw new Error('Failed to send password reset email');
  }

  return response.json();
}

export async function createClient(data: Partial<Client>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/clients`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create client');
  }
  return response.json();
}

export async function getClientById(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/clients/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch client');
  }
  return response.json();
}

export async function getClients(params?: {
  search?: string;
  status?: 'active' | 'inactive';
  industry?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.search) queryParams.append('q', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.industry) queryParams.append('industry', params.industry);
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  console.log(`Fetching clients from: ${NEXT_API_BASE_URL}/v1/clients?${queryParams}`);
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/clients?${queryParams}`);
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error fetching clients: ${response.status} ${errorText}`);
    throw new Error(`Failed to fetch clients: ${response.status} ${errorText}`);
  }
  const data = await response.json();
  return {
    data,
    headers: response.headers
  };
}

export async function deleteClient(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/clients/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete client');
  }
}

export async function archiveClient(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/clients/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status: 'inactive' }),
  });
  if (!response.ok) {
    throw new Error('Failed to archive client');
  }
  return response.json();
}

// Admin API functions
export async function getAdminTenant() {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/admin`);
  if (!response.ok) {
    throw new Error('Failed to fetch admin tenant');
  }
  return response.json();
}

export async function updateAdminTenant(data: Partial<Tenant>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/admin`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update admin tenant');
  }
  return response.json();
}

// Tenant API functions
export async function createTenant(data: Partial<Tenant>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create tenant');
  }
  return response.json();
}

export async function getTenantById(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch tenant');
  }
  return response.json();
}

export async function getTenants(params?: {
  search?: string;
  status?: 'active' | 'suspended';
  subscription?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.search) queryParams.append('q', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.subscription) queryParams.append('subscription', params.subscription);
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants?${queryParams}`);
  if (!response.ok) {
    throw new Error('Failed to fetch tenants');
  }
  const data = await response.json();
  return {
    data,
    headers: response.headers
  };
}

export async function deleteTenant(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete tenant');
  }
}

export async function archiveTenant(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status: 'suspended' }),
  });
  if (!response.ok) {
    throw new Error('Failed to archive tenant');
  }
  return response.json();
}

export async function updateTenant(id: string, data: Partial<Tenant>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update tenant');
  }
  return response.json();
}

// Report API functions
export async function getReports(params?: {
  search?: string;
  status?: string;
  category?: string;
  clientId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.search) queryParams.append('q', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.category) queryParams.append('category', params.category);
    if (params.clientId) queryParams.append('clientId', params.clientId);
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  console.log(`Fetching reports from: ${NEXT_API_BASE_URL}/v1/reports?${queryParams}`);
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/reports?${queryParams}`);
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error fetching reports: ${response.status} ${errorText}`);
    throw new Error(`Failed to fetch reports: ${response.status} ${errorText}`);
  }
  const responseData: PaginatedResponse<Report> = await response.json(); // Assuming PaginatedResponse is defined
  return {
    items: responseData.items, // Or whatever the array property is named in PagedResult
    totalCount: responseData.total, // Or totalItems, totalRecords
    totalPages: responseData.totalPages,
    currentPage: responseData.page,
    // Keep headers if still needed for other purposes, or remove if not
    headers: response.headers
  };
}

export async function createReport(data: Partial<Report>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/reports`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create report');
  }
  return response.json();
}

export async function updateReport(reportId: string, data: Partial<Report>) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/reports/${reportId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update report');
  }
  return response.json();
}

export async function getReportById(reportId: string): Promise<Report> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/reports/${reportId}`, {
    credentials: 'include'
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error fetching report ${reportId}: ${response.status} ${errorText}`);
    throw new Error(`Failed to fetch report ${reportId}: ${response.status} ${errorText}`);
  }
  return response.json();
}

export async function deleteReport(reportId: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/reports/${reportId}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete report');
  }
}

// Subscription API functions
export async function getSubscriptions(params?: {
  type?: 'basic' | 'professional' | 'enterprise';
  billingCycle?: 'monthly' | 'quarterly' | 'annual';
  isPopular?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.type) queryParams.append('type', params.type);
    if (params.billingCycle) queryParams.append('billingCycle', params.billingCycle);
    if (params.isPopular !== undefined) queryParams.append('isPopular', params.isPopular.toString());
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  const response = await fetch(`${NEXT_API_BASE_URL}/v1/subscriptions?${queryParams}`);
  if (!response.ok) {
    throw new Error('Failed to fetch subscriptions');
  }
  const data = await response.json();
  return {
    data,
    headers: response.headers
  };
}

export async function getSubscriptionById(id: string) {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/subscriptions/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch subscription');
  }
  return response.json();
}

export async function getSubscriptionByTypeAndBillingCycle(
  type: 'basic' | 'professional' | 'enterprise',
  billingCycle: 'monthly' | 'quarterly' | 'annual'
) {
  const queryParams = new URLSearchParams({
    type,
    billingCycle
  });

  const response = await fetch(`${NEXT_API_BASE_URL}/v1/subscriptions/by-type-and-cycle?${queryParams}`);
  if (!response.ok) {
    throw new Error('Failed to fetch subscription');
  }
  return response.json();
}

export async function getPopularSubscriptions() {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/subscriptions/popular`);
  if (!response.ok) {
    throw new Error('Failed to fetch popular subscriptions');
  }
  return response.json();
}

// Report Rendering API functions

// Placeholder type - replace with actual definition
export interface RenderingStatus {
  status: string;
  progress?: number;
  message?: string;
}

export async function renderReport(reportId: string): Promise<string> { // Fixed parameter name back to reportId
  // Calls the BFF route at /api/reports/[reportId]/render
  const response = await fetch(`${NEXT_API_BASE_URL}/reports/${reportId}/render`, {
    method: 'POST',
    credentials: 'include',
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error rendering report ${reportId}: ${response.status} ${errorText}`);
    throw new Error(`Failed to render report ${reportId}: ${response.status} ${errorText}`);
  }
  return response.text(); // Assuming rendered report is HTML string
}

export async function getReportRenderingStatus(): Promise<RenderingStatus> {
  // This might be a general status endpoint or specific to a reportId
  // This specific BFF route /api/report-rendering/status was not explicitly created yet.
  // Assuming it might exist or will be created. If it's report-specific, it should include an ID.
  // For now, let's assume a general status endpoint.
  const response = await fetch(`${NEXT_API_BASE_URL}/report-rendering/status`, { // Adjusted path
    credentials: 'include',
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error fetching report rendering status: ${response.status} ${errorText}`);
    throw new Error(`Failed to fetch report rendering status: ${response.status} ${errorText}`);
  }
  return response.json();
}

// Helper function for report exports
async function exportReport(
  reportId: string, // Fixed parameter name back to reportId
  format: 'pdf' | 'word' | 'powerpoint',
  onProgress?: (progress: number) => void
): Promise<{ blob: Blob; filename: string }> {
  // Calls the BFF route at /api/reports/[reportId]/export/[format]
  const response = await fetch(`${NEXT_API_BASE_URL}/reports/${reportId}/export/${format}`, {
    method: 'POST',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error exporting report ${reportId} to ${format}: ${response.status} ${errorText}`);
    throw new Error(`Failed to export report ${reportId} to ${format}: ${response.status} ${errorText}`);
  }

  // Handle progress if backend supports it (e.g., via ReadableStream)
  // For now, this is a simplified version without backend progress reporting
  if (onProgress) {
    // Simulate progress for now
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress <= 100) {
        onProgress(progress);
      } else {
        clearInterval(interval);
      }
    }, 200);
  }

  const blob = await response.blob();

  // Extract filename from Content-Disposition header or generate it
  let filename = `${reportId}.${format}`; // Default filename, using reportId
  const disposition = response.headers.get('Content-Disposition');
  if (disposition && disposition.indexOf('attachment') !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(disposition);
    if (matches != null && matches[1]) {
      filename = matches[1].replace(/['"]/g, '');
    }
  }
  // If the backend doesn't provide the exact filename as per spec ({client}-{reportName}-{date}.format)
  // we might need to fetch report details separately to construct it, or ensure backend sends it.
  // For now, we rely on Content-Disposition or a default.

  return { blob, filename };
}

export async function exportReportToPdf(
  reportId: string, // Fixed parameter name back to reportId
  onProgress?: (progress: number) => void
): Promise<{ blob: Blob; filename: string }> {
  return exportReport(reportId, 'pdf', onProgress);
}

export async function exportReportToWord(
  reportId: string, // Fixed parameter name back to reportId
  onProgress?: (progress: number) => void
): Promise<{ blob: Blob; filename: string }> {
  return exportReport(reportId, 'word', onProgress);
}

export async function exportReportToPowerPoint(
  reportId: string, // Fixed parameter name back to reportId
  onProgress?: (progress: number) => void
): Promise<{ blob: Blob; filename: string }> {
  return exportReport(reportId, 'powerpoint', onProgress);
}

// System API functions
export async function getCompanyProfile(): Promise<CompanyProfile> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/system/company-profile`, {
    credentials: 'include', // Important for sending auth cookies
  });
  if (!response.ok) {
    if (response.status === 401 || response.status === 403) {
      throw new Error('Unauthorized: You do not have permission to access company profile.');
    }
    throw new Error('Failed to fetch company profile');
  }
  return response.json();
}
