using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using Xunit;
using FluentAssertions;

namespace FY.WB.CSHero2.Test.ControllerTests
{
    public class UploadsControllerTests : TestBase, IDisposable
    {
        private string? _adminToken;
        private string? _tenant1Token;
        private HttpClient? _adminClient;
        private HttpClient? _tenant1Client;

        public UploadsControllerTests()
        {
            // Initialize tokens and clients
            InitializeAsync().Wait();
        }

        private async Task InitializeAsync()
        {
            // Use the helper methods from TestBase
            _adminToken = await GetAdminToken();
            _tenant1Token = await GetTenant1Token();

            // Create authenticated clients
            _adminClient = CreateAuthenticatedClient(_adminToken);
            _tenant1Client = CreateAuthenticatedClient(_tenant1Token);
        }

        #region Create Tests

        [Fact]
        public async Task CreateUpload_ValidData_ReturnsCreated()
        {
            // Arrange - Use timestamp to ensure unique filenames
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var createDto = new CreateUploadRequestDto
            {
                Filename = $"test-document-{timestamp}.pdf",
                Size = 1024000, // 1MB
                ContentType = "application/pdf",
                StoragePath = $"/uploads/test-document-{timestamp}.pdf",
                StorageProvider = "LocalStorage",
                ExternalUrl = $"https://example.com/files/test-document-{timestamp}.pdf",
                Checksum = "abc123def456"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PostAsync("/api/uploads", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created, 
                $"Expected 201 Created but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent, _jsonOptions);
            
            result.TryGetProperty("id", out var idProperty).Should().BeTrue("Response should contain an 'id' property");
            var uploadId = Guid.Parse(idProperty.GetString());
            
            Console.WriteLine($"Successfully created upload with ID: {uploadId}");

            // Verify the upload was created by retrieving it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            getResponse.IsSuccessStatusCode.Should().BeTrue("Should be able to retrieve the created upload");
        }

        [Fact]
        public async Task CreateUpload_InvalidData_ReturnsBadRequest()
        {
            // Arrange - Missing ALL required fields to ensure validation fails
            var createDto = new CreateUploadRequestDto
            {
                // All required fields are missing or invalid
                Filename = "", // Empty string should fail validation
                Size = 0, // Zero size should fail validation
                ContentType = "", // Empty string should fail validation
                StoragePath = "" // Empty string should fail validation
            };

            var content = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PostAsync("/api/uploads", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest, 
                $"Expected 400 Bad Request but got {response.StatusCode}");

            var responseContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Validation error response: {responseContent}");
        }

        #endregion

        #region Update Tests

        [Fact]
        public async Task UpdateUpload_ValidData_ReturnsNoContent()
        {
            // Arrange - First create an upload with unique filename
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var createDto = new CreateUploadRequestDto
            {
                Filename = $"original-file-{timestamp}.txt",
                Size = 512,
                ContentType = "text/plain",
                StoragePath = $"/uploads/original-file-{timestamp}.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _tenant1Client.PostAsync("/api/uploads", createContent);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Arrange - Update data with unique filename
            var updateTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + 1;
            var updateDto = new UpdateUploadRequestDto
            {
                Id = uploadId,
                Filename = $"updated-file-{updateTimestamp}.txt",
                ContentType = "text/plain",
                StoragePath = $"/uploads/updated-file-{updateTimestamp}.txt",
                Checksum = "updated-checksum"
            };

            var updateContent = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{uploadId}", updateContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent, 
                $"Expected 204 No Content but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            // Verify the upload was updated by retrieving it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            getResponse.IsSuccessStatusCode.Should().BeTrue();

            var getContent = await getResponse.Content.ReadAsStringAsync();
            var updatedUpload = JsonSerializer.Deserialize<UploadDto>(getContent, _jsonOptions);
            
            updatedUpload.Filename.Should().Be($"updated-file-{updateTimestamp}.txt");
            updatedUpload.Checksum.Should().Be("updated-checksum");
            
            Console.WriteLine($"Successfully updated upload: {updatedUpload.Filename}");
        }

        [Fact]
        public async Task UpdateUpload_NonExistentId_ReturnsNotFound()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();
            var updateDto = new UpdateUploadRequestDto
            {
                Id = nonExistentId,
                Filename = "non-existent.txt"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{nonExistentId}", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound, 
                $"Expected 404 Not Found but got {response.StatusCode}");
        }

        [Fact]
        public async Task UpdateUpload_MismatchedId_ReturnsBadRequest()
        {
            // Arrange
            var routeId = Guid.NewGuid();
            var bodyId = Guid.NewGuid();
            
            var updateDto = new UpdateUploadRequestDto
            {
                Id = bodyId, // Different from route ID
                Filename = "test.txt"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{routeId}", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest, 
                $"Expected 400 Bad Request but got {response.StatusCode}");
        }

        #endregion

        #region Delete Tests

        [Fact]
        public async Task DeleteUpload_ExistingId_ReturnsNoContent()
        {
            // Arrange - First create an upload with unique filename
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var createDto = new CreateUploadRequestDto
            {
                Filename = $"to-be-deleted-{timestamp}.txt",
                Size = 256,
                ContentType = "text/plain",
                StoragePath = $"/uploads/to-be-deleted-{timestamp}.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _tenant1Client.PostAsync("/api/uploads", createContent);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Act
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{uploadId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent, 
                $"Expected 204 No Content but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            // Verify the upload was deleted by trying to retrieve it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound, 
                "Upload should not be found after deletion");
            
            Console.WriteLine($"Successfully deleted upload with ID: {uploadId}");
        }

        [Fact]
        public async Task DeleteUpload_NonExistentId_ReturnsNotFound()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{nonExistentId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound, 
                $"Expected 404 Not Found but got {response.StatusCode}");
        }

        #endregion

        #region Multi-Tenancy Tests

        [Fact]
        public async Task UpdateUpload_CrossTenant_ReturnsForbidden()
        {
            // Arrange - Create an upload as admin (different tenant) with unique filename
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var createDto = new CreateUploadRequestDto
            {
                Filename = $"admin-file-{timestamp}.txt",
                Size = 1024,
                ContentType = "text/plain",
                StoragePath = $"/uploads/admin-file-{timestamp}.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _adminClient.PostAsync("/api/uploads", createContent);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Arrange - Try to update as tenant1
            var updateDto = new UpdateUploadRequestDto
            {
                Id = uploadId,
                Filename = "hacked-file.txt"
            };

            var updateContent = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{uploadId}", updateContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden, 
                $"Expected 403 Forbidden but got {response.StatusCode}. Cross-tenant access should be denied.");
        }

        [Fact]
        public async Task DeleteUpload_CrossTenant_ReturnsForbidden()
        {
            // Arrange - Create an upload as admin (different tenant) with unique filename
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var createDto = new CreateUploadRequestDto
            {
                Filename = $"admin-protected-{timestamp}.txt",
                Size = 2048,
                ContentType = "text/plain",
                StoragePath = $"/uploads/admin-protected-{timestamp}.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _adminClient.PostAsync("/api/uploads", createContent);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Act - Try to delete as tenant1
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{uploadId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden, 
                $"Expected 403 Forbidden but got {response.StatusCode}. Cross-tenant deletion should be denied.");
        }

        #endregion

        public void Dispose()
        {
            _adminClient?.Dispose();
            _tenant1Client?.Dispose();
        }
    }
}
