'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ReportTable } from '@/components/features/reports/report-table';
import { PageHeader } from '@/components/layouts/page-header';
import { Button } from '@/components/ui/button';

export default function ReportsPage() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <PageHeader
        title="Reports"
        description="Manage and track all your customer service reports"
        btnUrl="/client/reports/template-gallery"
        btnName="New Report"
        placeHolder="Search reports..."
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />

      {/* Feature Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex mb-12 justify-between gradient-primary-secondary rounded-xl px-8"
      >
        <div className="flex flex-col justify-between w-1/2 align-middle p-6 my-6"> 
          <div>
            <h3 className="text-lg font-semibold mb-2 text-white">Template Gallery</h3>
            <p className="text-white">
              Speed up build by choosing styles from our template gallery or by uploading an existing report into the system
            </p>
          </div>

          <div className="flex gap-6 " >
            <Button variant="white">
              <Link href="/client/reports/template-gallery">
                View Gallery
              </Link>
            </Button>
            <Button variant="whiteTransparent">
              <Link href="/client/reports/template-gallery">
                Upload Report
              </Link>
            </Button>
          </div>

        </div>

        <img src="/assets/images/ai-reports.svg" alt="decorative image" className="w-auto h-80 mt-[-40px] z-90" />
      </motion.div>

      <ReportTable searchQuery={searchQuery} />
    </div>
  );
}
