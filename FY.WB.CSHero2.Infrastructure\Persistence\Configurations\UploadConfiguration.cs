using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class UploadConfiguration : IEntityTypeConfiguration<Upload>
    {
        public void Configure(EntityTypeBuilder<Upload> builder)
        {
            builder.HasKey(u => u.Id);

            builder.Property(u => u.Filename)
                .IsRequired()
                .HasMaxLength(255); // Allow for long filenames with paths

            builder.Property(u => u.Size)
                .IsRequired();

            builder.Property(u => u.ContentType)
                .IsRequired()
                .HasMaxLength(100); // MIME types can be quite long

            builder.Property(u => u.StoragePath)
                .IsRequired()
                .HasMaxLength(1000); // Allow for long storage paths

            builder.Property(u => u.StorageProvider)
                .HasMaxLength(50); // e.g., "LocalStorage", "AzureBlob"

            builder.Property(u => u.ExternalUrl)
                .HasMaxLength(2000); // URLs can be quite long

            builder.Property(u => u.Checksum)
                .HasMaxLength(100); // MD5/SHA hashes are typically 32-64 chars

            // Create indexes for common query patterns
            builder.HasIndex(u => u.Filename);
            builder.HasIndex(u => u.ContentType);
            builder.HasIndex(u => u.StorageProvider);

            // Create a composite index for file lookup
            builder.HasIndex(u => new { u.Filename, u.StorageProvider });

            // Ensure unique combination of filename and storage path
            builder.HasIndex(u => new { u.Filename, u.StoragePath })
                .IsUnique();

            // Audit properties are handled by base entity configuration
            builder.Property(u => u.CreationTime)
                .IsRequired();

            builder.Property(u => u.LastModificationTime)
                .IsRequired(false);

            // Add check constraint to ensure Size is non-negative
            builder.ToTable(t => t.HasCheckConstraint("CK_Upload_Size", "[Size] >= 0"));
        }
    }
}
