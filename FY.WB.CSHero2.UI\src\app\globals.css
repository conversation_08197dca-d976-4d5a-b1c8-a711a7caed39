@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --primary: 138 43 226; 
    --secondary: 0 191 255;
    --secondary2: 0 126 222;
    --tertiary: 192 192 192;
    --background: 255 255 255;
    --foreground: 0 0 0;
    --nav-bg: 0 0 0; 
  }
}

/* Gradient utilities */
.gradient-primary-secondary {
  @apply bg-gradient-to-r from-[rgb(var(--primary))] to-[rgb(var(--secondary))];
}

.gradient-primary-tertiary {
  @apply bg-gradient-to-r from-[rgb(var(--primary))] to-[rgb(var(--tertiary))];
}

.gradient-secondary-tertiary {
  @apply bg-gradient-to-r from-[rgb(var(--secondary))] to-[rgb(var(--tertiary))];
}

.gradient-gray {
  @apply bg-gradient-to-br from-[gray-100] to-[gray-200];
}

/* Navigation transitions */
.nav-transition {
  @apply transition-all duration-300 ease-in-out;
}

.layout-container {
  @apply flex min-h-screen;
}

.main-content {
  @apply flex-1 p-6;
}

.page-header {
  @apply mb-8;
}

.page-header h1 {
  @apply text-3xl font-bold tracking-tight;
}

/* Container max width */
.max-w-inner {
  @apply max-w-[1440px];
}

/* Rounded corners */
.rounded-round {
  @apply rounded-[32px];
}

/* Button utilities */
.btn {
  @apply inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
}

.btn-fancy {
  @apply btn gradient-primary-tertiary text-white hover:opacity-90 px-6 py-3;
}

.btn-primary {
  @apply btn bg-primary text-white hover:bg-primary/90 px-6 py-3;
}

.btn-secondary {
  @apply btn bg-white/10 text-white hover:bg-white/20 px-6 py-3;
}

.btn-white {
  @apply btn bg-white text-primary hover:bg-white/90 px-6 py-3;
}

.btn-lg {
  @apply px-8 py-4 text-base;
}

.btn-sm {
  @apply px-4 py-2 text-xs;
}

/* Sidebar animations */
.transition-width {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
