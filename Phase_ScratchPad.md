# Project: Frontend Data Migration & Backend Integration
## Phase 1: Data Identification, Relocation, and Backend Entity Setup

This phase focuses on identifying all data currently managed or mocked in the frontend, defining corresponding backend entities, setting up database structures, and preparing the raw data for backend seeding.

*   [ ] **Task 1.1: Identify Frontend Data Sources**
    *   [ ] **Action 1.1.1:** Analyze `FY.WB.CSHero2.UI/src/data/db/db.json` to understand all entities and their structures.
        *   **Objective:** Document each top-level key in `db.json` as a potential entity. For each, list its properties and data types.
        *   **Deliverable:** A list of entities and their attributes derived from `db.json`.
    *   [ ] **Action 1.1.2:** Review Next.js API routes in `FY.WB.CSHero2.UI/src/app/api/` to identify if any other JSON data structures are being served directly or if data transformations occur in the BFF layer that might indicate a different underlying data model.
        *   **Objective:** Identify any data structures not present in `db.json` but used by the frontend.
        *   **Deliverable:** Notes on any additional data structures or transformations found in BFF API routes.
    *   [ ] **Action 1.1.3:** Compile a consolidated list of all distinct data entities that need to be migrated.
        *   **Objective:** Create a final list of entities for backend implementation.
        *   **Deliverable:** Master list of entities (e.g., `Report`, `User`, `Form`, `Upload`, `Template`, `Tenant`, `Invoice`, various metrics, in addition to `Client`).

*   [ ] **Task 1.2: Define/Update Backend Domain Entities**
    *   [ ] **Action 1.2.1:** For each entity identified in Task 1.1.3, create or update corresponding C# classes in `FY.WB.CSHero2.Domain/Entities/`.
        *   **Objective:** Translate the identified frontend data structures into strongly-typed C# domain models.
        *   **Considerations:** Pay attention to data types, nullability, and potential relationships between entities.
        *   **Deliverable:** C# class files for each domain entity (e.g., `Report.cs`, `UserProfile.cs` (if `User` from `db.json` is different from `ApplicationUser`), `Form.cs`, etc.).
    *   [ ] **Action 1.2.2:** Ensure new/updated entities inherit from appropriate base classes in `FY.WB.CSHero2.Domain/Entities/Core/` (e.g., `FullAuditedMultiTenantEntity<Guid>`, `AuditedEntity<Guid>`, etc.) based on whether they require auditing and/or multi-tenancy.
        *   **Objective:** Incorporate standard cross-cutting concerns (auditing, multi-tenancy, soft delete) into the entities.
        *   **Deliverable:** Updated entity class definitions with correct base class inheritance.

*   [ ] **Task 1.3: Update EF Core Configurations**
    *   [ ] **Action 1.3.1:** For each new or significantly updated domain entity, create or update its `IEntityTypeConfiguration<T>` class in `FY.WB.CSHero2.Infrastructure/Persistence/Configurations/`.
        *   **Objective:** Define database mapping rules, such as table names, column types, constraints, and relationships.
        *   **Deliverable:** C# configuration files (e.g., `ReportConfiguration.cs`, `FormConfiguration.cs`).

*   [ ] **Task 1.4: Create/Update Database Migrations**
    *   [ ] **Action 1.4.1:** Generate new EF Core migrations to reflect the schema changes from new/updated entities and their configurations.
        *   **Command:** `dotnet ef migrations add Add[EntityName]Entities -p FY.WB.CSHero2.Infrastructure -s FY.WB.CSHero2` (or similar, detailing the scope of changes).
        *   **Objective:** Create migration files that will update the database schema.
        *   **Deliverable:** New migration files in `FY.WB.CSHero2.Infrastructure/Persistence/Migrations/`.
    *   [ ] **Action 1.4.2:** Review the generated migration script for correctness.
    *   [ ] **Action 1.4.3:** Apply migrations to the development database.
        *   **Command:** `dotnet ef database update -p FY.WB.CSHero2.Infrastructure -s FY.WB.CSHero2` (or allow auto-apply on startup if configured).
        *   **Objective:** Update the database schema.
        *   **Deliverable:** Updated database schema.

*   [ ] **Task 1.5: Relocate and Transform JSON Data for Seeding**
    *   [ ] **Action 1.5.1:** For each entity, create new JSON files (or consolidate/transform data from `db.json`) structured to match the newly defined backend C# Domain Entities.
        *   **Objective:** Prepare data in a format that the backend `DataSeeder` can easily consume. This might involve renaming fields, restructuring nested objects, or generating new IDs if necessary.
        *   **Deliverable:** A set of JSON files (e.g., `seed.reports.json`, `seed.forms.json`) in the `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/` folder.
    *   [ ] **Action 1.5.2:** Ensure data consistency, especially for any foreign key relationships defined in the domain models.