"use client";

import { MetricCard } from "@/components/features/dashboard/metric-card";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getTodayMetrics, getWTDMetrics, getMTDMetrics, getYTDMetrics } from "@/lib/api-metrics";
import { motion } from 'framer-motion';

type TimeFrame = 'today' | 'wtd' | 'mtd' | 'ytd';

const timeFrameLabels: Record<TimeFrame, string> = {
  today: 'Today',
  wtd: 'Week to Date',
  mtd: 'Month to Date',
  ytd: 'Year to Date'
};

export default function DashboardPage() {
  const [timeframe, setTimeframe] = useState<TimeFrame>('today');

  const { data, isLoading, error } = useQuery({
    queryKey: ['dashboard', timeframe],
    queryFn: async () => {
      try {
        switch (timeframe) {
          case 'today':
            return await getTodayMetrics();
          case 'wtd':
            return await getWTDMetrics();
          case 'mtd':
            return await getMTDMetrics();
          case 'ytd':
            return await getYTDMetrics();
          default:
            return await getTodayMetrics();
        }
      } catch (error) {
        throw new Error('Failed to fetch dashboard data');
      }
    }
  });

  const formatCurrency = (value: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);

  const formatNumber = (value: number) =>
    new Intl.NumberFormat('en-US').format(value);

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Dashboard</h1>
            <p className="text-xl text-gray-600">
              Overview of your metrics and performance
            </p>
          </div>
          <div className="flex gap-2">
            {(Object.keys(timeFrameLabels) as TimeFrame[]).map((tf) => (
              <Button
                key={tf}
                className={timeframe === tf ? "bg-primary text-white" : "border border-input bg-background hover:bg-accent hover:text-foreground"}
                onClick={() => setTimeframe(tf)}
              >
                {timeFrameLabels[tf]}
              </Button>
            ))}
          </div>
        </div>

        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="p-6 space-y-4">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                <div className="h-8 bg-gray-200 rounded animate-pulse w-3/4"></div>
                <div className="h-16 bg-gray-200 rounded animate-pulse"></div>
              </Card>
            ))}
          </div>
        )}
        {error && (
          <div className="p-4 text-red-600 bg-red-50 rounded-lg">
            Error loading dashboard data. Please try again later.
          </div>
        )}
        {data?.metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Customers"
              value={data.metrics.totalCustomers.current}
              previousValue={data.metrics.totalCustomers.previousPeriod}
              trend={data.metrics.totalCustomers.trend}
              format={formatNumber}
            />
            <MetricCard
              title="New Customers"
              value={data.metrics.newCustomers.current}
              previousValue={data.metrics.newCustomers.previousPeriod}
              trend={data.metrics.newCustomers.trend}
              format={formatNumber}
            />
            <MetricCard
              title="Reports Created"
              value={data.metrics.reportsCreated.current}
              previousValue={data.metrics.reportsCreated.previousPeriod}
              trend={data.metrics.reportsCreated.trend}
              format={formatNumber}
            />
            <MetricCard
              title="Revenue"
              value={data.metrics.revenue.current}
              previousValue={data.metrics.revenue.previousPeriod}
              trend={data.metrics.revenue.trend}
              format={formatCurrency}
            />
          </div>
        )}
      </motion.div>
    </div>
  );
}
