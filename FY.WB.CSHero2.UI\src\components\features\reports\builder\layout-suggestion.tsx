"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Table } from "lucide-react";

interface LayoutSuggestionProps {
  data: any;
  dataType: any;
  onSelectLayout: (layout: any) => void;
}

export function LayoutSuggestion({
  data,
  dataType,
  onSelectLayout
}: LayoutSuggestionProps) {
  // Sample layout options that would be generated based on data analysis
  const sampleLayouts = [
    {
      id: "layout-table",
      name: "Table View",
      description: "Display data in a clean, sortable table format",
      icon: <Table className="h-8 w-8" />,
      preview: "/assets/images/placeholder.png.txt",
      components: [
        {
          type: "table",
          title: "Data Table",
          fields: dataType.fields.map((f: any) => f.name)
        }
      ]
    },
    {
      id: "layout-charts",
      name: "Charts Dashboard",
      description: "Visualize data with various chart types",
      icon: <BarChart className="h-8 w-8" />,
      preview: "/assets/images/placeholder.png.txt",
      components: [
        {
          type: "bar-chart",
          title: "Bar Chart"
        },
        {
          type: "line-chart",
          title: "Trend Analysis"
        },
        {
          type: "pie-chart",
          title: "Distribution"
        }
      ]
    },
    {
      id: "layout-comparison",
      name: "Comparison View",
      description: "Compare key metrics side by side",
      icon: <LineChart className="h-8 w-8" />,
      preview: "/assets/images/placeholder.png.txt",
      components: [
        {
          type: "metric-cards",
          title: "Key Metrics"
        },
        {
          type: "comparison-chart",
          title: "Period Comparison"
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {sampleLayouts.map((layout) => (
          <div
            key={layout.id}
            className="border rounded-lg p-4 flex flex-col hover:border-primary cursor-pointer transition-colors"
            onClick={() => onSelectLayout(layout)}
          >
            <div className="flex justify-center items-center py-8 bg-gray-100 rounded-md mb-4">
              {layout.icon}
            </div>
            <h3 className="text-lg font-medium mb-1">{layout.name}</h3>
            <p className="text-sm text-gray-500 mb-4">{layout.description}</p>
            <button className="mt-auto px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70">
              Select Layout
            </button>
          </div>
        ))}
      </div>
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-700">
          <strong>Note:</strong> This is a placeholder component. In a real implementation, 
          layouts would be dynamically suggested based on your data structure and content.
        </p>
      </div>
    </div>
  );
}
