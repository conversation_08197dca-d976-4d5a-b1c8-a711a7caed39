# Phase 5: Testing

## Overview

This phase focuses on comprehensive testing of the new report structure implementation. The testing approach emphasizes integration tests using real databases (no mocks) to ensure the system works correctly in a production-like environment.

## Tasks

### Task 5.1: Create Test Database Setup

**Description:** Create a setup for integration tests that uses a real SQL Server database.

**Steps:**
1. Create a new class `ReportStructureTestBase.cs` in `FY.WB.CSHero2.Test/`
2. Implement database setup and teardown:
   - Use a real SQL Server database (LocalDB or Docker container)
   - Apply migrations to create schema
   - Seed test data
   - Clean up after tests

**Code Example:**
```csharp
public abstract class ReportStructureTestBase : IDisposable
{
    protected readonly ApplicationDbContext DbContext;
    protected readonly IMediator Mediator;
    protected readonly ICurrentUserService CurrentUserService;
    
    protected ReportStructureTestBase()
    {
        // Create a unique database name for this test run
        var dbName = $"ReportStructureTest_{Guid.NewGuid()}";
        
        // Set up the database context with a real SQL Server database
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer($"Server=(localdb)\\mssqllocaldb;Database={dbName};Trusted_Connection=True;MultipleActiveResultSets=true")
            .Options;
            
        // Create mock services
        CurrentUserService = Substitute.For<ICurrentUserService>();
        CurrentUserService.UserId.Returns("00000000-0000-0000-0000-000000000001");
        CurrentUserService.TenantId.Returns(Guid.Parse("00000000-0000-0000-0000-000000000001"));
        
        // Create the database context
        DbContext = new ApplicationDbContext(options, CurrentUserService);
        
        // Apply migrations
        DbContext.Database.Migrate();
        
        // Set up mediator with handlers
        var serviceProvider = new ServiceCollection()
            .AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(CreateReportCommand).Assembly))
            .AddScoped<IApplicationDbContext>(provider => DbContext)
            .AddScoped<ICurrentUserService>(provider => CurrentUserService)
            .BuildServiceProvider();
            
        Mediator = serviceProvider.GetRequiredService<IMediator>();
        
        // Seed test data
        SeedTestData();
    }
    
    private void SeedTestData()
    {
        // Create test tenant
        var tenant = new TenantProfile
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
            Name = "Test Tenant",
            // Set other properties
        };
        DbContext.TenantProfiles.Add(tenant);
        
        // Create test reports with the old structure
        var report1 = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000101"),
            ReportNumber = "TEST-001",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 1",
            Category = "Test",
            SlideCount = 5,
            Status = "Draft",
            Author = "Test User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001")
        };
        DbContext.Reports.Add(report1);
        
        // Create test report version with JSON content
        var reportVersion1 = new ReportVersion
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000301"),
            ReportId = report1.Id,
            VersionNumber = 1,
            Description = "Initial version",
            IsCurrent = true,
            JsonData = @"{
                ""template"": ""standard"",
                ""sections"": [
                    {
                        ""section-title"": ""Introduction"",
                        ""type"": ""text"",
                        ""content"": {
                            ""heading"": ""Introduction"",
                            ""body"": ""This is the introduction section.""
                        }
                    },
                    {
                        ""section-title"": ""Data Analysis"",
                        ""type"": ""chart"",
                        ""content"": {
                            ""heading"": ""Data Analysis"",
                            ""chartType"": ""bar"",
                            ""data"": {
                                ""labels"": [""Jan"", ""Feb"", ""Mar""],
                                ""values"": [10, 20, 30]
                            }
                        }
                    }
                ]
            }"
        };
        DbContext.ReportVersions.Add(reportVersion1);
        
        // Save changes
        DbContext.SaveChanges();
    }
    
    public void Dispose()
    {
        // Delete the database
        DbContext.Database.EnsureDeleted();
        DbContext.Dispose();
    }
}
```

**Acceptance Criteria:**
- Test base class created
- Real SQL Server database used
- Migrations applied to create schema
- Test data seeded
- Database cleaned up after tests

### Task 5.2: Create Report Creation Tests

**Description:** Create integration tests for creating reports with the new structure.

**Steps:**
1. Create a new class `CreateReportTests.cs` in `FY.WB.CSHero2.Test/Integration/Reports/`
2. Implement tests for creating reports with sections and fields
3. Verify that the data is correctly stored in the database

**Code Example:**
```csharp
public class CreateReportTests : ReportStructureTestBase
{
    [Fact]
    public async Task CreateReport_WithSectionsAndFields_ShouldCreateEntities()
    {
        // Arrange
        var command = new CreateReportCommand(new CreateReportRequestDto
        {
            ReportNumber = "TEST-002",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 2",
            Category = "Test",
            SlideCount = 3,
            Status = "Draft",
            Author = "Test User",
            Sections = new List<ReportSectionDto>
            {
                new ReportSectionDto
                {
                    Title = "Executive Summary",
                    Type = "text",
                    Order = 0,
                    Fields = new List<ReportSectionFieldDto>
                    {
                        new ReportSectionFieldDto
                        {
                            Name = "heading",
                            Type = "string",
                            Content = "Executive Summary",
                            Order = 0
                        },
                        new ReportSectionFieldDto
                        {
                            Name = "body",
                            Type = "string",
                            Content = "This is the executive summary.",
                            Order = 1
                        }
                    }
                },
                new ReportSectionDto
                {
                    Title = "Financial Analysis",
                    Type = "chart",
                    Order = 1,
                    Fields = new List<ReportSectionFieldDto>
                    {
                        new ReportSectionFieldDto
                        {
                            Name = "heading",
                            Type = "string",
                            Content = "Financial Analysis",
                            Order = 0
                        },
                        new ReportSectionFieldDto
                        {
                            Name = "chartType",
                            Type = "string",
                            Content = "line",
                            Order = 1
                        },
                        new ReportSectionFieldDto
                        {
                            Name = "data",
                            Type = "json",
                            Content = @"{""labels"":[""Q1"",""Q2"",""Q3"",""Q4""],""values"":[100,150,200,250]}",
                            Order = 2
                        }
                    }
                }
            }
        });
        
        // Act
        var reportId = await Mediator.Send(command);
        
        // Assert
        var report = await DbContext.Reports
            .Include(r => r.Sections)
            .ThenInclude(s => s.Fields)
            .FirstOrDefaultAsync(r => r.Id == reportId);
            
        Assert.NotNull(report);
        Assert.Equal("Test Report 2", report.Name);
        Assert.Equal(2, report.Sections.Count);
        
        var section1 = report.Sections.FirstOrDefault(s => s.Title == "Executive Summary");
        Assert.NotNull(section1);
        Assert.Equal(2, section1.Fields.Count);
        
        var section2 = report.Sections.FirstOrDefault(s => s.Title == "Financial Analysis");
        Assert.NotNull(section2);
        Assert.Equal(3, section2.Fields.Count);
        
        var chartTypeField = section2.Fields.FirstOrDefault(f => f.Name == "chartType");
        Assert.NotNull(chartTypeField);
        Assert.Equal("line", chartTypeField.Content);
    }
}
```

**Acceptance Criteria:**
- Test class created
- Test verifies report creation with sections and fields
- Test uses real database
- Test verifies data integrity

### Task 5.3: Create Report Retrieval Tests

**Description:** Create integration tests for retrieving reports with the new structure.

**Steps:**
1. Create a new class `GetReportTests.cs` in `FY.WB.CSHero2.Test/Integration/Reports/`
2. Implement tests for retrieving reports with sections and fields
3. Verify that the data is correctly retrieved from the database

**Code Example:**
```csharp
public class GetReportTests : ReportStructureTestBase
{
    [Fact]
    public async Task GetReportById_WithSectionsAndFields_ShouldReturnCompleteStructure()
    {
        // Arrange
        // Create a report with sections and fields
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000102"),
            ReportNumber = "TEST-003",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 3",
            Category = "Test",
            SlideCount = 2,
            Status = "Draft",
            Author = "Test User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001")
        };
        DbContext.Reports.Add(report);
        
        var section1 = new ReportSection
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000401"),
            ReportId = report.Id,
            Title = "Overview",
            Type = "text",
            Order = 0
        };
        DbContext.ReportSections.Add(section1);
        
        var field1 = new ReportSectionField
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000501"),
            SectionId = section1.Id,
            Name = "heading",
            Type = "string",
            Content = "Overview",
            Order = 0
        };
        DbContext.ReportSectionFields.Add(field1);
        
        var field2 = new ReportSectionField
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000502"),
            SectionId = section1.Id,
            Name = "body",
            Type = "string",
            Content = "This is the overview section.",
            Order = 1
        };
        DbContext.ReportSectionFields.Add(field2);
        
        await DbContext.SaveChangesAsync();
        
        // Act
        var query = new GetReportByIdQuery(report.Id);
        var result = await Mediator.Send(query);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Report 3", result.Name);
        Assert.Equal(1, result.Sections.Count);
        
        var resultSection = result.Sections.First();
        Assert.Equal("Overview", resultSection.Title);
        Assert.Equal(2, resultSection.Fields.Count);
        
        var resultField1 = resultSection.Fields.FirstOrDefault(f => f.Name == "heading");
        Assert.NotNull(resultField1);
        Assert.Equal("Overview", resultField1.Content);
        
        var resultField2 = resultSection.Fields.FirstOrDefault(f => f.Name == "body");
        Assert.NotNull(resultField2);
        Assert.Equal("This is the overview section.", resultField2.Content);
    }
    
    [Fact]
    public async Task GetReportById_WithMultiTenancy_ShouldRespectTenantIsolation()
    {
        // Arrange
        // Create a report for a different tenant
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000103"),
            ReportNumber = "TEST-004",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000202"),
            ClientName = "Other Tenant Client",
            Name = "Other Tenant Report",
            Category = "Test",
            SlideCount = 1,
            Status = "Draft",
            Author = "Other User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000002") // Different tenant
        };
        DbContext.Reports.Add(report);
        await DbContext.SaveChangesAsync();
        
        // Act & Assert
        var query = new GetReportByIdQuery(report.Id);
        
        // Should throw NotFoundException because the report belongs to a different tenant
        await Assert.ThrowsAsync<NotFoundException>(() => Mediator.Send(query));
    }
}
```

**Acceptance Criteria:**
- Test class created
- Test verifies report retrieval with sections and fields
- Test verifies multi-tenant data isolation
- Test uses real database

### Task 5.4: Create Report Update Tests

**Description:** Create integration tests for updating reports with the new structure.

**Steps:**
1. Create a new class `UpdateReportTests.cs` in `FY.WB.CSHero2.Test/Integration/Reports/`
2. Implement tests for updating reports with sections and fields
3. Verify that the data is correctly updated in the database

**Code Example:**
```csharp
public class UpdateReportTests : ReportStructureTestBase
{
    [Fact]
    public async Task UpdateReport_WithSectionsAndFields_ShouldUpdateEntities()
    {
        // Arrange
        // Create a report with sections and fields
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000104"),
            ReportNumber = "TEST-005",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 5",
            Category = "Test",
            SlideCount = 2,
            Status = "Draft",
            Author = "Test User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001")
        };
        DbContext.Reports.Add(report);
        
        var section1 = new ReportSection
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000402"),
            ReportId = report.Id,
            Title = "Original Section",
            Type = "text",
            Order = 0
        };
        DbContext.ReportSections.Add(section1);
        
        var field1 = new ReportSectionField
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000503"),
            SectionId = section1.Id,
            Name = "heading",
            Type = "string",
            Content = "Original Heading",
            Order = 0
        };
        DbContext.ReportSectionFields.Add(field1);
        
        await DbContext.SaveChangesAsync();
        
        // Act
        var command = new UpdateReportCommand(new UpdateReportRequestDto
        {
            Id = report.Id,
            Name = "Updated Report",
            Category = "Updated",
            SlideCount = 3,
            Status = "In Progress",
            Author = "Updated User",
            Sections = new List<ReportSectionDto>
            {
                new ReportSectionDto
                {
                    Id = section1.Id,
                    Title = "Updated Section",
                    Type = "text",
                    Order = 0,
                    Fields = new List<ReportSectionFieldDto>
                    {
                        new ReportSectionFieldDto
                        {
                            Id = field1.Id,
                            Name = "heading",
                            Type = "string",
                            Content = "Updated Heading",
                            Order = 0
                        },
                        new ReportSectionFieldDto
                        {
                            Name = "body",
                            Type = "string",
                            Content = "New body content",
                            Order = 1
                        }
                    }
                },
                new ReportSectionDto
                {
                    Title = "New Section",
                    Type = "chart",
                    Order = 1,
                    Fields = new List<ReportSectionFieldDto>
                    {
                        new ReportSectionFieldDto
                        {
                            Name = "chartType",
                            Type = "string",
                            Content = "pie",
                            Order = 0
                        }
                    }
                }
            }
        });
        
        await Mediator.Send(command);
        
        // Assert
        var updatedReport = await DbContext.Reports
            .Include(r => r.Sections)
            .ThenInclude(s => s.Fields)
            .FirstOrDefaultAsync(r => r.Id == report.Id);
            
        Assert.NotNull(updatedReport);
        Assert.Equal("Updated Report", updatedReport.Name);
        Assert.Equal("Updated", updatedReport.Category);
        Assert.Equal(2, updatedReport.Sections.Count);
        
        var updatedSection = updatedReport.Sections.FirstOrDefault(s => s.Id == section1.Id);
        Assert.NotNull(updatedSection);
        Assert.Equal("Updated Section", updatedSection.Title);
        Assert.Equal(2, updatedSection.Fields.Count);
        
        var updatedField = updatedSection.Fields.FirstOrDefault(f => f.Id == field1.Id);
        Assert.NotNull(updatedField);
        Assert.Equal("Updated Heading", updatedField.Content);
        
        var newSection = updatedReport.Sections.FirstOrDefault(s => s.Title == "New Section");
        Assert.NotNull(newSection);
        Assert.Equal(1, newSection.Fields.Count);
        
        var newField = newSection.Fields.FirstOrDefault(f => f.Name == "chartType");
        Assert.NotNull(newField);
        Assert.Equal("pie", newField.Content);
    }
}
```

**Acceptance Criteria:**
- Test class created
- Test verifies report update with sections and fields
- Test verifies adding, updating, and removing sections and fields
- Test uses real database

### Task 5.5: Create Report Deletion Tests

**Description:** Create integration tests for deleting reports with the new structure.

**Steps:**
1. Create a new class `DeleteReportTests.cs` in `FY.WB.CSHero2.Test/Integration/Reports/`
2. Implement tests for deleting reports with sections and fields
3. Verify that the data is correctly deleted from the database

**Code Example:**
```csharp
public class DeleteReportTests : ReportStructureTestBase
{
    [Fact]
    public async Task DeleteReport_WithSectionsAndFields_ShouldDeleteAllEntities()
    {
        // Arrange
        // Create a report with sections and fields
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000105"),
            ReportNumber = "TEST-006",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 6",
            Category = "Test",
            SlideCount = 2,
            Status = "Draft",
            Author = "Test User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001")
        };
        DbContext.Reports.Add(report);
        
        var section1 = new ReportSection
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000403"),
            ReportId = report.Id,
            Title = "Section to Delete",
            Type = "text",
            Order = 0
        };
        DbContext.ReportSections.Add(section1);
        
        var field1 = new ReportSectionField
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000504"),
            SectionId = section1.Id,
            Name = "heading",
            Type = "string",
            Content = "Heading to Delete",
            Order = 0
        };
        DbContext.ReportSectionFields.Add(field1);
        
        await DbContext.SaveChangesAsync();
        
        // Act
        var command = new DeleteReportCommand(report.Id);
        await Mediator.Send(command);
        
        // Assert
        var deletedReport = await DbContext.Reports.FindAsync(report.Id);
        Assert.Null(deletedReport);
        
        var deletedSection = await DbContext.ReportSections.FindAsync(section1.Id);
        Assert.Null(deletedSection);
        
        var deletedField = await DbContext.ReportSectionFields.FindAsync(field1.Id);
        Assert.Null(deletedField);
    }
    
    [Fact]
    public async Task DeleteReport_WithMultiTenancy_ShouldRespectTenantIsolation()
    {
        // Arrange
        // Create a report for a different tenant
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000106"),
            ReportNumber = "TEST-007",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000202"),
            ClientName = "Other Tenant Client",
            Name = "Other Tenant Report",
            Category = "Test",
            SlideCount = 1,
            Status = "Draft",
            Author = "Other User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000002") // Different tenant
        };
        DbContext.Reports.Add(report);
        await DbContext.SaveChangesAsync();
        
        // Act & Assert
        var command = new DeleteReportCommand(report.Id);
        
        // Should throw NotFoundException because the report belongs to a different tenant
        await Assert.ThrowsAsync<NotFoundException>(() => Mediator.Send(command));
        
        // Verify the report still exists
        var reportStillExists = await DbContext.Reports.FindAsync(report.Id);
        Assert.NotNull(reportStillExists);
    }
}
```

**Acceptance Criteria:**
- Test class created
- Test verifies report deletion with cascading delete of sections and fields
- Test verifies multi-tenant data isolation
- Test uses real database

### Task 5.6: Create Data Migration Tests

**Description:** Create integration tests for the data migration process.

**Steps:**
1. Create a new class `ReportDataMigrationTests.cs` in `FY.WB.CSHero2.Test/Integration/Reports/`
2. Implement tests for migrating report data from JSON to the new structure
3. Verify that the data is correctly migrated

**Code Example:**
```csharp
public class ReportDataMigrationTests : ReportStructureTestBase
{
    [Fact]
    public async Task MigrateReportData_WithValidJson_ShouldCreateSectionsAndFields()
    {
        // Arrange
        // Create a report with JSON content
        var report = new Report
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000107"),
            ReportNumber = "TEST-008",
            ClientId = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            ClientName = "Test Client",
            Name = "Test Report 8",
            Category = "Test",
            SlideCount = 2,
            Status = "Draft",
            Author = "Test User",
            TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001")
        };
        DbContext.Reports.Add(report);
        
        var reportVersion = new ReportVersion
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000302"),
            ReportId = report.Id,
            VersionNumber = 1,
            Description = "Initial version",
            IsCurrent = true,
            JsonData = @"{
                ""template"": ""standard"",
                ""sections"": [
                    {
                        ""section-title"": ""Summary"",
                        ""type"": ""text"",
                        ""content"": {
                            ""heading"": ""Summary"",
                            ""body"": ""This is the summary section.""
                        }
                    },
                    {
                        ""section-title"": ""Results"",
                        ""type"": ""table"",
                        ""content"": {
                            ""heading"": ""Results"",
                            ""columns"": [""Name"", ""Value""],
                            ""rows"": [
                                [""Item 1"", ""100""],
                                [""Item 2"", ""200""]
                            ]
                        }
                    }
                ]
            }"
        };
        DbContext.ReportVersions.Add(reportVersion);
        
        await DbContext.SaveChangesAsync();
        
        // Create the migration service
        var logger = Substitute.For<ILogger<ReportDataMigrationService>>();
        var migrationService = new ReportDataMigrationService(DbContext, logger);
        
        // Act
        var migratedCount = await migrationService.MigrateReportDataAsync();
        
        // Assert
        Assert.Equal(1, migratedCount);
        
        var migratedReport = await DbContext.Reports
            .Include(r => r.Sections)
            .ThenInclude(s => s.Fields)
            .FirstOrDefaultAsync(r => r.Id == report.Id);
            
        Assert.NotNull(migratedReport);
        Assert.Equal(2, migratedReport.Sections.Count);
        
        var summarySection = migratedReport.Sections.FirstOrDefault(s => s.Title == "Summary");
        Assert.NotNull(summarySection);
        Assert.Equal("text", summarySection.Type);
        Assert.Equal(2, summarySection.Fields.Count);
        
        var headingField = summarySection.Fields.FirstOrDefault(f => f.Name == "heading");
        Assert.NotNull(headingField);
        Assert.Equal("Summary", headingField.Content);
        
        var bodyField = summarySection.Fields.FirstOrDefault(f => f.Name == "body");
        Assert.NotNull(bodyField);
        Assert.Equal("This is the summary section.", bodyField.Content);
        
        var resultsSection = migratedReport.Sections.FirstOrDefault(s => s.Title == "Results");
        Assert.NotNull(resultsSection);
        Assert.Equal("table", resultsSection.Type);
    }
}
```

**Acceptance Criteria:**
- Test class created
- Test verifies data migration from JSON to structured entities
- Test verifies correct extraction of sections and fields
- Test uses real database

## Dependencies

- Phase 1: Domain Model Changes
- Phase 2: Database Migration
- Phase 3: Application Layer Updates
- Phase 4: API and Frontend Integration

## Deliverables

- Integration test base class
- Integration tests for report creation
- Integration tests for report retrieval
- Integration tests for report update
- Integration tests for report deletion
- Integration tests for data migration

## Estimated Effort

- Task 5.1: 2 hours
- Task 5.2: 2 hours
- Task 5.3: 2 hours
- Task 5.4: 2 hours
- Task 5.5: 2 hours
- Task 5.6: 2 hours

**Total: 12 hours**

## Testing Approach Summary

- **Real Database Testing**: All tests use a real SQL Server database (no mocks)
- **Comprehensive Coverage**: Tests cover all CRUD operations and data migration
- **Multi-tenant Isolation**: Tests verify proper tenant isolation
- **Data Integrity**: Tests verify data integrity across all operations
- **Error Handling**: Tests verify proper error handling and validation