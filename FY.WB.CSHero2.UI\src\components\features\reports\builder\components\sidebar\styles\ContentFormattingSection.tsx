"use client";

import { ContentFormattingOptions } from "../../../types";
import { contentFormattingConfig } from "../../../config/contentFields";
import { FormSection } from "../../ui/FormSection";
import { FormField } from "../../ui/FormField";
import { getSectionValues } from "../../../utils/formUtils";

interface ContentFormattingSectionProps {
  content?: ContentFormattingOptions;
  onUpdate: (field: string, value: any) => void;
}

export function ContentFormattingSection({ content = {}, onUpdate }: ContentFormattingSectionProps) {
  const handleNestedUpdate = (category: string, field: string, value: any): void => {
    // Special handling for lists
    if (category === "lists") {
      if (field === "bulletStyleLevel1") {
        const currentBulletStyle = content.lists?.bulletStyle || {};
        onUpdate("lists", {
          ...content.lists,
          bulletStyle: {
            ...currentBulletStyle,
            level1: value
          }
        });
        return;
      } else if (field === "bulletStyleLevel2") {
        const currentBulletStyle = content.lists?.bulletStyle || {};
        onUpdate("lists", {
          ...content.lists,
          bulletStyle: {
            ...currentBulletStyle,
            level2: value
          }
        });
        return;
      } else if (field === "numberedListStyleLevel1") {
        const currentNumberedListStyle = content.lists?.numberedListStyle || {};
        onUpdate("lists", {
          ...content.lists,
          numberedListStyle: {
            ...currentNumberedListStyle,
            level1: value
          }
        });
        return;
      } else if (field === "numberedListStyleLevel2") {
        const currentNumberedListStyle = content.lists?.numberedListStyle || {};
        onUpdate("lists", {
          ...content.lists,
          numberedListStyle: {
            ...currentNumberedListStyle,
            level2: value
          }
        });
        return;
      } else if (field === "spacingBetweenItems" || field === "spacingBeforeAfterLists") {
        const currentSpacing = content.lists?.spacing || {};
        const spacingField = field === "spacingBetweenItems" ? "betweenItems" : "beforeAfterLists";
        
        onUpdate("lists", {
          ...content.lists,
          spacing: {
            ...currentSpacing,
            [spacingField]: value
          }
        });
        return;
      }
    }
    
    // Default handling for other fields
    const currentCategory = content[category as keyof ContentFormattingOptions] as Record<string, any> || {};
    const updatedCategory = {
      ...currentCategory,
      [field]: value
    };
    
    onUpdate(category, updatedCategory);
  };

  // Helper function to get values for a specific section
  const getValuesForSection = (sectionId: string): Record<string, any> => {
    if (sectionId === "lists") {
      // Combine lists fields
      return {
        bulletStyleLevel1: content.lists?.bulletStyle?.level1,
        bulletStyleLevel2: content.lists?.bulletStyle?.level2,
        numberedListStyleLevel1: content.lists?.numberedListStyle?.level1,
        numberedListStyleLevel2: content.lists?.numberedListStyle?.level2,
        indentation: content.lists?.indentation,
        spacingBetweenItems: content.lists?.spacing?.betweenItems,
        spacingBeforeAfterLists: content.lists?.spacing?.beforeAfterLists
      };
    }
    
    // For other sections, get values directly
    return getSectionValues(content, sectionId);
  };

  return (
    <div className="space-y-4">
      {contentFormattingConfig.sections.map(section => {
        const sectionValues = getValuesForSection(section.id);
        
        return (
          <FormSection 
            key={section.id}
            title={section.title}
            defaultExpanded={section.defaultExpanded}
          >
            {section.fields.map(field => (
              <FormField
                key={field.name}
                field={field}
                value={sectionValues[field.name]}
                onChange={(value) => handleNestedUpdate(section.id, field.name, value)}
                parentValues={sectionValues}
              />
            ))}
          </FormSection>
        );
      })}
    </div>
  );
}
