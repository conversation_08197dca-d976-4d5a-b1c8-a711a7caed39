using System;

namespace FY.WB.CSHero2.Application.Reports.Dtos
{
    public class ReportDto
    {
        public Guid Id { get; set; }
        public string ReportNumber { get; set; } = string.Empty;
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }
}
