import { readJsonFile } from './utils';

export interface Metric {
  current: number;
  previousPeriod: number;
  trend: number[];
}

export interface Metrics {
  totalCustomers: Metric;
  newCustomers: Metric;
  reportsCreated: Metric;
  revenue: Metric;
}

export interface MetricsData {
  metrics: Metrics;
}

/**
 * Get today's metrics
 * @returns Today's metrics data
 */
export async function getTodayMetrics(): Promise<MetricsData> {
  const data = await readJsonFile<any>();
  return data.today;
}

/**
 * Get week-to-date metrics
 * @returns Week-to-date metrics data
 */
export async function getWTDMetrics(): Promise<MetricsData> {
  const data = await readJsonFile<any>();
  return data.wtd;
}

/**
 * Get month-to-date metrics
 * @returns Month-to-date metrics data
 */
export async function getMTDMetrics(): Promise<MetricsData> {
  const data = await readJsonFile<any>();
  return data.mtd;
}

/**
 * Get year-to-date metrics
 * @returns Year-to-date metrics data
 */
export async function getYTDMetrics(): Promise<MetricsData> {
  const data = await readJsonFile<any>();
  return data.ytd;
}
