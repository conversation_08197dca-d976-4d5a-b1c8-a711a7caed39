"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Search,
  FileText,
  MoreVertical,
  Eye,
  Download,
  Copy,
  ExternalLink,
  Clock,
  Check,
  Users,
  Mail,
  Link as LinkIcon
} from "lucide-react";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

// Mock data for shared reports
const MOCK_SHARED_REPORTS = [
  {
    id: "report-1",
    name: "Q1 2025 Performance Report",
    owner: {
      id: "user-1",
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: null
    },
    sharedBy: {
      id: "user-1",
      name: "<PERSON> <PERSON>",
      email: "<EMAIL>",
      avatar: null
    },
    sharedAt: new Date("2025-01-15T10:30:00"),
    permission: "view",
    lastViewed: new Date("2025-01-20T14:45:00"),
    type: "people"
  },
  {
    id: "report-2",
    name: "Customer Satisfaction Survey Results",
    owner: {
      id: "user-2",
      name: "Emily Johnson",
      email: "<EMAIL>",
      avatar: null
    },
    sharedBy: {
      id: "user-2",
      name: "Emily Johnson",
      email: "<EMAIL>",
      avatar: null
    },
    sharedAt: new Date("2025-01-18T09:15:00"),
    permission: "edit",
    lastViewed: new Date("2025-01-22T11:20:00"),
    type: "people"
  },
  {
    id: "report-3",
    name: "Marketing Campaign Analysis",
    owner: {
      id: "user-3",
      name: "Michael Brown",
      email: "<EMAIL>",
      avatar: null
    },
    sharedBy: {
      id: "user-3",
      name: "Michael Brown",
      email: "<EMAIL>",
      avatar: null
    },
    sharedAt: new Date("2025-01-20T16:45:00"),
    permission: "comment",
    lastViewed: null,
    type: "email"
  },
  {
    id: "report-4",
    name: "Product Development Roadmap",
    owner: {
      id: "user-4",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      avatar: null
    },
    sharedBy: {
      id: "user-4",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      avatar: null
    },
    sharedAt: new Date("2025-01-22T13:10:00"),
    permission: "view",
    lastViewed: new Date("2025-01-23T10:05:00"),
    type: "link"
  },
  {
    id: "report-5",
    name: "Financial Forecast 2025",
    owner: {
      id: "user-5",
      name: "David Lee",
      email: "<EMAIL>",
      avatar: null
    },
    sharedBy: {
      id: "user-1",
      name: "John Smith",
      email: "<EMAIL>",
      avatar: null
    },
    sharedAt: new Date("2025-01-25T11:30:00"),
    permission: "admin",
    lastViewed: new Date("2025-01-26T09:15:00"),
    type: "people"
  }
];

export default function SharedReportsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [sortBy, setSortBy] = useState("recent");

  // Filter reports based on search query and active tab
  const filteredReports = MOCK_SHARED_REPORTS.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          report.owner.name.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === "all") return matchesSearch;
    return matchesSearch && report.type === activeTab;
  });

  // Sort reports based on selected sort option
  const sortedReports = [...filteredReports].sort((a, b) => {
    switch (sortBy) {
      case "recent":
        return new Date(b.sharedAt).getTime() - new Date(a.sharedAt).getTime();
      case "name":
        return a.name.localeCompare(b.name);
      case "owner":
        return a.owner.name.localeCompare(b.owner.name);
      default:
        return 0;
    }
  });

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  // Get permission badge color
  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "view":
        return "bg-blue-100 text-blue-800";
      case "comment":
        return "bg-green-100 text-green-800";
      case "edit":
        return "bg-primary/10 text-purple-800";
      case "admin":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get share type icon
  const getShareTypeIcon = (type: string) => {
    switch (type) {
      case "people":
        return <Users className="w-4 h-4 text-primary" />;
      case "email":
        return <Mail className="w-4 h-4 text-green-500" />;
      case "link":
        return <LinkIcon className="w-4 h-4 text-purple-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-12 max-w-inner px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Shared Reports</h1>
            <p className="text-xl text-gray-600">
              View and manage reports shared with you
            </p>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
            <Input
              placeholder="Search shared reports..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="owner">Owner</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList>
            <TabsTrigger value="all" className="flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              All
            </TabsTrigger>
            <TabsTrigger value="people" className="flex items-center">
              <Users className="w-4 h-4 mr-2" />
              Shared by People
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="w-4 h-4 mr-2" />
              Email Invitations
            </TabsTrigger>
            <TabsTrigger value="link" className="flex items-center">
              <LinkIcon className="w-4 h-4 mr-2" />
              Link Sharing
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {sortedReports.length > 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">Report Name</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Shared By</TableHead>
                  <TableHead>Shared On</TableHead>
                  <TableHead>Permission</TableHead>
                  <TableHead>Last Viewed</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="mr-2">
                          {getShareTypeIcon(report.type)}
                        </div>
                        <Link href={`/reports/${report.id}/view`} className="font-medium text-blue-600 hover:underline">
                          {report.name}
                        </Link>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mr-2">
                          {report.owner.avatar ? (
                            <div className="relative w-6 h-6 rounded-full overflow-hidden">
                              <Image
                                src={report.owner.avatar}
                                alt={report.owner.name}
                                className="object-cover"
                                fill
                                sizes="24px"
                              />
                            </div>
                          ) : (
                            report.owner.name.charAt(0)
                          )}
                        </div>
                        <span>{report.owner.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mr-2">
                          {report.sharedBy.avatar ? (
                            <div className="relative w-6 h-6 rounded-full overflow-hidden">
                              <Image
                                src={report.sharedBy.avatar}
                                alt={report.sharedBy.name}
                                className="object-cover"
                                fill
                                sizes="24px"
                              />
                            </div>
                          ) : (
                            report.sharedBy.name.charAt(0)
                          )}
                        </div>
                        <span>{report.sharedBy.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(report.sharedAt)}</TableCell>
                    <TableCell>
                      <Badge className={`${getPermissionColor(report.permission)} capitalize`}>
                        {report.permission}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {report.lastViewed ? (
                        <span className="text-gray-600 flex items-center">
                          <Check className="w-4 h-4 mr-1 text-green-500" />
                          {formatDate(report.lastViewed)}
                        </span>
                      ) : (
                        <span className="text-gray-500 flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          Not viewed
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="w-4 h-4 mr-2" />
                            Make a Copy
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Open in New Tab
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <FileText className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No shared reports found</h3>
            <p className="text-gray-500 mb-6">
              {searchQuery
                ? `No reports match your search "${searchQuery}"`
                : "You don't have any shared reports yet"}
            </p>
            {searchQuery && (
              <Button variant="outline" onClick={() => setSearchQuery("")}>
                Clear Search
              </Button>
            )}
          </div>
        )}
      </motion.div>
    </div>
  );
}
