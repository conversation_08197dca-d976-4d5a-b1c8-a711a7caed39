"use client";

import { motion } from "framer-motion";
import {
  FileText,
  Wand2,
  Share2,
} from "lucide-react";

const steps = [
  {
    title: "Input Your Data",
    description: "Upload your customer service data or connect to your existing systems. We support multiple data formats and sources.",
    icon: FileText,
    gradient: "gradient-primary-secondary",
  },
  {
    title: "Customize Your Report",
    description: "Use our intuitive drag-and-drop builder to create your perfect report. Add charts, tables, and AI-powered insights.",
    icon: Wand2,
    gradient: "gradient-secondary-tertiary",
  },
  {
    title: "Share & Collaborate",
    description: "Export your report in multiple formats and share with your team. Collect feedback and iterate together.",
    icon: Share2,
    gradient: "gradient-primary-tertiary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    x: -20,
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function Process() {
  return (
    <section className="py-24 bg-white rounded-round my-12 md:px-16">
      <div className="container mx-auto px-2 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">
            Simple Process, Powerful Results
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create professional customer service reports in three easy steps
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {steps.map((step, index) => (
            <motion.div
              key={step.title}
              variants={itemVariants}
              className="relative"
            >
              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-8 left-1/2 w-full h-1 bg-white/20">
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true, amount: 1 }}
                    transition={{ duration: 1, delay: 0.2 }}
                    className={`${step.gradient} h-full origin-left`}
                  />
                </div>
              )}

              {/* Step Content */}
              <div className="flex flex-col items-center text-center">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 360 }}
                  transition={{ duration: 0.3 }}
                  className={`w-16 h-16 rounded-full ${step.gradient} flex items-center justify-center mb-6 relative z-10`}
                >
                  <step.icon className="w-8 h-8 text-white" />
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="text-2xl font-semibold mb-4 text-gray-900">
                    {step.title}
                  </h3>
                  <p className="text-gray-600">
                    {step.description}
                  </p>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
