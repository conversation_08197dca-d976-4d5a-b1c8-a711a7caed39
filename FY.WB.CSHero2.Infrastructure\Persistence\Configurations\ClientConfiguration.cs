using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ClientConfiguration : IEntityTypeConfiguration<Client>
    {
        public void Configure(EntityTypeBuilder<Client> builder)
        {
            builder.HasKey(c => c.Id);

            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.Email)
                .IsRequired()
                .HasMaxLength(100);
            
            builder.HasIndex(c => new { c.TenantId, c.Email }).IsUnique();

            builder.Property(c => c.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(c => c.CompanyName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.Phone)
                .HasMaxLength(20);

            builder.Property(c => c.Address)
                .HasMaxLength(200);

            builder.Property(c => c.CompanySize)
                .HasMaxLength(50);

            builder.Property(c => c.Industry)
                .HasMaxLength(100);

            builder.Property(c => c.CreationTime)
                .IsRequired();

            builder.Property(c => c.LastModificationTime)
                .IsRequired(false); // Allow null for LastModificationTime

            // Make TenantId required and add foreign key relationship
            builder.Property(c => c.TenantId)
                .IsRequired();

            builder.HasOne(c => c.TenantProfile)
                .WithMany(tp => tp.Clients)
                .HasForeignKey(c => c.TenantId)
                .OnDelete(DeleteBehavior.Restrict);

            // Add index on TenantId for better query performance
            builder.HasIndex(c => c.TenantId);
        }
    }
}
