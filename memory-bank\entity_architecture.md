# Entity Architecture: SaaS Template

**Version:** 1.1
**Date:** 2025-05-12
**Related Documents:** `systemPatterns.md`, `techContext.md`

## 1. Overview

This document describes the entity architecture implemented in the SaaS Template project. The architecture provides a hierarchical structure of base entity classes and interfaces to support auditing and multi-tenancy features.

## 2. Entity Hierarchy

The entity architecture is organized as a hierarchy of base classes and interfaces:

```
Entity<TId>
├── AuditedEntity<TId> (implements ICreationAuditedObject, IModificationAuditedObject)
│   └── FullAuditedEntity<TId> (implements IDeletionAuditedObject)
│       └── FullAuditedMultiTenantEntity<TId> (implements IMultiTenant)
└── MultiTenantEntity<TId> (implements IMultiTenant)
    └── AuditedMultiTenantEntity<TId> (implements ICreationAuditedObject, IModificationAuditedObject)
        └── FullAuditedMultiTenantEntity<TId> (implements IDeletionAuditedObject)
```

## 3. Base Entity Classes

### 3.1. Entity<TId>

The root base class for all entities. It provides:
- An ID property of type TId
- Equality comparison based on ID
- Operator overloads for equality comparison

```csharp
public abstract class Entity<TId> : IEntity<TId>
{
    public virtual TId Id { get; protected set; } = default!;
    
    // Equality methods and operator overloads
}
```

### 3.2. AuditedEntity<TId>

Extends Entity<TId> with creation and modification auditing:
- Creation time and creator ID
- Modification time and modifier ID

```csharp
public abstract class AuditedEntity<TId> : Entity<TId>, ICreationAuditedObject, IModificationAuditedObject
{
    public DateTime CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
}
```

### 3.3. FullAuditedEntity<TId>

Extends AuditedEntity<TId> with deletion auditing:
- Deletion time and deleter ID
- IsDeleted flag for soft deletion

```csharp
public abstract class FullAuditedEntity<TId> : AuditedEntity<TId>, IDeletionAuditedObject
{
    public bool IsDeleted { get; set; }
    public DateTime? DeletionTime { get; set; }
    public Guid? DeleterId { get; set; }
}
```

### 3.4. MultiTenantEntity<TId>

Extends Entity<TId> with multi-tenancy:
- TenantId property to associate the entity with a specific tenant

```csharp
public abstract class MultiTenantEntity<TId> : Entity<TId>, IMultiTenant
{
    public Guid? TenantId { get; set; }
}
```

### 3.5. AuditedMultiTenantEntity<TId>

Combines AuditedEntity<TId> and MultiTenantEntity<TId>:
- Provides both auditing and multi-tenancy features

```csharp
public abstract class AuditedMultiTenantEntity<TId> : AuditedEntity<TId>, IMultiTenant
{
    public Guid? TenantId { get; set; }
}
```

### 3.6. FullAuditedMultiTenantEntity<TId>

The most comprehensive base class, combining FullAuditedEntity<TId> and multi-tenancy:
- Provides full auditing (creation, modification, deletion) and multi-tenancy

```csharp
public abstract class FullAuditedMultiTenantEntity<TId> : FullAuditedEntity<TId>, IMultiTenant
{
    public Guid? TenantId { get; set; }
}
```

## 4. Interfaces

### 4.1. IEntity<TId>

Defines the basic entity interface with an ID property:

```csharp
public interface IEntity<TId>
{
    TId Id { get; }
}
```

### 4.2. ICreationAuditedObject

Defines properties for tracking creation:

```csharp
public interface ICreationAuditedObject
{
    DateTime CreationTime { get; set; }
    Guid? CreatorId { get; set; }
}
```

### 4.3. IModificationAuditedObject

Defines properties for tracking modification:

```csharp
public interface IModificationAuditedObject
{
    DateTime? LastModificationTime { get; set; }
    Guid? LastModifierId { get; set; }
}
```

### 4.4. IDeletionAuditedObject

Defines properties for tracking deletion (for soft delete):

```csharp
public interface IDeletionAuditedObject
{
    bool IsDeleted { get; set; }
    DateTime? DeletionTime { get; set; }
    Guid? DeleterId { get; set; }
}
```

### 4.5. IMultiTenant

Defines the property for associating an entity with a tenant:

```csharp
public interface IMultiTenant
{
    Guid? TenantId { get; set; }
}
```

## 5. Usage Examples

### 5.1. Client Entity

The Client entity inherits from FullAuditedMultiTenantEntity<Guid> to get full auditing and multi-tenancy features:

```csharp
public class Client : FullAuditedMultiTenantEntity<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? Address { get; set; }
    public string? CompanySize { get; set; }
    public string? Industry { get; set; }
    
    // Constructor
    public Client(Guid id, string name, string email, string status, string companyName, 
                 string? phone, string? address, string? companySize, string? industry)
    {
        Id = id;
        Name = name;
        Email = email;
        Status = status;
        CompanyName = companyName;
        Phone = phone;
        Address = address;
        CompanySize = companySize;
        Industry = industry;
    }
    
    // Method to update status
    public void SetStatus(string status)
    {
        Status = status;
    }
}
```

### 5.2. AppTenantInfo

The AppTenantInfo class implements ITenantInfo and auditing interfaces:

```csharp
public class AppTenantInfo : ITenantInfo, ICreationAuditedObject, IModificationAuditedObject
{
    // ITenantInfo properties
    public string? Id { get; set; }
    public string? Identifier { get; set; }
    public string? Name { get; set; }
    public string? ConnectionString { get; set; }
    
    // ICreationAuditedObject
    public DateTime CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    
    // IModificationAuditedObject
    public DateTime? LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
}
```

### 5.3. ApplicationUser

The ApplicationUser class extends IdentityUser<Guid> and implements auditing and multi-tenancy interfaces:

```csharp
public class ApplicationUser : IdentityUser<Guid>,
                              IMultiTenant,
                              ISoftDelete,
                              ICreationAuditedObject,
                              IModificationAuditedObject,
                               IDeletionAuditedObject
{
    // Additional profile properties
    public string? CompanyName { get; set; }
    public string? CompanyUrl { get; set; }
    public bool IsAdmin { get; set; } // Added property to indicate admin status

    // IMultiTenant
    public virtual Guid? TenantId { get; set; }
    
    // ISoftDelete
    public virtual bool IsDeleted { get; set; }
    
    // ICreationAuditedObject
    public virtual DateTime CreationTime { get; set; }
    public virtual Guid? CreatorId { get; set; }
    
    // IModificationAuditedObject
    public virtual DateTime? LastModificationTime { get; set; }
    public virtual Guid? LastModifierId { get; set; }
    
    // IDeletionAuditedObject
    public virtual DateTime? DeletionTime { get; set; }
    public virtual Guid? DeleterId { get; set; }
}
```

The ApplicationUser is located in the Infrastructure.Persistence namespace, following Clean Architecture principles by keeping framework-specific implementations in the Infrastructure layer.

## 6. Benefits

This entity architecture provides several benefits:

1. **Code Reuse**: Common functionality is implemented once in base classes and reused across entities.
2. **Consistency**: All entities follow the same patterns for auditing and multi-tenancy.
3. **Flexibility**: Entities can choose the appropriate base class based on their requirements.
4. **Maintainability**: Changes to auditing or multi-tenancy logic can be made in one place.
5. **Extensibility**: New entity types can easily adopt the existing patterns.

## 7. Implementation Notes

- The DbContext is responsible for automatically setting audit properties (CreationTime, LastModificationTime, etc.) during SaveChanges.
- Multi-tenancy filtering is handled at the query level, ensuring that entities are only visible to the appropriate tenant.
- Soft deletion is implemented via the IsDeleted flag, allowing entities to be "deleted" without actually removing them from the database.
