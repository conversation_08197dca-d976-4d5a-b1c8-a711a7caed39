using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Service for advanced version comparison and analysis
    /// </summary>
    public class VersionComparisonService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<VersionComparisonService> _logger;

        public VersionComparisonService(
            ApplicationDbContext context,
            ILogger<VersionComparisonService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Performs a detailed comparison between two versions with visual diff
        /// </summary>
        public async Task<DetailedVersionComparison> CompareVersionsDetailedAsync(
            Guid reportId,
            int version1,
            int version2,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Performing detailed comparison between versions {Version1} and {Version2} for report {ReportId}",
                    version1, version2, reportId);

                var versions = await _context.ReportVersions
                    .Include(rv => rv.ComponentDefinitions)
                    .Where(rv => rv.ReportId == reportId &&
                                (rv.VersionNumber == version1 || rv.VersionNumber == version2))
                    .ToListAsync(cancellationToken);

                var v1 = versions.FirstOrDefault(v => v.VersionNumber == version1);
                var v2 = versions.FirstOrDefault(v => v.VersionNumber == version2);

                if (v1 == null || v2 == null)
                {
                    throw new InvalidOperationException($"One or both versions not found for report {reportId}");
                }

                var comparison = new DetailedVersionComparison
                {
                    ReportId = reportId,
                    Version1 = version1,
                    Version2 = version2,
                    ComparedAt = DateTime.UtcNow,
                    Version1Info = CreateVersionInfo(v1),
                    Version2Info = CreateVersionInfo(v2)
                };

                // Perform detailed component comparison
                comparison.ComponentComparisons = await CompareComponentsDetailedAsync(v1, v2, cancellationToken);

                // Perform data comparison
                comparison.DataComparisons = CompareDataDetailed(v1, v2);

                // Calculate impact analysis
                comparison.ImpactAnalysis = CalculateImpactAnalysis(comparison);

                // Generate migration recommendations
                comparison.MigrationRecommendations = GenerateMigrationRecommendations(comparison);

                // Calculate compatibility score
                comparison.CompatibilityScore = CalculateCompatibilityScore(comparison);

                _logger.LogInformation("Detailed version comparison completed for report {ReportId}: {ChangeCount} changes found",
                    reportId, comparison.ComponentComparisons.Count + comparison.DataComparisons.Count);

                return comparison;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing detailed version comparison for report {ReportId}", reportId);
                throw;
            }
        }

        /// <summary>
        /// Analyzes the impact of rolling back to a specific version
        /// </summary>
        public async Task<RollbackImpactAnalysis> AnalyzeRollbackImpactAsync(
            Guid reportId,
            int targetVersion,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Analyzing rollback impact for report {ReportId} to version {TargetVersion}",
                    reportId, targetVersion);

                var currentVersion = await _context.ReportVersions
                    .Include(rv => rv.ComponentDefinitions)
                    .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.IsCurrent, cancellationToken);

                var targetVersionEntity = await _context.ReportVersions
                    .Include(rv => rv.ComponentDefinitions)
                    .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.VersionNumber == targetVersion, cancellationToken);

                if (currentVersion == null || targetVersionEntity == null)
                {
                    throw new InvalidOperationException($"Current version or target version {targetVersion} not found for report {reportId}");
                }

                var analysis = new RollbackImpactAnalysis
                {
                    ReportId = reportId,
                    CurrentVersion = currentVersion.VersionNumber,
                    TargetVersion = targetVersion,
                    AnalyzedAt = DateTime.UtcNow
                };

                // Analyze what will be lost
                analysis.DataLoss = AnalyzeDataLoss(currentVersion, targetVersionEntity);

                // Analyze component changes
                analysis.ComponentChanges = AnalyzeComponentRollbackChanges(currentVersion, targetVersionEntity);

                // Analyze feature impact
                analysis.FeatureImpact = AnalyzeFeatureImpact(currentVersion, targetVersionEntity);

                // Calculate risk assessment
                analysis.RiskAssessment = CalculateRollbackRisk(analysis);

                // Generate recommendations
                analysis.Recommendations = GenerateRollbackRecommendations(analysis);

                _logger.LogInformation("Rollback impact analysis completed for report {ReportId}: Risk level {RiskLevel}",
                    reportId, analysis.RiskAssessment.RiskLevel);

                return analysis;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing rollback impact for report {ReportId}", reportId);
                throw;
            }
        }

        /// <summary>
        /// Creates a backup before performing a rollback
        /// </summary>
        public async Task<Guid> CreateRollbackBackupAsync(
            Guid reportId,
            int targetVersion,
            string reason,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Creating rollback backup for report {ReportId} before rolling back to version {TargetVersion}",
                    reportId, targetVersion);

                var currentVersion = await _context.ReportVersions
                    .Include(rv => rv.ComponentDefinitions)
                    .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.IsCurrent, cancellationToken);

                if (currentVersion == null)
                {
                    throw new InvalidOperationException($"No current version found for report {reportId}");
                }

                // Get the next version number
                var latestVersionNumber = await _context.ReportVersions
                    .Where(rv => rv.ReportId == reportId)
                    .MaxAsync(rv => (int?)rv.VersionNumber, cancellationToken) ?? 0;

                var backupVersionNumber = latestVersionNumber + 1;

                // Create backup version
                var backupVersion = currentVersion.CreateCopy(
                    backupVersionNumber,
                    $"Backup before rollback to version {targetVersion}: {reason}",
                    currentVersion.CreatorId ?? Guid.Empty);

                // Mark as backup
                backupVersion.IsCurrent = false;

                _context.ReportVersions.Add(backupVersion);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Created rollback backup version {BackupVersion} for report {ReportId}",
                    backupVersionNumber, reportId);

                return backupVersion.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating rollback backup for report {ReportId}", reportId);
                throw;
            }
        }

        /// <summary>
        /// Performs a safe rollback with validation and backup
        /// </summary>
        public async Task<RollbackResult> PerformSafeRollbackAsync(
            Guid reportId,
            int targetVersion,
            RollbackOptions options,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Performing safe rollback for report {ReportId} to version {TargetVersion}",
                    reportId, targetVersion);

                var result = new RollbackResult
                {
                    ReportId = reportId,
                    TargetVersion = targetVersion,
                    StartedAt = DateTime.UtcNow
                };

                // Step 1: Validate rollback
                var validation = await ValidateRollbackAsync(reportId, targetVersion, cancellationToken);
                result.ValidationResult = validation;

                if (!validation.IsValid && !options.ForceRollback)
                {
                    result.Success = false;
                    result.ErrorMessage = "Rollback validation failed. Use ForceRollback option to proceed anyway.";
                    result.CompletedAt = DateTime.UtcNow;
                    return result;
                }

                // Step 2: Create backup if requested
                if (options.CreateBackup)
                {
                    result.BackupVersionId = await CreateRollbackBackupAsync(reportId, targetVersion, options.Reason, cancellationToken);
                }

                // Step 3: Perform the rollback
                var targetVersionEntity = await _context.ReportVersions
                    .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.VersionNumber == targetVersion, cancellationToken);

                if (targetVersionEntity == null)
                {
                    throw new InvalidOperationException($"Target version {targetVersion} not found for report {reportId}");
                }

                // Mark all versions as not current
                var allVersions = await _context.ReportVersions
                    .Where(rv => rv.ReportId == reportId)
                    .ToListAsync(cancellationToken);

                foreach (var version in allVersions)
                {
                    version.SetAsNotCurrent();
                }

                // Mark target version as current
                targetVersionEntity.SetAsCurrent();

                // Update report's current version
                var report = await _context.Reports
                    .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

                if (report != null)
                {
                    report.CurrentVersionId = targetVersionEntity.Id;
                }

                await _context.SaveChangesAsync(cancellationToken);

                result.Success = true;
                result.CompletedAt = DateTime.UtcNow;

                _logger.LogInformation("Successfully completed safe rollback for report {ReportId} to version {TargetVersion}",
                    reportId, targetVersion);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing safe rollback for report {ReportId}", reportId);
                return new RollbackResult
                {
                    ReportId = reportId,
                    TargetVersion = targetVersion,
                    Success = false,
                    ErrorMessage = ex.Message,
                    StartedAt = DateTime.UtcNow,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        #region Private Helper Methods

        private VersionInfo CreateVersionInfo(ReportVersion version)
        {
            return new VersionInfo
            {
                VersionNumber = version.VersionNumber,
                Description = version.Description,
                CreatedAt = version.CreationTime,
                CreatedBy = version.CreatorId ?? Guid.Empty,
                IsCurrent = version.IsCurrent,
                ComponentCount = version.ComponentDefinitions.Count,
                DataSize = version.ComponentDataSize + version.JsonDataSize
            };
        }

        private async Task<List<DetailedComponentComparison>> CompareComponentsDetailedAsync(
            ReportVersion v1,
            ReportVersion v2,
            CancellationToken cancellationToken)
        {
            var comparisons = new List<DetailedComponentComparison>();

            try
            {
                var components1 = v1.GetComponentData<ComponentResult>() ?? new ComponentResult();
                var components2 = v2.GetComponentData<ComponentResult>() ?? new ComponentResult();

                var allSectionIds = components1.Components.Select(c => c.SectionId)
                    .Union(components2.Components.Select(c => c.SectionId))
                    .Distinct();

                foreach (var sectionId in allSectionIds)
                {
                    var comp1 = components1.Components.FirstOrDefault(c => c.SectionId == sectionId);
                    var comp2 = components2.Components.FirstOrDefault(c => c.SectionId == sectionId);

                    var comparison = new DetailedComponentComparison
                    {
                        SectionId = sectionId,
                        SectionName = comp1?.SectionName ?? comp2?.SectionName ?? sectionId
                    };

                    if (comp1 == null && comp2 != null)
                    {
                        comparison.ChangeType = "Added";
                        comparison.NewComponent = comp2;
                    }
                    else if (comp1 != null && comp2 == null)
                    {
                        comparison.ChangeType = "Removed";
                        comparison.OldComponent = comp1;
                    }
                    else if (comp1 != null && comp2 != null)
                    {
                        comparison.OldComponent = comp1;
                        comparison.NewComponent = comp2;

                        if (comp1.ComponentCode != comp2.ComponentCode)
                        {
                            comparison.ChangeType = "Modified";
                            comparison.CodeDiff = GenerateCodeDiff(comp1.ComponentCode, comp2.ComponentCode);
                        }
                        else
                        {
                            comparison.ChangeType = "Unchanged";
                        }
                    }

                    comparisons.Add(comparison);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error comparing components between versions");
            }

            return comparisons;
        }

        private List<DetailedDataComparison> CompareDataDetailed(ReportVersion v1, ReportVersion v2)
        {
            var comparisons = new List<DetailedDataComparison>();

            try
            {
                var data1 = v1.GetReportData();
                var data2 = v2.GetReportData();

                var allKeys = data1.Keys.Union(data2.Keys).Distinct();

                foreach (var key in allKeys)
                {
                    var hasValue1 = data1.TryGetValue(key, out var value1);
                    var hasValue2 = data2.TryGetValue(key, out var value2);

                    var comparison = new DetailedDataComparison
                    {
                        FieldName = key
                    };

                    if (!hasValue1 && hasValue2)
                    {
                        comparison.ChangeType = "Added";
                        comparison.NewValue = value2;
                    }
                    else if (hasValue1 && !hasValue2)
                    {
                        comparison.ChangeType = "Removed";
                        comparison.OldValue = value1;
                    }
                    else if (hasValue1 && hasValue2)
                    {
                        comparison.OldValue = value1;
                        comparison.NewValue = value2;

                        var str1 = JsonSerializer.Serialize(value1);
                        var str2 = JsonSerializer.Serialize(value2);

                        if (str1 != str2)
                        {
                            comparison.ChangeType = "Modified";
                            comparison.ValueDiff = GenerateValueDiff(str1, str2);
                        }
                        else
                        {
                            comparison.ChangeType = "Unchanged";
                        }
                    }

                    comparisons.Add(comparison);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error comparing data between versions");
            }

            return comparisons;
        }

        private CodeDiff GenerateCodeDiff(string oldCode, string newCode)
        {
            var oldLines = oldCode.Split('\n');
            var newLines = newCode.Split('\n');

            var diff = new CodeDiff();

            // Simple diff implementation - in production, use a proper diff library
            var maxLines = Math.Max(oldLines.Length, newLines.Length);

            for (int i = 0; i < maxLines; i++)
            {
                var oldLine = i < oldLines.Length ? oldLines[i] : null;
                var newLine = i < newLines.Length ? newLines[i] : null;

                if (oldLine == null && newLine != null)
                {
                    diff.LinesAdded++;
                    diff.DiffLines.Add(new DiffLine
                    {
                        NewLineNumber = i + 1,
                        ChangeType = "Added",
                        Content = newLine
                    });
                }
                else if (oldLine != null && newLine == null)
                {
                    diff.LinesRemoved++;
                    diff.DiffLines.Add(new DiffLine
                    {
                        OldLineNumber = i + 1,
                        ChangeType = "Removed",
                        Content = oldLine
                    });
                }
                else if (oldLine != null && newLine != null)
                {
                    if (oldLine != newLine)
                    {
                        diff.LinesModified++;
                        diff.DiffLines.Add(new DiffLine
                        {
                            OldLineNumber = i + 1,
                            NewLineNumber = i + 1,
                            ChangeType = "Modified",
                            Content = newLine
                        });
                    }
                }
            }

            diff.ChangePercentage = maxLines > 0
                ? (double)(diff.LinesAdded + diff.LinesRemoved + diff.LinesModified) / maxLines * 100
                : 0;

            return diff;
        }

        private string GenerateValueDiff(string oldValue, string newValue)
        {
            // Simple value diff - in production, use a proper diff library
            return $"Changed from: {oldValue} to: {newValue}";
        }

        private ImpactAnalysis CalculateImpactAnalysis(DetailedVersionComparison comparison)
        {
            var analysis = new ImpactAnalysis();

            // Calculate component impact
            var componentChanges = comparison.ComponentComparisons.Where(c => c.ChangeType != "Unchanged").ToList();
            analysis.ComponentsAffected = componentChanges.Count;
            analysis.ComponentsAdded = componentChanges.Count(c => c.ChangeType == "Added");
            analysis.ComponentsRemoved = componentChanges.Count(c => c.ChangeType == "Removed");
            analysis.ComponentsModified = componentChanges.Count(c => c.ChangeType == "Modified");

            // Calculate data impact
            var dataChanges = comparison.DataComparisons.Where(d => d.ChangeType != "Unchanged").ToList();
            analysis.DataFieldsAffected = dataChanges.Count;
            analysis.DataFieldsAdded = dataChanges.Count(d => d.ChangeType == "Added");
            analysis.DataFieldsRemoved = dataChanges.Count(d => d.ChangeType == "Removed");
            analysis.DataFieldsModified = dataChanges.Count(d => d.ChangeType == "Modified");

            // Calculate overall impact level
            var totalChanges = analysis.ComponentsAffected + analysis.DataFieldsAffected;
            analysis.OverallImpactLevel = totalChanges switch
            {
                0 => "None",
                <= 5 => "Low",
                <= 15 => "Medium",
                <= 30 => "High",
                _ => "Critical"
            };

            return analysis;
        }

        private List<string> GenerateMigrationRecommendations(DetailedVersionComparison comparison)
        {
            var recommendations = new List<string>();

            var removedComponents = comparison.ComponentComparisons.Where(c => c.ChangeType == "Removed").ToList();
            if (removedComponents.Any())
            {
                recommendations.Add($"Consider backing up data from {removedComponents.Count} removed components before proceeding.");
            }

            var modifiedComponents = comparison.ComponentComparisons.Where(c => c.ChangeType == "Modified").ToList();
            if (modifiedComponents.Any())
            {
                recommendations.Add($"Review changes in {modifiedComponents.Count} modified components for compatibility issues.");
            }

            var removedData = comparison.DataComparisons.Where(d => d.ChangeType == "Removed").ToList();
            if (removedData.Any())
            {
                recommendations.Add($"Export data for {removedData.Count} removed fields before migration.");
            }

            if (!recommendations.Any())
            {
                recommendations.Add("No specific migration steps required. Changes appear to be safe.");
            }

            return recommendations;
        }

        private int CalculateCompatibilityScore(DetailedVersionComparison comparison)
        {
            var score = 100;

            // Deduct points for removed components
            var removedComponents = comparison.ComponentComparisons.Count(c => c.ChangeType == "Removed");
            score -= removedComponents * 20;

            // Deduct points for removed data
            var removedData = comparison.DataComparisons.Count(d => d.ChangeType == "Removed");
            score -= removedData * 10;

            // Deduct points for major modifications
            var majorModifications = comparison.ComponentComparisons
                .Where(c => c.ChangeType == "Modified" && c.CodeDiff?.ChangePercentage > 50)
                .Count();
            score -= majorModifications * 15;

            return Math.Max(0, score);
        }

        private DataLossAnalysis AnalyzeDataLoss(ReportVersion currentVersion, ReportVersion targetVersion)
        {
            var analysis = new DataLossAnalysis();

            try
            {
                var currentData = currentVersion.GetReportData();
                var targetData = targetVersion.GetReportData();

                var lostFields = currentData.Keys.Except(targetData.Keys).ToList();
                analysis.LostDataFields = lostFields;
                analysis.EstimatedDataLoss = lostFields.Count > 0 ? "Medium" : "None";

                var currentComponents = currentVersion.GetComponentData<ComponentResult>() ?? new ComponentResult();
                var targetComponents = targetVersion.GetComponentData<ComponentResult>() ?? new ComponentResult();

                var lostComponents = currentComponents.Components
                    .Where(c => !targetComponents.Components.Any(tc => tc.SectionId == c.SectionId))
                    .Select(c => c.SectionId)
                    .ToList();

                analysis.LostComponents = lostComponents;

                if (lostComponents.Any() || lostFields.Any())
                {
                    analysis.RecommendedActions.Add("Create a backup before proceeding with rollback");
                    analysis.RecommendedActions.Add("Export data from components and fields that will be lost");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error analyzing data loss");
                analysis.EstimatedDataLoss = "Unknown";
            }

            return analysis;
        }

        private List<ComponentRollbackChange> AnalyzeComponentRollbackChanges(ReportVersion currentVersion, ReportVersion targetVersion)
        {
            var changes = new List<ComponentRollbackChange>();

            try
            {
                var currentComponents = currentVersion.GetComponentData<ComponentResult>() ?? new ComponentResult();
                var targetComponents = targetVersion.GetComponentData<ComponentResult>() ?? new ComponentResult();

                var allSectionIds = currentComponents.Components.Select(c => c.SectionId)
                    .Union(targetComponents.Components.Select(c => c.SectionId))
                    .Distinct();

                foreach (var sectionId in allSectionIds)
                {
                    var currentComp = currentComponents.Components.FirstOrDefault(c => c.SectionId == sectionId);
                    var targetComp = targetComponents.Components.FirstOrDefault(c => c.SectionId == sectionId);

                    var change = new ComponentRollbackChange
                    {
                        SectionId = sectionId,
                        SectionName = currentComp?.SectionName ?? targetComp?.SectionName ?? sectionId
                    };

                    if (currentComp != null && targetComp == null)
                    {
                        change.ChangeType = "WillBeRemoved";
                        change.Impact = "High";
                    }
                    else if (currentComp == null && targetComp != null)
                    {
                        change.ChangeType = "WillBeAdded";
                        change.Impact = "Medium";
                    }
                    else if (currentComp != null && targetComp != null && currentComp.ComponentCode != targetComp.ComponentCode)
                    {
                        change.ChangeType = "WillBeReverted";
                        change.Impact = "Medium";
                    }
                    else
                    {
                        change.ChangeType = "NoChange";
                        change.Impact = "None";
                    }

                    if (change.ChangeType != "NoChange")
                    {
                        changes.Add(change);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error analyzing component rollback changes");
            }

            return changes;
        }

        private FeatureImpactAnalysis AnalyzeFeatureImpact(ReportVersion currentVersion, ReportVersion targetVersion)
        {
            var analysis = new FeatureImpactAnalysis();

            // Analyze version differences to determine feature impact
            var versionDiff = currentVersion.VersionNumber - targetVersion.VersionNumber;

            analysis.VersionsToRollback = versionDiff;
            analysis.EstimatedFeatureLoss = versionDiff switch
            {
                1 => "Minimal",
                <= 5 => "Low",
                <= 10 => "Medium",
                _ => "High"
            };

            analysis.AffectedFeatures = new List<string>
            {
                "Recent component updates",
                "Data modifications",
                "Configuration changes"
            };

            return analysis;
        }

        private RiskAssessment CalculateRollbackRisk(RollbackImpactAnalysis analysis)
        {
            var assessment = new RiskAssessment();

            var riskScore = 0;

            // Data loss risk
            if (analysis.DataLoss.LostDataFields.Any())
            {
                riskScore += analysis.DataLoss.LostDataFields.Count * 10;
            }

            if (analysis.DataLoss.LostComponents.Any())
            {
                riskScore += analysis.DataLoss.LostComponents.Count * 20;
            }

            // Component change risk
            var highImpactChanges = analysis.ComponentChanges.Count(c => c.Impact == "High");
            riskScore += highImpactChanges * 15;

            // Feature impact risk
            riskScore += analysis.FeatureImpact.VersionsToRollback * 5;

            assessment.RiskScore = riskScore;
            assessment.RiskLevel = riskScore switch
            {
                <= 20 => "Low",
                <= 50 => "Medium",
                <= 100 => "High",
                _ => "Critical"
            };

            assessment.RiskFactors = new List<string>();

            if (analysis.DataLoss.LostDataFields.Any())
            {
                assessment.RiskFactors.Add($"Data loss: {analysis.DataLoss.LostDataFields.Count} fields");
            }

            if (analysis.DataLoss.LostComponents.Any())
            {
                assessment.RiskFactors.Add($"Component loss: {analysis.DataLoss.LostComponents.Count} components");
            }

            if (highImpactChanges > 0)
            {
                assessment.RiskFactors.Add($"High impact changes: {highImpactChanges}");
            }

            return assessment;
        }

        private List<string> GenerateRollbackRecommendations(RollbackImpactAnalysis analysis)
        {
            var recommendations = new List<string>();

            if (analysis.RiskAssessment.RiskLevel == "Critical" || analysis.RiskAssessment.RiskLevel == "High")
            {
                recommendations.Add("Create a full backup before proceeding");
                recommendations.Add("Consider creating a new branch instead of rolling back");
                recommendations.Add("Review all changes carefully with stakeholders");
            }

            if (analysis.DataLoss.LostDataFields.Any())
            {
                recommendations.Add("Export data from fields that will be lost");
            }

            if (analysis.DataLoss.LostComponents.Any())
            {
                recommendations.Add("Document component configurations that will be lost");
            }

            recommendations.AddRange(analysis.DataLoss.RecommendedActions);

            if (!recommendations.Any())
            {
                recommendations.Add("Rollback appears safe to proceed");
            }

            return recommendations;
        }

        private async Task<RollbackValidation> ValidateRollbackAsync(Guid reportId, int targetVersion, CancellationToken cancellationToken)
        {
            var validation = new RollbackValidation
            {
                IsValid = true,
                ValidationErrors = new List<string>(),
                ValidationWarnings = new List<string>()
            };

            // Check if target version exists
            var targetVersionExists = await _context.ReportVersions
                .AnyAsync(rv => rv.ReportId == reportId && rv.VersionNumber == targetVersion, cancellationToken);

            if (!targetVersionExists)
            {
                validation.IsValid = false;
                validation.ValidationErrors.Add($"Target version {targetVersion} does not exist");
            }

            // Check if target version is not the current version
            var currentVersion = await _context.ReportVersions
                .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.IsCurrent, cancellationToken);

            if (currentVersion?.VersionNumber == targetVersion)
            {
                validation.IsValid = false;
                validation.ValidationErrors.Add("Cannot rollback to the current version");
            }

            // Add warnings for potential issues
            if (currentVersion != null && currentVersion.VersionNumber - targetVersion > 5)
            {
                validation.ValidationWarnings.Add("Rolling back more than 5 versions may result in significant data loss");
            }

            return validation;
        }

        #endregion
    }
}
