export interface ReportSection {
  'section-title': string;
  type: 'cover' | 'text' | 'chart' | 'table' | 'list' | 'timeline';
  content: any;
}

export interface ReportContent {
  template: string;
  sections: ReportSection[];
}

export interface Report {
  reportId: string;
  clientId: string;
  clientName: string;
  reportName: string;
  category: string;
  slideCount: number;
  createdAt: string;
  lastModified: string;
  status: 'Completed' | 'In Progress' | 'Under Review' | 'Draft';
  author: string;
  content?: ReportContent;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  sections?: ReportSection[];
  chartData?: any[]; // Chart data for preview
  thumbnailUrl?: string;
  pages?: any[];
  templateData?: any; // For backward compatibility
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
