'use client';

import { useState, useEffect } from 'react';
import { Building } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/providers/toast-provider';
import { CompanyProfile } from '@/types/company';
// import { updateAdminTenant } from '@/lib/api'; // updateAdminTenant will be replaced or removed

interface AdminBillingDetailsCardProps {
  companyProfile: CompanyProfile | null;
  onUpdate?: () => void; // This might become irrelevant if saving is disabled
}

export function AdminBillingDetailsCard({ companyProfile, onUpdate }: AdminBillingDetailsCardProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: companyProfile?.name || '', // Contact Name (using CompanyProfile.Name as per user)
    email: companyProfile?.email || '',
    company: companyProfile?.name || '', // Company Name (using CompanyProfile.Name)
    billingAddress: {
      street: companyProfile?.addressLine1 || '',
      city: companyProfile?.city || '',
      state: companyProfile?.state || '',
      zipCode: companyProfile?.zipCode || '',
      country: companyProfile?.country || '',
    }
  });

  useEffect(() => {
    if (companyProfile) {
      setFormData({
        name: companyProfile.name || '', // Contact Name
        email: companyProfile.email || '',
        company: companyProfile.name || '', // Company Name
        billingAddress: {
          street: `${companyProfile.addressLine1 || ''}${companyProfile.addressLine2 ? ' ' + companyProfile.addressLine2 : ''}`.trim(),
          city: companyProfile.city || '',
          state: companyProfile.state || '',
          zipCode: companyProfile.zipCode || '',
          country: companyProfile.country || '',
        }
      });
    }
  }, [companyProfile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleBillingAddressChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      billingAddress: {
        ...formData.billingAddress,
        [field]: value,
      },
    });
  };

  const handleSave = async () => {
    // TODO: Implement updating company profile. 
    // Currently, CompanyProfile is read from appsettings.json and is not editable via API.
    // Direct modification of appsettings.json at runtime by an API is not recommended.
    // A database-backed store for CompanyProfile would be a better approach for editability.
    setLoading(true);
    toast({
      title: 'Note',
      description: 'Updating company profile is not implemented in this version. Data is read-only from configuration.',
      variant: 'default',
    });
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    setLoading(false);
    // if (!companyProfile) return;

    // try {
    //   setLoading(true);
      
    //   const updateData = {
    //     name: formData.company, // Corresponds to CompanyProfile.Name
    //     // contactName: formData.name, // No direct field in CompanyProfileDto for this
    //     email: formData.email,
    //     addressLine1: formData.billingAddress.street, // This needs splitting if addressLine2 is separate
    //     // addressLine2: ???
    //     city: formData.billingAddress.city,
    //     state: formData.billingAddress.state,
    //     zipCode: formData.billingAddress.zipCode,
    //     country: formData.billingAddress.country,
    //     // phone: companyProfile.phone, // Not in form
    //     // website: companyProfile.website, // Not in form
    //     // logoUrl: companyProfile.logoUrl, // Not in form
    //   };

    //   // Placeholder for API call to update company profile
    //   // await updateCompanyProfile(updateData); 
      
    //   toast({
    //     title: 'Success',
    //     description: 'Billing details updated successfully (Simulated)',
    //   });

    //   if (onUpdate) {
    //     onUpdate();
    //   }
    // } catch (error) {
    //   console.error('Error updating billing details:', error);
    //   toast({
    //     title: 'Error',
    //     description: 'Failed to update billing details (Simulated)',
    //     variant: 'destructive',
    //   });
    // } finally {
    //   setLoading(false);
    // }
  };

  return (
    <div className="grid grid-cols-2 gap-8">
      {/* Company Information Card */}
      <Card className="col-span-1 p-6 bg-white from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
        <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Company Information</h2>
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="company" className="text-sm font-medium text-muted-foreground">
              Company Name
            </label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder="Company name"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium text-muted-foreground">
              Contact Name
            </label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Contact name"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium text-muted-foreground">
              Email Address
            </label>
            <Input
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Email address"
            />
          </div>
        </div>
      </Card>

      {/* Billing Address Card */}
      <Card className="col-span-1 p-6 bg-white from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
        <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Billing Address</h2>
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="street" className="text-sm font-medium text-muted-foreground">
              Street Address
            </label>
            <Input
              id="street"
              value={formData.billingAddress.street}
              onChange={(e) => handleBillingAddressChange('street', e.target.value)}
              placeholder="Street address"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="city" className="text-sm font-medium text-muted-foreground">
              City
            </label>
            <Input
              id="city"
              value={formData.billingAddress.city}
              onChange={(e) => handleBillingAddressChange('city', e.target.value)}
              placeholder="City"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="state" className="text-sm font-medium text-muted-foreground">
                State/Province
              </label>
              <Input
                id="state"
                value={formData.billingAddress.state}
                onChange={(e) => handleBillingAddressChange('state', e.target.value)}
                placeholder="State/Province"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="zipCode" className="text-sm font-medium text-muted-foreground">
                ZIP/Postal Code
              </label>
              <Input
                id="zipCode"
                value={formData.billingAddress.zipCode}
                onChange={(e) => handleBillingAddressChange('zipCode', e.target.value)}
                placeholder="ZIP/Postal Code"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="country" className="text-sm font-medium text-muted-foreground">
              Country
            </label>
            <Input
              id="country"
              value={formData.billingAddress.country}
              onChange={(e) => handleBillingAddressChange('country', e.target.value)}
              placeholder="Country"
            />
          </div>
        </div>
      </Card>

      {/* Save Button */}
      <div className="col-span-2 flex justify-end">
        <Button 
          onClick={handleSave}
          disabled={loading}
          className="px-8 bg-white text-primary hover:text-primary/50 hover:bg-white"
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}
