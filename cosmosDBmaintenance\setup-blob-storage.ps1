# Azure Blob Storage Setup for Report Rendering Engine
# Creates storage account and containers in existing rg_CSHero resource group

param(
    [string]$ResourceGroup = "rg_CSHero",
    [string]$StorageAccount = "csherostorage", 
    [string]$Location = "centralus"
)

Write-Host "=== Azure Blob Storage Setup ===" -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Cyan
Write-Host "Storage Account: $StorageAccount" -ForegroundColor Cyan
Write-Host "Location: $Location" -ForegroundColor Cyan
Write-Host ""

try {
    # Check if logged into Azure CLI
    $account = az account show 2>$null | ConvertFrom-Json
    if (-not $account) {
        Write-Host "Please login to Azure CLI first: az login" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Logged in as: $($account.user.name)" -ForegroundColor Green
    Write-Host ""

    # Create storage account
    Write-Host "Creating storage account: $StorageAccount" -ForegroundColor Yellow
    az storage account create `
        --name $StorageAccount `
        --resource-group $ResourceGroup `
        --location $Location `
        --sku "Standard_LRS" `
        --kind "StorageV2" `
        --access-tier "Hot" `
        --allow-blob-public-access false

    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create storage account"
    }

    # Create blob containers
    Write-Host "Creating blob container: report-data" -ForegroundColor Yellow
    az storage container create `
        --name "report-data" `
        --account-name $StorageAccount `
        --auth-mode login `
        --public-access off

    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create blob container"
    }

    Write-Host ""
    Write-Host "✅ Blob storage setup complete!" -ForegroundColor Green
    Write-Host "Storage Account: $StorageAccount" -ForegroundColor White
    Write-Host "Container: report-data" -ForegroundColor White

} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
