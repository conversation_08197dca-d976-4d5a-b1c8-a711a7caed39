"use client";

import { motion } from "framer-motion";
import { 
  Shield,
  Database,
  Settings,
  Lock,
  Share2,
  UserCheck,
  Mail,
  RefreshCw
} from "lucide-react";

export default function PrivacyPage() {
  return (
    <div>
      <div className="gradient-primary-secondary py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold mb-4 text-white">Privacy Policy</h1>
            <p className="text-xl text-gray-300">
              Your privacy is important to us. Learn how we protect and manage your data.
            </p>
          </motion.div>
        </div>
      </div>

      <div className="bg-white">
        <div className="container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="prose max-w-4xl mx-auto text-gray-600">
              <p className="text-lg mb-8">Last updated: February 18, 2024</p>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                    >
                      <Shield className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">1. Introduction</h2>
                </div>
                <p className="mb-4">
                  At CS-Hero, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our customer service analysis report generation service.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >
                      <Database className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">2. Information We Collect</h2>
                </div>
                <h3 className="text-xl font-medium mb-3 text-gray-800">2.1 Personal Information</h3>
                <p className="mb-4">We may collect personal information that you provide to us, including but not limited to:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Name and contact information</li>
                  <li>Company information</li>
                  <li>Login credentials</li>
                  <li>Payment information</li>
                  <li>Usage data and preferences</li>
                </ul>

                <h3 className="text-xl font-medium mb-3 text-gray-800">2.2 Usage Information</h3>
                <p className="mb-4">We automatically collect certain information when you use our service, including:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Log data and analytics</li>
                  <li>Device and browser information</li>
                  <li>IP address and location data</li>
                  <li>Cookie and tracking data</li>
                </ul>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.4 }}
                    >
                      <Settings className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">3. How We Use Your Information</h2>
                </div>
                <p className="mb-4">We use the collected information for various purposes, including:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Providing and maintaining our service</li>
                  <li>Processing your transactions</li>
                  <li>Sending you service updates and notifications</li>
                  <li>Improving our service and user experience</li>
                  <li>Analyzing usage patterns and trends</li>
                  <li>Preventing fraud and ensuring security</li>
                </ul>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.5 }}
                    >
                      <Lock className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">4. Data Security</h2>
                </div>
                <p className="mb-4">
                  We implement appropriate technical and organizational security measures to protect your personal information. However, no method of transmission over the Internet or electronic storage is 100% secure, and we cannot guarantee absolute security.
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                    >
                      <Share2 className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">5. Data Sharing and Disclosure</h2>
                </div>
                <p className="mb-4">We may share your information with:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Service providers and business partners</li>
                  <li>Legal authorities when required by law</li>
                  <li>Third parties with your consent</li>
                </ul>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.7 }}
                    >
                      <UserCheck className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">6. Your Rights</h2>
                </div>
                <p className="mb-4">You have the right to:</p>
                <ul className="list-disc pl-6 mb-4">
                  <li>Access your personal information</li>
                  <li>Correct inaccurate data</li>
                  <li>Request deletion of your data</li>
                  <li>Object to data processing</li>
                  <li>Data portability</li>
                </ul>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-primary-secondary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.8 }}
                    >
                      <Mail className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">7. Contact Us</h2>
                </div>
                <p className="mb-4">
                  If you have any questions about this Privacy Policy, please contact us at:
                </p>
                <p className="mb-4">
                  Email: <EMAIL><br />
                  Address: 123 Business Ave, Suite 100<br />
                  New York, NY 10001<br />
                  United States
                </p>
              </section>

              <section className="mb-16">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-lg gradient-secondary-tertiary flex items-center justify-center mr-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.9 }}
                    >
                      <RefreshCw className="w-5 h-5 text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">8. Changes to This Policy</h2>
                </div>
                <p className="mb-4">
                  We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.
                </p>
              </section>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
