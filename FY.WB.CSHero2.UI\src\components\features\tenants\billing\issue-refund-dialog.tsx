'use client';

import { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface IssueRefundDialogProps {
  onIssueRefund: (amount: string, type: string, reason: string, note: string) => void;
}

export function IssueRefundDialog({ onIssueRefund }: IssueRefundDialogProps) {
  const [refundAmount, setRefundAmount] = useState('');
  const [refundType, setRefundType] = useState('refund');
  const [refundReason, setRefundReason] = useState('');
  const [refundNote, setRefundNote] = useState('');
  const [open, setOpen] = useState(false);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!refundAmount || !refundReason) {
      return; // Don't submit if required fields are empty
    }
    
    setIsSubmitting(true);
    try {
      await onIssueRefund(refundAmount, refundType, refundReason, refundNote);
      setOpen(false);
      // Reset form
      setRefundAmount('');
      setRefundType('refund');
      setRefundReason('');
      setRefundNote('');
    } catch (error) {
      console.error('Error issuing refund:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <RefreshCw className="h-4 w-4" />
          Issue Refund
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Issue Refund or Credit</DialogTitle>
          <DialogDescription>
            Process a refund or apply a credit to the tenant's account.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="refundType" className="text-sm font-medium text-muted-foreground">
              Type
            </label>
            <Select value={refundType} onValueChange={setRefundType}>
              <SelectTrigger id="refundType">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="refund">Refund</SelectItem>
                <SelectItem value="credit">Account Credit</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label htmlFor="refundAmount" className="text-sm font-medium text-muted-foreground">
              Amount
            </label>
            <Input
              id="refundAmount"
              value={refundAmount}
              onChange={(e) => setRefundAmount(e.target.value)}
              placeholder="Enter amount (e.g. 99.99)"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="refundReason" className="text-sm font-medium text-muted-foreground">
              Reason
            </label>
            <Select
              value={refundReason}
              onValueChange={setRefundReason}
            >
              <SelectTrigger id="refundReason">
                <SelectValue placeholder="Select reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overcharge">Billing Error/Overcharge</SelectItem>
                <SelectItem value="service">Service Issue</SelectItem>
                <SelectItem value="cancellation">Subscription Cancellation</SelectItem>
                <SelectItem value="duplicate">Duplicate Payment</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label htmlFor="refundNote" className="text-sm font-medium text-muted-foreground">
              Additional Notes
            </label>
            <Input
              id="refundNote"
              value={refundNote}
              onChange={(e) => setRefundNote(e.target.value)}
              placeholder="Add details about this refund"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setOpen(false)} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            type="button" 
            onClick={handleSubmit} 
            disabled={!refundAmount || !refundReason || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary/30 border-r-white"></div>
                Processing...
              </>
            ) : (
              'Process Refund'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
