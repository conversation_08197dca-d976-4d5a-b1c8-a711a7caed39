import { readJsonFile, writeJsonFile } from './utils';
import { Tenant } from '@/types/tenant';
import { getTenantById, updateTenant } from './tenants';

/**
 * Get the admin tenant
 * @returns Admin tenant object or null if not found
 */
export async function getAdminTenant(): Promise<Tenant | null> {
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants || data.tenants.length === 0) {
    return null;
  }
  
  // For simplicity, we'll consider the first tenant as the admin tenant
  return data.tenants[0] || null;
}

/**
 * Update the admin tenant
 * @param updates Tenant data to update
 * @returns Updated admin tenant or null if not found
 */
export async function updateAdminTenant(updates: Partial<Tenant>): Promise<Tenant | null> {
  const adminTenant = await getAdminTenant();
  
  if (!adminTenant) {
    return null;
  }
  
  return updateTenant(adminTenant.id, updates);
}
