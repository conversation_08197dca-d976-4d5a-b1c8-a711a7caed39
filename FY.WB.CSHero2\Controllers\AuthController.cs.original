using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http; // For StatusCodes
using FY.WB.CSHero2.Infrastructure.Persistence; // For ApplicationUser
using Finbuckle.MultiTenant; // For MultiTenant functionality
using Finbuckle.MultiTenant.Abstractions; // For ITenantInfo
using Microsoft.Extensions.Logging; // For ILogger

namespace FY.WB.CSHero2.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IConfiguration _configuration;
        private readonly IMultiTenantStore<AppTenantInfo> _tenantStore;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration,
            IMultiTenantStore<AppTenantInfo> tenantStore,
            ILogger<AuthController> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _configuration = configuration;
            _tenantStore = tenantStore;
            _logger = logger;
        }

        public class RegisterModel
        {
            public string? Email { get; set; }
            public string? Password { get; set; }
            public string? CompanyName { get; set; }
            public string? CompanyUrl { get; set; }
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterModel model)
        {
            // Add CRITICAL log at the very beginning to help diagnose if we're hitting this endpoint
            _logger.LogCritical("AUTH REGISTER ENDPOINT CALLED: Request received from {RequestIP}",
                HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "unknown");
            
            // Detailed debugging info about the request
            _logger.LogInformation("Request method: {Method}, Content-Type: {ContentType}, Content-Length: {ContentLength}",
                Request.Method,
                Request.ContentType,
                Request.ContentLength);
                
            // Log headers for debugging
            foreach (var header in Request.Headers)
            {
                _logger.LogInformation("Header: {Key} = {Value}", 
                    header.Key, string.Join(", ", header.Value));
            }
            
            // Log the received model data (exclude sensitive info like password)
            _logger.LogInformation("Register model received: Email={Email}, CompanyName={CompanyName}",
                model?.Email, model?.CompanyName);
                
            // Explicitly log if any of the required fields are null to help debugging
            if (model.Email == null)
                _logger.LogWarning("Email is null");
            if (model.Password == null)
                _logger.LogWarning("Password is null");
            if (string.IsNullOrEmpty(model.CompanyName))
                _logger.LogWarning("CompanyName is null or empty");
                
            if (model.Email == null || model.Password == null || string.IsNullOrEmpty(model.CompanyName))
            {
                _logger.LogWarning("Register validation failed: missing required fields");
                return BadRequest(new { Message = "Email, Password, and Company Name are required." });
            }

            try
            {
                // Log DB access attempt
                _logger.LogInformation("Attempting database operations for registration");
                // Create a new tenant based on the company name
                var tenantId = Guid.NewGuid();
                
                // Generate a unique identifier from the company name (lowercase, no spaces)
                var identifier = model.CompanyName.ToLower().Replace(" ", "-");
                
                // Ensure identifier is unique by appending a random suffix if needed
                identifier = $"{identifier}-{Guid.NewGuid().ToString().Substring(0, 8)}";
                
                // Create the new tenant
                var newTenant = new AppTenantInfo
                {
                    Id = tenantId.ToString(),
                    Identifier = identifier,
                    Name = model.CompanyName,
                    // Use the default connection string for now
                    ConnectionString = _configuration.GetConnectionString("DefaultConnection")
                };
                
                // Log connection string to verify it's properly loaded
                _logger.LogInformation("Using connection string: {ConnectionString}", 
                    _configuration.GetConnectionString("DefaultConnection"));
                
                // Add the tenant to the store
                _logger.LogInformation("Attempting to add tenant with ID {TenantId} and identifier {Identifier}",
                    tenantId, identifier);
                
                var addResult = await _tenantStore.TryAddAsync(newTenant);
                if (!addResult)
                {
                    _logger.LogError("Failed to add tenant to store");
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        new { Message = "Failed to add tenant to store" });
                }
                
                _logger.LogInformation("Successfully added tenant with ID {TenantId}", tenantId);
                
                // Create the user and associate with the new tenant
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    CompanyName = model.CompanyName,
                    CompanyUrl = model.CompanyUrl,
                    TenantId = tenantId // Assign the newly created tenant ID to the user
                };
                
                _logger.LogInformation("Attempting to create user with email {Email}", model.Email);
                var result = await _userManager.CreateAsync(user, model.Password);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation("User registration successful for {Email}", model.Email);
                    return Ok(new {
                        Message = "User and tenant registered successfully",
                        TenantId = tenantId.ToString(),
                        TenantIdentifier = identifier
                    });
                }
                
                _logger.LogWarning("User registration failed for {Email}", model.Email);
                foreach (var error in result.Errors)
                {
                    _logger.LogWarning("Registration error: {ErrorCode} - {ErrorDescription}", 
                        error.Code, error.Description);
                    ModelState.AddModelError(string.Empty, error.Description);
                }
                return BadRequest(ModelState);
            }
            catch (Exception ex)
            {
                // Log the exception with full stack trace
                _logger.LogError(ex, "Error during registration: {ErrorMessage}", ex.Message);
                
                // Log any inner exceptions as well
                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "Inner exception: {ErrorMessage}", 
                        ex.InnerException.Message);
                }
                
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { 
                        Message = $"Error creating tenant: {ex.Message}", 
                        StackTrace = ex.StackTrace,
                        InnerException = ex.InnerException?.Message
                    });
            }
        }

        public class LoginModel
        {
            public string? Email { get; set; }
            public string? Password { get; set; }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginModel model)
        {
            if (model.Email == null || model.Password == null)
            {
                return BadRequest(new { Message = "Email and Password are required." });
            }

            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user != null && await _userManager.CheckPasswordAsync(user, model.Password))
            {
                var authClaims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Email, user.Email ?? ""),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                };

                // Add tenant ID claim if available
                if (user.TenantId.HasValue)
                {
                    authClaims.Add(new Claim("tenant_id", user.TenantId.Value.ToString()));
                }

                // Add roles as claims if needed
                // var userRoles = await _userManager.GetRolesAsync(user);
                // foreach (var userRole in userRoles)
                // {
                //     authClaims.Add(new Claim(ClaimTypes.Role, userRole));
                // }
                
                var jwtKey = _configuration["Jwt:Key"];
                if (string.IsNullOrEmpty(jwtKey))
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { Message = "JWT Key is not configured." });
                }

                var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));

                var token = new JwtSecurityToken(
                    issuer: _configuration["Jwt:Issuer"],
                    audience: _configuration["Jwt:Audience"],
                    expires: DateTime.Now.AddHours(3),
                    claims: authClaims,
                    signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                    );

                return Ok(new
                {
                    token = new JwtSecurityTokenHandler().WriteToken(token),
                    expiration = token.ValidTo,
                    userId = user.Id.ToString(),
                    email = user.Email,
                    tenantId = user.TenantId?.ToString()
                });
            }
            return Unauthorized(new { Message = "Invalid credentials" });
        }
    }
}
