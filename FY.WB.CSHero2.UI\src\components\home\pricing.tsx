"use client";

import { motion } from "framer-motion";
import { Check } from "lucide-react";

const plans = [
  {
    name: "Basic",
    price: "$49",
    description: "Perfect for small teams getting started",
    features: [
      "Up to 50 reports/month",
      "5 team tenants",
      "Basic templates",
      "Email support",
      "Export to PDF",
    ],
    gradient: "gradient-primary-secondary",
  },
  {
    name: "Pro",
    price: "$99",
    description: "For growing teams with advanced needs",
    features: [
      "Unlimited reports",
      "25 team tenants",
      "Advanced templates",
      "Priority support",
      "Export to PDF & PPT",
      "Custom branding",
      "API access",
    ],
    gradient: "gradient-secondary-tertiary",
    popular: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    description: "Custom solutions for large organizations",
    features: [
      "Unlimited everything",
      "Unlimited team tenants",
      "Custom templates",
      "24/7 support",
      "All export formats",
      "Custom integrations",
      "SLA guarantee",
      "Dedicated manager",
    ],
    gradient: "gradient-primary-tertiary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function PricingPreview() {
  return (
    <section className="py-24 bg-gray-50 rounded-round md:px-16">
      <div className="container mx-auto px-2 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose the plan that best fits your needs
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {plans.map((plan) => (
            <motion.div
              key={plan.name}
              variants={cardVariants}
              whileHover={{ y: -10 }}
              className="relative"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-8 flex flex-col">
                {plan.popular && (
                  <div className={`absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${plan.gradient} text-white text-sm font-semibold py-1 px-4 rounded-full`}>
                    Most Popular
                  </div>
                )}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  <div className={`text-4xl font-bold mb-2 ${plan.gradient} bg-clip-text text-transparent`}>
                    {plan.price}
                    {plan.price !== "Custom" && <span className="text-lg text-gray-600">/mo</span>}
                  </div>
                  <p className="text-gray-600">{plan.description}</p>
                </div>
                <ul className="space-y-4 mb-8 flex-grow">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center">
                      <Check className="w-5 h-5 text-green-500 mr-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <a
                    href="/pricing"
                    className={`block w-full py-3 px-6 rounded-lg text-center text-white ${plan.gradient} font-semibold hover:opacity-90 transition-opacity`}
                  >
                    {plan.price === "Custom" ? "Contact Sales" : "Get Started"}
                  </a>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
