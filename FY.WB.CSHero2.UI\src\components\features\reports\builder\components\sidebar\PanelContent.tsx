"use client";

import { PanelContentProps } from "../../types";
import { PanelHeader } from "./PanelHeader";
import { ReportInfoPanel } from "./ReportInfoPanel";
import { SectionsPanel } from "./SectionsPanel";
import { CustomizeStylesPanel } from "./CustomizeStylesPanel";
import { StyleTemplatePanel } from "./StyleTemplatePanel";
import { ImportReportPanel } from "./ImportReportPanel";

export function PanelContent({ 
  title, 
  onClose, 
  onSectionClick,
  reportInfo,
  clients,
  onUpdateReportInfo,
  templateName,
  sections,
  onAddSection,
  onUpdateSection,
  templates,
  selectedTemplate,
  onTemplateSelect,
  styles,
  onUpdateStyle
}: PanelContentProps) {
  return (
    <div className="h-full bg-white p-6 rounded-l-lg shadow-lg flex flex-col">
      <PanelHeader title={title} onClose={onClose} />

      <div className="space-y-6 flex-1 overflow-y-auto">
        {title === 'Report Info' && reportInfo && onUpdateReportInfo && (
          <ReportInfoPanel 
            reportInfo={reportInfo} 
            clients={clients} 
            onUpdateReportInfo={onUpdateReportInfo} 
            templateName={templateName} 
          />
        )}

        {title === 'Sections' && sections && onAddSection && (
          <SectionsPanel 
            sections={sections} 
            onAddSection={onAddSection} 
            onSectionClick={onSectionClick}
            onUpdateSection={onUpdateSection}
          />
        )}

        {title === 'Template' && onUpdateStyle && styles && (
          <StyleTemplatePanel 
            selectedTemplate={styles.styleTemplateId || ''} 
            onTemplateSelect={(templateId) => onUpdateStyle('styleTemplateId', templateId)} 
          />
        )}

        {title === 'Style' && styles && onUpdateStyle && (
          <CustomizeStylesPanel 
            styles={styles} 
            onUpdateStyle={onUpdateStyle} 
          />
        )}

        {title === 'Import Report' && (
          <ImportReportPanel 
            onImportSuccess={(templateId) => {
              console.log("Template imported:", templateId);
            }} 
          />
        )}
      </div>
    </div>
  );
}
