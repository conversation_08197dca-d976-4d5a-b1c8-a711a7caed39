using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using FY.WB.CSHero2.Application.Forms.Dtos;
using Xunit;
using FluentAssertions;

namespace FY.WB.CSHero2.Test.ControllerTests
{
    public class FormsControllerTests : TestBase, IDisposable
    {
        private string? _adminToken;
        private string? _tenant1Token;
        private HttpClient? _adminClient;
        private HttpClient? _tenant1Client;

        public FormsControllerTests()
        {
            // Initialize tokens and clients
            InitializeAsync().Wait();
        }

        private async Task InitializeAsync()
        {
            // Use the helper methods from TestBase
            _adminToken = await GetAdminToken();
            _tenant1Token = await GetTenant1Token();

            // Create authenticated clients
            _adminClient = CreateAuthenticatedClient(_adminToken);
            _tenant1Client = CreateAuthenticatedClient(_tenant1Token);
        }

        [Fact]
        public async Task GetForms_WithAdminToken_ReturnsAllForms()
        {
            // Act
            var response = await _adminClient!.GetAsync("/api/forms");
            
            // Assert
            response.IsSuccessStatusCode.Should().BeTrue($"Request failed with status code {response.StatusCode}");
            
            var content = await response.Content.ReadAsStringAsync();
            var forms = JsonSerializer.Deserialize<List<FormDto>>(content, _jsonOptions);
            
            forms.Should().NotBeNull();
            
            // Output the forms for debugging
            Console.WriteLine($"Found {forms!.Count} forms:");
            foreach (var formItem in forms)
            {
                Console.WriteLine($"Form ID: {formItem.Id}, Title: {formItem.Title}, TenantId: {formItem.TenantId}");
            }
            
            // If we don't have any forms, we'll need to skip further tests
            if (forms.Count == 0)
            {
                return; // Skip test - no forms found
            }
        }
        
        [Fact]
        public async Task GetForms_WithTenant1Token_ReturnsOnlyTenant1Forms()
        {
            // Act
            var response = await _tenant1Client!.GetAsync("/api/forms");
            
            // Assert
            response.IsSuccessStatusCode.Should().BeTrue($"Request failed with status code {response.StatusCode}");
            
            var content = await response.Content.ReadAsStringAsync();
            var forms = JsonSerializer.Deserialize<List<FormDto>>(content, _jsonOptions);
            
            forms.Should().NotBeNull();
            
            // Output the forms for debugging
            Console.WriteLine($"Found {forms!.Count} forms for tenant1:");
            foreach (var formItem in forms)
            {
                Console.WriteLine($"Form ID: {formItem.Id}, Title: {formItem.Title}, TenantId: {formItem.TenantId}");
            }
            
            // Verify all returned forms belong to tenant 1
            if (forms.Count > 0)
            {
                foreach (var formItem in forms)
                {
                    formItem.TenantId.Should().Be(_tenant1Id, 
                        $"Form with ID {formItem.Id} belongs to tenant {formItem.TenantId} but should belong to {_tenant1Id}");
                }
            }
            else
            {
                Console.WriteLine("No forms found for tenant1. This is expected if no forms exist for this tenant.");
            }
        }
        
        [Fact]
        public async Task GetFormById_WithAdminToken_ReturnsFormIfExists()
        {
            // We'll first check if we can find our form in the list of all forms
            var listResponse = await _adminClient!.GetAsync("/api/forms");
            if (!listResponse.IsSuccessStatusCode)
            {
                return; // Skip test - failed to get forms list
            }
            
            var content = await listResponse.Content.ReadAsStringAsync();
            var forms = JsonSerializer.Deserialize<List<FormDto>>(content, _jsonOptions);
            
            // Log the available forms for debugging
            Console.WriteLine($"Available forms for admin: {forms?.Count ?? 0}");
            if (forms != null)
            {
                foreach (var formItem in forms)
                {
                    Console.WriteLine($"Form ID: {formItem.Id}, Title: {formItem.Title}, TenantId: {formItem.TenantId}");
                }
            }
            
            // Find a form ID to test with
            if (forms == null || forms.Count == 0)
            {
                return; // Skip test - no forms found
            }
            
            // Get the first form
            var formIdToTest = forms[0].Id;
            Console.WriteLine($"Testing with existing form ID: {formIdToTest}");
            
            // Act - Test getting the form by ID
            var response = await _adminClient.GetAsync($"/api/forms/{formIdToTest}");
            
            // Assert
            response.IsSuccessStatusCode.Should().BeTrue($"Request failed with status code {response.StatusCode}");
            
            var formContent = await response.Content.ReadAsStringAsync();
            var retrievedForm = JsonSerializer.Deserialize<FormDto>(formContent, _jsonOptions);
            
            retrievedForm.Should().NotBeNull("Form should not be null");
            retrievedForm!.Id.Should().Be(formIdToTest, "Form ID should match the requested ID");
            Console.WriteLine($"Successfully retrieved form with ID: {retrievedForm.Id}, Title: {retrievedForm.Title}");
        }
        
        [Fact]
        public async Task GetFormById_WithTenant1Token_ReturnsFormIfBelongsToTenant1()
        {
            // We'll first check if we can find a form belonging to tenant1 in the list of forms
            var listResponse = await _tenant1Client!.GetAsync("/api/forms");
            if (!listResponse.IsSuccessStatusCode)
            {
                return; // Skip test - failed to get forms list
            }
            
            var content = await listResponse.Content.ReadAsStringAsync();
            var forms = JsonSerializer.Deserialize<List<FormDto>>(content, _jsonOptions);
            
            // Log the available forms for debugging
            Console.WriteLine($"Available forms for tenant1: {forms?.Count ?? 0}");
            if (forms != null)
            {
                foreach (var formItem in forms)
                {
                    Console.WriteLine($"Form ID: {formItem.Id}, Title: {formItem.Title}, TenantId: {formItem.TenantId}");
                }
            }
            
            // Find a form ID to test with
            if (forms == null || forms.Count == 0)
            {
                return; // Skip test - no forms found for tenant1
            }
            
            // Get the first form
            var formIdToTest = forms[0].Id;
            Console.WriteLine($"Testing with existing form ID: {formIdToTest}");
            
            // Act - Test getting the form by ID
            var response = await _tenant1Client.GetAsync($"/api/forms/{formIdToTest}");
            
            // Assert
            response.IsSuccessStatusCode.Should().BeTrue($"Request failed with status code {response.StatusCode}");
            
            var formContent = await response.Content.ReadAsStringAsync();
            var retrievedForm = JsonSerializer.Deserialize<FormDto>(formContent, _jsonOptions);
            
            retrievedForm.Should().NotBeNull("Form should not be null");
            retrievedForm!.Id.Should().Be(formIdToTest, "Form ID should match the requested ID");
            retrievedForm.TenantId.Should().Be(_tenant1Id, "Form should belong to tenant1");
            Console.WriteLine($"Successfully retrieved form with ID: {retrievedForm.Id}, Title: {retrievedForm.Title}");
        }
        
        [Fact]
        public async Task GetFormById_WithTenant1Token_ReturnsNotFoundForOtherTenantForm()
        {
            // We need to find a form that belongs to a different tenant
            // First, get all forms as admin
            var adminListResponse = await _adminClient!.GetAsync("/api/forms");
            if (!adminListResponse.IsSuccessStatusCode)
            {
                return; // Skip test - failed to get forms list as admin
            }
            
            var content = await adminListResponse.Content.ReadAsStringAsync();
            var forms = JsonSerializer.Deserialize<List<FormDto>>(content, _jsonOptions);
            
            if (forms == null || forms.Count == 0)
            {
                return; // Skip test - no forms found by admin
            }
            
            // Find a form that doesn't belong to tenant1
            FormDto? otherTenantForm = null;
            foreach (var formItem in forms)
            {
                Console.WriteLine($"Admin sees form: ID={formItem.Id}, TenantId={formItem.TenantId}");
                if (formItem.TenantId != _tenant1Id)
                {
                    otherTenantForm = formItem;
                    break;
                }
            }
            
            if (otherTenantForm == null)
            {
                return; // Skip test - no forms from different tenant found
            }
            
            Console.WriteLine($"Found form from different tenant: ID={otherTenantForm.Id}, TenantId={otherTenantForm.TenantId}");
            
            // Act - Try to get this form as tenant1
            var response = await _tenant1Client!.GetAsync($"/api/forms/{otherTenantForm.Id}");
            
            // Assert - Should return 404 Not Found
            response.StatusCode.Should().Be(HttpStatusCode.NotFound,
                "Should return 404 Not Found when trying to access a form from another tenant");
        }
        
        public void Dispose()
        {
            _adminClient?.Dispose();
            _tenant1Client?.Dispose();
        }
    }
}
