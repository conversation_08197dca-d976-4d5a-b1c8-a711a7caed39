using System;
using System.Collections.Generic;
using System.Text.Json;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents a version of a report with component data and JSON data
    /// Enables version control and rollback capabilities
    /// </summary>
    public class ReportVersion : AuditedEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the parent report
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Navigation property to the parent report
        /// </summary>
        public virtual Report Report { get; set; } = null!;

        /// <summary>
        /// Sequential version number (1, 2, 3, etc.)
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// Description of what changed in this version
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// When this version was created (use CreationTime from base class)
        /// </summary>
        public DateTime CreatedAt
        {
            get => CreationTime;
            set => CreationTime = value;
        }

        /// <summary>
        /// JSON serialized component data (generated React components)
        /// </summary>
        public string ComponentDataJson { get; set; } = string.Empty;

        /// <summary>
        /// JSON serialized report data (field values, chart data, etc.)
        /// </summary>
        public string JsonData { get; set; } = string.Empty;

        /// <summary>
        /// Whether this is the current active version
        /// </summary>
        public bool IsCurrent { get; set; }

        /// <summary>
        /// Size of the component data in bytes (for storage tracking)
        /// </summary>
        public long ComponentDataSize { get; set; }

        /// <summary>
        /// Size of the JSON data in bytes (for storage tracking)
        /// </summary>
        public long JsonDataSize { get; set; }

        /// <summary>
        /// Reference to CosmosDB style document
        /// </summary>
        public string? StyleDocumentId { get; set; }

        /// <summary>
        /// Path to Azure Blob Storage for large JSON data
        /// </summary>
        public string? DataBlobPath { get; set; }

        /// <summary>
        /// Indicates if data is stored in blob vs inline JSON
        /// </summary>
        public bool IsDataInBlob { get; set; }

        /// <summary>
        /// Navigation property to component definitions
        /// </summary>
        public virtual ICollection<ComponentDefinition> ComponentDefinitions { get; set; } = new List<ComponentDefinition>();

        /// <summary>
        /// Gets the report data as a dictionary
        /// </summary>
        public Dictionary<string, object> GetReportData()
        {
            if (string.IsNullOrEmpty(JsonData))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(JsonData) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the report data from a dictionary
        /// </summary>
        public void SetReportData(Dictionary<string, object> data)
        {
            JsonData = JsonSerializer.Serialize(data ?? new Dictionary<string, object>());
            JsonDataSize = System.Text.Encoding.UTF8.GetByteCount(JsonData);
        }

        /// <summary>
        /// Sets the component data and calculates size
        /// </summary>
        public void SetComponentData(object componentData)
        {
            ComponentDataJson = JsonSerializer.Serialize(componentData ?? new object());
            ComponentDataSize = System.Text.Encoding.UTF8.GetByteCount(ComponentDataJson);
        }

        /// <summary>
        /// Gets the component data as the specified type
        /// </summary>
        public T? GetComponentData<T>() where T : class
        {
            if (string.IsNullOrEmpty(ComponentDataJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<T>(ComponentDataJson);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Marks this version as current and unmarks others
        /// </summary>
        public void SetAsCurrent()
        {
            IsCurrent = true;
        }

        /// <summary>
        /// Marks this version as not current
        /// </summary>
        public void SetAsNotCurrent()
        {
            IsCurrent = false;
        }

        /// <summary>
        /// Gets the total storage size for this version
        /// </summary>
        public long GetTotalSize()
        {
            return ComponentDataSize + JsonDataSize;
        }

        /// <summary>
        /// Checks if this version can be deleted (not current and not the first version)
        /// </summary>
        public bool CanBeDeleted()
        {
            return !IsCurrent && VersionNumber > 1;
        }

        /// <summary>
        /// Creates a copy of this version with a new version number
        /// </summary>
        public ReportVersion CreateCopy(int newVersionNumber, string description, Guid createdBy)
        {
            return new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = ReportId,
                VersionNumber = newVersionNumber,
                Description = description,
                CreationTime = DateTime.UtcNow,
                CreatorId = createdBy,
                ComponentDataJson = ComponentDataJson,
                JsonData = JsonData,
                IsCurrent = false,
                ComponentDataSize = ComponentDataSize,
                JsonDataSize = JsonDataSize
            };
        }
    }
}
