import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { cookies } from 'next/headers'; // Import cookies

const ASPNET_API_URL = process.env.ASPNET_API_URL || 'http://localhost:5056';

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  const { reportId } = params;

  if (!reportId) {
    return NextResponse.json({ message: 'Report ID is required' }, { status: 400 });
  }

  const cookieStore = cookies();
  const authTokenCookie = cookieStore.get('authToken');

  if (!authTokenCookie) {
    return NextResponse.json({ message: 'Authentication token not found' }, { status: 401 });
  }

  const headers = new Headers();
  headers.append('Authorization', `Bearer ${authTokenCookie.value}`);
  headers.append('Content-Type', 'application/json');

  try {
    console.log(`BFF: Fetching report ${reportId} from backend: ${ASPNET_API_URL}/api/Reports/${reportId}`);
    const apiResponse = await fetch(`${ASPNET_API_URL}/api/Reports/${reportId}`, {
      method: 'GET',
      headers: headers,
    });

    const responseBody = await apiResponse.json();

    if (!apiResponse.ok) {
      console.error(`BFF: Error from backend while fetching report ${reportId}: ${apiResponse.status} ${JSON.stringify(responseBody)}`);
      return NextResponse.json(
        { message: responseBody.message || `Failed to fetch report from backend: ${apiResponse.status}` },
        { status: apiResponse.status }
      );
    }

    console.log(`BFF: Successfully fetched report ${reportId}.`);
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error(`BFF: Internal error while fetching report ${reportId}:`, error);
    return NextResponse.json({ message: 'Internal server error in BFF' }, { status: 500 });
  }
}
