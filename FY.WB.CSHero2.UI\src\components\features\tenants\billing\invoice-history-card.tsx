'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  CreditCard, 
  Eye, 
  Download, 
  Mail, 
  ArrowUpDown, 
  Search, 
  MoreHorizontal,
  FileEdit,
  Trash2,
  Receipt
} from 'lucide-react';
import { 
  generateInvoice, 
  generateInvoiceNumber, 
  downloadInvoice,
  InvoiceLineItem
} from '@/lib/generate-invoice';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { ProcessPaymentDialog } from './process-payment-dialog';
import { IssueRefundDialog } from './issue-refund-dialog';
import { useToast } from '@/components/providers/toast-provider';
import { Invoice } from '@/types/invoice';
import { createInvoice, deleteInvoice } from '@/lib/api-invoices';
import { getTenantById, getCompanyProfile, getSubscriptionByTypeAndBillingCycle } from '@/lib/api';

interface InvoiceHistoryCardProps {
  billingHistory: Invoice[];
}

export function InvoiceHistoryCard({ billingHistory }: InvoiceHistoryCardProps) {
  const { toast } = useToast();
  const [filteredHistory, setFilteredHistory] = useState<Invoice[]>(billingHistory);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<keyof Invoice>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [page, setPage] = useState(1);
  const [limit] = useState(5);
  const [totalPages, setTotalPages] = useState(1);

  // Update filtered history when props or search/sort changes
  useEffect(() => {
    let result = [...billingHistory];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item => 
        item.orderNumber.toLowerCase().includes(query) ||
        item.type.toLowerCase().includes(query) ||
        item.plans.toLowerCase().includes(query) ||
        item.amount.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    result.sort((a, b) => {
      const aValue = a[sortBy] || '';
      const bValue = b[sortBy] || '';
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
    
    // Calculate total pages
    setTotalPages(Math.max(1, Math.ceil(result.length / limit)));
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedResult = result.slice(startIndex, startIndex + limit);
    
    setFilteredHistory(paginatedResult);
  }, [billingHistory, searchQuery, sortBy, sortOrder, page, limit]);

  const handleSort = (column: keyof Invoice) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  const handleProcessPayment = async (amount: string, method: string, note: string) => {
    try {
      if (billingHistory.length === 0) return;
      
      // Create a new payment invoice
      const tenantId = billingHistory[0].tenantId;
      await createInvoice({
        tenantId,
        type: 'Payment',
        orderNumber: `PMT-${Date.now().toString().slice(-8)}`,
        plans: note || 'Manual payment',
        amount,
        status: 'paid'
      });
      
      toast({
        title: 'Success',
        description: `Payment of ${amount} has been processed successfully.`,
      });
      
      // In a real app, we would refresh the billing history here
    } catch (error) {
      console.error('Error processing payment:', error);
      toast({
        title: 'Error',
        description: 'Failed to process payment',
        variant: 'destructive',
      });
    }
  };

  const handleIssueRefund = async (amount: string, type: string, reason: string, note: string) => {
    try {
      if (billingHistory.length === 0) return;
      
      // Create a new refund/credit invoice
      const tenantId = billingHistory[0].tenantId;
      await createInvoice({
        tenantId,
        type: type === 'refund' ? 'Refund' : 'Credit',
        orderNumber: `${type === 'refund' ? 'REF' : 'CRD'}-${Date.now().toString().slice(-8)}`,
        plans: reason + (note ? `: ${note}` : ''),
        amount,
        status: 'paid'
      });
      
      toast({
        title: 'Success',
        description: `${type === 'refund' ? 'Refund' : 'Credit'} of ${amount} has been issued successfully.`,
      });
      
      // In a real app, we would refresh the billing history here
    } catch (error) {
      console.error('Error issuing refund:', error);
      toast({
        title: 'Error',
        description: 'Failed to issue refund',
        variant: 'destructive',
      });
    }
  };

  const handleGenerateInvoice = async (invoice: Invoice) => {
    try {
      // Get the tenant data
      const tenant = await getTenantById(invoice.tenantId);
      
      // Get the company profile data
      const companyProfile = await getCompanyProfile();
      
      if (!tenant || !companyProfile) {
        throw new Error('Failed to fetch tenant or company data');
      }
      
      // Generate invoice data
      const today = new Date();
      
      // Create subscription period dates
      const startDate = new Date(invoice.date);
      const endDate = new Date(startDate);
      
      // Set end date and due date based on billing cycle
      const billingCycle = tenant.billingCycle || 'annual';
      let dueDate = new Date(today);
      
      if (billingCycle === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1); // 1 month subscription
        dueDate.setMonth(dueDate.getMonth() + 1); // Due in 1 month
      } else if (billingCycle === 'quarterly') {
        endDate.setMonth(endDate.getMonth() + 3); // 3 month subscription
        dueDate.setMonth(dueDate.getMonth() + 3); // Due in 3 months
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1); // Default to annual
        dueDate.setFullYear(dueDate.getFullYear() + 1); // Due in 1 year
      }

      // Create line items based on invoice data
      const lineItems: InvoiceLineItem[] = [];
      
      // Try to get subscription data if available
      try {
        const subscriptionType = tenant.subscription || 'basic';
        
        // Use the subscription data from the API
        const subscriptionResponse = await getSubscriptionByTypeAndBillingCycle(
          subscriptionType as 'basic' | 'professional' | 'enterprise',
          billingCycle as 'monthly' | 'quarterly' | 'annual'
        );
        
        if (subscriptionResponse) {
          // Add subscription line item with data from the API
          lineItems.push({
            description: subscriptionResponse.name,
            quantity: 1,
            unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          });
        } else {
          // Fallback to invoice data if subscription not found
          lineItems.push({
            description: invoice.plans,
            quantity: 1,
            unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          });
        }
      } catch (error) {
        // Fallback to invoice data if there's an error
        console.error('Error fetching subscription data:', error);
        lineItems.push({
          description: invoice.plans,
          quantity: 1,
          unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        });
      }
      
      // Generate invoice number (use the existing order number)
      const invoiceNumber = invoice.orderNumber;

      // Generate the invoice HTML
      const { html } = generateInvoice({
        tenant,
        companyProfile,
        invoiceNumber,
        issueDate: invoice.date,
        dueDate: dueDate.toISOString(),
        lineItems,
        discount: undefined
      });

      // Download the invoice as PDF
      downloadInvoice(html, `invoice-${invoiceNumber}.pdf`);

      toast({
        title: 'Success',
        description: 'PDF Invoice generated and downloaded successfully',
      });
    } catch (error) {
      console.error('Error generating invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF invoice',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteInvoice = async (id: string) => {
    try {
      await deleteInvoice(id);
      toast({
        title: 'Success',
        description: 'Invoice deleted successfully',
      });
      // In a real app, we would refresh the billing history here
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete invoice',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-[rgb(var(--primary))]">Invoice and Billing History</h2>
        <div className="flex gap-2">
          <ProcessPaymentDialog onProcessPayment={handleProcessPayment} />
          <IssueRefundDialog onIssueRefund={handleIssueRefund} />
        </div>
      </div>
      
      <div className="flex gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
          <Input
            placeholder="Search invoices..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      <div className="rounded-lg border bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-muted/5 transition-colors">
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSort('date');
                  }}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Date
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSort('type');
                  }}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Type
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSort('status');
                  }}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Status
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">Order Number</div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">Description</div>
              </TableHead>
              <TableHead className="text-right">
                <div className="flex items-center gap-1 ml-auto">Amount</div>
              </TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredHistory.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <p className="text-muted-foreground">No invoices found</p>
                    <p className="text-sm text-muted-foreground">Process a payment to create your first invoice</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredHistory.map((item) => (
                <TableRow 
                  key={item.id}
                  className="hover:bg-primary/5 hover:shadow-sm cursor-pointer transition-all duration-200"
                  onClick={() => handleGenerateInvoice(item)}
                >
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{item.date}</div>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.type === 'Invoice' 
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20' 
                        : item.type === 'Payment'
                        ? 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20'
                        : item.type === 'Refund'
                        ? 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20'
                        : 'bg-gradient-to-r from-purple-50 to-purple-100/50 text-purple-700 ring-1 ring-purple-600/20'
                    }`}>
                      {item.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.status === 'paid' 
                        ? 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20' 
                        : item.status === 'pending'
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20'
                        : item.status === 'overdue'
                        ? 'bg-gradient-to-r from-red-50 to-red-100/50 text-red-700 ring-1 ring-red-600/20'
                        : 'bg-gradient-to-r from-gray-50 to-gray-100/50 text-gray-700 ring-1 ring-gray-600/20'
                    }`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{item.orderNumber}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-muted-foreground">{item.plans}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">{item.amount}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          className="h-8 w-8 p-0 hover:bg-primary/5 hover:text-primary transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem 
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGenerateInvoice(item);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGenerateInvoice(item);
                          }}
                        >
                          <Download className="h-4 w-4" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 text-red-600 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteInvoice(item.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-4 py-4 border-t bg-muted/5">
          <p className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setPage(p => Math.max(1, p - 1));
              }}
              disabled={page === 1}
              className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setPage(p => Math.min(totalPages, p + 1));
              }}
              disabled={page === totalPages}
              className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
