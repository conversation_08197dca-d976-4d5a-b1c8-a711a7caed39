"use client";

import { motion } from "framer-motion";
import {
  MessageSquare,
  Mail,
  Database,
  LineChart,
  Webhook,
  RefreshCw,
} from "lucide-react";

const integrations = [
  {
    name: "Help Desk Systems",
    description: "Connect with popular help desk platforms to import ticket data and metrics.",
    icon: MessageSquare,
    examples: ["Zendesk", "Freshdesk", "ServiceNow"],
    gradient: "gradient-primary-secondary",
  },
  {
    name: "Email Platforms",
    description: "Import customer communication data from email service providers.",
    icon: Mail,
    examples: ["Gmail", "Outlook", "Custom SMTP"],
    gradient: "gradient-secondary-tertiary",
  },
  {
    name: "CRM Systems",
    description: "Sync customer data and interactions from your CRM platform.",
    icon: Database,
    examples: ["Salesforce", "HubSpot", "Dynamics"],
    gradient: "gradient-primary-tertiary",
  },
  {
    name: "Analytics Tools",
    description: "Connect with analytics platforms for deeper data insights.",
    icon: LineChart,
    examples: ["Google Analytics", "Mixpanel", "Custom"],
    gradient: "gradient-primary-secondary",
  },
  {
    name: "Webhooks",
    description: "Set up custom webhooks to automate report generation and delivery.",
    icon: Webhook,
    examples: ["REST API", "GraphQL", "Custom"],
    gradient: "gradient-secondary-tertiary",
  },
  {
    name: "Real-time Sync",
    description: "Keep your reports up-to-date with real-time data synchronization.",
    icon: RefreshCw,
    examples: ["WebSocket", "Server Events", "Polling"],
    gradient: "gradient-primary-tertiary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function Integrations() {
  return (
    <section className="py-24 bg-gray-50 rounded-round md:px-16">
      <div className="container mx-auto px-2 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">
            Seamless Integrations
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Connect with your existing tools and automate your reporting workflow
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {integrations.map((integration) => (
            <motion.div
              key={integration.name}
              variants={cardVariants}
              whileHover={{ y: -5 }}
              className="relative"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-8">
                <div className={`w-12 h-12 rounded-lg ${integration.gradient} flex items-center justify-center mb-6`}>
                  <integration.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">
                  {integration.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {integration.description}
                </p>
                <div className="space-y-2">
                  <p className="text-sm font-semibold text-gray-900">Popular Integrations:</p>
                  <div className="flex flex-wrap gap-2">
                    {integration.examples.map((example) => (
                      <span
                        key={example}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700"
                      >
                        {example}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* API Documentation CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <a
            href="/docs/api"
            className="inline-flex items-center px-6 py-3 rounded-lg bg-[rgb(var(--primary))] text-white font-semibold hover:opacity-90 transition-opacity"
          >
            View API Documentation
            <svg
              className="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              />
            </svg>
          </a>
        </motion.div>
      </div>
    </section>
  );
}
