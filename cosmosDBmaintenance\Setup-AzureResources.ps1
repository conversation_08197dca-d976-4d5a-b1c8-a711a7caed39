# Azure Resource Setup Script for Report Rendering Engine
# This script creates Cosmos DB and Storage Account resources

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$Location,
    
    [Parameter(Mandatory=$true)]
    [string]$CosmosAccountName,
    
    [Parameter(Mandatory=$true)]
    [string]$StorageAccountName,
    
    [Parameter(Mandatory=$false)]
    [string]$SubscriptionId
)

# Import required modules
Import-Module AzureRM.Profile
Import-Module AzureRM.Resources
Import-Module AzureRM.Storage

Write-Host "=== Azure Resources Setup for Report Rendering Engine ===" -ForegroundColor Green
Write-Host ""

try {
    # Login to Azure
    Write-Host "Logging into Azure..." -ForegroundColor Yellow
    $context = Login-AzureRmAccount
    
    if ($SubscriptionId) {
        Write-Host "Setting subscription to: $SubscriptionId" -ForegroundColor Yellow
        Select-AzureRmSubscription -SubscriptionId $SubscriptionId
    }
    
    # Create Resource Group
    Write-Host "Creating Resource Group: $ResourceGroupName" -ForegroundColor Yellow
    $rg = New-AzureRmResourceGroup -Name $ResourceGroupName -Location $Location -Force
    Write-Host "✅ Resource Group created: $($rg.ResourceGroupName)" -ForegroundColor Green
    
    # Create Storage Account
    Write-Host "Creating Storage Account: $StorageAccountName" -ForegroundColor Yellow
    $storageAccount = New-AzureRmStorageAccount `
        -ResourceGroupName $ResourceGroupName `
        -Name $StorageAccountName `
        -Location $Location `
        -SkuName "Standard_LRS" `
        -Kind "StorageV2"
    
    Write-Host "✅ Storage Account created: $($storageAccount.StorageAccountName)" -ForegroundColor Green
    
    # Get Storage Account Key
    $storageKey = (Get-AzureRmStorageAccountKey -ResourceGroupName $ResourceGroupName -Name $StorageAccountName)[0].Value
    $storageConnectionString = "DefaultEndpointsProtocol=https;AccountName=$StorageAccountName;AccountKey=$storageKey;EndpointSuffix=core.windows.net"
    
    # Create Cosmos DB Account using ARM template
    Write-Host "Creating Cosmos DB Account: $CosmosAccountName" -ForegroundColor Yellow
    
    $cosmosTemplate = @{
        '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#"
        contentVersion = "*******"
        parameters = @{
            accountName = @{
                type = "string"
                defaultValue = $CosmosAccountName
            }
            location = @{
                type = "string"
                defaultValue = $Location
            }
        }
        resources = @(
            @{
                type = "Microsoft.DocumentDB/databaseAccounts"
                apiVersion = "2021-04-15"
                name = "[parameters('accountName')]"
                location = "[parameters('location')]"
                properties = @{
                    databaseAccountOfferType = "Standard"
                    consistencyPolicy = @{
                        defaultConsistencyLevel = "Session"
                    }
                    locations = @(
                        @{
                            locationName = "[parameters('location')]"
                            failoverPriority = 0
                        }
                    )
                    capabilities = @(
                        @{
                            name = "EnableServerless"
                        }
                    )
                }
            }
        )
        outputs = @{
            cosmosEndpoint = @{
                type = "string"
                value = "[reference(parameters('accountName')).documentEndpoint]"
            }
        }
    }
    
    # Save template to temp file
    $templatePath = "$env:TEMP\cosmos-template.json"
    $cosmosTemplate | ConvertTo-Json -Depth 10 | Out-File -FilePath $templatePath -Encoding UTF8
    
    # Deploy Cosmos DB
    $deployment = New-AzureRmResourceGroupDeployment `
        -ResourceGroupName $ResourceGroupName `
        -TemplateFile $templatePath `
        -accountName $CosmosAccountName `
        -location $Location
    
    Write-Host "✅ Cosmos DB Account created: $CosmosAccountName" -ForegroundColor Green
    
    # Get Cosmos DB connection details
    $cosmosEndpoint = $deployment.Outputs.cosmosEndpoint.Value
    $cosmosKeys = Invoke-AzureRmResourceAction `
        -Action listKeys `
        -ResourceType "Microsoft.DocumentDb/databaseAccounts" `
        -ApiVersion "2021-04-15" `
        -ResourceGroupName $ResourceGroupName `
        -Name $CosmosAccountName `
        -Force
    
    $cosmosPrimaryKey = $cosmosKeys.primaryMasterKey
    
    # Clean up temp file
    Remove-Item $templatePath -Force
    
    Write-Host ""
    Write-Host "=== SETUP COMPLETE ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Connection Details:" -ForegroundColor Cyan
    Write-Host "Cosmos DB Endpoint: $cosmosEndpoint" -ForegroundColor White
    Write-Host "Cosmos DB Primary Key: $cosmosPrimaryKey" -ForegroundColor White
    Write-Host "Storage Connection String: $storageConnectionString" -ForegroundColor White
    Write-Host ""
    Write-Host "📝 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Update CosmosDbSetup.cs with the connection details above" -ForegroundColor White
    Write-Host "2. Run the CosmosDbSetup.exe to create databases and containers" -ForegroundColor White
    Write-Host "3. Update your appsettings.json with these connection strings" -ForegroundColor White
    Write-Host ""
    
    # Save connection details to file
    $connectionDetails = @"
# Azure Connection Details for Report Rendering Engine
# Generated on $(Get-Date)

## Cosmos DB
CosmosEndpoint=$cosmosEndpoint
CosmosPrimaryKey=$cosmosPrimaryKey

## Blob Storage
BlobConnectionString=$storageConnectionString

## Resource Details
ResourceGroup=$ResourceGroupName
Location=$Location
CosmosAccount=$CosmosAccountName
StorageAccount=$StorageAccountName
"@
    
    $connectionDetails | Out-File -FilePath "azure-connection-details.txt" -Encoding UTF8
    Write-Host "💾 Connection details saved to: azure-connection-details.txt" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error during setup: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
    exit 1
}
