using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using Xunit;
using FluentAssertions;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// Integration tests for the hybrid storage architecture (SQL + CosmosDB + Blob Storage)
    /// </summary>
    public class ReportStorageIntegrationTests : ReportRenderingIntegrationTestBase
    {
        [Fact]
        public async Task CreateReport_WithHybridStorage_ShouldStoreDataAcrossAllSystems()
        {
            // Arrange
            var reportName = "Integration Test Report";

            // Act - Create a new report
            var report = await CreateTestReportAsync(reportName);
            var version = await CreateTestReportVersionAsync(report.Id);

            // Create and save style document
            var styleDoc = CreateTestStyleDocument(version.Id);
            var styleId = await _styleService.SaveStyleAsync(styleDoc);

            // Create and save report data
            var reportData = CreateTestReportData();
            var blobPath = await _blobService.SaveReportDataAsync(
                _testTenantId, report.Id, version.Id, reportData);

            // Update version with external references
            version.StyleDocumentId = styleId;
            version.DataBlobPath = blobPath;
            version.IsDataInBlob = true;
            await _dbContext.SaveChangesAsync();

            // Assert - Verify data exists in all storage systems
            
            // 1. SQL Server - Report metadata
            var savedReport = await _dbContext.Reports
                .Include(r => r.Versions)
                .FirstOrDefaultAsync(r => r.Id == report.Id);

            savedReport.Should().NotBeNull();
            savedReport!.Name.Should().Be(reportName);
            savedReport.Versions.Should().HaveCount(1);
            savedReport.Versions.First().StyleDocumentId.Should().Be(styleId);
            savedReport.Versions.First().DataBlobPath.Should().Be(blobPath);
            savedReport.Versions.First().IsDataInBlob.Should().BeTrue();

            // 2. CosmosDB - Style document
            var retrievedStyle = await _styleService.GetStyleAsync(styleId, _testTenantId);
            retrievedStyle.Should().NotBeNull();
            retrievedStyle!.ReportVersionId.Should().Be(version.Id);
            retrievedStyle.StyleType.Should().Be("report");
            retrievedStyle.HtmlContent.Should().NotBeNullOrEmpty();

            // 3. Blob Storage - Report data
            var retrievedData = await _blobService.GetReportDataAsync(blobPath);
            retrievedData.Should().NotBeNull();
            retrievedData.Should().ContainKey("header.title");
            retrievedData["header.title"].Should().Be("Test Report Title");
        }

        [Fact]
        public async Task GetReportWithAllData_ShouldRetrieveFromAllStorageSystems()
        {
            // Arrange - Use pre-seeded test data
            var reportId = _testReport.Id;
            var versionId = _testReportVersion.Id;

            // Act - Retrieve data from all storage systems
            
            // 1. Get report metadata from SQL
            var report = await _dbContext.Reports
                .Include(r => r.Versions)
                .ThenInclude(v => v.ComponentDefinitions)
                .FirstOrDefaultAsync(r => r.Id == reportId);

            // 2. Get style from CosmosDB
            var currentVersion = report!.Versions.First(v => v.IsCurrent);
            var style = await _styleService.GetStyleAsync(currentVersion.StyleDocumentId!, _testTenantId);

            // 3. Get data from Blob Storage
            var reportData = await _blobService.GetReportDataAsync(currentVersion.DataBlobPath!);

            // Assert - Verify complete data retrieval
            report.Should().NotBeNull();
            report.Name.Should().Be("Q4 2024 Financial Report");
            report.Versions.Should().HaveCount(1);

            style.Should().NotBeNull();
            style!.StyleType.Should().Be("report");
            style.HtmlContent.Should().Contain("report-template");
            style.CssStyles.Should().Contain("font-family");

            reportData.Should().NotBeNull();
            reportData.Should().ContainKey("financials.revenue");
            reportData["financials.revenue"].Should().Be(1250000);
        }

        [Fact]
        public async Task UpdateReportData_ShouldUpdateBlobStorageOnly()
        {
            // Arrange
            var versionId = _testReportVersion.Id;
            var originalBlobPath = _testReportVersion.DataBlobPath!;

            // Get original data
            var originalData = await _blobService.GetReportDataAsync(originalBlobPath);
            var originalRevenue = originalData["financials.revenue"];

            // Act - Update only the report data
            var updatedData = new Dictionary<string, object>(originalData)
            {
                ["financials.revenue"] = 1500000,
                ["financials.profit"] = 400000,
                ["metadata.lastUpdated"] = DateTime.UtcNow
            };

            var success = await _blobService.UpdateReportDataAsync(originalBlobPath, updatedData);

            // Assert
            success.Should().BeTrue();

            // Verify SQL metadata unchanged
            var version = await _dbContext.ReportVersions.FindAsync(versionId);
            version!.DataBlobPath.Should().Be(originalBlobPath);
            version.IsDataInBlob.Should().BeTrue();

            // Verify blob data updated
            var retrievedData = await _blobService.GetReportDataAsync(originalBlobPath);
            retrievedData["financials.revenue"].Should().Be(1500000);
            retrievedData["financials.profit"].Should().Be(400000);
            retrievedData["financials.revenue"].Should().NotBe(originalRevenue);
        }

        [Fact]
        public async Task CreateReportFromTemplate_ShouldCopyStylesAndStructure()
        {
            // Arrange
            var templateId = _testTemplate.Id;

            // Act - Create new report from template
            var newReport = await CreateTestReportAsync("Report from Template");
            var newVersion = await CreateTestReportVersionAsync(newReport.Id);

            // Copy style from template to new report
            var newStyleId = await _styleService.CreateStyleFromTemplateAsync(
                templateId, newVersion.Id, _testTenantId, _testUserId);

            // Create new report data based on template structure
            var newReportData = new Dictionary<string, object>
            {
                ["header.title"] = "New Report from Template",
                ["header.date"] = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                ["financials.revenue"] = 2000000,
                ["financials.expenses"] = 1500000,
                ["financials.profit"] = 500000
            };

            var newBlobPath = await _blobService.SaveReportDataAsync(
                _testTenantId, newReport.Id, newVersion.Id, newReportData);

            // Update version with references
            newVersion.StyleDocumentId = newStyleId;
            newVersion.DataBlobPath = newBlobPath;
            newVersion.IsDataInBlob = true;
            await _dbContext.SaveChangesAsync();

            // Assert - Verify template-based report creation
            
            // 1. SQL - Report should reference template
            var savedReport = await _dbContext.Reports.FindAsync(newReport.Id);
            savedReport!.TemplateId.Should().Be(templateId);
            savedReport.ReportType.Should().Be("Template-based");

            // 2. CosmosDB - Style should be copied from template
            var newStyle = await _styleService.GetStyleAsync(newStyleId, _testTenantId);
            var templateStyle = await _styleService.GetTemplateStyleAsync(templateId, _testTenantId);

            newStyle.Should().NotBeNull();
            newStyle!.StyleType.Should().Be("report");
            newStyle.ReportVersionId.Should().Be(newVersion.Id);
            newStyle.HtmlContent.Should().Be(templateStyle!.HtmlContent);

            // 3. Blob Storage - Data should follow template structure
            var retrievedData = await _blobService.GetReportDataAsync(newBlobPath);
            retrievedData.Should().ContainKey("header.title");
            retrievedData.Should().ContainKey("financials.revenue");
            retrievedData["header.title"].Should().Be("New Report from Template");
        }

        [Fact]
        public async Task DeleteReport_ShouldCleanupAllStorageSystems()
        {
            // Arrange - Create a test report to delete
            var reportToDelete = await CreateTestReportAsync("Report to Delete");
            var versionToDelete = await CreateTestReportVersionAsync(reportToDelete.Id);

            // Create style and data
            var styleDoc = CreateTestStyleDocument(versionToDelete.Id);
            var styleId = await _styleService.SaveStyleAsync(styleDoc);
            var reportData = CreateTestReportData();
            var blobPath = await _blobService.SaveReportDataAsync(
                _testTenantId, reportToDelete.Id, versionToDelete.Id, reportData);

            // Update version with references
            versionToDelete.StyleDocumentId = styleId;
            versionToDelete.DataBlobPath = blobPath;
            versionToDelete.IsDataInBlob = true;
            await _dbContext.SaveChangesAsync();

            // Verify data exists before deletion
            var existsBefore = await _blobService.ExistsAsync(blobPath);
            existsBefore.Should().BeTrue();

            // Act - Delete the report and associated data
            
            // 1. Delete from Blob Storage
            var blobDeleted = await _blobService.DeleteReportDataAsync(blobPath);
            
            // 2. Delete from CosmosDB
            var styleDeleted = await _styleService.DeleteStyleAsync(styleId, _testTenantId);
            
            // 3. Delete from SQL Server
            _dbContext.Reports.Remove(reportToDelete);
            await _dbContext.SaveChangesAsync();

            // Assert - Verify cleanup across all systems
            blobDeleted.Should().BeTrue();
            styleDeleted.Should().BeTrue();

            var reportExists = await _dbContext.Reports.AnyAsync(r => r.Id == reportToDelete.Id);
            reportExists.Should().BeFalse();

            var blobExists = await _blobService.ExistsAsync(blobPath);
            blobExists.Should().BeFalse();

            var styleExists = await _styleService.GetStyleAsync(styleId, _testTenantId);
            styleExists.Should().BeNull();
        }

        [Fact]
        public async Task GetReportsByTenant_ShouldRespectMultiTenancy()
        {
            // Arrange - Create additional test data for different tenant
            var otherTenantId = Guid.NewGuid();
            await _dataSeeder.SeedAdditionalTestDataAsync(_dbContext, _styleService, _blobService, 2);

            // Act - Get reports for test tenant
            var tenantReports = await _dbContext.Reports
                .Where(r => r.TenantId == _testTenantId)
                .Include(r => r.Versions)
                .ToListAsync();

            var tenantStyles = await _styleService.GetStylesByTenantAsync(_testTenantId);
            var tenantBlobs = await _blobService.ListReportDataAsync(_testTenantId);

            // Assert - Verify tenant isolation
            tenantReports.Should().NotBeEmpty();
            tenantReports.All(r => r.TenantId == _testTenantId).Should().BeTrue();

            tenantStyles.Should().NotBeEmpty();
            tenantStyles.All(s => s.TenantId == _testTenantId).Should().BeTrue();

            tenantBlobs.Should().NotBeEmpty();
            tenantBlobs.All(b => b.StartsWith(_testTenantId.ToString())).Should().BeTrue();
        }

        [Fact]
        public async Task GetReportMetrics_ShouldAggregateFromAllSources()
        {
            // Arrange - Seed additional test data
            await _dataSeeder.SeedAdditionalTestDataAsync(_dbContext, _styleService, _blobService, 5);

            // Act - Calculate metrics across all storage systems
            
            // SQL metrics
            var totalReports = await _dbContext.Reports
                .Where(r => r.TenantId == _testTenantId)
                .CountAsync();

            var totalVersions = await _dbContext.ReportVersions
                .Where(v => v.Report.TenantId == _testTenantId)
                .CountAsync();

            var totalComponents = await _dbContext.ComponentDefinitions
                .Where(c => c.ReportVersion.Report.TenantId == _testTenantId)
                .CountAsync();

            // CosmosDB metrics
            var totalStyles = await _styleService.GetStylesByTenantAsync(_testTenantId);
            var reportStyles = totalStyles.Where(s => s.StyleType == "report").Count();
            var templateStyles = totalStyles.Where(s => s.StyleType == "template").Count();

            // Blob Storage metrics
            var totalBlobs = await _blobService.ListReportDataAsync(_testTenantId);
            var totalBlobSize = 0L;
            foreach (var blobPath in totalBlobs)
            {
                var size = await _blobService.GetSizeAsync(blobPath);
                if (size.HasValue) totalBlobSize += size.Value;
            }

            // Assert - Verify metrics make sense
            totalReports.Should().BeGreaterThan(0);
            totalVersions.Should().BeGreaterOrEqualTo(totalReports);
            totalComponents.Should().BeGreaterOrEqualTo(0);
            
            totalStyles.Should().HaveCountGreaterThan(0);
            reportStyles.Should().BeGreaterThan(0);
            templateStyles.Should().BeGreaterThan(0);
            
            totalBlobs.Should().HaveCountGreaterThan(0);
            totalBlobSize.Should().BeGreaterThan(0);

            Console.WriteLine($"Storage Metrics for Tenant {_testTenantId}:");
            Console.WriteLine($"  SQL: {totalReports} reports, {totalVersions} versions, {totalComponents} components");
            Console.WriteLine($"  CosmosDB: {totalStyles.Count} styles ({reportStyles} report, {templateStyles} template)");
            Console.WriteLine($"  Blob Storage: {totalBlobs.Count} blobs, {totalBlobSize:N0} bytes total");
        }
    }
}