using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Service for managing report data in Azure Blob Storage
    /// </summary>
    public interface IReportDataBlobService
    {
        /// <summary>
        /// Gets report data from blob storage as a dictionary
        /// </summary>
        /// <param name="blobPath">The blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Report data as dictionary</returns>
        Task<Dictionary<string, object>> GetReportDataAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report data from blob storage as a stream
        /// </summary>
        /// <param name="blobPath">The blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Stream containing the report data</returns>
        Task<Stream> GetReportDataStreamAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report data from blob storage as raw JSON string
        /// </summary>
        /// <param name="blobPath">The blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>JSON string containing the report data</returns>
        Task<string> GetReportDataJsonAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves report data to blob storage
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <param name="data">The report data to save</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The blob path where data was saved</returns>
        Task<string> SaveReportDataAsync(Guid tenantId, Guid reportId, Guid versionId, Dictionary<string, object> data, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves report data to blob storage from a stream
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <param name="dataStream">The data stream to save</param>
        /// <param name="contentType">The content type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The blob path where data was saved</returns>
        Task<string> SaveReportDataStreamAsync(Guid tenantId, Guid reportId, Guid versionId, Stream dataStream, string contentType = "application/json", CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves report data to blob storage from JSON string
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <param name="jsonData">The JSON data to save</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The blob path where data was saved</returns>
        Task<string> SaveReportDataJsonAsync(Guid tenantId, Guid reportId, Guid versionId, string jsonData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates existing report data in blob storage
        /// </summary>
        /// <param name="blobPath">The existing blob path</param>
        /// <param name="data">The updated report data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if updated successfully</returns>
        Task<bool> UpdateReportDataAsync(string blobPath, Dictionary<string, object> data, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes report data from blob storage
        /// </summary>
        /// <param name="blobPath">The blob path to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteReportDataAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if report data exists in blob storage
        /// </summary>
        /// <param name="blobPath">The blob path to check</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the blob exists</returns>
        Task<bool> ExistsAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the size of report data in blob storage
        /// </summary>
        /// <param name="blobPath">The blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Size in bytes, or null if blob doesn't exist</returns>
        Task<long?> GetSizeAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets metadata about the blob
        /// </summary>
        /// <param name="blobPath">The blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Blob metadata</returns>
        Task<BlobMetadata?> GetMetadataAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Copies report data from one blob to another
        /// </summary>
        /// <param name="sourceBlobPath">The source blob path</param>
        /// <param name="tenantId">The target tenant ID</param>
        /// <param name="reportId">The target report ID</param>
        /// <param name="versionId">The target version ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The new blob path</returns>
        Task<string> CopyReportDataAsync(string sourceBlobPath, Guid tenantId, Guid reportId, Guid versionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lists all report data blobs for a tenant
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of blob paths</returns>
        Task<List<string>> ListReportDataAsync(Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lists all report data blobs for a specific report
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of blob paths</returns>
        Task<List<string>> ListReportVersionDataAsync(Guid tenantId, Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves component assets (images, charts, etc.) to blob storage
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <param name="assetName">The asset name</param>
        /// <param name="assetStream">The asset data stream</param>
        /// <param name="contentType">The content type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The blob path where asset was saved</returns>
        Task<string> SaveAssetAsync(Guid tenantId, Guid reportId, Guid versionId, string assetName, Stream assetStream, string contentType, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a component asset from blob storage
        /// </summary>
        /// <param name="blobPath">The asset blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Stream containing the asset data</returns>
        Task<Stream> GetAssetAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a component asset from blob storage
        /// </summary>
        /// <param name="blobPath">The asset blob path</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteAssetAsync(string blobPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates a blob path for report data
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <returns>The generated blob path</returns>
        string GenerateReportDataPath(Guid tenantId, Guid reportId, Guid versionId);

        /// <summary>
        /// Generates a blob path for component assets
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="reportId">The report ID</param>
        /// <param name="versionId">The version ID</param>
        /// <param name="assetName">The asset name</param>
        /// <returns>The generated blob path</returns>
        string GenerateAssetPath(Guid tenantId, Guid reportId, Guid versionId, string assetName);
    }

    /// <summary>
    /// Metadata about a blob in storage
    /// </summary>
    public class BlobMetadata
    {
        /// <summary>
        /// The blob path
        /// </summary>
        public string BlobPath { get; set; } = string.Empty;

        /// <summary>
        /// Size in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Content type
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// When the blob was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the blob was last modified
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag for concurrency control
        /// </summary>
        public string ETag { get; set; } = string.Empty;

        /// <summary>
        /// Custom metadata properties
        /// </summary>
        public Dictionary<string, string> Properties { get; set; } = new();
    }
}