using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.TenantProfiles.Commands
{
    public class CreateTenantProfileCommandHandler : IRequestHandler<CreateTenantProfileCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateTenantProfileCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateTenantProfileCommand request, CancellationToken cancellationToken)
        {
            var entity = new TenantProfile
            {
                Name = request.Dto.Name,
                Email = request.Dto.Email,
                Status = request.Dto.Status,
                Phone = request.Dto.Phone,
                Company = request.Dto.Company,
                Subscription = request.Dto.Subscription,
                BillingCycle = request.Dto.BillingCycle,
                NextBillingDate = request.Dto.NextBillingDate,
                SubscriptionStatus = request.Dto.SubscriptionStatus,
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };

            if (request.Dto.PaymentMethod != null)
            {
                entity.SetPaymentMethod(request.Dto.PaymentMethod);
            }

            if (request.Dto.BillingAddress != null)
            {
                entity.SetBillingAddress(request.Dto.BillingAddress);
            }
            
            // Ensure TenantId is set if not handled by interceptor explicitly for creation
            if (_currentUserService.TenantId.HasValue)
            {
                entity.TenantId = _currentUserService.TenantId.Value;
            }
            else
            {
                // Handle case where TenantId is not available, perhaps throw an exception or assign a default
                // For now, we assume TenantId should be available for a logged-in user creating their profile.
                // Or, if this is an admin creating a profile for a new tenant, logic might differ.
                // Based on current plan, user creates their own profile.
                throw new InvalidOperationException("TenantId is required to create a TenantProfile.");
            }


            _context.TenantProfiles.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
