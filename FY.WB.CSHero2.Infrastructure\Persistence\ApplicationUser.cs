using Microsoft.AspNetCore.Identity;
using System;
using FY.WB.CSHero2.Domain.Entities.Core; // For our new interfaces

// It's good practice to place Identity-related classes in a specific Identity sub-namespace
// within Infrastructure, but for now, we'll use Persistence to co-locate with DbContext
// unless you prefer FY.WB.CSHero2.Infrastructure.Identity.
namespace FY.WB.CSHero2.Infrastructure.Persistence
{
    public class ApplicationUser : IdentityUser<Guid>,
                                   IMultiTenant,
                                   ISoftDelete,
                                   ICreationAuditedObject,
                                   IModificationAuditedObject,
                                   IDeletionAuditedObject
    {
        // Properties from IdentityUser<Guid> (Id, UserName, Email, PhoneNumber, etc.) are inherited.

        // Custom properties from project brief (or your requirements)
        public string? CompanyName { get; set; }
        public string? CompanyUrl { get; set; } // As per authentication_guide.md

        // IMultiTenant
        public virtual Guid? TenantId { get; set; }

        // ISoftDelete
        public virtual bool IsDeleted { get; set; }

        // ICreationAuditedObject
        public virtual DateTime CreationTime { get; set; }
        public virtual Guid? CreatorId { get; set; }

        // IModificationAuditedObject
        public virtual DateTime? LastModificationTime { get; set; }
        public virtual Guid? LastModifierId { get; set; }

        // IDeletionAuditedObject
        public virtual DateTime? DeletionTime { get; set; }
        public virtual Guid? DeleterId { get; set; }

        // Admin flag
        public bool IsAdmin { get; set; }

        public ApplicationUser()
        {
            // Audit properties like CreationTime will be set by DbContext or services
            // Default IsAdmin to false for security
            IsAdmin = false;
        }
    }
}
