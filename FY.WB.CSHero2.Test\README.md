# Integration Tests for FY.WB.CSHero2

## Overview

This project contains integration tests for the FY.WB.CSHero2 API, focusing on verifying multi-tenant data isolation using Finbuckle. The tests make real HTTP calls to the API endpoints to verify that:

1. Data is properly isolated between tenants
2. Tenants can only access their own data
3. Authentication and authorization are working correctly
4. Finbuckle multi-tenancy is properly implemented

## Prerequisites

Before running the tests, ensure:

1. The FY.WB.CSHero2 API project is running on http://localhost:5056
2. The database has been seeded with test data (this should happen automatically on startup)
3. The test tenant users exist in the system:
   - Tenant 1: <EMAIL> / Test123!
   - Tenant 2: <EMAIL> / Test123!

## Running the Tests

1. Start the API project:
   ```
   cd FY.WB.CSHero2
   dotnet run
   ```

2. In a separate terminal, run the tests:
   ```
   cd FY.WB.CSHero2.Test
   dotnet test
   ```

## Test Structure

### TestBase.cs

Base class for all integration tests that:
- Sets up HTTP clients
- Authenticates with the API
- Provides helper methods for making authenticated requests

### ControllerTests/FormsControllerTests.cs

Tests for the Forms controller that verify:
- `GET /api/forms` returns only forms belonging to the authenticated tenant
- `GET /api/forms/{id}` returns a form if it belongs to the authenticated tenant
- `GET /api/forms/{id}` returns 404 Not Found if the form belongs to a different tenant

## Adding New Tests

Follow these steps to add tests for a new controller:

1. Create a new test class that inherits from `TestBase`
2. In the `SetUp` method, authenticate with the API for both tenants
3. Write tests that verify multi-tenant data isolation
4. Include positive tests (accessing own data) and negative tests (attempting to access other tenant's data)

## Troubleshooting

If tests fail, check:

1. Is the API running on the expected URL (http://localhost:5056)?
2. Has the database been seeded correctly?
3. Are the test tenant users available and their passwords correct?
4. Is Finbuckle properly configured in the API project?

For authentication errors, verify that the JWT token is being generated correctly and that the claim-based tenant resolution strategy is working.
