import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { API_BASE_URL } from '@/lib/constants'; // Backend API
import { getReportById } from '@/lib/api'; // To fetch report details for filename

// Helper to sanitize filenames
const sanitizeFilename = (name: string) => {
  return name.replace(/[^a-z0-9_.\s-]/gi, '_').replace(/\s+/g, '_');
};

export async function POST(
  request: NextRequest,
  { params }: { params: { reportId: string; format: string } }
) {
  const { reportId, format } = params;

  if (!reportId || !format) {
    return NextResponse.json({ message: 'Report ID and format are required' }, { status: 400 });
  }

  const supportedFormats = ['pdf', 'word', 'powerpoint'];
  if (!supportedFormats.includes(format.toLowerCase())) {
    return NextResponse.json({ message: `Unsupported format: ${format}` }, { status: 400 });
  }

  const authToken = request.cookies.get('authToken')?.value;
  if (!authToken) {
    return NextResponse.json({ message: 'Authentication token is missing' }, { status: 401 });
  }

  try {
    // Fetch report details to construct the filename
    // Note: getReportById is a client-side function calling the BFF.
    // If this BFF route is called from the client-side api.ts, this is fine.
    // If this BFF route could be called directly, consider how to get report/client name.
    // For now, assuming this BFF is called by our api.ts which can pre-fetch or pass details.
    // A simpler approach is if the backend export endpoint itself returns the correct Content-Disposition.
    
    // This call to getReportById might be problematic if it itself calls a BFF route that needs auth.
    // Let's assume for now the backend will provide the correct Content-Disposition header.
    // If not, this part needs to be re-evaluated. The backend should ideally set the filename.

    const backendResponse = await fetch(`${API_BASE_URL}/api/ReportRendering/export/${reportId}/${format.toLowerCase()}`, {
      method: 'POST', // Assuming POST to initiate export, adjust if GET
      headers: {
        'Authorization': `Bearer ${authToken}`,
        // 'Accept': 'application/octet-stream', // Or specific MIME type for the format
      },
      // body: JSON.stringify({}), // If your backend expects a body for POST
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error(`Backend error exporting report ${reportId} to ${format}: ${backendResponse.status} ${errorText}`);
      return NextResponse.json(
        { message: `Failed to export report: ${errorText || backendResponse.statusText}` },
        { status: backendResponse.status }
      );
    }

    const blob = await backendResponse.blob();
    const headers = new Headers();

    // Try to get filename from backend's Content-Disposition first
    let finalFilename = `${sanitizeFilename(reportId)}-export.${format.toLowerCase()}`; // Default
    const disposition = backendResponse.headers.get('Content-Disposition');
    if (disposition && disposition.includes('attachment')) {
      const filenameMatch = disposition.match(/filename\*?=['"]?(?:UTF-\d'')?([^'";\n]*)/i);
      if (filenameMatch && filenameMatch[1]) {
        finalFilename = decodeURIComponent(filenameMatch[1]);
      }
    } else {
        // Fallback: Construct filename if backend doesn't provide it
        // This requires fetching report and client details, which adds complexity.
        // For simplicity, we'll use a generic name if not provided by backend.
        // Ideally, the backend /api/ReportRendering/export endpoint should set the
        // Content-Disposition header with the correctly formatted filename.
        // Example: Content-Disposition: attachment; filename="ClientName-ReportName-Date.pdf"
        console.warn(`Content-Disposition header not found or missing filename. Using default: ${finalFilename}`);
    }
    
    headers.set('Content-Type', backendResponse.headers.get('Content-Type') || 'application/octet-stream');
    headers.set('Content-Disposition', `attachment; filename="${finalFilename}"`);

    return new NextResponse(blob, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error(`Error in BFF while exporting report ${reportId} to ${format}:`, error);
    let message = 'Internal server error during export';
    if (error instanceof Error) {
      message = error.message;
    }
    return NextResponse.json({ message }, { status: 500 });
  }
}
