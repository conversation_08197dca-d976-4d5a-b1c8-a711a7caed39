using Newtonsoft.Json;

namespace FY.WB.CSHero2.Infrastructure.Models;

public class ReportDataDocument
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonProperty("reportId")]
    public string ReportId { get; set; } = string.Empty;
    
    [JsonProperty("tenantId")]
    public string TenantId { get; set; } = string.Empty;
    
    [JsonProperty("reportInfo")]
    public ReportInfoData ReportInfo { get; set; } = new();
    
    [JsonProperty("sections")]
    public List<SectionData> Sections { get; set; } = new();
    
    [JsonProperty("styles")]
    public object? Styles { get; set; }
    
    [JsonProperty("template")]
    public string? Template { get; set; }
    
    [JsonProperty("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    [JsonProperty("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    [JsonProperty("version")]
    public int Version { get; set; } = 1;
}

public class ReportInfoData
{
    [JsonProperty("title")]
    public string Title { get; set; } = string.Empty;
    
    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;
    
    [JsonProperty("clientId")]
    public string? ClientId { get; set; }
    
    [JsonProperty("clientName")]
    public string? ClientName { get; set; }
    
    [JsonProperty("startDate")]
    public string StartDate { get; set; } = string.Empty;
    
    [JsonProperty("endDate")]
    public string EndDate { get; set; } = string.Empty;
}

public class SectionData
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonProperty("title")]
    public string Title { get; set; } = string.Empty;
    
    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;
    
    [JsonProperty("content")]
    public Dictionary<string, object> Content { get; set; } = new();
}
