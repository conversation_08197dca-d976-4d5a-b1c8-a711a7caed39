# System Patterns: SaaS Template

**Version:** 0.5
**Date:** 2025-05-30
**Related Documents:** `projectbrief.md`, `techContext.md`, `activeContext.md`

## 1. Overall Architecture

The system follows a client-server architecture with a decoupled frontend and backend:

-   **Backend API:** ASP.NET Core 8 Web API (`UN_WB_TemlateProject`) responsible for business logic, data persistence, user authentication, and multi-tenancy management.
-   **Frontend UI:** Next.js application (`un_wb_templateproject_ui`) responsible for user interface, client-side state management, and interaction with the backend API.
-   **Hybrid Storage Architecture:** Multi-storage system optimized for different data types:
    -   **SQL Server:** Core metadata, relationships, and structured data (accessed via Entity Framework Core)
    -   **CosmosDB:** Document storage for styles, formatting, and flexible schema data
    -   **Azure Blob Storage:** Large data files, assets, and JSON data storage

```mermaid
graph TD
    User[End User] -->|Browser| FE[Next.js Frontend: un_wb_templateproject_ui]
    FE -->|HTTP/S API Calls via BFF| BFF[Next.js API Routes (BFF)]
    BFF -->|HTTP/S API Calls| BE[ASP.NET Core API: UN_WB_TemlateProject]
    BE -->|EF Core| DB[SQL Server Database]
    BE -->|CosmosDB SDK| CosmosDB[CosmosDB - Styles & Documents]
    BE -->|Blob SDK| BlobStorage[Azure Blob Storage - Large Data]
    BE -->|Identity| IdentityStore[ASP.NET Core Identity Tables in DB]
    BE -->|Finbuckle| TenantStore[Tenant Info Store (In-Memory/DB)]
```

## 2. Key Technical Decisions & Patterns

### 2.1. Backend (ASP.NET Core API)

-   **RESTful API Design:** Standard HTTP verbs and status codes for API interactions.
-   **ASP.NET Core Identity:** Used for core user management (registration, password hashing, user store).
    -   `ApplicationUser` custom class extends `IdentityUser<Guid>` and implements auditing and multi-tenancy interfaces.
    -   `ApplicationDbContext` (standard `IdentityDbContext`) manages Identity tables. Multi-tenancy for entities implementing `IMultiTenant` is handled manually within `ApplicationDbContext` (query filtering in `OnModelCreating`, `TenantId` setting in `SaveChanges`).
-   **JWT Authentication:**
    -   Stateless authentication using JWT Bearer tokens.
    -   Tokens are generated by the `/api/Auth/login` endpoint upon successful credential validation.
    -   Tokens include standard claims (`sub`, `email`, `jti`) plus custom claims: `tenant_id` (if applicable) and `IsAdmin`.
    -   Token validation parameters (issuer, audience, signing key) are configured in `Program.cs` and `appsettings.json`.
-   **Entity Framework Core:** ORM for database interaction.
    -   Code-First approach with migrations.
    -   Migrations are applied automatically on application startup for development convenience (with improved logging in `Program.cs`).
-   **Domain Entity Architecture:**
    -   **Base Entity Classes:** A hierarchy of base entity classes in the Domain layer:
        -   `Entity<TId>`: Base entity with ID and equality comparison.
        -   `AuditedEntity<TId>`: Adds creation and modification auditing.
        -   `FullAuditedEntity<TId>`: Adds deletion auditing.
        -   `MultiTenantEntity<TId>`: Adds tenant ID for multi-tenancy.
        -   `FullAuditedMultiTenantEntity<TId>`: Combines full auditing and multi-tenancy.
    -   **Auditing Interfaces:** Interfaces for different auditing aspects:
        -   `ICreationAuditedObject`: Tracks creation time and creator.
        -   `IModificationAuditedObject`: Tracks modification time and modifier.
        -   `IDeletionAuditedObject`: Tracks deletion time, deleter, and deletion status.
    -   **Multi-tenancy Interface:** `IMultiTenant` interface for tenant-specific entities.

-   **CQRS Pattern for GET Operations (Phase 3.5):**
    -   **Controllers:** API controllers (e.g., `FormsController`, `InvoicesController`, `ReportsController`, `TemplatesController`, `TenantProfilesController`, `UploadsController`) for core entities now use MediatR to dispatch queries for GET operations.
    -   **Queries:** Specific query classes (e.g., `GetFormsQuery`, `GetFormByIdQuery`) are defined in the Application layer, encapsulating the request for data.
    -   **Query Handlers:** Corresponding handlers in the Application layer process these queries, interact with `IApplicationDbContext`, apply filtering/sorting/pagination, and map domain entities to DTOs.
    -   **DTOs:** Data Transfer Objects are used for query parameters (e.g., `FormQueryParametersDto`) and for returning data (e.g., `FormDto`).
    -   **Multi-tenancy in Queries:** Handlers respect multi-tenancy. For entities like `TenantProfile`, handlers contain logic to differentiate data access for admin users (can see all, ignore tenant filters) versus regular users (see only their own tenant's data). For other multi-tenant entities, the global query filter in `ApplicationDbContext` ensures data isolation.
    -   **Error Handling & Logging:** Controllers and handlers include logging for requests, responses, and errors.

-   **Multi-tenancy (Finbuckle & Manual):**
    -   **Tenant Definition:** `AppTenantInfo` class implementing `ITenantInfo` and auditing interfaces.
    -   **Tenant Creation:** New tenants are created dynamically during user registration in `AuthController.Register` and added to the Finbuckle store.
    -   **Tenant Resolution:** Configured with Claim Strategy (`tenant_id`), Route Strategy, and an In-Memory Store via Finbuckle in `MultiTenancyConfiguration.cs`.
    -   **Data Isolation:** Handled manually in `ApplicationDbContext` via global query filters (`OnModelCreating`) and `TenantId` assignment (`SaveChanges`) for entities implementing `IMultiTenant`. This is a workaround due to the previous CS0308 issue preventing the use of `MultiTenantIdentityDbContext`.
    -   **Service Registration:** `AddMultiTenant<AppTenantInfo>()` and `app.UseMultiTenant()`. The problematic self-referential `ITenantInfo` registration has been removed, fixing the startup hang.
-   **Dependency Injection:** Leverages ASP.NET Core's built-in DI container. Startup DI resolution issues related to `ITenantInfo` have been resolved.
-   **Configuration:** Uses `appsettings.json` for application settings, connection strings, and JWT parameters.
-   **Error Handling:** Standard ASP.NET Core error handling, with specific error messages returned from API endpoints. Enhanced logging added to `AuthController` and `Program.cs` startup.
-   **Seeding:** `DataSeeder` class populates initial `Client` data and creates a default `Admin` user (`IsAdmin=true`, `EmailConfirmed=true`) on startup.

### 2.2. Frontend (Next.js)

-   **App Router:** Utilizes Next.js App Router for file-system based routing and layouts.
-   **TypeScript:** For static typing and improved code quality.
-   **Tailwind CSS:** For utility-first CSS styling.
-   **Component-Based Architecture:** UI built with React components.
-   **Layout System:**
    -   Root Layout (`src/app/layout.tsx`): Provides `<html>` and `<body>` tags, wraps global providers like `AuthProvider`.
    -   Route Group Layouts (`src/app/(group)/layout.tsx`): Define shells for specific sections (public, client, tenant).
    -   Shared Layout Components (`src/app/(layouts)/...`): Reusable layout structures (e.g., `PublicLayout.tsx`).
-   **Client-Side State Management (Authentication):**
    -   React Context API (`AuthContext`) to manage user authentication state (user object, token, loading status) and provide login/logout methods.
    -   JWT and user data are stored in `localStorage` by `AuthContext` (Note: for middleware integration, HttpOnly cookies are preferred for tokens).
-   **Backend-For-Frontend (BFF) Pattern:**
    -   Next.js API Routes (e.g., `/api/auth/login`, `/api/auth/register`) act as a proxy between the Next.js client and the ASP.NET Core API.
    -   This pattern is crucial for securely handling authentication tokens (e.g., setting HttpOnly cookies) and can encapsulate API interaction logic.
-   **Route Protection:**
    -   Next.js Middleware (`src/middleware.ts`) intercepts requests to protected routes.
    -   Currently checks for a placeholder `authToken` cookie and redirects to `/login` if absent. This will be fully effective once the BFF sets the HttpOnly cookie.
-   **Styling:** Global styles in `src/app/globals.css`, component-level styling via Tailwind CSS classes.

### 2.3. Hybrid Storage Architecture

-   **Schema Management:** EF Core Migrations manage database schema changes.
-   **User and Role Storage:** Standard ASP.NET Core Identity tables (`AspNetUsers`, `AspNetRoles`, etc.).
-   **Tenant Information Storage:** (Future with EF Core store for Finbuckle) A separate table for tenant metadata.

### 2.4. Report Rendering Engine

The Report Rendering Engine is a specialized component that leverages Large Language Models (LLMs) to generate HTML templates based on structured data and metadata. It follows Clean Architecture principles with well-defined layers:

-   **Domain Layer:** Contains core entities, interfaces, and models defining the business domain.
    -   Interfaces (`ILlmClient`, `IDatabaseService`, `IHtmlValidator`) define service contracts.
    -   Models (`LlmMetrics`, `ValidationResult`) define domain objects.
    -   Entities (`DocumentTemplateMetadata`, `ReportStyle`, etc.) define business objects.

-   **Application Layer:** Contains business logic and orchestration services.
    -   Models (`CDataWrapper`, `RenderRequest`, etc.) define application-specific representations.
    -   Services (`PromptBuilder`, `XmlPromptSerializer`, `ReportRenderer`) implement business logic.
    -   The `ReportRenderer` service orchestrates the entire rendering process.

-   **Infrastructure Layer:** Contains implementations of domain interfaces.
    -   Models (`LlmConfig`) define infrastructure-specific configurations.
    -   Services (`LlmClientFactory`, `OpenAiLlmClient`, `AnthropicLlmClient`) implement external service integration.

-   **Key Features:**
    -   **Parallel Data Loading:** Template data, metadata, and existing HTML are loaded in parallel.
    -   **Prompt Caching:** Frequently used prompts can be cached for performance.
    -   **Factory Pattern:** The `LlmClientFactory` creates appropriate LLM clients based on configuration.
    -   **XML-Based Prompting:** Structured XML prompts provide clear instructions to the LLM.
    -   **Error Handling:** Comprehensive error handling with custom exceptions and detailed logging.
    -   **Timeout Protection:** LLM calls have configurable timeouts to prevent hanging operations.

```mermaid
graph TD
    Request[Request] --> ReportRenderer[ReportRenderer]
    ReportRenderer -->|Parallelize| Data[Fetch Data]
    ReportRenderer -->|Parallelize| Metadata[Fetch Metadata]
    ReportRenderer -->|Parallelize| ExistingHtml[Fetch HTML]
    ReportRenderer -->|Cache| Prompt[Get Instruction Prompt]
    Data & Metadata & ExistingHtml & Prompt --> PromptBuilder[PromptBuilder]
    PromptBuilder --> XmlSerializer[XmlPromptSerializer]
    XmlSerializer --> LlmClientFactory[LlmClientFactory]
    LlmClientFactory -->|Provider Switch| LlmClient[LLM Client]
    LlmClient --> HtmlValidator[HtmlValidator]
    HtmlValidator --> Result[HTML Result]
```

## 3. Critical Implementation Paths

-   **User Registration:** Next.js UI -> Next.js BFF Register API -> ASP.NET Core Register API -> Tenant Creation (in Finbuckle Store) -> Identity User Creation (with `TenantId`) -> Database.
-   **User Login:** Next.js UI -> Next.js BFF Login API -> ASP.NET Core Login API -> Identity Validation -> JWT Generation (with `tenant_id` and `IsAdmin` claims) -> BFF sets HttpOnly Cookie -> Next.js AuthContext updates.
-   **Authenticated API Request:** Next.js UI -> (Token automatically sent via cookie) -> ASP.NET Core API -> JWT Validation -> Finbuckle resolves tenant from `tenant_id` claim -> Action Execution -> DbContext applies manual tenant filtering.
-   **Report Rendering:** Client Request -> ReportRenderer -> Parallel Data Fetching -> PromptBuilder -> XmlPromptSerializer -> LlmClientFactory -> LLM API Call -> HTML Validation (optional) -> HTML Response.

## 4. Known Issues / Blockers

-   **Startup Hang:** Resolved by removing the incorrect `ITenantInfo` registration in `MultiTenancyConfiguration.cs`.
-   **CS0308 Error with `MultiTenantIdentityDbContext`:** This was previously resolved by implementing proper auditing abstractions and moving `AppTenantInfo` to the Infrastructure layer. The `ApplicationDbContext` continues to use a standard `IdentityDbContext` with multi-tenancy handled manually via the `IMultiTenant` interface.
-   **Duplicate ApplicationUser Issue:** (Resolved Previously) The duplicate ApplicationUser class in the Domain layer was removed, and all references now use the correct ApplicationUser from the Infrastructure.Persistence namespace.

## 5. Entity Architecture

The system uses a hierarchical entity architecture to support auditing and multi-tenancy:

```mermaid
graph TD
    Entity[Entity<TId>] --> AuditedEntity[AuditedEntity<TId>]
    Entity --> MultiTenantEntity[MultiTenantEntity<TId>]
    AuditedEntity --> FullAuditedEntity[FullAuditedEntity<TId>]
    AuditedEntity --> AuditedMultiTenantEntity[AuditedMultiTenantEntity<TId>]
    FullAuditedEntity --> FullAuditedMultiTenantEntity[FullAuditedMultiTenantEntity<TId>]
    MultiTenantEntity --> AuditedMultiTenantEntity
    AuditedMultiTenantEntity --> FullAuditedMultiTenantEntity
    
    ICreationAuditedObject[ICreationAuditedObject] --> AuditedEntity
    IModificationAuditedObject[IModificationAuditedObject] --> AuditedEntity
    IDeletionAuditedObject[IDeletionAuditedObject] --> FullAuditedEntity
    IMultiTenant[IMultiTenant] --> MultiTenantEntity
```

This architecture allows entities to inherit the appropriate base class based on their requirements:
- `Client` inherits from `FullAuditedMultiTenantEntity<Guid>` for full auditing and multi-tenancy.
- `AppTenantInfo` implements `ITenantInfo`, `ICreationAuditedObject`, and `IModificationAuditedObject`.
- `ApplicationUser` inherits from `IdentityUser<Guid>` and implements auditing interfaces.

## 6. Hybrid Storage Patterns

The system implements a sophisticated hybrid storage architecture optimizing data placement across three storage systems:

### 6.1. Storage System Responsibilities

**SQL Server (Primary Metadata Store):**
- Core business entities with relationships and referential integrity
- External storage references (`StyleDocumentId`, `DataBlobPath`, `AssetBlobPath`)
- Enhanced Report Rendering entities: `Template`, `Report`, `ReportVersion`, `ComponentDefinition`

**CosmosDB (Document Store):**
- Style documents with flexible JSON schema
- Tenant-based partitioning for multi-tenant isolation
- Container: `report-styles` with optimized indexing

**Azure Blob Storage (Large Data Store):**
- Hierarchical organization: `tenants/{tenantId}/reports/{reportId}/versions/{versionId}/`
- Large JSON files, component assets, and binary data
- Multiple access patterns (Dictionary, Stream, JSON)

### 6.2. Cross-System Integration

**Reference Linking:** SQL Server entities contain foreign keys and path references to external storage
**Data Flow:** Automatic routing based on data type and size
**Multi-tenancy:** Consistent tenant isolation across all three systems
**Performance:** Right storage for right data type optimizes cost and performance
