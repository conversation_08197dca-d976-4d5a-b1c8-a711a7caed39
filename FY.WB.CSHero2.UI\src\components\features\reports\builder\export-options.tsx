"use client";

import { useState } from "react";
import { Download, FileText, Image, FileCode } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface ExportOptionsProps {
  reportId: string;
  reportName: string;
  reportPages: {
    id: string;
    name: string;
    description?: string;
  }[];
  onExport: (config: any) => void;
}

export function ExportOptions({
  reportId,
  reportName,
  reportPages,
  onExport
}: ExportOptionsProps) {
  const [format, setFormat] = useState<string>("pdf");
  const [orientation, setOrientation] = useState<string>("portrait");
  const [paperSize, setPaperSize] = useState<string>("a4");
  const [includeFooter, setIncludeFooter] = useState<boolean>(true);
  const [tableOfContents, setTableOfContents] = useState<boolean>(true);
  const [pageNumbers, setPageNumbers] = useState<boolean>(true);
  const [selectedPages, setSelectedPages] = useState<string[]>(
    reportPages.map(page => page.id)
  );

  const handlePageToggle = (pageId: string) => {
    if (selectedPages.includes(pageId)) {
      setSelectedPages(selectedPages.filter(id => id !== pageId));
    } else {
      setSelectedPages([...selectedPages, pageId]);
    }
  };

  const handleExport = () => {
    const config = {
      reportId,
      format,
      orientation,
      paperSize,
      includeFooter,
      tableOfContents,
      pageNumbers,
      pages: reportPages.filter(page => selectedPages.includes(page.id))
    };
    
    onExport(config);
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium mb-3">Export Format</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div
            className={`border rounded-lg p-4 flex flex-col items-center cursor-pointer ${
              format === "pdf" ? "border-primary bg-primary/5" : ""
            }`}
            onClick={() => setFormat("pdf")}
          >
            <FileText className="h-8 w-8 mb-2 text-red-500" />
            <span className="text-sm font-medium">PDF</span>
          </div>
          <div
            className={`border rounded-lg p-4 flex flex-col items-center cursor-pointer ${
              format === "png" ? "border-primary bg-primary/5" : ""
            }`}
            onClick={() => setFormat("png")}
          >
            <Image className="h-8 w-8 mb-2 text-green-500" aria-label="PNG format option" />
            <span className="text-sm font-medium">PNG</span>
          </div>
          <div
            className={`border rounded-lg p-4 flex flex-col items-center cursor-pointer ${
              format === "html" ? "border-primary bg-primary/5" : ""
            }`}
            onClick={() => setFormat("html")}
          >
            <FileCode className="h-8 w-8 mb-2 text-blue-500" />
            <span className="text-sm font-medium">HTML</span>
          </div>
          <div
            className={`border rounded-lg p-4 flex flex-col items-center cursor-pointer ${
              format === "xlsx" ? "border-primary bg-primary/5" : ""
            }`}
            onClick={() => setFormat("xlsx")}
          >
            <FileText className="h-8 w-8 mb-2 text-green-700" />
            <span className="text-sm font-medium">Excel</span>
          </div>
        </div>
      </div>

      {(format === "pdf" || format === "png") && (
        <div>
          <h4 className="text-lg font-medium mb-3">Page Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Paper Size</label>
              <select
                className="w-full p-2 border rounded-md"
                value={paperSize}
                onChange={(e) => setPaperSize(e.target.value)}
              >
                <option value="a4">A4</option>
                <option value="letter">Letter</option>
                <option value="legal">Legal</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Orientation</label>
              <select
                className="w-full p-2 border rounded-md"
                value={orientation}
                onChange={(e) => setOrientation(e.target.value)}
              >
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>
          </div>
        </div>
      )}

      <div>
        <h4 className="text-lg font-medium mb-3">Include Pages</h4>
        <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
          {reportPages.map(page => (
            <div key={page.id} className="flex items-center">
              <Checkbox
                id={`page-${page.id}`}
                checked={selectedPages.includes(page.id)}
                onCheckedChange={() => handlePageToggle(page.id)}
              />
              <label htmlFor={`page-${page.id}`} className="ml-2 text-sm font-medium">
                {page.name}
              </label>
            </div>
          ))}
        </div>
      </div>

      {format === "pdf" && (
        <div>
          <h4 className="text-lg font-medium mb-3">Additional Options</h4>
          <div className="space-y-2">
            <div className="flex items-center">
              <Checkbox
                id="table-of-contents"
                checked={tableOfContents}
                onCheckedChange={(checked) => 
                  setTableOfContents(checked === true)
                }
              />
              <label htmlFor="table-of-contents" className="ml-2 text-sm font-medium">
                Include Table of Contents
              </label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="page-numbers"
                checked={pageNumbers}
                onCheckedChange={(checked) => 
                  setPageNumbers(checked === true)
                }
              />
              <label htmlFor="page-numbers" className="ml-2 text-sm font-medium">
                Include Page Numbers
              </label>
            </div>
            <div className="flex items-center">
              <Checkbox
                id="include-footer"
                checked={includeFooter}
                onCheckedChange={(checked) => 
                  setIncludeFooter(checked === true)
                }
              />
              <label htmlFor="include-footer" className="ml-2 text-sm font-medium">
                Include Footer Information
              </label>
            </div>
          </div>
        </div>
      )}

      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-700">
          <strong>Note:</strong> This is a placeholder component. In a real implementation, 
          export functionality would generate actual document files in the selected format.
        </p>
      </div>

      <div className="flex justify-end">
        <button
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70 flex items-center"
          onClick={handleExport}
        >
          <Download className="h-4 w-4 mr-2" />
          Export as {format.toUpperCase()}
        </button>
      </div>
    </div>
  );
}
