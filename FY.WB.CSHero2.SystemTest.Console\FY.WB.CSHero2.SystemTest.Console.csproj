<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Application\FY.WB.CSHero2.ReportRenderingEngine.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Domain\FY.WB.CSHero2.ReportRenderingEngine.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\FY.WB.CSHero2\SimpleSystemTest.cs" Link="SimpleSystemTest.cs" />
  </ItemGroup>

</Project>
