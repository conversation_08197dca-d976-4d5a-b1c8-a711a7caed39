// Import the database
import db from '@/data/db/db.json';

/**
 * In-memory database initialized from static data
 * This replaces the file-based approach that used Node.js fs module
 */
let inMemoryDb: any = { ...db };

/**
 * Read data from the in-memory database
 * @returns Data from the in-memory database
 */
export async function readJsonFile<T>(): Promise<T> {
  return inMemoryDb as T;
}

/**
 * Update data in the in-memory database
 * In a production environment, this would be replaced with a database call
 * @param data New data to replace the entire database
 */
export async function writeJsonFile<T>(_filePath: string, data: T): Promise<void> {
  inMemoryDb = data;
}

/**
 * Generate a new ID for a collection
 * @param collection Array of objects with id property
 * @returns New unique ID
 */
export function generateId<T extends { id: string | number }>(collection: T[]): string {
  if (collection.length === 0) return '1';
  
  const ids = collection.map(item => 
    typeof item.id === 'number' ? item.id : parseInt(item.id as string, 10)
  );
  
  return String(Math.max(...ids) + 1);
}

/**
 * Filter an array based on query parameters
 * @param array Array to filter
 * @param query Query parameters
 * @returns Filtered array
 */
export function filterByQuery<T>(array: T[], query: Record<string, string | string[]>): T[] {
  if (!query || Object.keys(query).length === 0) {
    return array;
  }

  return array.filter(item => {
    return Object.entries(query).every(([key, value]) => {
      // Skip pagination and sorting parameters
      if (['_page', '_limit', '_sort', '_order'].includes(key)) {
        return true;
      }

      const itemValue = (item as any)[key];
      if (Array.isArray(value)) {
        return value.some(v => String(itemValue) === v);
      }
      return String(itemValue) === value;
    });
  });
}

/**
 * Sort an array based on sort parameters
 * @param array Array to sort
 * @param sortBy Property to sort by
 * @param order Sort order ('asc' or 'desc')
 * @returns Sorted array
 */
export function sortArray<T>(array: T[], sortBy?: string, order: 'asc' | 'desc' = 'asc'): T[] {
  if (!sortBy) return array;

  return [...array].sort((a, b) => {
    const aValue = (a as any)[sortBy];
    const bValue = (b as any)[sortBy];

    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Paginate an array
 * @param array Array to paginate
 * @param page Page number (1-based)
 * @param limit Items per page
 * @returns Paginated array
 */
export function paginateArray<T>(array: T[], page?: number, limit?: number): T[] {
  if (!page || !limit) return array;
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return array.slice(startIndex, endIndex);
}

/**
 * Process an array with filtering, sorting, and pagination
 * @param array Array to process
 * @param options Processing options
 * @returns Processed array
 */
export function processArray<T>(
  array: T[], 
  options: { 
    query?: Record<string, string | string[]>,
    sortBy?: string,
    order?: 'asc' | 'desc',
    page?: number,
    limit?: number
  }
): T[] {
  const { query, sortBy, order, page, limit } = options;
  
  let result = array;
  
  // Apply filtering
  if (query) {
    result = filterByQuery(result, query);
  }
  
  // Apply sorting
  if (sortBy) {
    result = sortArray(result, sortBy, order);
  }
  
  // Apply pagination
  if (page && limit) {
    result = paginateArray(result, page, limit);
  }
  
  return result;
}
