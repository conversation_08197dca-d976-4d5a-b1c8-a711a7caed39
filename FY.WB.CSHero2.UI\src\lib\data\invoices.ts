// Re-export functions from api-invoices.ts with adaptations for route.ts
import { 
  getInvoices as apiGetInvoices, 
  getInvoiceById, 
  getInvoicesByTenantId, 
  createInvoice, 
  updateInvoice, 
  deleteInvoice 
} from '@/lib/api-invoices';

// Adapt getInvoices to match the signature used in route.ts
export async function getInvoices(params?: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
}) {
  // Convert the parameters to match the api-invoices.ts function
  const apiParams: Parameters<typeof apiGetInvoices>[0] = {
    limit: params?.limit,
    page: params?.page,
    sortBy: params?.sortBy,
    sortOrder: params?.order,
  };
  
  // Add any query parameters
  if (params?.query) {
    Object.entries(params.query).forEach(([key, value]) => {
      if (key === 'q' || key === 'search') {
        apiParams.search = value as string;
      } else if (key === 'type') {
        apiParams.type = value as 'Invoice' | 'Credit' | 'Refund' | 'Payment';
      } else if (key === 'status') {
        apiParams.status = value as 'paid' | 'pending' | 'overdue' | 'canceled';
      } else if (key === 'tenantId') {
        apiParams.tenantId = value as string;
      }
    });
  }
  
  return apiGetInvoices(apiParams);
}

export { 
  getInvoiceById, 
  getInvoicesByTenantId, 
  createInvoice, 
  updateInvoice, 
  deleteInvoice 
};
