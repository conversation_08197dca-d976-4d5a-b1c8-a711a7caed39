using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities
{
    /// <summary>
    /// Represents a style document stored in CosmosDB for report styling and formatting
    /// </summary>
    public class ReportStyleDocument
    {
        /// <summary>
        /// Unique identifier for the document (format: style-{reportVersionId} or style-{templateId})
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Partition key for CosmosDB (tenantId)
        /// </summary>
        [JsonPropertyName("partitionKey")]
        public string PartitionKey { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the report version this style belongs to (null for template styles)
        /// </summary>
        [JsonPropertyName("reportVersionId")]
        public Guid? ReportVersionId { get; set; }

        /// <summary>
        /// Reference to the template this style belongs to (null for report styles)
        /// </summary>
        [JsonPropertyName("templateId")]
        public Guid? TemplateId { get; set; }

        /// <summary>
        /// Tenant ID for multi-tenancy
        /// </summary>
        [JsonPropertyName("tenantId")]
        public Guid TenantId { get; set; }

        /// <summary>
        /// Type of style document (report, template, component)
        /// </summary>
        [JsonPropertyName("styleType")]
        public string StyleType { get; set; } = "report";

        /// <summary>
        /// HTML content with styling applied
        /// </summary>
        [JsonPropertyName("htmlContent")]
        public string HtmlContent { get; set; } = string.Empty;

        /// <summary>
        /// CSS styles for the content
        /// </summary>
        [JsonPropertyName("cssStyles")]
        public string CssStyles { get; set; } = string.Empty;

        /// <summary>
        /// Inline styles as JSON object
        /// </summary>
        [JsonPropertyName("inlineStyles")]
        public Dictionary<string, object> InlineStyles { get; set; } = new();

        /// <summary>
        /// Component-specific styles for sections
        /// </summary>
        [JsonPropertyName("componentStyles")]
        public Dictionary<string, ComponentStyle> ComponentStyles { get; set; } = new();

        /// <summary>
        /// Metadata about the style document
        /// </summary>
        [JsonPropertyName("metadata")]
        public StyleMetadata Metadata { get; set; } = new();

        /// <summary>
        /// When this document was created
        /// </summary>
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this document was last modified
        /// </summary>
        [JsonPropertyName("lastModified")]
        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who created this document
        /// </summary>
        [JsonPropertyName("createdBy")]
        public Guid CreatedBy { get; set; }

        /// <summary>
        /// Who last modified this document
        /// </summary>
        [JsonPropertyName("lastModifiedBy")]
        public Guid LastModifiedBy { get; set; }

        /// <summary>
        /// Version of the style document for change tracking
        /// </summary>
        [JsonPropertyName("version")]
        public int Version { get; set; } = 1;

        /// <summary>
        /// Whether this style document is active
        /// </summary>
        [JsonPropertyName("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creates a style document ID for a report version
        /// </summary>
        public static string CreateReportStyleId(Guid reportVersionId)
        {
            return $"style-report-{reportVersionId}";
        }

        /// <summary>
        /// Creates a style document ID for a template
        /// </summary>
        public static string CreateTemplateStyleId(Guid templateId)
        {
            return $"style-template-{templateId}";
        }

        /// <summary>
        /// Creates a style document ID for a component
        /// </summary>
        public static string CreateComponentStyleId(Guid componentId)
        {
            return $"style-component-{componentId}";
        }

        /// <summary>
        /// Updates the last modified timestamp and user
        /// </summary>
        public void UpdateModified(Guid userId)
        {
            LastModified = DateTime.UtcNow;
            LastModifiedBy = userId;
            Version++;
        }

        /// <summary>
        /// Checks if this is a report-specific style
        /// </summary>
        public bool IsReportStyle()
        {
            return StyleType == "report" && ReportVersionId.HasValue;
        }

        /// <summary>
        /// Checks if this is a template-specific style
        /// </summary>
        public bool IsTemplateStyle()
        {
            return StyleType == "template" && TemplateId.HasValue;
        }

        /// <summary>
        /// Checks if this is a component-specific style
        /// </summary>
        public bool IsComponentStyle()
        {
            return StyleType == "component";
        }
    }

    /// <summary>
    /// Metadata about the style document
    /// </summary>
    public class StyleMetadata
    {
        /// <summary>
        /// CSS framework used (TailwindCSS, Bootstrap, etc.)
        /// </summary>
        [JsonPropertyName("framework")]
        public string Framework { get; set; } = "TailwindCSS";

        /// <summary>
        /// Framework version
        /// </summary>
        [JsonPropertyName("frameworkVersion")]
        public string FrameworkVersion { get; set; } = "3.0";

        /// <summary>
        /// Theme name or identifier
        /// </summary>
        [JsonPropertyName("theme")]
        public string Theme { get; set; } = "default";

        /// <summary>
        /// Color scheme (light, dark, auto)
        /// </summary>
        [JsonPropertyName("colorScheme")]
        public string ColorScheme { get; set; } = "light";

        /// <summary>
        /// Tags for categorization and search
        /// </summary>
        [JsonPropertyName("tags")]
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// Custom properties for extensibility
        /// </summary>
        [JsonPropertyName("customProperties")]
        public Dictionary<string, object> CustomProperties { get; set; } = new();

        /// <summary>
        /// Responsive breakpoints configuration
        /// </summary>
        [JsonPropertyName("breakpoints")]
        public Dictionary<string, string> Breakpoints { get; set; } = new()
        {
            ["sm"] = "640px",
            ["md"] = "768px",
            ["lg"] = "1024px",
            ["xl"] = "1280px",
            ["2xl"] = "1536px"
        };
    }

    /// <summary>
    /// Component-specific styling information
    /// </summary>
    public class ComponentStyle
    {
        /// <summary>
        /// Component identifier
        /// </summary>
        [JsonPropertyName("componentId")]
        public string ComponentId { get; set; } = string.Empty;

        /// <summary>
        /// Component type (header, chart, table, etc.)
        /// </summary>
        [JsonPropertyName("componentType")]
        public string ComponentType { get; set; } = string.Empty;

        /// <summary>
        /// CSS classes for the component
        /// </summary>
        [JsonPropertyName("cssClasses")]
        public List<string> CssClasses { get; set; } = new();

        /// <summary>
        /// Inline styles for the component
        /// </summary>
        [JsonPropertyName("inlineStyles")]
        public Dictionary<string, string> InlineStyles { get; set; } = new();

        /// <summary>
        /// Custom CSS for the component
        /// </summary>
        [JsonPropertyName("customCss")]
        public string CustomCss { get; set; } = string.Empty;

        /// <summary>
        /// Layout properties
        /// </summary>
        [JsonPropertyName("layout")]
        public ComponentLayout Layout { get; set; } = new();
    }

    /// <summary>
    /// Layout properties for components
    /// </summary>
    public class ComponentLayout
    {
        /// <summary>
        /// Width specification
        /// </summary>
        [JsonPropertyName("width")]
        public string Width { get; set; } = "100%";

        /// <summary>
        /// Height specification
        /// </summary>
        [JsonPropertyName("height")]
        public string Height { get; set; } = "auto";

        /// <summary>
        /// Margin specification
        /// </summary>
        [JsonPropertyName("margin")]
        public string Margin { get; set; } = "0";

        /// <summary>
        /// Padding specification
        /// </summary>
        [JsonPropertyName("padding")]
        public string Padding { get; set; } = "0";

        /// <summary>
        /// Display type (block, flex, grid, etc.)
        /// </summary>
        [JsonPropertyName("display")]
        public string Display { get; set; } = "block";

        /// <summary>
        /// Position type (static, relative, absolute, etc.)
        /// </summary>
        [JsonPropertyName("position")]
        public string Position { get; set; } = "static";
    }
}