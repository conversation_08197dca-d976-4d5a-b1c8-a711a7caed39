using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.Infrastructure.Interfaces;

/// <summary>
/// Factory for creating tenant-aware ApplicationDbContext instances
/// Ensures proper tenant isolation and thread safety
/// </summary>
public interface ITenantDbContextFactory
{
    /// <summary>
    /// Creates a new ApplicationDbContext instance with current tenant context
    /// </summary>
    /// <returns>A new ApplicationDbContext instance</returns>
    ApplicationDbContext CreateDbContext();
    
    /// <summary>
    /// Creates a new ApplicationDbContext instance for a specific tenant
    /// </summary>
    /// <param name="tenantId">The tenant identifier</param>
    /// <returns>A new ApplicationDbContext instance</returns>
    ApplicationDbContext CreateDbContext(string tenantId);
}