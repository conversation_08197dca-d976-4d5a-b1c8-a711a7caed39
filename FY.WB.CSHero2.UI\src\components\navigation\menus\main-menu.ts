import { Home, FileText, Book, Mail, HelpCircle, DollarSign } from "lucide-react";
import { NavItem } from "../types";

export const mainMenu: NavItem[] = [
  { href: "/", label: "Home", icon: Home },
  { href: "/features", label: "Features", icon: FileText },
  { href: "/pricing", label: "Pricing", icon: DollarSign },
  { href: "/docs", label: "Documentation", icon: Book },
  { href: "/blog", label: "Blog", icon: FileText },
  { href: "/contact", label: "Contact", icon: Mail }
];
