namespace FY.WB.CSHero2.Application.Reports.Dtos
{
    public class ReportQueryParametersDto
    {
        public string? SearchTerm { get; set; }
        public string? Status { get; set; } // e.g., "draft", "published", "archived"
        public string? Category { get; set; }
        public string? ClientId { get; set; } // Filter reports by client ID
        
        public string? SortBy { get; set; } // e.g., "name", "creationTime"
        public string? SortOrder { get; set; } // "asc" or "desc"

        private int _page = 1;
        public int Page
        {
            get => _page;
            set => _page = (value < 1) ? 1 : value;
        }

        private int _pageSize = 10;
        private const int MaxPageSize = 100;
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : (value < 1) ? 1 : value;
        }
    }
}
