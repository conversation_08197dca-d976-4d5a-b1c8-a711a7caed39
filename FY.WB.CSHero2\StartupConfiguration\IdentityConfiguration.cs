using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using FY.WB.CSHero2.Infrastructure.Persistence;
using System;
using Microsoft.AspNetCore.Authentication.JwtBearer;

namespace FY.WB.CSHero2.StartupConfiguration
{
    public static class IdentityConfiguration
    {
        public static IServiceCollection AddIdentityConfiguration(this IServiceCollection services)
        {
            services.AddIdentity<ApplicationUser, IdentityRole<Guid>>(options =>
            {
                // Configure identity options
                options.SignIn.RequireConfirmedAccount = false;
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequiredLength = 8;

                // Configure lockout
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
                options.Lockout.MaxFailedAccessAttempts = 5;
                options.Lockout.AllowedForNewUsers = true;

                // Configure user
                options.User.RequireUniqueEmail = true;
                options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
            })
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddRoles<IdentityRole<Guid>>() // Added this line
            .AddDefaultTokenProviders();

            // Configure application cookie options to return 401/403 for API requests
            services.ConfigureApplicationCookie(options =>
            {
                // Override the default cookie behavior for API requests
                options.Events.OnRedirectToLogin = context =>
                {
                    context.Response.StatusCode = 401;
                    return System.Threading.Tasks.Task.CompletedTask;
                };
                options.Events.OnRedirectToAccessDenied = context =>
                {
                    context.Response.StatusCode = 403;
                    return System.Threading.Tasks.Task.CompletedTask;
                };
            });

            return services;
        }
    }
}
