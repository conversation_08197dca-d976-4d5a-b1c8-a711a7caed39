"use client";

import { useState } from "react";
import { Upload } from "lucide-react";

interface ImportReportPanelProps {
  onImportSuccess?: (templateId: string) => void;
}

export function ImportReportPanel({ onImportSuccess }: ImportReportPanelProps) {
  const [templateName, setTemplateName] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [message, setMessage] = useState({ type: "", text: "" });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!templateName.trim()) {
      setMessage({ type: "error", text: "Please enter a template name" });
      return;
    }
    
    if (!file) {
      setMessage({ type: "error", text: "Please select a file to upload" });
      return;
    }
    
    // For this phase, we'll just show a success message without actually submitting data
    setIsUploading(true);
    
    // Simulate processing delay
    setTimeout(() => {
      setIsUploading(false);
      setMessage({ 
        type: "success", 
        text: "Template imported successfully! It will now appear in your template list." 
      });
      
      // Reset form
      setTemplateName("");
      setFile(null);
      
      // Notify parent component (if needed in future phases)
      if (onImportSuccess) {
        onImportSuccess("placeholder-id");
      }
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium mb-2">Import Report Template</h3>
        <p className="text-sm text-gray-500">
          Upload a PDF or Word document to create a new template based on its layout and formatting.
        </p>
      </div>
      
      {message.text && (
        <div className={`p-3 rounded ${
          message.type === "error" ? "bg-red-50 text-red-600" : 
          message.type === "success" ? "bg-green-50 text-green-600" : ""
        }`}>
          {message.text}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Template Name</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            placeholder="Enter a name for this template"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Upload File</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {file ? (
              <div className="text-sm">
                <p className="font-medium">{file.name}</p>
                <p className="text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                <button
                  type="button"
                  className="text-primary hover:underline mt-2"
                  onClick={() => setFile(null)}
                >
                  Remove
                </button>
              </div>
            ) : (
              <>
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-2">
                  <label htmlFor="file-upload" className="cursor-pointer text-primary hover:underline">
                    Click to upload
                  </label>
                  <p className="text-xs text-gray-500 mt-1">PDF or Word documents only</p>
                </div>
                <input
                  id="file-upload"
                  type="file"
                  className="hidden"
                  accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  onChange={handleFileChange}
                />
              </>
            )}
          </div>
        </div>
        
        <button
          type="submit"
          className={`w-full py-2 px-4 rounded ${
            isUploading 
              ? "bg-gray-300 cursor-not-allowed" 
              : "bg-primary text-white hover:bg-primary/90"
          }`}
          disabled={isUploading}
        >
          {isUploading ? "Importing..." : "Import Template"}
        </button>
      </form>
    </div>
  );
}
