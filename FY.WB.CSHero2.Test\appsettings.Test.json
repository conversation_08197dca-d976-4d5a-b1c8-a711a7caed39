{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CSHero2Test;Trusted_Connection=true;MultipleActiveResultSets=true;", "CosmosDb": "AccountEndpoint=https://localhost:8081/;AccountKey=C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==;", "BlobStorage": "UseDevelopmentStorage=true;"}, "CosmosDb": {"DatabaseName": "CSHero2Test", "Containers": {"ReportStyles": "report-styles"}}, "BlobStorage": {"ContainerName": "test-report-data"}, "ReportRenderingEngine": {"LLM": {"Provider": "OpenAI", "Model": "gpt-4", "ApiKey": "test-api-key", "MaxTokens": 4000, "TimeoutSeconds": 30}, "ComponentGeneration": {"Framework": "NextJS", "TypeScript": true, "StyleFramework": "TailwindCSS", "ComponentLibrary": "Custom"}, "Export": {"PdfEngine": "<PERSON><PERSON><PERSON><PERSON><PERSON>har<PERSON>", "OfficeEngine": "OpenXML", "TempDirectory": "./temp/exports"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FY.WB.CSHero2": "Debug"}}}