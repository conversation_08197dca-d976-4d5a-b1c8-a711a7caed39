using System;
using Microsoft.Data.SqlClient;

// This is a simple direct SQL Server connection test
// Run it with: dotnet FY.WB.CSHero2.dll --sql-test
public static class SqlTest
{
    public static void RunTest()
    {
        try
        {
            string connectionString = "Server=TRENTS-PC\\MSSQLSERVER02;Database=FY.WB.CSHero2;Trusted_Connection=True;TrustServerCertificate=True;Connection Timeout=30";
            Console.WriteLine("Testing connection to: " + connectionString);

            using (var connection = new SqlConnection(connectionString))
            {
                Console.WriteLine("Opening connection...");
                connection.Open();
                Console.WriteLine("Connection successful!");

                Console.WriteLine("Testing command execution...");
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT @@VERSION";
                    var version = command.ExecuteScalar()?.ToString();
                    Console.WriteLine($"SQL Server Version: {version}");
                }
            }
            
            Console.WriteLine("All tests completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error connecting to database: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
            
            // Print full stack trace for debugging
            Console.WriteLine("Stack trace:");
            Console.WriteLine(ex.StackTrace);
        }
    }
}
