using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Implementation of report service for managing report instances
    /// </summary>
    public class ReportServiceImpl : IReportService
    {
        private readonly ILogger<ReportServiceImpl> _logger;
        private readonly ApplicationDbContext _context;

        public ReportServiceImpl(
            ILogger<ReportServiceImpl> logger,
            ApplicationDbContext context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// Gets a specific report by ID
        /// </summary>
        public async Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting report with ID: {ReportId}", reportId);

            var report = await _context.Reports
                .Include(r => r.Template)
                .Include(r => r.Versions)
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report == null)
            {
                throw new InvalidOperationException($"Report with ID {reportId} not found");
            }

            return report;
        }

        /// <summary>
        /// Gets all reports owned by the current user
        /// </summary>
        public async Task<List<Report>> GetUserReportsAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting user reports");

            // Note: In a real implementation, this would filter by current user
            // For now, returning all reports
            return await _context.Reports
                .Include(r => r.Template)
                .OrderByDescending(r => r.LastModificationTime)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Creates a new report instance
        /// </summary>
        public async Task<Report> CreateReportAsync(CreateReportRequest request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Creating new report: {ReportName}", request.Name);

            var report = new Report(
                Guid.NewGuid(),
                string.Empty, // ReportNumber - would be generated by business logic
                request.ClientId,
                request.ClientName,
                request.Name,
                request.Category,
                0, // SlideCount - initial value
                "Draft",
                request.Author)
            {
                TemplateId = request.TemplateId,
                ReportType = request.TemplateId.HasValue ? "Template-based" : "Custom"
            };

            _context.Reports.Add(report);

            // Create initial version with provided data
            if (request.InitialData.Any())
            {
                var initialVersion = new ReportVersion
                {
                    ReportId = report.Id,
                    VersionNumber = 1,
                    Description = "Initial version",
                    IsCurrent = true
                };

                initialVersion.SetReportData(request.InitialData);
                _context.ReportVersions.Add(initialVersion);

                report.CurrentVersionId = initialVersion.Id;
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created report {ReportId}: {ReportName}",
                report.Id, report.Name);

            return report;
        }

        /// <summary>
        /// Updates the data for a report without triggering a re-render
        /// </summary>
        public async Task UpdateReportDataAsync(Guid reportId, Dictionary<string, object> data, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Updating data for report {ReportId}", reportId);

            var report = await GetReportAsync(reportId, cancellationToken);
            var currentVersion = await GetCurrentVersionAsync(reportId, cancellationToken);

            if (currentVersion == null)
            {
                throw new InvalidOperationException($"No current version found for report {reportId}");
            }

            // Update the current version's data
            currentVersion.SetReportData(data);
            // Note: LastModificationTime will be set by EF Core interceptors

            // Update report timestamp will be handled by EF Core interceptors

            _context.ReportVersions.Update(currentVersion);
            _context.Reports.Update(report);

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully updated data for report {ReportId}", reportId);
        }

        /// <summary>
        /// Gets the current version of a report
        /// </summary>
        public async Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting current version for report {ReportId}", reportId);

            var currentVersion = await _context.ReportVersions
                .Include(rv => rv.ComponentDefinitions)
                .Where(rv => rv.ReportId == reportId && rv.IsCurrent)
                .FirstOrDefaultAsync(cancellationToken);

            if (currentVersion == null)
            {
                throw new InvalidOperationException($"No current version found for report {reportId}");
            }

            return currentVersion;
        }

        /// <summary>
        /// Deletes a report and all its versions
        /// </summary>
        public async Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Deleting report {ReportId}", reportId);

            var report = await GetReportAsync(reportId, cancellationToken);

            // Delete all versions and their component definitions (cascade delete should handle this)
            _context.Reports.Remove(report);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully deleted report {ReportId}", reportId);
        }

        /// <summary>
        /// Gets reports by client with pagination
        /// </summary>
        public async Task<(List<Report> Reports, int TotalCount)> GetReportsByClientAsync(
            Guid clientId,
            int page = 1,
            int pageSize = 20,
            CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting reports for client {ClientId}, page {Page}, size {PageSize}",
                clientId, page, pageSize);

            var query = _context.Reports
                .Include(r => r.Template)
                .Where(r => r.ClientId == clientId);

            var totalCount = await query.CountAsync(cancellationToken);

            var reports = await query
                .OrderByDescending(r => r.LastModificationTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (reports, totalCount);
        }

        /// <summary>
        /// Gets reports created from a specific template
        /// </summary>
        public async Task<List<Report>> GetReportsByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting reports created from template {TemplateId}", templateId);

            return await _context.Reports
                .Include(r => r.Template)
                .Where(r => r.TemplateId == templateId)
                .OrderByDescending(r => r.CreationTime)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Updates basic report information (name, status, etc.)
        /// </summary>
        public async Task<Report> UpdateReportAsync(Guid reportId, UpdateReportRequest request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Updating report {ReportId}", reportId);

            var report = await GetReportAsync(reportId, cancellationToken);

            // Update properties
            if (!string.IsNullOrEmpty(request.Name))
                report.Name = request.Name;

            if (!string.IsNullOrEmpty(request.Status))
                report.Status = request.Status;

            if (!string.IsNullOrEmpty(request.Category))
                report.Category = request.Category;

            if (!string.IsNullOrEmpty(request.Author))
                report.Author = request.Author;

            // LastModificationTime will be set by EF Core interceptors

            _context.Reports.Update(report);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully updated report {ReportId}", reportId);

            return report;
        }

        /// <summary>
        /// Gets report data as a dictionary for the current version
        /// </summary>
        public async Task<Dictionary<string, object>> GetReportDataAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting data for report {ReportId}", reportId);

            var currentVersion = await GetCurrentVersionAsync(reportId, cancellationToken);
            return currentVersion.GetReportData();
        }

        /// <summary>
        /// Checks if a report exists and user has access to it
        /// </summary>
        public async Task<bool> ReportExistsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Checking if report {ReportId} exists", reportId);

            return await _context.Reports
                .AnyAsync(r => r.Id == reportId, cancellationToken);
        }

        /// <summary>
        /// Gets report statistics for the current user
        /// </summary>
        public async Task<ReportStatistics> GetReportStatisticsAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting report statistics");

            var reports = await _context.Reports.ToListAsync(cancellationToken);

            var statistics = new ReportStatistics
            {
                TotalReports = reports.Count,
                TemplateBasedReports = reports.Count(r => r.TemplateId.HasValue),
                CustomReports = reports.Count(r => !r.TemplateId.HasValue),
                ReportsThisMonth = reports.Count(r => r.CreationTime >= DateTime.UtcNow.AddMonths(-1)),
                ReportsByStatus = reports
                    .GroupBy(r => r.Status)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ReportsByCategory = reports
                    .GroupBy(r => r.Category)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return statistics;
        }
    }
}
