"use client";

import { motion } from "framer-motion";
import { Star } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Customer Service Manager",
    company: "TechCorp Inc.",
    content: "The report generation capabilities have transformed how we analyze our customer service data. The AI insights are particularly valuable for identifying trends we might have missed.",
    gradient: "gradient-primary-secondary",
  },
  {
    name: "<PERSON>",
    role: "Head of Support",
    company: "CloudServe Solutions",
    content: "We've reduced our reporting time by 75% while delivering more insightful reports. The customizable templates and collaboration features are game-changers for our team.",
    gradient: "gradient-secondary-tertiary",
  },
  {
    name: "<PERSON>",
    role: "Operations Director",
    company: "Global Services Ltd",
    content: "The ability to generate professional reports with AI-powered insights has significantly improved our decision-making process. Our executives love the clear, actionable data presentation.",
    gradient: "gradient-primary-tertiary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function Testimonials() {
  return (
    <section className="py-24">
      <div className="container mx-auto px-2 max-w-inner md:px-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 text-white">
            Trusted by Industry Leaders
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            See how companies are improving their customer service reporting with our platform
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.name}
              variants={cardVariants}
              whileHover={{ y: -5 }}
              className="relative"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-8 flex flex-col">
                {/* Stars */}
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${testimonial.gradient} [&>path]:fill-current text-white`}
                    />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="flex-grow">
                  <p className="text-gray-600 mb-6">"{testimonial.content}"</p>
                </blockquote>

                {/* Author */}
                <div className="mt-4">
                  <div className={`h-px w-12 mb-4 ${testimonial.gradient}`} />
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                  <p className="text-sm text-gray-600">{testimonial.company}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <p className="text-white/80 mb-4">Trusted by teams worldwide</p>
          <div className="flex justify-center items-center space-x-8">
            {/* Replace with actual company logos */}
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="w-32 h-12 bg-white/10 rounded"
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
