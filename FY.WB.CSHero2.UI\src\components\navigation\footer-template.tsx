"use client";

import Link from "next/link";
import { footerMenu } from "./menus/footer-menu";
import { Facebook, Twitter, Linkedin, Instagram } from "lucide-react";

const socialLinks = [
  { icon: Facebook, href: "#" },
  { icon: Twitter, href: "#" },
  { icon: Linkedin, href: "#" },
  { icon: Instagram, href: "#" },
];

const quickLinks = [
  { href: "/services", label: "Our Services" },
  { href: "/about", label: "About Us" },
  { href: "/case-studies", label: "Case Studies" },
  { href: "/blog", label: "AI Marketing Blog" },
];

const solutions = [
  { href: "/ai-landing", label: "AI Landing Pages" },
  { href: "/smart-ads", label: "Smart Ad Campaigns" },
  { href: "/content", label: "Content Generation" },
  { href: "/leads", label: "Lead Generation" },
];

export function FooterTemplate() {
  return (
    <footer className="bg-nav-bg">
      <div className="mx-auto max-w-[1920px] px-4">
        <div className="mx-auto max-w-inner py-16">
          <div className="grid grid-cols-1 gap-12 md:grid-cols-4">
            {/* Brand & Social */}
            <div>
              <Link href="/" className="text-xl font-bold text-white">
                CS-Hero
              </Link>
              <p className="mt-4 text-sm text-white/80">
                Pioneering the future of customer service analysis with AI-powered solutions that deliver measurable results.
              </p>
              <div className="mt-6 flex gap-4">
                {socialLinks.map((social, index) => (
                  <Link 
                    key={index} 
                    href={social.href}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    <social.icon className="h-5 w-5" />
                  </Link>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="font-semibold text-white mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {quickLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-sm text-white/60 hover:text-white transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h3 className="font-semibold text-white mb-4">Our Solutions</h3>
              <ul className="space-y-2">
                {solutions.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-sm text-white/60 hover:text-white transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h3 className="font-semibold text-white mb-4">Contact Us</h3>
              <div className="space-y-2 text-sm text-white/80">
                <p className="font-medium">CS-Hero HQ</p>
                <p>100 Innovation Way</p>
                <p>San Francisco, CA 94105</p>
                <p className="mt-4">+1 (415) 555-0123</p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10">
          <div className="mx-auto max-w-inner py-6 flex flex-col md:flex-row justify-between items-center">
            <div className="flex gap-6 mb-4 md:mb-0">
              {footerMenu.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-sm text-white/60 hover:text-white transition-colors"
                >
                  {item.label}
                </Link>
              ))}
            </div>
            <p className="text-sm text-white/60">
              © {new Date().getFullYear()} CS-Hero. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
