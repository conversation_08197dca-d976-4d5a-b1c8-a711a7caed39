using System;
using System.Collections.Generic;

namespace FY.WB.CSHero2.Domain.Entities.Core
{
    public abstract class Entity<TId> : IEntity<TId>
    {
        public virtual TId Id { get; protected set; } = default!;

        protected Entity()
        {
            // Id will be set by EF Core or by derived classes
        }

        protected Entity(TId id)
        {
            Id = id;
        }

        public override bool Equals(object? obj)
        {
            if (obj == null || !(obj is Entity<TId> entity))
            {
                return false;
            }

            if (ReferenceEquals(this, entity))
            {
                return true;
            }

            if (GetType() != entity.GetType())
            {
                return false;
            }

            // If Id is the default value for TId (e.g., Guid.Empty for Guid, 0 for int),
            // then they are not equal unless they are the same reference.
            if (Equals(Id, default(TId)) || Equals(entity.Id, default(TId)))
            {
                return false;
            }

            // Use EqualityComparer<T>.Default to handle null values and value types
            return EqualityComparer<TId>.Default.Equals(Id, entity.Id);
        }

        public override int GetHashCode()
        {
            // If Id is the default value, use the base GetHashCode to avoid collisions
            // for transient entities.
            if (Equals(Id, default(TId)))
            {
                return base.GetHashCode();
            }
            return (GetType().ToString() + Id).GetHashCode();
        }

        public static bool operator ==(Entity<TId>? left, Entity<TId>? right)
        {
            if (left is null)
            {
                return right is null;
            }
            return left.Equals(right);
        }

        public static bool operator !=(Entity<TId>? left, Entity<TId>? right)
        {
            return !(left == right);
        }
    }
}