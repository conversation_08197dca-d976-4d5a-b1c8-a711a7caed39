// Mark this route as dynamic to prevent static generation errors
// This is the correct Next.js App Router config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

import { default as dynamicImport } from 'next/dynamic';

// Create a static fallback component that doesn't use browser APIs
function StaticBillingPage() {
  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <div className="mb-16">
        <h1 className="text-4xl font-bold mb-4">Billing</h1>
        <p className="text-xl text-gray-600">Loading billing information...</p>
      </div>
      <div className="flex items-center justify-center h-64">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
      </div>
    </div>
  );
}

// Import the component with SSR disabled
const AdminBillingPage = dynamicImport(
  () => import('@/components/features/admin/billing/AdminBillingPage'),
  { 
    ssr: false, // Critical flag to prevent server-side rendering
    loading: () => <StaticBillingPage />
  }
);

// Simple wrapper that just renders the dynamic import
export default function BillingPage() {
  return <AdminBillingPage />;
}
