using FY.WB.CSHero2.Application.Clients.Dtos;
using MediatR;

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class UpdateClientCommand : IRequest<ClientDto?>
    {
        public string Id { get; }
        public UpdateClientRequestDto ClientDto { get; }

        public UpdateClientCommand(string id, UpdateClientRequestDto clientDto)
        {
            Id = id;
            ClientDto = clientDto;
        }
    }
}
