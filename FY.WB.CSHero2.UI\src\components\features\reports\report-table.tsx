'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation'; // Import useRouter
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  MoreHorizontal,
  ArrowUpDown,
  FileEdit,
  Eye,
  Send,
  Trash2
} from 'lucide-react';
import { Report } from '@/types/report';
import { getReports, deleteReport, updateReport } from '@/lib/api';
import { useToast } from '@/components/providers/toast-provider';

interface ReportTableProps {
  searchQuery?: string;
}

export function ReportTable({ searchQuery = '' }: ReportTableProps) {
  const router = useRouter(); // Initialize useRouter
  const [sortBy, setSortBy] = useState<keyof Report>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(10);
  const { toast } = useToast();

  const fetchReports = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getReports({ // Changed variable name
        search: searchQuery || undefined,
        sortBy: sortBy,
        sortOrder: sortOrder,
        page,
        limit,
      });
      // Assuming getReports now returns { items: Report[], totalCount: number, totalPages: number, currentPage: number }
      setReports(response.items || []); // Ensure reports is always an array
      setTotalPages(response.totalPages || 1);
      // Optionally, update current page if backend dictates it, though usually frontend controls this for pagination
      // setPage(response.currentPage || 1);
      setError(null);
    } catch (err) {
      setError('Failed to fetch reports');
      toast({
        title: 'Error',
        description: 'Failed to fetch reports',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [searchQuery, sortBy, sortOrder, page, limit, toast]);

  useEffect(() => {
    fetchReports();
  }, [fetchReports]);

  const handleSort = (column: keyof Report) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteReport(id);
      toast({
        title: 'Success',
        description: 'Report deleted successfully',
      });
      fetchReports();
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to delete report',
        variant: 'destructive',
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'completed':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20';
      case 'in progress':
        return 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20';
      case 'under review':
        return 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20';
      case 'draft':
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
      default:
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
    }
  };

  return (
    <div className="rounded-lg border bg-gradient-to-b from-card to-card/95 shadow-md transition-all duration-200">
      <div className="relative">
        <Table>
          <TableHeader className="bg-secondary/25 rounded-t-lg">
            <TableRow className="hover:bg-muted/5 transition-colors">
              <TableHead className="w-[300px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort('name')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Report Name
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[200px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort('clientName')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Client
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[150px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort('category')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Category
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[150px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort('creationTime')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Created
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[100px] text-center">Slides</TableHead>
              <TableHead className="w-[150px]">
                <Button
                  variant="ghost"
                  onClick={() => handleSort('status')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Status
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-red-600">
                  {error}
                </TableCell>
              </TableRow>
            ) : reports.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <p className="text-muted-foreground">No reports found</p>
                    <p className="text-sm text-muted-foreground">Create your first report to get started</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              reports.map((report) => (
                <TableRow key={report.id} className="hover:bg-muted/5 transition-colors">
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-primary/90">{report.name}</span>
                      <span className="text-sm text-muted-foreground">ID: {report.id}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium text-foreground/90">{report.clientName}</div>
                  </TableCell>
                  <TableCell>
                    <div className="inline-flex items-center rounded-md bg-primary/10 text-primary px-2 py-1 text-sm font-medium">
                      {report.category}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{formatDate(report.creationTime)}</div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="inline-flex items-center justify-center rounded-md bg-muted/50 w-8 h-8 text-sm font-medium">
                      {report.slideCount}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                      {report.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-primary/5 hover:text-primary transition-colors">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={() => router.push(`/reports/${report.id}/view`)} // Fixed navigation to use route group path
                        >
                          <Eye className="h-4 w-4" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={() => router.push(`/reports/${report.id}/edit`)} // Fixed navigation to use route group path
                        >
                          <FileEdit className="h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center gap-2 cursor-pointer">
                          <Send className="h-4 w-4" />
                          Send
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2 text-red-600 cursor-pointer"
                          onClick={() => handleDelete(report.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-4 py-4 border-t bg-muted/5">
        <p className="text-sm text-muted-foreground">
          Page {page} of {totalPages}
        </p>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1 || loading}
            className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages || loading}
            className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
