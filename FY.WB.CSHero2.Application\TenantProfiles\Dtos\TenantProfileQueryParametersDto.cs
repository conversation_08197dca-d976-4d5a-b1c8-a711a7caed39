namespace FY.WB.CSHero2.Application.TenantProfiles.Dtos
{
    public class TenantProfileQueryParametersDto
    {
        public string? Search { get; set; } // Search by Name, Company, Email
        public string? Status { get; set; } // Filter by "active", "suspended"
        public string? Subscription { get; set; } // Filter by subscription type e.g., "professional"
        public string? SortBy { get; set; } // e.g., "Name", "Company", "Subscription", "CreatedAt"
        public string? SortOrder { get; set; } // "asc" or "desc"
        public int? Page { get; set; } = 1;
        public int? Limit { get; set; } = 10;
    }
}
