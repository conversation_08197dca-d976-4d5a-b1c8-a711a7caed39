using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Reports.Dtos;
using FY.WB.CSHero2.Application.Reports.Queries;
using FY.WB.CSHero2.Application.Reports.Commands;
using FY.WB.CSHero2.Application.Common.Exceptions;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Controllers
{
    /// <summary>
    /// Controller for managing reports in the system.
    /// </summary>
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("Operations for managing reports")]
    public class ReportsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ReportsController> _logger;

        /// <summary>
        /// Initializes a new instance of the ReportsController.
        /// </summary>
        /// <param name="mediator">The mediator service.</param>
        /// <param name="logger">The logger service.</param>
        public ReportsController(IMediator mediator, ILogger<ReportsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Retrieves all reports accessible to the current user, with pagination and filtering.
        /// </summary>
        /// <param name="queryParameters">Query parameters for pagination, sorting, and filtering.</param>
        /// <returns>A paged list of reports.</returns>
        /// <response code="200">Returns the paged list of reports.</response>
        /// <response code="401">If the user is not authenticated.</response>
        [HttpGet]
        [SwaggerOperation(
            Summary = "Get all reports",
            Description = "Retrieves a paged list of all reports accessible to the current user, supporting pagination, sorting, and filtering.",
            OperationId = "GetReports",
            Tags = new[] { "Reports" }
        )]
        [SwaggerResponse(200, "Paged list of reports retrieved successfully", typeof(PagedResult<ReportDto>))]
        [SwaggerResponse(401, "User is not authenticated")]
        public async Task<ActionResult<PagedResult<ReportDto>>> GetReports([FromQuery] ReportQueryParametersDto queryParameters)
        {
            var query = new GetReportsQuery(queryParameters);
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Retrieves a specific report by its ID.
        /// </summary>
        /// <param name="id">The ID of the report to retrieve.</param>
        /// <returns>The requested report.</returns>
        /// <response code="200">Returns the requested report.</response>
        /// <response code="401">If the user is not authenticated.</response>
        /// <response code="404">If the report is not found.</response>
        [HttpGet("{id}")]
        [SwaggerOperation(
            Summary = "Get a specific report",
            Description = "Retrieves a specific report by its ID",
            OperationId = "GetReportById", // Changed from GetReport to be more specific
            Tags = new[] { "Reports" }
        )]
        [SwaggerResponse(200, "Report found and returned successfully", typeof(ReportDto))]
        [SwaggerResponse(401, "User is not authenticated")]
        [SwaggerResponse(404, "Report not found")]
        public async Task<ActionResult<ReportDto>> GetReportById(
            [SwaggerParameter("The ID of the report to retrieve")] string id)
        {
            if (!Guid.TryParse(id, out var reportId))
            {
                return BadRequest("Invalid report ID format.");
            }
            var query = new GetReportByIdQuery(reportId);
            var report = await _mediator.Send(query);

            if (report == null)
            {
                _logger.LogWarning("Report with ID {ReportId} not found.", reportId);
                return NotFound(new { message = $"Report with ID {reportId} not found." });
            }
            _logger.LogInformation("Successfully retrieved report with ID {ReportId}", reportId);
            return Ok(report);
        }

        /// <summary>
        /// Creates a new report.
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), 201)]
        [ProducesResponseType(400)]
        [SwaggerOperation(Summary = "Create a new report", OperationId = "CreateReport", Tags = new[] { "Reports" })]
        public async Task<IActionResult> CreateReport([FromBody] CreateReportRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new report with name: {ReportName}", dto.Name);
            try
            {
                var command = new CreateReportCommand(dto);
                var reportId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created report with ID: {ReportId}", reportId);
                return CreatedAtAction(nameof(GetReportById), new { id = reportId.ToString() }, new { id = reportId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating report.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating report: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the report.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing report.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [SwaggerOperation(Summary = "Update an existing report", OperationId = "UpdateReport", Tags = new[] { "Reports" })]
        public async Task<IActionResult> UpdateReport(Guid id, [FromBody] UpdateReportRequestDto dto)
        {
            _logger.LogInformation("Attempting to update report with ID: {ReportId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for report update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateReportCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated report with ID: {ReportId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating report with ID: {ReportId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Report with ID {ReportId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update report with ID: {ReportId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report with ID {ReportId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the report.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a report by its ID.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        [SwaggerOperation(Summary = "Delete a report by ID", OperationId = "DeleteReport", Tags = new[] { "Reports" })]
        public async Task<IActionResult> DeleteReport(Guid id)
        {
            _logger.LogInformation("Attempting to delete report with ID: {ReportId}", id);
            try
            {
                var command = new DeleteReportCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted report with ID: {ReportId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Report with ID {ReportId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete report with ID: {ReportId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report with ID {ReportId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the report.", details = ex.Message });
            }
        }
    }
}
