<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.Application\FY.WB.CSHero2.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Domain\FY.WB.CSHero2.ReportRenderingEngine.Domain.csproj" />
  </ItemGroup>

</Project>
