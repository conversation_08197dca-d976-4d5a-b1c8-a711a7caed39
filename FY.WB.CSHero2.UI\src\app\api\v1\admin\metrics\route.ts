import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/lib/constants';

// Mock metrics data structure
interface MetricData {
  current: number;
  previousPeriod: number;
  trend: number[];
}

interface Metrics {
  totalCustomers: MetricData;
  newCustomers: MetricData;
  reportsCreated: MetricData;
  revenue: MetricData;
}

interface MetricsResponse {
  metrics: Metrics;
  timeframe: string;
}

// Generate a trend array from a single value
function generateTrendArray(value: number): number[] {
  return [
    value * 0.7,
    value * 0.8,
    value * 0.9,
    value,
    value * 1.1
  ];
}

// Mock data for different timeframes
const mockMetrics: Record<string, MetricsResponse> = {
  today: {
    metrics: {
      totalCustomers: { 
        current: 1250, 
        previousPeriod: 1200, 
        trend: generateTrendArray(4.17)
      },
      newCustomers: { 
        current: 25, 
        previousPeriod: 20, 
        trend: generateTrendArray(25)
      },
      reportsCreated: { 
        current: 45, 
        previousPeriod: 40, 
        trend: generateTrendArray(12.5)
      },
      revenue: { 
        current: 12500, 
        previousPeriod: 11000, 
        trend: generateTrendArray(13.64)
      }
    },
    timeframe: 'today'
  },
  wtd: {
    metrics: {
      totalCustomers: { 
        current: 1250, 
        previousPeriod: 1150, 
        trend: generateTrendArray(8.7)
      },
      newCustomers: { 
        current: 75, 
        previousPeriod: 65, 
        trend: generateTrendArray(15.38)
      },
      reportsCreated: { 
        current: 180, 
        previousPeriod: 160, 
        trend: generateTrendArray(12.5)
      },
      revenue: { 
        current: 45000, 
        previousPeriod: 40000, 
        trend: generateTrendArray(12.5)
      }
    },
    timeframe: 'wtd'
  },
  mtd: {
    metrics: {
      totalCustomers: { 
        current: 1250, 
        previousPeriod: 1100, 
        trend: generateTrendArray(13.64)
      },
      newCustomers: { 
        current: 150, 
        previousPeriod: 130, 
        trend: generateTrendArray(15.38)
      },
      reportsCreated: { 
        current: 450, 
        previousPeriod: 400, 
        trend: generateTrendArray(12.5)
      },
      revenue: { 
        current: 125000, 
        previousPeriod: 110000, 
        trend: generateTrendArray(13.64)
      }
    },
    timeframe: 'mtd'
  },
  ytd: {
    metrics: {
      totalCustomers: { 
        current: 1250, 
        previousPeriod: 950, 
        trend: generateTrendArray(31.58)
      },
      newCustomers: { 
        current: 300, 
        previousPeriod: 250, 
        trend: generateTrendArray(20)
      },
      reportsCreated: { 
        current: 1200, 
        previousPeriod: 1000, 
        trend: generateTrendArray(20)
      },
      revenue: { 
        current: 500000, 
        previousPeriod: 450000, 
        trend: generateTrendArray(11.11)
      }
    },
    timeframe: 'ytd'
  }
};

/**
 * GET /api/v1/admin/metrics
 * Returns metrics data for the specified timeframe
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || 'today';

    // Check if user is authenticated and has admin rights
    const authToken = cookies().get('authToken')?.value;
    if (!authToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // In the future, this would call the backend API
    // For now, return mock data based on the timeframe
    const validTimeframes = ['today', 'wtd', 'mtd', 'ytd'];
    const validTimeframe = validTimeframes.includes(timeframe) ? timeframe : 'today';
    
    console.log(`Returning metrics data for timeframe: ${validTimeframe}`);
    return NextResponse.json(mockMetrics[validTimeframe]);
  } catch (error) {
    console.error('Error fetching metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
