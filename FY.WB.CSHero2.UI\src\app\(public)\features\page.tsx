"use client";

import { motion } from "framer-motion";
import { 
  <PERSON>U<PERSON>, 
  <PERSON><PERSON><PERSON>3, 
  <PERSON><PERSON>, 
  Share2, 
  Wand2,
  FileDown,
  Users,
  PaintBucket
} from "lucide-react";

const features = [
  {
    title: "Data Input & Analysis",
    description: "Effortlessly capture and analyze customer service data with AI-powered insights",
    icon: FileUp,
    gradient: "gradient-primary-secondary",
    items: [
      "Smart forms with validation",
      "Bulk data import",
      "AI-powered analysis",
      "Real-time data processing",
      "50MB file upload support"
    ],
    demoPreview: {
      title: "Interactive Form Builder",
      description: "Try our intuitive form builder with real-time validation (Coming Soon)",
    }
  },
  {
    title: "Visual Report Builder",
    description: "Create stunning reports with our drag-and-drop interface and pre-built templates",
    icon: BarChart3,
    gradient: "gradient-secondary-tertiary",
    items: [
      "Drag-and-drop editor",
      "Real-time preview",
      "5+ pre-built templates",
      "Custom charts & graphs",
      "AI-enhanced content"
    ],
    demoPreview: {
      title: "Report Builder Demo",
      description: "Experience our visual report builder in action (Coming Soon)",
    }
  },
  {
    title: "Branding & Customization",
    description: "Make reports truly yours with advanced branding and customization options",
    icon: Palette,
    gradient: "gradient-primary-tertiary",
    items: [
      "Custom logo placement",
      "Brand color schemes",
      "Font customization",
      "Layout control",
      "Template styling"
    ],
    demoPreview: {
      title: "Brand Customizer",
      description: "See how your brand elements come to life (Coming Soon)",
    }
  },
  {
    title: "Export & Sharing",
    description: "Share your insights effortlessly with multiple export formats and sharing options",
    icon: Share2,
    gradient: "gradient-primary-secondary",
    items: [
      "PDF export",
      "PowerPoint export",
      "Direct link sharing",
      "Email integration",
      "Access controls"
    ],
    demoPreview: {
      title: "Export Preview",
      description: "Preview how your reports look in different formats (Coming Soon)",
    }
  }
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function FeaturesPage() {
  return (
    <>
      <div className="gradient-primary-secondary">
        <div className="container mx-auto px-4 py-24 md:px-16">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl font-bold mb-4 text-white">Powerful Features</h1>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Everything you need to create, analyze, and share professional customer service reports
            </p>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto"
          >
            {features.map((feature) => (
              <motion.div
                key={feature.title}
                variants={itemVariants}
                className="relative group"
                whileHover={{ y: -5 }}
              >
                <div className="h-full bg-white rounded-lg shadow-lg p-8 transition-shadow duration-300 group-hover:shadow-xl">
                  <div className="flex items-center mb-6">
                    <motion.div
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <feature.icon className={`w-8 h-8 ${feature.gradient} bg-clip-text text-transparent mr-3`} />
                    </motion.div>
                    <h2 className={`text-2xl font-bold ${feature.gradient} bg-clip-text text-transparent`}>
                      {feature.title}
                    </h2>
                  </div>
                  <p className="text-gray-600 mb-6">{feature.description}</p>
                  
                  {/* Feature List */}
                  <ul className="space-y-3 mb-8">
                    {feature.items.map((item) => (
                      <li key={item} className="flex items-center text-gray-600 group/item">
                        <Wand2 className="w-4 h-4 text-[rgb(var(--primary))] mr-2 transition-transform duration-300 group-hover/item:scale-125" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Demo Preview Section */}
                  <div className={`${feature.gradient} rounded-lg p-6 text-white`}>
                    <h3 className="text-lg font-semibold mb-2">{feature.demoPreview.title}</h3>
                    <p className="text-sm opacity-90">{feature.demoPreview.description}</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="mt-4 px-4 py-2 bg-white text-[rgb(var(--primary))] rounded-lg font-semibold hover:bg-opacity-90 transition-colors"
                    >
                      Coming Soon
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* CTA Section */}
      <section className="bg-white py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              className="w-12 h-12 mx-auto mb-6 gradient-primary-secondary rounded-xl flex items-center justify-center"
            >
              <Wand2 className="w-6 h-6 text-white" />
            </motion.div>
            <h2 className="text-4xl font-bold mb-6 gradient-primary-secondary bg-clip-text text-transparent">
              Ready to Transform Your Customer Service Reporting?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of teams who have already improved their reporting workflow
            </p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <a
                  href="/client/reports/create"
                  className="inline-flex items-center px-8 py-4 rounded-lg gradient-primary-secondary text-white font-semibold hover:opacity-90 transition-opacity"
                >
                  Start Creating Reports
                  <svg
                    className="ml-2 w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    />
                  </svg>
                </a>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <a
                  href="/contact"
                  className="inline-flex items-center px-8 py-4 rounded-lg border border-[rgb(var(--primary))] text-[rgb(var(--primary))] font-semibold hover:bg-[rgb(var(--primary))] hover:text-white transition-colors"
                >
                  Contact Sales
                </a>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              { number: "10K+", label: "Reports Generated" },
              { number: "98%", label: "Customer Satisfaction" },
              { number: "24/7", label: "Expert Support" },
            ].map((stat) => (
              <div
                key={stat.label}
                className="text-center"
              >
                <div className="text-4xl font-bold mb-2 gradient-primary-secondary bg-clip-text text-transparent">
                  {stat.number}
                </div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>
    </>
  );
}
