using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Service for managing report styles in CosmosDB
    /// </summary>
    public interface IReportStyleService
    {
        /// <summary>
        /// Gets a style document by its ID
        /// </summary>
        /// <param name="styleDocumentId">The style document ID</param>
        /// <param name="tenantId">The tenant ID for partition key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The style document or null if not found</returns>
        Task<ReportStyleDocument?> GetStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a style document for a specific report version
        /// </summary>
        /// <param name="reportVersionId">The report version ID</param>
        /// <param name="tenantId">The tenant ID for partition key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The style document or null if not found</returns>
        Task<ReportStyleDocument?> GetReportStyleAsync(Guid reportVersionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a style document for a specific template
        /// </summary>
        /// <param name="templateId">The template ID</param>
        /// <param name="tenantId">The tenant ID for partition key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The style document or null if not found</returns>
        Task<ReportStyleDocument?> GetTemplateStyleAsync(Guid templateId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves a style document to CosmosDB
        /// </summary>
        /// <param name="style">The style document to save</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The ID of the saved style document</returns>
        Task<string> SaveStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing style document
        /// </summary>
        /// <param name="style">The style document to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if updated successfully</returns>
        Task<bool> UpdateStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a style document
        /// </summary>
        /// <param name="styleDocumentId">The style document ID</param>
        /// <param name="tenantId">The tenant ID for partition key</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all style documents for a tenant
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="styleType">Optional filter by style type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of style documents</returns>
        Task<List<ReportStyleDocument>> GetStylesByTenantAsync(Guid tenantId, string? styleType = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a copy of a style document for a new report version
        /// </summary>
        /// <param name="sourceStyleId">The source style document ID</param>
        /// <param name="targetReportVersionId">The target report version ID</param>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="userId">The user creating the copy</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The ID of the new style document</returns>
        Task<string> CopyStyleForReportVersionAsync(string sourceStyleId, Guid targetReportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a style document from a template for a new report
        /// </summary>
        /// <param name="templateId">The template ID</param>
        /// <param name="reportVersionId">The report version ID</param>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="userId">The user creating the style</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The ID of the new style document</returns>
        Task<string> CreateStyleFromTemplateAsync(Guid templateId, Guid reportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches style documents by tags or metadata
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="searchTerm">Search term for tags or metadata</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching style documents</returns>
        Task<List<ReportStyleDocument>> SearchStylesAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets style documents by framework type
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <param name="framework">The framework name (e.g., "TailwindCSS")</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of style documents using the specified framework</returns>
        Task<List<ReportStyleDocument>> GetStylesByFrameworkAsync(Guid tenantId, string framework, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates a style document before saving
        /// </summary>
        /// <param name="style">The style document to validate</param>
        /// <returns>Validation result with any errors</returns>
        Task<StyleValidationResult> ValidateStyleAsync(ReportStyleDocument style);
    }

    /// <summary>
    /// Result of style document validation
    /// </summary>
    public class StyleValidationResult
    {
        /// <summary>
        /// Whether the style document is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Creates a successful validation result
        /// </summary>
        public static StyleValidationResult Success()
        {
            return new StyleValidationResult { IsValid = true };
        }

        /// <summary>
        /// Creates a failed validation result with errors
        /// </summary>
        public static StyleValidationResult Failure(params string[] errors)
        {
            return new StyleValidationResult 
            { 
                IsValid = false, 
                Errors = new List<string>(errors) 
            };
        }
    }
}