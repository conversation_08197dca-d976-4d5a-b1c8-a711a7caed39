# Data Seeding Documentation

## Overview
This document outlines the comprehensive data seeding strategy for the CSHero application, covering SQL Server, CosmosDB, and Azure Blob Storage seeding across multiple storage systems.

## Data Seeding Architecture

### Storage Systems
1. **SQL Server** - Core application data and metadata
2. **CosmosDB** - Report styling and formatting documents
3. **Azure Blob Storage** - Large data files and assets (planned)

## SQL Server Data Seeding

### Main Seeder
- **File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/DataSeeder.cs`
- **Entry Point**: `DataSeeder.SeedAsync(IServiceProvider serviceProvider)`
- **Triggered From**: `FY.WB.CSHero2/Program.cs` during application startup

### Seeded Entities

#### 1. TenantProfiles
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/tenant-profiles.json`
- **Entity**: `TenantProfile`
- **Count**: 3 tenant profiles
- **Key Features**:
  - Multi-tenant organization data
  - Payment method and billing address as JSON
  - Maps to Finbuckle multi-tenancy system
- **Special Handling**: Uses DTO mapping for complex JSON properties

#### 2. Clients
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/clients.json`
- **Entity**: `Client`
- **Count**: 5 clients
- **Key Features**:
  - Company information and contact details
  - Industry and company size data
  - Linked to tenant profiles

#### 3. Reports
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/reports.json`
- **Entity**: `Report`
- **Count**: 6 reports
- **Key Features**:
  - Report metadata and status
  - Foreign key relationships to clients
  - Custom seeding logic for client name lookup
- **Special Handling**: FK constraint resolution by ClientName and TenantId

#### 4. Invoices
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/invoices.json`
- **Entity**: `Invoice`
- **Count**: 8 invoices
- **Key Features**:
  - Billing and payment information
  - Multiple invoice types (Invoice, Payment, Refund)
  - Linked to tenant profiles

#### 5. Forms
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/forms.json`
- **Entity**: `Form`
- **Count**: 2 forms
- **Key Features**:
  - Customer feedback and support forms
  - Priority and category classification

#### 6. Templates
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/templates.json`
- **Entity**: `Template`
- **Count**: 1 template
- **Key Features**:
  - Report template definitions
  - Complex JSON properties (Tags, Sections, Fields)
- **Special Handling**: DTO mapping for complex collections

#### 7. Uploads
- **Source File**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/uploads.json`
- **Entity**: `Upload`
- **Count**: 2 uploads
- **Key Features**:
  - File metadata and type information
  - Upload tracking and management

### User and Role Seeding

#### Admin User
- **Email**: `<EMAIL>`
- **Password**: `AdminPass123!`
- **Role**: Admin
- **Features**: System administrator with full access

#### Tenant Users
- **Source**: Generated from TenantProfile data
- **Password**: `Test123!` (for all test users)
- **Role**: User
- **Features**: 
  - One user per tenant profile
  - Email derived from company name
  - Proper tenant isolation via TenantId

#### Roles
- **Admin Role**: Full system access
- **User Role**: Standard tenant user access

## CosmosDB Data Seeding

### CosmosDB Seeder
- **File**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/CosmosDbSeeder.cs`
- **Entry Point**: `SeedReportStylesAsync(IReportStyleService styleService, ILogger logger)`
- **Status**: Implemented but not yet integrated (commented out in DataSeeder, to avoid circular dependency issues)

### Seeded Documents

#### 1. Template Styles
- **Container**: `report-styles`
- **Count**: 1 template style
- **Document Type**: `ReportStyleDocument`
- **Key Features**:
  - TailwindCSS-based styling
  - Corporate blue theme
  - Responsive design templates
  - Component-specific styles

#### 2. Report Styles
- **Container**: `report-styles`
- **Count**: 6 report styles (one per report from old JSON data)
- **Document Type**: `ReportStyleDocument`
- **Key Features**:
  - Category-specific themes and colors:
    - Satisfaction Survey: Green theme (#10b981)
    - Performance Metrics: Blue theme (#3b82f6)
    - Action Plan: Orange theme (#f59e0b)
    - Channel Analysis: Purple theme (#8b5cf6)
    - User Experience: Red theme (#ef4444)
    - Product Improvement: Cyan theme (#06b6d4)
  - Professional HTML templates
  - Print-friendly styles
  - Mobile responsiveness

### Document Structure
- **Partition Key**: TenantId for multi-tenancy
- **Framework**: TailwindCSS 3.0
- **Components**: Header, metrics, charts, insights, footer
- **Metadata**: Theme, tags, custom properties

## Azure Blob Storage Seeding ✅

### Implementation
- **File**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/BlobStorageSeeder.cs`
- **Purpose**: Seed sample report data and assets from old mock data
- **Integration**: Called from `Program.cs` after CosmosDB seeding
- **Structure**:
  ```
  report-data/
  ├── {tenantId}/
  │   ├── reports/
  │   │   ├── {reportId}/
  │   │   │   ├── versions/
  │   │   │   │   ├── {versionId}/
  │   │   │   │   │   ├── data.json
  │   │   │   │   │   └── assets/
  │   │   │   │   │       ├── charts/
  │   │   │   │   │       └── images/
  ```

### Seeded Data Types
1. **Dashboard Metrics**: JSON files from old data (today, wtd, mtd, ytd) mapped to report categories
2. **Report Data**: Correlated data from old mock reports to current seeded reports
3. **Sample Assets**: Placeholder chart and image assets for testing
4. **Template Assets**: Default template resources

### Data Correlation Strategy
- **Old Mock Reports** → **Current Seeded Reports** by category mapping:
  - "Satisfaction Survey" → "Feedback" category reports
  - "Performance Metrics" → "Performance" category reports
  - "Action Plan" → "Strategy" category reports
  - "Channel Analysis" → "Usage" category reports
  - "User Experience" → "Usage" category reports
  - "Product Improvement" → "Performance" category reports

## Data Source Files

### JSON Seed Data Location
- **Base Path**: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/`
- **Files**:
  - `tenant-profiles.json` - Tenant organization data
  - `clients.json` - Client company information
  - `reports.json` - Report metadata
  - `invoices.json` - Billing and payment data
  - `forms.json` - Customer feedback forms
  - `templates.json` - Report template definitions
  - `uploads.json` - File upload metadata

### Legacy Data Source
- **File**: `memory-bank/old_json_data/src/data/db/db.json`
- **Purpose**: Source for CosmosDB and Blob Storage seeding
- **Contains**: Dashboard metrics, original report data, user information

## Seeding Process Flow

### 1. Application Startup
```
Program.cs → DataSeeder.SeedAsync()
```

### 2. SQL Server Seeding Order
1. TenantProfiles (first - required for FK relationships)
2. Clients (required for Reports FK)
3. Reports (depends on Clients)
4. Invoices, Forms, Templates, Uploads (independent)
5. Users and Roles (after tenant setup)

### 3. CosmosDB Seeding (When Enabled)
1. Template styles
2. Report styles (mapped from old JSON data)

### 4. Blob Storage Seeding (Planned)
1. Dashboard metrics JSON files
2. Generated chart images
3. Sample assets and templates

## Configuration and Dependencies

### Required Services
- `ApplicationDbContext` - SQL Server access
- `UserManager<ApplicationUser>` - Identity management
- `RoleManager<IdentityRole<Guid>>` - Role management
- `IMultiTenantStore<AppTenantInfo>` - Multi-tenancy
- `IReportStyleService` - CosmosDB access (when enabled)
- `IReportDataBlobService` - Blob Storage access (planned)

### Safety Features
- **Empty Table Checks**: Only seeds if tables are empty
- **Error Handling**: Continues on individual entity failures
- **Logging**: Comprehensive logging throughout process
- **FK Resolution**: Smart foreign key relationship handling

## Integration Status

### ✅ Implemented and Active
- SQL Server data seeding (all entities)
- User and role creation
- Multi-tenant user setup

### ✅ Implemented but Disabled
- CosmosDB style document seeding (commented out due to service registration)

### 🔄 Planned
- Blob Storage data and asset seeding
- Chart image generation
- Integration of CosmosDB seeding with main process

## Testing and Validation

### Verification Steps
1. Check table population after startup
2. Verify user login functionality
3. Confirm tenant isolation
4. Validate foreign key relationships
5. Test multi-tenancy features

### Logging Output
- Entity counts (added vs skipped)
- Error details for failed operations
- Service availability status
- Timing information for performance monitoring

## Future Enhancements

### Planned Improvements
1. **Dynamic Data Volume**: Configurable seed data quantities
2. **Environment-Specific Seeding**: Different data sets for dev/test/prod
3. **Incremental Seeding**: Update existing data without full reset
4. **Asset Generation**: Automated chart and image creation
5. **Data Validation**: Enhanced integrity checks and validation rules

### Performance Optimizations
1. **Batch Operations**: Bulk insert capabilities
2. **Parallel Seeding**: Concurrent seeding of independent entities
3. **Caching**: Reduce redundant database queries during seeding
4. **Progress Tracking**: Real-time seeding progress indicators
