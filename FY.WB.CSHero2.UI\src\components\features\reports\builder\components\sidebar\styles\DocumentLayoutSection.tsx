"use client";

import { DocumentLayoutOptions } from "../../../types";
import { FormSection } from "../../ui/FormSection";
import { FormField } from "../../ui/FormField";
import { getSectionValues } from "../../../utils/formUtils";
import { FieldConfig, SectionConfig } from "../../../config/styleFieldConfig";

// Import the document layout configuration
import { documentLayoutConfig } from "../../../config/layoutFields";

interface DocumentLayoutSectionProps {
  layout?: DocumentLayoutOptions;
  onUpdate: (field: string, value: any) => void;
}

export function DocumentLayoutSection({ layout = {}, onUpdate }: DocumentLayoutSectionProps) {
  const handleNestedUpdate = (category: string, field: string, value: any): void => {
    // Special handling for margins
    if (category === "pageSetup") {
      if (field === "pageSize") {
        // Update the page size
        onUpdate(field, value);
        return;
      } else if (field === "pageSizeDimensions") {
        // Update the custom page size dimensions
        onUpdate(field, value);
        return;
      } else if (field === "marginsPreset") {
        // Update the margins preset
        const margins = layout.margins || {};
        onUpdate("margins", { ...margins, preset: value });
        return;
      } else if (field.startsWith("margin")) {
        // Update individual margin values
        const margins = layout.margins || {};
        const marginField = field.replace("margin", "").toLowerCase();
        onUpdate("margins", { ...margins, [marginField]: value });
        return;
      }
    }
    
    // Special handling for title page elements
    if (category === "titlePage") {
      if (field.startsWith("title") && field !== "titlePage") {
        // Handle title properties
        const title = layout.titlePage?.title || {};
        const titleField = field.replace("title", "").toLowerCase();
        
        onUpdate("titlePage", {
          ...layout.titlePage,
          title: { ...title, [titleField]: value }
        });
        return;
      } else if (field.startsWith("subtitle")) {
        // Handle subtitle properties
        const subtitle = layout.titlePage?.subtitle || {};
        if (field === "subtitleShow") {
          onUpdate("titlePage", {
            ...layout.titlePage,
            subtitle: { ...subtitle, show: value }
          });
        } else {
          const subtitleField = field.replace("subtitle", "").toLowerCase();
          onUpdate("titlePage", {
            ...layout.titlePage,
            subtitle: { ...subtitle, [subtitleField]: value }
          });
        }
        return;
      } else if (field.startsWith("author")) {
        // Handle author properties
        const author = layout.titlePage?.author || {};
        if (field === "authorShow") {
          onUpdate("titlePage", {
            ...layout.titlePage,
            author: { ...author, show: value }
          });
        } else {
          const authorField = field.replace("author", "").toLowerCase();
          onUpdate("titlePage", {
            ...layout.titlePage,
            author: { ...author, [authorField]: value }
          });
        }
        return;
      } else if (field.startsWith("date")) {
        // Handle date properties
        const date = layout.titlePage?.date || {};
        if (field === "dateShow") {
          onUpdate("titlePage", {
            ...layout.titlePage,
            date: { ...date, show: value }
          });
        } else {
          const dateField = field.replace("date", "").toLowerCase();
          onUpdate("titlePage", {
            ...layout.titlePage,
            date: { ...date, [dateField]: value }
          });
        }
        return;
      }
    }
    
    // Special handling for header/footer
    if (category === "headerFooter") {
      if (field.startsWith("pageNumbering")) {
        // Handle page numbering properties
        const pageNumbering = layout.headerFooter?.pageNumbering || {};
        const pageNumberingField = field.replace("pageNumbering", "").toLowerCase();
        
        onUpdate("headerFooter", {
          ...layout.headerFooter,
          pageNumbering: { ...pageNumbering, [pageNumberingField]: value }
        });
        return;
      }
    }
    
    // Default handling for other fields
    onUpdate(field, value);
  };

  // Helper function to get values for a specific section
  const getValuesForSection = (sectionId: string): Record<string, any> => {
    if (sectionId === "pageSetup") {
      // Combine page setup fields with margins
      return {
        pageSize: layout.pageSize,
        pageSizeDimensions: layout.pageSizeDimensions,
        orientation: layout.orientation,
        marginsPreset: layout.margins?.preset,
        marginTop: layout.margins?.top,
        marginBottom: layout.margins?.bottom,
        marginLeft: layout.margins?.left,
        marginRight: layout.margins?.right,
        bleed: layout.bleed
      };
    } else if (sectionId === "titlePage") {
      // Combine title page fields
      return {
        titleFont: layout.titlePage?.title?.font,
        titleSize: layout.titlePage?.title?.size,
        titleAlignment: layout.titlePage?.title?.alignment,
        subtitleShow: layout.titlePage?.subtitle?.show,
        subtitleFont: layout.titlePage?.subtitle?.font,
        subtitleSize: layout.titlePage?.subtitle?.size,
        authorShow: layout.titlePage?.author?.show,
        authorFormat: layout.titlePage?.author?.format,
        dateShow: layout.titlePage?.date?.show,
        dateFormat: layout.titlePage?.date?.format,
        customDateFormat: layout.titlePage?.date?.customFormat
      };
    } else if (sectionId === "headerFooter") {
      // Combine header/footer fields
      return {
        headerContent: layout.headerFooter?.headerContent,
        customHeaderContent: layout.headerFooter?.customHeaderContent,
        footerContent: layout.headerFooter?.footerContent,
        customFooterContent: layout.headerFooter?.customFooterContent,
        pageNumberingStyle: layout.headerFooter?.pageNumbering?.style,
        pageNumberingPosition: layout.headerFooter?.pageNumbering?.position,
        pageNumberingFormat: layout.headerFooter?.pageNumbering?.format,
        firstPage: layout.headerFooter?.firstPage,
        sectionBreaks: layout.headerFooter?.sectionBreaks
      };
    }
    
    // For other sections, get values directly
    return getSectionValues(layout, sectionId);
  };

  return (
    <div className="space-y-4">
      {documentLayoutConfig.sections.map((section: SectionConfig) => {
        const sectionValues = getValuesForSection(section.id);
        
        return (
          <FormSection 
            key={section.id}
            title={section.title}
            defaultExpanded={section.defaultExpanded}
          >
            {section.fields.map((field: FieldConfig) => (
              <FormField
                key={field.name}
                field={field}
                value={sectionValues[field.name]}
                onChange={(value) => handleNestedUpdate(section.id, field.name, value)}
                parentValues={sectionValues}
              />
            ))}
          </FormSection>
        );
      })}
    </div>
  );
}
