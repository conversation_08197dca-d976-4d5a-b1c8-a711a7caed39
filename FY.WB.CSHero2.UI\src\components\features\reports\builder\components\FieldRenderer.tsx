"use client";

import { useState } from "react";
import { Upload, Table as TableI<PERSON>, Pencil, Wand2, ChevronDown, ChevronUp, Check } from "lucide-react";
import { Field, FieldRendererProps } from "../types";

export function FieldRenderer({ field, onFieldUpdate }: FieldRendererProps) {
  const [tableDialogOpen, setTableDialogOpen] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [nameValue, setNameValue] = useState(field.name);
  const [showDescription, setShowDescription] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState(field.description || '');
  const [tableData, setTableData] = useState<string[][]>(() => {
    // Try to parse the content as JSON if it exists
    if (field.content) {
      try {
        const parsedData = JSON.parse(field.content);
        if (Array.isArray(parsedData) && parsedData.every(row => Array.isArray(row))) {
          return parsedData;
        }
      } catch (e) {
        // If parsing fails, ignore and use default
        console.error('Failed to parse table data:', e);
      }
    }
    
    // Default initialization
    const rows = field.config?.rows || 3;
    const cols = field.config?.columns || 3;
    return Array(rows).fill(0).map(() => Array(cols).fill(''));
  });
  const [textContent, setTextContent] = useState(field.content || '');

  const handleTableCellChange = (rowIndex: number, colIndex: number, value: string) => {
    const newData = [...tableData];
    newData[rowIndex][colIndex] = value;
    setTableData(newData);
    
    if (onFieldUpdate) {
      onFieldUpdate({
        ...field,
        content: JSON.stringify(newData)
      });
    }
  };

  const handleTextChange = (value: string) => {
    setTextContent(value);
    
    if (onFieldUpdate) {
      onFieldUpdate({
        ...field,
        content: value
      });
    }
  };

  const handleNameSave = () => {
    if (onFieldUpdate && nameValue.trim()) {
      onFieldUpdate({
        ...field,
        name: nameValue
      });
    }
    setIsEditingName(false);
  };

  const toggleDescription = () => {
    setShowDescription(!showDescription);
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescriptionValue(e.target.value);
  };

  const saveDescription = () => {
    if (onFieldUpdate) {
      onFieldUpdate({
        ...field,
        description: descriptionValue
      });
    }
    setShowDescription(false);
  };

  const enhanceDescription = () => {
    // Here we'd typically call an AI enhancement function
    // For now, just add some placeholder text if empty
    if (!descriptionValue) {
      setDescriptionValue("This field contains important data that will be used to generate insights in the report.");
    }
  };

  // Render the field header with name editing and description toggle
  const FieldHeader = () => (
    <div className="mb-2">
      <div className="flex items-center justify-between">
        {isEditingName ? (
          <div className="flex items-center flex-1">
            <input
              type="text"
              value={nameValue}
              onChange={(e) => setNameValue(e.target.value)}
              onBlur={handleNameSave}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleNameSave();
                } else if (e.key === 'Escape') {
                  setIsEditingName(false);
                  setNameValue(field.name);
                }
              }}
              className="flex-1 p-1 border rounded mr-2"
              autoFocus
            />
            <button
              className="p-1 rounded hover:bg-gray-200"
              onClick={handleNameSave}
            >
              <Check className="h-4 w-4" />
            </button>
          </div>
        ) : (
          <>
            <div className="font-medium">{field.name}</div>
            <div className="flex items-center space-x-1">
              <button 
                className="p-1 rounded hover:bg-gray-200 text-gray-500"
                onClick={() => setIsEditingName(true)}
              >
                <Pencil className="h-4 w-4" />
              </button>
              <button 
                className="p-1 rounded hover:bg-gray-200 text-gray-500"
                onClick={toggleDescription}
              >
                {showDescription ? 
                  <ChevronUp className="h-4 w-4" /> : 
                  <ChevronDown className="h-4 w-4" />
                }
              </button>
            </div>
          </>
        )}
      </div>

      {showDescription && (
        <div className="mt-2 p-2 bg-gray-50 rounded border">
          <label className="block text-sm text-gray-600 mb-1">
            Description for AI
          </label>
          <textarea 
            className="w-full p-2 border rounded text-sm mb-2"
            rows={2}
            value={descriptionValue}
            onChange={handleDescriptionChange}
            placeholder="Describe this field for the AI that will generate the report..."
          />
          <div className="flex justify-end space-x-2">
            <button
              className="px-2 py-1 text-xs border rounded hover:bg-gray-50"
              onClick={() => setShowDescription(false)}
            >
              Cancel
            </button>
            <button
              className="px-2 py-1 text-xs border rounded hover:bg-gray-50 flex items-center"
              onClick={enhanceDescription}
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Enhance
            </button>
            <button
              className="px-2 py-1 text-xs bg-primary text-white rounded"
              onClick={saveDescription}
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );

  // Wrap the field content with the header
  const renderFieldWithHeader = (fieldContent: React.ReactNode) => (
    <div className="space-y-1">
      <FieldHeader />
      {fieldContent}
    </div>
  );

  // Main field rendering switch
  switch (field.type) {
    case 'String':
      return renderFieldWithHeader(
        <input 
          type="text" 
          className="w-full px-3 py-2 border rounded-md"
          placeholder={`Enter ${field.name}`}
          value={textContent}
          onChange={(e) => handleTextChange(e.target.value)}
        />
      );

    case 'Number':
      return renderFieldWithHeader(
        <input 
          type="number" 
          className="w-full px-3 py-2 border rounded-md"
          placeholder={`Enter ${field.name}`}
          value={textContent}
          onChange={(e) => handleTextChange(e.target.value)}
        />
      );

    case 'Date':
      return renderFieldWithHeader(
        <input 
          type="date" 
          className="w-full px-3 py-2 border rounded-md"
          value={textContent}
          onChange={(e) => handleTextChange(e.target.value)}
        />
      );

    case 'TextArea':
      return renderFieldWithHeader(
        <div className="border rounded-md cursor-pointer group relative" onClick={() => setTableDialogOpen(true)}>
          <div className="p-4 max-h-[200px] overflow-y-auto prose prose-sm max-w-none">
            {textContent ? (
              <div dangerouslySetInnerHTML={{ __html: textContent }} />
            ) : (
              <p className="text-gray-400 italic">Click to add content for {field.name}...</p>
            )}
          </div>

          {tableDialogOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
                <h3 className="text-lg font-medium mb-4">Edit {field.name}</h3>
                <textarea
                  className="w-full px-3 py-2 border rounded-md min-h-[300px]"
                  value={textContent}
                  onChange={(e) => handleTextChange(e.target.value)}
                  placeholder={`Enter ${field.name}...`}
                />
                <div className="flex justify-end mt-4">
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={() => setTableDialogOpen(false)}
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      );

    case 'Table':
      return renderFieldWithHeader(
        <>
          <div
            className="border rounded-md p-4 cursor-pointer hover:bg-gray-50"
            onClick={() => setTableDialogOpen(true)}
          >
            <div className="flex items-center justify-center">
              <TableIcon className="h-6 w-6 text-gray-400 mr-2" />
              <span>Click to edit table data ({field.config?.rows || 3}×{field.config?.columns || 3})</span>
            </div>
            <div className="mt-3 overflow-auto max-h-[150px]">
              <table className="w-full border-collapse">
                <tbody>
                  {tableData.slice(0, 3).map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.slice(0, 3).map((cell, colIndex) => (
                        <td key={colIndex} className="border p-1 text-sm truncate">
                          {cell || '—'}
                        </td>
                      ))}
                      {row.length > 3 && <td className="border p-1 text-sm text-gray-400">...</td>}
                    </tr>
                  ))}
                  {tableData.length > 3 && (
                    <tr>
                      {Array(Math.min(4, tableData[0]?.length || 0)).fill(0).map((_, i) => (
                        <td key={i} className="border p-1 text-sm text-gray-400 text-center">...</td>
                      ))}
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {tableDialogOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
                <h3 className="text-lg font-medium mb-4">Edit Table: {field.name}</h3>
                <div className="py-4 overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        <th className="border p-2 bg-gray-50"></th>
                        {Array(field.config?.columns || 3).fill(0).map((_, colIndex) => (
                          <th key={colIndex} className="border p-2 bg-gray-50">Column {colIndex + 1}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          <td className="border p-2 bg-gray-50 font-medium">Row {rowIndex + 1}</td>
                          {row.map((cell, colIndex) => (
                            <td key={colIndex} className="border p-1">
                              <input
                                type="text"
                                value={cell}
                                onChange={(e) => handleTableCellChange(rowIndex, colIndex, e.target.value)}
                                className="w-full px-2 py-1 border rounded-md min-w-[100px]"
                              />
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="flex justify-end mt-4">
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={() => setTableDialogOpen(false)}
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      );

    case 'Image':
      return renderFieldWithHeader(
        <div className="border-2 border-dashed rounded-md p-8 text-center">
          <div className="flex flex-col items-center">
            <Upload className="h-10 w-10 text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-2">Drag and drop an image here or click to select</p>
            <button className="px-4 py-2 border rounded-md hover:bg-gray-50">
              Select Image
            </button>
          </div>
        </div>
      );

    default:
      return renderFieldWithHeader(<div>Unknown field type</div>);
  }
}
