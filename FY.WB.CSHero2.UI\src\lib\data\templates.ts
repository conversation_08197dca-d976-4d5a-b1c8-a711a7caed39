// Template data and functions for the application
export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  slideCount: number;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface TemplateQueryOptions {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
}

// Mock template data for development
const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'Client Success Report',
    description: 'Comprehensive template for client success metrics and outcomes',
    category: 'Performance',
    slideCount: 12,
    thumbnail: '/templates/client-success.png',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    isActive: true
  },
  {
    id: '2',
    name: 'Service Analysis Template',
    description: 'Template for analyzing service delivery and quality metrics',
    category: 'Analysis',
    slideCount: 8,
    thumbnail: '/templates/service-analysis.png',
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    isActive: true
  },
  {
    id: '3',
    name: 'Monthly Progress Report',
    description: 'Standard template for monthly progress tracking',
    category: 'Progress',
    slideCount: 10,
    thumbnail: '/templates/monthly-progress.png',
    createdAt: '2024-02-01T09:15:00Z',
    updatedAt: '2024-02-01T09:15:00Z',
    isActive: true
  },
  {
    id: '4',
    name: 'Outcome Assessment',
    description: 'Template for comprehensive outcome assessments',
    category: 'Assessment',
    slideCount: 15,
    thumbnail: '/templates/outcome-assessment.png',
    createdAt: '2024-02-10T11:45:00Z',
    updatedAt: '2024-02-10T11:45:00Z',
    isActive: true
  }
];

/**
 * Get templates with optional filtering, sorting, and pagination
 */
export async function getTemplates(options: TemplateQueryOptions = {}): Promise<{
  items: Template[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  let filteredTemplates = [...mockTemplates];

  // Apply query filters
  if (options.query) {
    Object.entries(options.query).forEach(([key, value]) => {
      if (typeof value === 'string' && value) {
        filteredTemplates = filteredTemplates.filter(template => {
          const templateValue = (template as any)[key];
          if (typeof templateValue === 'string') {
            return templateValue.toLowerCase().includes(value.toLowerCase());
          }
          return false;
        });
      }
    });
  }

  // Apply sorting
  if (options.sortBy) {
    const sortKey = options.sortBy as keyof Template;
    const order = options.order || 'asc';
    
    filteredTemplates.sort((a, b) => {
      const aValue = a[sortKey];
      const bValue = b[sortKey];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return order === 'asc' ? comparison : -comparison;
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });
  }

  // Apply pagination
  const page = options.page || 1;
  const limit = options.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);
  const totalCount = filteredTemplates.length;
  const totalPages = Math.ceil(totalCount / limit);

  return {
    items: paginatedTemplates,
    totalCount,
    totalPages,
    currentPage: page
  };
}

/**
 * Create a new template
 */
export async function createTemplate(templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  const newTemplate: Template = {
    ...templateData,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // In a real implementation, this would save to the backend
  mockTemplates.push(newTemplate);

  return newTemplate;
}

/**
 * Get a single template by ID
 */
export async function getTemplateById(id: string): Promise<Template | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  const template = mockTemplates.find(t => t.id === id);
  return template || null;
}

/**
 * Update an existing template
 */
export async function updateTemplate(id: string, updates: Partial<Omit<Template, 'id' | 'createdAt'>>): Promise<Template | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  const templateIndex = mockTemplates.findIndex(t => t.id === id);
  if (templateIndex === -1) {
    return null;
  }

  const updatedTemplate: Template = {
    ...mockTemplates[templateIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  mockTemplates[templateIndex] = updatedTemplate;
  return updatedTemplate;
}

/**
 * Delete a template
 */
export async function deleteTemplate(id: string): Promise<boolean> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  const templateIndex = mockTemplates.findIndex(t => t.id === id);
  if (templateIndex === -1) {
    return false;
  }

  mockTemplates.splice(templateIndex, 1);
  return true;
}
