namespace FY.WB.CSHero2.Application.Clients.Dtos
{
    public class ClientQueryParametersDto
    {
        public string? SearchTerm { get; set; }
        public string? Status { get; set; } // e.g., "active", "inactive"
        public string? Industry { get; set; }
        
        public string? SortBy { get; set; } // e.g., "name", "createdAt"
        public string? SortOrder { get; set; } // "asc" or "desc"

        private int _page = 1;
        public int Page
        {
            get => _page;
            set => _page = (value < 1) ? 1 : value;
        }

        private int _pageSize = 10;
        private const int MaxPageSize = 100;
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : (value < 1) ? 1 : value;
        }
    }
}
