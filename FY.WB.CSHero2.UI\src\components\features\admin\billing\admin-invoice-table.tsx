'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Eye, 
  Download, 
  ArrowUpDown, 
  Search, 
  MoreHorizontal,
  FileEdit,
  Trash2,
  Receipt
} from 'lucide-react';
import { 
  generateInvoice, 
  generateInvoiceNumber, 
  downloadInvoice,
  InvoiceLineItem
} from '@/lib/generate-invoice';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/providers/toast-provider';
import { Invoice } from '@/types/invoice';
import { Tenant } from '@/types/tenant';
import { getInvoices, deleteInvoice } from '@/lib/api-invoices';
import { getTenantById, getAdminTenant, getSubscriptionByTypeAndBillingCycle } from '@/lib/api';

export function AdminInvoiceTable() {
  const { toast } = useToast();
  const [invoices, setInvoices] = useState<(Invoice & { tenantName?: string })[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<(Invoice & { tenantName?: string })[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  // Using string type to allow for 'tenantName' which is not in the Invoice type
  const [sortBy, setSortBy] = useState<string>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all invoices and populate tenant names
  useEffect(() => {
    const fetchInvoicesWithTenantNames = async () => {
      try {
        setLoading(true);
        
        // Get all invoices
        const { data: allInvoices } = await getInvoices();
        
        // Filter to last 3 months
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        
        const recentInvoices = allInvoices.filter((invoice: Invoice) => {
          const invoiceDate = new Date(invoice.date);
          return invoiceDate >= threeMonthsAgo;
        });
        
        // Create a map to store tenant data to avoid duplicate API calls
        const tenantMap = new Map<string, Tenant>();
        
        // Fetch tenant data for each invoice
        const invoicesWithTenantNames = await Promise.all(
          recentInvoices.map(async (invoice: Invoice) => {
            try {
              // Check if we already have this tenant's data
              if (!tenantMap.has(invoice.tenantId)) {
                const tenant = await getTenantById(invoice.tenantId);
                tenantMap.set(invoice.tenantId, tenant);
              }
              
              const tenant = tenantMap.get(invoice.tenantId);
              return {
                ...invoice,
                tenantName: tenant?.name || 'Unknown Tenant'
              };
            } catch (err) {
              console.error(`Error fetching tenant ${invoice.tenantId}:`, err);
              return {
                ...invoice,
                tenantName: 'Unknown Tenant'
              };
            }
          })
        );
        
        setInvoices(invoicesWithTenantNames);
        setFilteredInvoices(invoicesWithTenantNames);
        setTotalPages(Math.max(1, Math.ceil(invoicesWithTenantNames.length / limit)));
        setError(null);
      } catch (err) {
        console.error('Error fetching invoices:', err);
        setError('Failed to fetch invoices');
        toast({
          title: 'Error',
          description: 'Failed to fetch invoices',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvoicesWithTenantNames();
  }, [toast, limit]);

  // Update filtered invoices when search/sort changes
  useEffect(() => {
    let result = [...invoices];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item => 
        (item.tenantName?.toLowerCase().includes(query) || false) ||
        item.orderNumber.toLowerCase().includes(query) ||
        item.type.toLowerCase().includes(query) ||
        item.plans.toLowerCase().includes(query) ||
        item.amount.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    result.sort((a, b) => {
      if (sortBy === 'tenantName') {
        const aValue = a.tenantName || '';
        const bValue = b.tenantName || '';
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
      } else {
        const aValue = a[sortBy as keyof Invoice] || '';
        const bValue = b[sortBy as keyof Invoice] || '';
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
        return 0;
      }
    });
    
    // Calculate total pages
    setTotalPages(Math.max(1, Math.ceil(result.length / limit)));
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedResult = result.slice(startIndex, startIndex + limit);
    
    setFilteredInvoices(paginatedResult);
  }, [invoices, searchQuery, sortBy, sortOrder, page, limit]);

  const handleSort = (column: string) => {
    if (column === 'tenantName') {
      if (sortBy === 'tenantName') {
        setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
      } else {
        setSortBy('date'); // We can't directly set to tenantName as it's not a key of Invoice
        setSortOrder('desc');
      }
    } else if (invoices.length > 0 && column in invoices[0]) {
      if (sortBy === column) {
        setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
      } else {
        setSortBy(column);
        setSortOrder('desc');
      }
    }
  };

  const handleGenerateInvoice = async (invoice: Invoice & { tenantName?: string }) => {
    try {
      // Get the tenant data
      const tenant = await getTenantById(invoice.tenantId);
      
      // Get the admin tenant data
      const adminTenant = await getAdminTenant();
      
      if (!tenant || !adminTenant) {
        throw new Error('Failed to fetch tenant or admin data');
      }
      
      // Generate invoice data
      const today = new Date();
      const dueDate = new Date(today);
      dueDate.setDate(today.getDate() + 30); // Due in 30 days

      // Create subscription period dates
      const startDate = new Date(invoice.date);
      const endDate = new Date(startDate);
      
      // Set end date and due date based on billing cycle
      const billingCycle = tenant.billingCycle || 'annual';
      if (billingCycle === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
        dueDate.setMonth(dueDate.getMonth() + 1); // Due in 1 month
      } else if (billingCycle === 'quarterly') {
        endDate.setMonth(endDate.getMonth() + 3);
        dueDate.setMonth(dueDate.getMonth() + 3); // Due in 3 months
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1); // Default to annual
        dueDate.setFullYear(dueDate.getFullYear() + 1); // Due in 1 year
      }

      // Create line items based on invoice data
      const lineItems: InvoiceLineItem[] = [];
      
      // Try to get subscription data if available
      try {
        const subscriptionType = tenant.subscription || 'basic';
        
        // Use the subscription data from the API
        const subscriptionResponse = await getSubscriptionByTypeAndBillingCycle(
          subscriptionType as 'basic' | 'professional' | 'enterprise',
          billingCycle as 'monthly' | 'quarterly' | 'annual'
        );
        
        if (subscriptionResponse) {
          // Add subscription line item with data from the API
          lineItems.push({
            description: subscriptionResponse.name,
            quantity: 1,
            unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          });
        } else {
          // Fallback to invoice data if subscription not found
          lineItems.push({
            description: invoice.plans,
            quantity: 1,
            unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          });
        }
      } catch (error) {
        // Fallback to invoice data if there's an error
        console.error('Error fetching subscription data:', error);
        lineItems.push({
          description: invoice.plans,
          quantity: 1,
          unitPrice: parseFloat(invoice.amount.replace(/[^0-9.-]+/g, '')),
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        });
      }
      
      // Generate invoice number (use the existing order number)
      const invoiceNumber = invoice.orderNumber;

      // Generate the invoice HTML
      const { html } = generateInvoice({
        tenant,
        adminTenant,
        invoiceNumber,
        issueDate: invoice.date,
        dueDate: dueDate.toISOString(),
        lineItems,
        discount: undefined
      });

      // Download the invoice as PDF
      downloadInvoice(html, `invoice-${invoiceNumber}.pdf`);

      toast({
        title: 'Success',
        description: 'PDF Invoice generated and downloaded successfully',
      });
    } catch (error) {
      console.error('Error generating invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF invoice',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteInvoice = async (id: string) => {
    try {
      await deleteInvoice(id);
      
      // Update the local state
      const updatedInvoices = invoices.filter(invoice => invoice.id !== id);
      setInvoices(updatedInvoices);
      
      toast({
        title: 'Success',
        description: 'Invoice deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete invoice',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">All Tenant Invoices (Last 3 Months)</h2>
        <div className="flex items-center justify-center h-32">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">All Tenant Invoices (Last 3 Months)</h2>
        <div className="p-4 text-red-600">
          {error}
        </div>
      </Card>
    );
  }

  return (
    <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-[rgb(var(--primary))]">All Tenant Invoices (Last 3 Months)</h2>
      </div>
      
      <div className="flex gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
          <Input
            placeholder="Search invoices..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      <div className="rounded-lg border bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-muted/5 transition-colors">
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('date')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Date
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('tenantName')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Tenant
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('type')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Type
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <Button 
                  variant="ghost" 
                  onClick={() => handleSort('status')}
                  className="flex items-center gap-1 hover:bg-primary/5 hover:text-primary transition-colors"
                >
                  Status
                  <ArrowUpDown className="ml-2 h-4 w-4 text-muted-foreground/70" />
                </Button>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">Order Number</div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">Description</div>
              </TableHead>
              <TableHead className="text-right">
                <div className="flex justify-end items-center gap-1 ml-auto">Amount</div>
              </TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredInvoices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <p className="text-muted-foreground">No invoices found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredInvoices.map((item) => (
                <TableRow 
                  key={item.id}
                  className="hover:bg-primary/5 hover:shadow-sm cursor-pointer transition-all duration-200"
                >
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{item.date}</div>
                  </TableCell>
                  <TableCell>
                    <Link 
                      href={`/admin/tenants/${item.tenantId}`}
                      className="text-sm font-medium text-primary hover:text-primary/80 hover:underline transition-colors"
                    >
                      {item.tenantName}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.type === 'Invoice' 
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20' 
                        : item.type === 'Payment'
                        ? 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20'
                        : item.type === 'Refund'
                        ? 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20'
                        : 'bg-gradient-to-r from-purple-50 to-purple-100/50 text-purple-700 ring-1 ring-purple-600/20'
                    }`}>
                      {item.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.status === 'paid' 
                        ? 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20' 
                        : item.status === 'pending'
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20'
                        : item.status === 'overdue'
                        ? 'bg-gradient-to-r from-red-50 to-red-100/50 text-red-700 ring-1 ring-red-600/20'
                        : 'bg-gradient-to-r from-gray-50 to-gray-100/50 text-gray-700 ring-1 ring-gray-600/20'
                    }`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{item.orderNumber}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-muted-foreground">{item.plans}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">{item.amount}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-primary/5 hover:text-primary transition-colors">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem 
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={() => handleGenerateInvoice(item)}
                        >
                          <Eye className="h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={() => handleGenerateInvoice(item)}
                        >
                          <Download className="h-4 w-4" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="flex items-center gap-2 text-red-600 cursor-pointer"
                          onClick={() => handleDeleteInvoice(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-4 py-4 border-t bg-muted/5">
          <p className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
              className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors disabled:hover:bg-transparent"
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
