using System;

namespace FY.WB.CSHero2.Application.Invoices.Dtos
{
    public class InvoiceQueryParametersDto
    {
        public string? Search { get; set; }
        public string? Type { get; set; } // Filter by "Invoice", "Payment", "Refund"
        public string? Status { get; set; } // Filter by "paid", "overdue", etc.
        public string? SortBy { get; set; } // e.g., "Date", "Amount", "OrderNumber"
        public string? SortOrder { get; set; } // "asc" or "desc"
        public int? Page { get; set; } = 1;
        public int? Limit { get; set; } = 10;
    }
}
