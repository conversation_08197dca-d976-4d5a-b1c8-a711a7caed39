"use client";

import { useState } from "react";
import Image from "next/image";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  <PERSON><PERSON>Header, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  Mail, 
  Link, 
  Copy, 
  Check, 
  Clock, 
  Calendar, 
  Trash2, 
  Plus, 
  Search 
} from "lucide-react";

// Define permission types
export type PermissionLevel = "view" | "edit" | "comment" | "admin";

// Define user/team tenant type
export interface TeamTenant {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
}

// Define sharing permission type
export interface SharingPermission {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  permissionLevel: PermissionLevel;
  addedAt: Date;
  expiresAt?: Date;
}

// Define email sharing type
export interface EmailShare {
  id: string;
  email: string;
  permissionLevel: PermissionLevel;
  message?: string;
  sentAt: Date;
  expiresAt?: Date;
  viewed: boolean;
}

// Define link sharing type
export interface LinkShare {
  id: string;
  url: string;
  permissionLevel: PermissionLevel;
  createdAt: Date;
  expiresAt?: Date;
  accessCount: number;
  active: boolean;
}

// Define props for the share modal
interface ShareModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reportId: string;
  reportName: string;
  onShare?: (data: {
    permissions: SharingPermission[];
    emails: EmailShare[];
    link?: LinkShare;
  }) => void;
  initialPermissions?: SharingPermission[];
  initialEmails?: EmailShare[];
  initialLink?: LinkShare;
  teamTenants?: TeamTenant[];
}

export function ShareModal({
  open,
  onOpenChange,
  reportId,
  reportName,
  onShare,
  initialPermissions = [],
  initialEmails = [],
  initialLink,
  teamTenants = []
}: ShareModalProps) {
  // State for the active tab
  const [activeTab, setActiveTab] = useState<string>("people");
  
  // State for permissions
  const [permissions, setPermissions] = useState<SharingPermission[]>(initialPermissions);
  
  // State for email sharing
  const [emails, setEmails] = useState<EmailShare[]>(initialEmails);
  const [newEmail, setNewEmail] = useState<string>("");
  const [emailPermission, setEmailPermission] = useState<PermissionLevel>("view");
  const [emailMessage, setEmailMessage] = useState<string>("");
  
  // State for link sharing
  const [linkSharing, setLinkSharing] = useState<boolean>(!!initialLink);
  const [linkPermission, setLinkPermission] = useState<PermissionLevel>(initialLink?.permissionLevel || "view");
  const [linkExpiration, setLinkExpiration] = useState<string>(initialLink?.expiresAt ? "custom" : "never");
  const [linkExpirationDate, setLinkExpirationDate] = useState<string>(
    initialLink?.expiresAt 
      ? new Date(initialLink.expiresAt).toISOString().split("T")[0]
      : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
  );
  const [linkCopied, setLinkCopied] = useState<boolean>(false);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedTenant, setSelectedTenant] = useState<string>("");
  const [tenantPermission, setTenantPermission] = useState<PermissionLevel>("view");
  
  // Filter team tenants based on search query
  const filteredTenants = teamTenants.filter(tenant => 
    tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tenant.email.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Filter out tenants who already have permissions
  const availableTenants = filteredTenants.filter(tenant => 
    !permissions.some(p => p.userId === tenant.id)
  );
  
  // Handle adding a new permission
  const handleAddPermission = () => {
    if (!selectedTenant) return;
    
    const tenant = teamTenants.find(m => m.id === selectedTenant);
    if (!tenant) return;
    
    const newPermission: SharingPermission = {
      id: `perm-${Date.now()}`,
      userId: tenant.id,
      userName: tenant.name,
      userEmail: tenant.email,
      userAvatar: tenant.avatar,
      permissionLevel: tenantPermission,
      addedAt: new Date()
    };
    
    setPermissions([...permissions, newPermission]);
    setSelectedTenant("");
    setSearchQuery("");
  };
  
  // Handle removing a permission
  const handleRemovePermission = (id: string) => {
    setPermissions(permissions.filter(p => p.id !== id));
  };
  
  // Handle changing a permission level
  const handleChangePermissionLevel = (id: string, level: PermissionLevel) => {
    setPermissions(permissions.map(p => 
      p.id === id ? { ...p, permissionLevel: level } : p
    ));
  };
  
  // Handle adding an email share
  const handleAddEmailShare = () => {
    if (!newEmail || !newEmail.includes("@")) return;
    
    const newEmailShare: EmailShare = {
      id: `email-${Date.now()}`,
      email: newEmail,
      permissionLevel: emailPermission,
      message: emailMessage,
      sentAt: new Date(),
      viewed: false
    };
    
    setEmails([...emails, newEmailShare]);
    setNewEmail("");
    setEmailMessage("");
  };
  
  // Handle removing an email share
  const handleRemoveEmailShare = (id: string) => {
    setEmails(emails.filter(e => e.id !== id));
  };
  
  // Generate a share link
  const generateShareLink = (): string => {
    return `https://app.example.com/shared/report/${reportId}?token=sample-token-${Date.now()}`;
  };
  
  // Handle copying the link
  const handleCopyLink = () => {
    navigator.clipboard.writeText(generateShareLink());
    setLinkCopied(true);
    setTimeout(() => setLinkCopied(false), 2000);
  };
  
  // Handle saving all sharing settings
  const handleSave = () => {
    // Create link share object if link sharing is enabled
    let linkShare: LinkShare | undefined;
    if (linkSharing) {
      linkShare = {
        id: initialLink?.id || `link-${Date.now()}`,
        url: generateShareLink(),
        permissionLevel: linkPermission,
        createdAt: initialLink?.createdAt || new Date(),
        expiresAt: linkExpiration === "never" ? undefined : new Date(linkExpirationDate),
        accessCount: initialLink?.accessCount || 0,
        active: true
      };
    }
    
    // Call the onShare callback with all sharing data
    if (onShare) {
      onShare({
        permissions,
        emails,
        link: linkShare
      });
    }
    
    onOpenChange(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Share "{reportName}"</DialogTitle>
          <DialogDescription>
            Share this report with team tenants or external users
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="people" className="flex items-center">
              <Users className="w-4 h-4 mr-2" />
              People
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="w-4 h-4 mr-2" />
              Email
            </TabsTrigger>
            <TabsTrigger value="link" className="flex items-center">
              <Link className="w-4 h-4 mr-2" />
              Link
            </TabsTrigger>
          </TabsList>
          
          {/* People Tab */}
          <TabsContent value="people" className="space-y-4">
            <div className="flex space-x-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
                <Input
                  placeholder="Search team tenants..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={tenantPermission} onValueChange={(value) => setTenantPermission(value as PermissionLevel)}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Permission" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="view">Can view</SelectItem>
                  <SelectItem value="comment">Can comment</SelectItem>
                  <SelectItem value="edit">Can edit</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleAddPermission} disabled={!selectedTenant}>
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
            
            {searchQuery && availableTenants.length > 0 && (
              <div className="border rounded-md max-h-[200px] overflow-y-auto">
                {availableTenants.map(tenant => (
                  <div
                    key={tenant.id}
                    className="p-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                    onClick={() => setSelectedTenant(tenant.id)}
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mr-2">
                          {tenant.avatar ? (
                            <div className="relative w-8 h-8 rounded-full overflow-hidden">
                              <Image 
                                src={tenant.avatar} 
                                alt={tenant.name} 
                                className="object-cover"
                                fill
                                sizes="32px"
                              />
                            </div>
                          ) : (
                            tenant.name.charAt(0)
                          )}
                      </div>
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-sm text-gray-500">{tenant.email}</div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border ${selectedTenant === tenant.id ? 'bg-primary border-primary' : 'border-gray-300'}`} />
                  </div>
                ))}
              </div>
            )}
            
            {permissions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Permission</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {permissions.map(permission => (
                    <TableRow key={permission.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mr-2">
                            {permission.userAvatar ? (
                              <div className="relative w-8 h-8 rounded-full overflow-hidden">
                                <Image 
                                  src={permission.userAvatar} 
                                  alt={permission.userName} 
                                  className="object-cover"
                                  fill
                                  sizes="32px"
                                />
                              </div>
                            ) : (
                              permission.userName.charAt(0)
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{permission.userName}</div>
                            <div className="text-sm text-gray-500">{permission.userEmail}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select 
                          value={permission.permissionLevel} 
                          onValueChange={(value) => handleChangePermissionLevel(permission.id, value as PermissionLevel)}
                        >
                          <SelectTrigger className="w-[120px]">
                            <SelectValue placeholder="Permission" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="view">Can view</SelectItem>
                            <SelectItem value="comment">Can comment</SelectItem>
                            <SelectItem value="edit">Can edit</SelectItem>
                            <SelectItem value="admin">Admin</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                          onClick={() => handleRemovePermission(permission.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <Users className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p>No team tenants added yet</p>
                <p className="text-sm">Search and add team tenants to share this report</p>
              </div>
            )}
          </TabsContent>
          
          {/* Email Tab */}
          <TabsContent value="email" className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    placeholder="<EMAIL>"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email-permission">Permission</Label>
                  <Select value={emailPermission} onValueChange={(value) => setEmailPermission(value as PermissionLevel)}>
                    <SelectTrigger id="email-permission">
                      <SelectValue placeholder="Permission" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="view">Can view</SelectItem>
                      <SelectItem value="comment">Can comment</SelectItem>
                      <SelectItem value="edit">Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Message (optional)</Label>
                <textarea
                  id="message"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary min-h-[100px]"
                  placeholder="Add a message to the email..."
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                />
              </div>
              
              <Button onClick={handleAddEmailShare} disabled={!newEmail || !newEmail.includes("@")}>
                <Mail className="w-4 h-4 mr-1" />
                Send Email Invitation
              </Button>
            </div>
            
            {emails.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Permission</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {emails.map(email => (
                    <TableRow key={email.id}>
                      <TableCell>{email.email}</TableCell>
                      <TableCell>
                        <span className="capitalize">{email.permissionLevel}</span>
                      </TableCell>
                      <TableCell>
                        {email.viewed ? (
                          <span className="text-green-500 flex items-center">
                            <Check className="w-4 h-4 mr-1" />
                            Viewed
                          </span>
                        ) : (
                          <span className="text-gray-500 flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            Pending
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                          onClick={() => handleRemoveEmailShare(email.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <Mail className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p>No email invitations sent yet</p>
                <p className="text-sm">Add email addresses to share this report</p>
              </div>
            )}
          </TabsContent>
          
          {/* Link Tab */}
          <TabsContent value="link" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={linkSharing}
                onCheckedChange={setLinkSharing}
                id="link-sharing"
              />
              <Label htmlFor="link-sharing">Enable link sharing</Label>
            </div>
            
            {linkSharing && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="link-permission">Permission</Label>
                  <Select value={linkPermission} onValueChange={(value) => setLinkPermission(value as PermissionLevel)}>
                    <SelectTrigger id="link-permission">
                      <SelectValue placeholder="Permission" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="view">Can view</SelectItem>
                      <SelectItem value="comment">Can comment</SelectItem>
                      <SelectItem value="edit">Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="link-expiration">Link Expiration</Label>
                  <Select value={linkExpiration} onValueChange={setLinkExpiration}>
                    <SelectTrigger id="link-expiration">
                      <SelectValue placeholder="Expiration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="never">Never expires</SelectItem>
                      <SelectItem value="1day">1 day</SelectItem>
                      <SelectItem value="7days">7 days</SelectItem>
                      <SelectItem value="30days">30 days</SelectItem>
                      <SelectItem value="custom">Custom date</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {linkExpiration === "custom" && (
                  <div className="space-y-2">
                    <Label htmlFor="expiration-date">Expiration Date</Label>
                    <Input
                      id="expiration-date"
                      type="date"
                      value={linkExpirationDate}
                      onChange={(e) => setLinkExpirationDate(e.target.value)}
                      min={new Date().toISOString().split("T")[0]}
                    />
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="share-link">Share Link</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="share-link"
                      value={generateShareLink()}
                      readOnly
                    />
                    <Button onClick={handleCopyLink}>
                      {linkCopied ? (
                        <Check className="w-4 h-4 mr-1" />
                      ) : (
                        <Copy className="w-4 h-4 mr-1" />
                      )}
                      {linkCopied ? "Copied" : "Copy"}
                    </Button>
                  </div>
                </div>
                
                {initialLink && (
                  <div className="text-sm text-gray-500 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Created on {new Date(initialLink.createdAt).toLocaleDateString()}
                    {initialLink.accessCount > 0 && (
                      <span className="ml-4">
                        Accessed {initialLink.accessCount} times
                      </span>
                    )}
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Sharing Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
