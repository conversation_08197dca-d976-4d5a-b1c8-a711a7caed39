# Database Schema Changes for Multi-Storage Report Structure

## Overview

This document outlines the database schema changes required to implement the multi-storage approach for the report structure. The changes involve modifying existing tables and creating new tables to support the new architecture.

## Current Schema

The current schema stores all report data in SQL tables:

### Reports Table

```sql
CREATE TABLE [dbo].[Reports] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [TenantId] UNIQUEIDENTIFIER NULL,
    [CreatorId] UNIQUEIDENTIFIER NULL,
    [CreatedAt] DATETIME2 NOT NULL,
    [LastModifiedAt] DATETIME2 NULL,
    [LastModifiedById] UNIQUEIDENTIFIER NULL,
    [IsPublished] BIT NOT NULL DEFAULT 0,
    [IsArchived] BIT NOT NULL DEFAULT 0,
    CONSTRAINT [FK_Reports_TenantProfiles_TenantId] FOREIGN KEY ([TenantId]) REFERENCES [dbo].[TenantProfiles] ([Id]),
    CONSTRAINT [FK_Reports_AspNetUsers_CreatorId] FOREIGN KEY ([CreatorId]) REFERENCES [dbo].[AspNetUsers] ([Id]),
    CONSTRAINT [FK_Reports_AspNetUsers_LastModifiedById] FOREIGN KEY ([LastModifiedById]) REFERENCES [dbo].[AspNetUsers] ([Id])
);
```

### ReportVersions Table

```sql
CREATE TABLE [dbo].[ReportVersions] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY,
    [ReportId] UNIQUEIDENTIFIER NOT NULL,
    [VersionNumber] INT NOT NULL,
    [VersionName] NVARCHAR(100) NULL,
    [CreatorId] UNIQUEIDENTIFIER NULL,
    [CreatedAt] DATETIME2 NOT NULL,
    [IsCurrent] BIT NOT NULL DEFAULT 0,
    [IsPublished] BIT NOT NULL DEFAULT 0,
    [JsonData] NVARCHAR(MAX) NULL,
    CONSTRAINT [FK_ReportVersions_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [dbo].[Reports] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_ReportVersions_AspNetUsers_CreatorId] FOREIGN KEY ([CreatorId]) REFERENCES [dbo].[AspNetUsers] ([Id])
);
```

### ComponentDefinitions Table

```sql
CREATE TABLE [dbo].[ComponentDefinitions] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY,
    [ReportVersionId] UNIQUEIDENTIFIER NOT NULL,
    [SectionId] NVARCHAR(100) NULL,
    [SectionName] NVARCHAR(100) NULL,
    [ComponentCode] NVARCHAR(MAX) NULL,
    [ImportsJson] NVARCHAR(MAX) NULL,
    CONSTRAINT [FK_ComponentDefinitions_ReportVersions_ReportVersionId] FOREIGN KEY ([ReportVersionId]) REFERENCES [dbo].[ReportVersions] ([Id]) ON DELETE CASCADE
);
```

## Schema Changes

### 1. Modify Reports Table

Add columns to store references to external storage:

```sql
ALTER TABLE [dbo].[Reports]
ADD [DataDocumentId] NVARCHAR(100) NULL,
    [ComponentsBlobId] NVARCHAR(255) NULL;
```

### 2. Modify ReportVersions Table

Add columns to store references to external storage and remove the JSON data column:

```sql
ALTER TABLE [dbo].[ReportVersions]
ADD [DataDocumentId] NVARCHAR(100) NULL,
    [ComponentsBlobId] NVARCHAR(255) NULL,
    [NeedsMigration] BIT NOT NULL DEFAULT 0;
```

In a separate migration (after data migration is complete):

```sql
ALTER TABLE [dbo].[ReportVersions]
DROP COLUMN [JsonData];
```

### 3. Create ReportStyles Table

Create a new table to store report style information:

```sql
CREATE TABLE [dbo].[ReportStyles] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY,
    [ReportId] UNIQUEIDENTIFIER NOT NULL,
    [Theme] NVARCHAR(50) NULL,
    [ColorScheme] NVARCHAR(50) NULL,
    [Typography] NVARCHAR(50) NULL,
    [Spacing] NVARCHAR(50) NULL,
    [LayoutOptionsJson] NVARCHAR(MAX) NULL,
    [TypographyOptionsJson] NVARCHAR(MAX) NULL,
    [StructureOptionsJson] NVARCHAR(MAX) NULL,
    [ContentOptionsJson] NVARCHAR(MAX) NULL,
    [VisualOptionsJson] NVARCHAR(MAX) NULL,
    CONSTRAINT [FK_ReportStyles_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [dbo].[Reports] ([Id]) ON DELETE CASCADE
);

CREATE UNIQUE INDEX [IX_ReportStyles_ReportId] ON [dbo].[ReportStyles] ([ReportId]);
```

### 4. Modify ComponentDefinitions Table

In a separate migration (after data migration is complete):

```sql
DROP TABLE [dbo].[ComponentDefinitions];
```

## Entity Framework Core Configuration

### 1. ReportConfiguration

```csharp
public class ReportConfiguration : IEntityTypeConfiguration<Report>
{
    public void Configure(EntityTypeBuilder<Report> builder)
    {
        builder.ToTable("Reports");
        builder.HasKey(r => r.Id);
        
        builder.Property(r => r.Name).HasMaxLength(100).IsRequired();
        builder.Property(r => r.Description).HasMaxLength(500);
        builder.Property(r => r.CreatedAt).IsRequired();
        builder.Property(r => r.IsPublished).IsRequired().HasDefaultValue(false);
        builder.Property(r => r.IsArchived).IsRequired().HasDefaultValue(false);
        
        // New properties for multi-storage
        builder.Property(r => r.DataDocumentId).HasMaxLength(100);
        builder.Property(r => r.ComponentsBlobId).HasMaxLength(255);
        
        // Relationships
        builder.HasOne(r => r.Tenant)
            .WithMany()
            .HasForeignKey(r => r.TenantId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasOne(r => r.Creator)
            .WithMany()
            .HasForeignKey(r => r.CreatorId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasOne(r => r.LastModifiedBy)
            .WithMany()
            .HasForeignKey(r => r.LastModifiedById)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasMany(r => r.Versions)
            .WithOne(v => v.Report)
            .HasForeignKey(v => v.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(r => r.Style)
            .WithOne()
            .HasForeignKey<ReportStyle>(s => s.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
```

### 2. ReportVersionConfiguration

```csharp
public class ReportVersionConfiguration : IEntityTypeConfiguration<ReportVersion>
{
    public void Configure(EntityTypeBuilder<ReportVersion> builder)
    {
        builder.ToTable("ReportVersions");
        builder.HasKey(v => v.Id);
        
        builder.Property(v => v.VersionNumber).IsRequired();
        builder.Property(v => v.VersionName).HasMaxLength(100);
        builder.Property(v => v.CreatedAt).IsRequired();
        builder.Property(v => v.IsCurrent).IsRequired().HasDefaultValue(false);
        builder.Property(v => v.IsPublished).IsRequired().HasDefaultValue(false);
        
        // New properties for multi-storage
        builder.Property(v => v.DataDocumentId).HasMaxLength(100);
        builder.Property(v => v.ComponentsBlobId).HasMaxLength(255);
        builder.Property(v => v.NeedsMigration).IsRequired().HasDefaultValue(false);
        
        // Relationships
        builder.HasOne(v => v.Report)
            .WithMany(r => r.Versions)
            .HasForeignKey(v => v.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(v => v.Creator)
            .WithMany()
            .HasForeignKey(v => v.CreatorId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
```

### 3. ReportStyleConfiguration

```csharp
public class ReportStyleConfiguration : IEntityTypeConfiguration<ReportStyle>
{
    public void Configure(EntityTypeBuilder<ReportStyle> builder)
    {
        builder.ToTable("ReportStyles");
        builder.HasKey(s => s.Id);
        
        builder.Property(s => s.ReportId).IsRequired();
        builder.Property(s => s.Theme).HasMaxLength(50);
        builder.Property(s => s.ColorScheme).HasMaxLength(50);
        builder.Property(s => s.Typography).HasMaxLength(50);
        builder.Property(s => s.Spacing).HasMaxLength(50);
        
        builder.Property(s => s.LayoutOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.TypographyOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.StructureOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.ContentOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.VisualOptionsJson).HasColumnType("nvarchar(max)");
        
        // Relationships
        builder.HasOne(s => s.Report)
            .WithOne(r => r.Style)
            .HasForeignKey<ReportStyle>(s => s.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(s => s.ReportId)
            .IsUnique()
            .HasDatabaseName("IX_ReportStyles_ReportId");
    }
}
```

## Migration Scripts

### 1. Initial Schema Changes

```csharp
public partial class AddMultiStorageSupport : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Add columns to Reports table
        migrationBuilder.AddColumn<string>(
            name: "DataDocumentId",
            table: "Reports",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);
            
        migrationBuilder.AddColumn<string>(
            name: "ComponentsBlobId",
            table: "Reports",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
            
        // Add columns to ReportVersions table
        migrationBuilder.AddColumn<string>(
            name: "DataDocumentId",
            table: "ReportVersions",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);
            
        migrationBuilder.AddColumn<string>(
            name: "ComponentsBlobId",
            table: "ReportVersions",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
            
        migrationBuilder.AddColumn<bool>(
            name: "NeedsMigration",
            table: "ReportVersions",
            type: "bit",
            nullable: false,
            defaultValue: false);
            
        // Create ReportStyles table
        migrationBuilder.CreateTable(
            name: "ReportStyles",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Theme = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                ColorScheme = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                Typography = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                Spacing = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                LayoutOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                TypographyOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                StructureOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                ContentOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                VisualOptionsJson = table.Column<string>(type: "nvarchar(max)", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ReportStyles", x => x.Id);
                table.ForeignKey(
                    name: "FK_ReportStyles_Reports_ReportId",
                    column: x => x.ReportId,
                    principalTable: "Reports",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportStyles_ReportId",
            table: "ReportStyles",
            column: "ReportId",
            unique: true);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // Drop ReportStyles table
        migrationBuilder.DropTable(
            name: "ReportStyles");
            
        // Remove columns from ReportVersions table
        migrationBuilder.DropColumn(
            name: "DataDocumentId",
            table: "ReportVersions");
            
        migrationBuilder.DropColumn(
            name: "ComponentsBlobId",
            table: "ReportVersions");
            
        migrationBuilder.DropColumn(
            name: "NeedsMigration",
            table: "ReportVersions");
            
        // Remove columns from Reports table
        migrationBuilder.DropColumn(
            name: "DataDocumentId",
            table: "Reports");
            
        migrationBuilder.DropColumn(
            name: "ComponentsBlobId",
            table: "Reports");
    }
}
```

### 2. Post-Migration Cleanup

```csharp
public partial class RemoveOldReportStructure : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Drop ComponentDefinitions table
        migrationBuilder.DropTable(
            name: "ComponentDefinitions");
            
        // Remove JsonData column from ReportVersions table
        migrationBuilder.DropColumn(
            name: "JsonData",
            table: "ReportVersions");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // Add JsonData column back to ReportVersions table
        migrationBuilder.AddColumn<string>(
            name: "JsonData",
            table: "ReportVersions",
            type: "nvarchar(max)",
            nullable: true);
            
        // Recreate ComponentDefinitions table
        migrationBuilder.CreateTable(
            name: "ComponentDefinitions",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ReportVersionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                SectionId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                SectionName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                ComponentCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                ImportsJson = table.Column<string>(type: "nvarchar(max)", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ComponentDefinitions", x => x.Id);
                table.ForeignKey(
                    name: "FK_ComponentDefinitions_ReportVersions_ReportVersionId",
                    column: x => x.ReportVersionId,
                    principalTable: "ReportVersions",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });
            
        migrationBuilder.CreateIndex(
            name: "IX_ComponentDefinitions_ReportVersionId",
            table: "ComponentDefinitions",
            column: "ReportVersionId");
    }
}
```

## Data Migration SQL Script

This script can be used to create initial ReportStyle records for existing reports:

```sql
-- Create ReportStyle records for existing reports
INSERT INTO [dbo].[ReportStyles] (
    [Id],
    [ReportId],
    [Theme],
    [ColorScheme],
    [Typography],
    [Spacing]
)
SELECT 
    NEWID() AS [Id],
    [Id] AS [ReportId],
    'default' AS [Theme],
    'default' AS [ColorScheme],
    'default' AS [Typography],
    'default' AS [Spacing]
FROM [dbo].[Reports] r
WHERE NOT EXISTS (
    SELECT 1 FROM [dbo].[ReportStyles] s WHERE s.[ReportId] = r.[Id]
);
```

## Indexes

To optimize query performance, the following indexes should be created:

```sql
-- Index for querying reports by tenant
CREATE INDEX [IX_Reports_TenantId] ON [dbo].[Reports] ([TenantId]);

-- Index for querying reports by creator
CREATE INDEX [IX_Reports_CreatorId] ON [dbo].[Reports] ([CreatorId]);

-- Index for querying report versions by report
CREATE INDEX [IX_ReportVersions_ReportId] ON [dbo].[ReportVersions] ([ReportId]);

-- Index for querying current versions
CREATE INDEX [IX_ReportVersions_ReportId_IsCurrent] ON [dbo].[ReportVersions] ([ReportId], [IsCurrent]);

-- Index for querying published versions
CREATE INDEX [IX_ReportVersions_ReportId_IsPublished] ON [dbo].[ReportVersions] ([ReportId], [IsPublished]);

-- Index for querying reports by external storage references
CREATE INDEX [IX_Reports_DataDocumentId] ON [dbo].[Reports] ([DataDocumentId]);
CREATE INDEX [IX_Reports_ComponentsBlobId] ON [dbo].[Reports] ([ComponentsBlobId]);

-- Index for querying report versions by external storage references
CREATE INDEX [IX_ReportVersions_DataDocumentId] ON [dbo].[ReportVersions] ([DataDocumentId]);
CREATE INDEX [IX_ReportVersions_ComponentsBlobId] ON [dbo].[ReportVersions] ([ComponentsBlobId]);
```

## Entity Models

### Report Entity

```csharp
public class Report
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public Guid? TenantId { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public Guid? LastModifiedById { get; set; }
    public bool IsPublished { get; set; }
    public bool IsArchived { get; set; }
    
    // New properties for multi-storage
    public string DataDocumentId { get; set; }
    public string ComponentsBlobId { get; set; }
    
    // Navigation properties
    public virtual ICollection<ReportVersion> Versions { get; set; }
    public virtual ReportStyle Style { get; set; }
    public virtual TenantProfile Tenant { get; set; }
    public virtual ApplicationUser Creator { get; set; }
    public virtual ApplicationUser LastModifiedBy { get; set; }
}
```

### ReportVersion Entity

```csharp
public class ReportVersion
{
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    public string VersionName { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsCurrent { get; set; }
    public bool IsPublished { get; set; }
    
    // New properties for multi-storage
    public string DataDocumentId { get; set; }
    public string ComponentsBlobId { get; set; }
    public bool NeedsMigration { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; }
    public virtual ApplicationUser Creator { get; set; }
}
```

### ReportStyle Entity

```csharp
public class ReportStyle
{
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public string Theme { get; set; }
    public string ColorScheme { get; set; }
    public string Typography { get; set; }
    public string Spacing { get; set; }
    
    // JSON serialized options
    public string LayoutOptionsJson { get; set; }
    public string TypographyOptionsJson { get; set; }
    public string StructureOptionsJson { get; set; }
    public string ContentOptionsJson { get; set; }
    public string VisualOptionsJson { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; }
}
```

## Conclusion

These database schema changes provide the foundation for the multi-storage approach to the report structure. The changes maintain backward compatibility during the migration process and allow for a phased transition to the new architecture.

The key changes include:

1. Adding references to external storage in the Reports and ReportVersions tables
2. Creating a new ReportStyles table to store style information
3. Planning for the removal of the JsonData column and ComponentDefinitions table after migration

These changes, combined with the appropriate indexes and entity configurations, will support the new multi-storage architecture while maintaining data integrity and query performance.