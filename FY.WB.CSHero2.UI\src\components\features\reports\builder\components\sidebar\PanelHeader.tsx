"use client";

import { X } from "lucide-react";

interface PanelHeaderProps {
  title: string;
  onClose: () => void;
}

export function PanelHeader({ title, onClose }: PanelHeaderProps) {
  return (
    <div className="flex justify-between items-center mb-6">
      <h2 className="text-2xl font-bold">{title}</h2>
      <button className="p-1 rounded-full hover:bg-gray-100" onClick={onClose}>
        <X className="h-5 w-5" />
      </button>
    </div>
  );
}
