using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Uploads.Commands
{
    public class UpdateUploadCommandHandler : IRequestHandler<UpdateUploadCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateUploadCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateUploadCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Uploads
                .FirstOrDefaultAsync(u => u.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Upload), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this upload.");
            }

            bool metadataUpdated = false;
            if (request.Dto.Filename != null || request.Dto.ContentType != null)
            {
                entity.UpdateMetadata(
                    request.Dto.Filename ?? entity.Filename,
                    request.Dto.ContentType ?? entity.ContentType
                );
                metadataUpdated = true;
            }

            bool storageDetailsUpdated = false;
            if (request.Dto.StoragePath != null || request.Dto.StorageProvider != null || request.Dto.ExternalUrl != null)
            {
                entity.UpdateStorageDetails(
                    request.Dto.StoragePath ?? entity.StoragePath,
                    request.Dto.StorageProvider, // Pass null if DTO value is null
                    request.Dto.ExternalUrl      // Pass null if DTO value is null
                );
                storageDetailsUpdated = true;
            }
            
            if (request.Dto.Checksum != null)
            {
                entity.SetChecksum(request.Dto.Checksum);
            }

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            // if any of the entity's properties were actually changed.

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
