using System;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Factory for creating different types of LLM clients
    /// </summary>
    public class LlmClientFactory
    {
        private readonly ILogger _logger;
        private readonly IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// Constructor with logger and HTTP client factory dependencies
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="httpClientFactory">The HTTP client factory</param>
        public LlmClientFactory(ILogger<LlmClientFactory> logger, IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Creates an appropriate LLM client based on configuration
        /// </summary>
        /// <param name="config">The LLM configuration</param>
        /// <returns>An instance of ILlmClient</returns>
        /// <exception cref="ArgumentException">Thrown when the provider is not supported</exception>
        public ILlmClient CreateClient(LlmConfig config)
        {
            return config.Provider?.ToLowerInvariant() switch
            {
                "anthropic" => new AnthropicLlmClient(config, _logger, _httpClientFactory),
                "openai" => new OpenAiLlmClient(config, _logger, _httpClientFactory),
                _ => throw new ArgumentException($"Unsupported LLM provider: {config.Provider}")
            };
        }
    }
}
