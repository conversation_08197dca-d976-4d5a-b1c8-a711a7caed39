using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Uploads.Commands
{
    public class DeleteUploadCommandHandler : IRequestHandler<DeleteUploadCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;
        // private readonly IFileStorageService _fileStorageService; // Optional: For deleting from actual storage

        public DeleteUploadCommandHandler(
            IApplicationDbContext context, 
            ICurrentUserService currentUserService
            // IFileStorageService fileStorageService // Optional
            )
        {
            _context = context;
            _currentUserService = currentUserService;
            // _fileStorageService = fileStorageService; // Optional
        }

        public async Task Handle(DeleteUploadCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Uploads
                .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Upload), request.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to delete this upload.");
            }

            // Optional: Delete the actual file from storage
            // if (!string.IsNullOrEmpty(entity.StoragePath))
            // {
            //     await _fileStorageService.DeleteFileAsync(entity.StoragePath, entity.StorageProvider);
            // }

            // Use soft delete instead of hard delete
            entity.IsDeleted = true;
            entity.DeletionTime = DateTime.UtcNow;
            entity.DeleterId = Guid.TryParse(_currentUserService.UserId, out var deleterId) ? deleterId : null;
            _context.Uploads.Update(entity);

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
