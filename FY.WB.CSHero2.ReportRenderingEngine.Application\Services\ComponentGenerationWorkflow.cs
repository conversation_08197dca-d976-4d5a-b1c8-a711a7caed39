using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Services;
using FY.WB.CSHero2.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Orchestrates the complete component generation workflow with LLM integration
    /// </summary>
    public class ComponentGenerationWorkflow
    {
        private readonly ILlmClient _llmClient;
        private readonly EnhancedPromptBuilder _promptBuilder;
        private readonly ComponentCacheService _cacheService;
        private readonly ILogger<ComponentGenerationWorkflow> _logger;

        public ComponentGenerationWorkflow(
            ILlmClient llmClient,
            EnhancedPromptBuilder promptBuilder,
            ComponentCacheService cacheService,
            ILogger<ComponentGenerationWorkflow> logger)
        {
            _llmClient = llmClient ?? throw new ArgumentNullException(nameof(llmClient));
            _promptBuilder = promptBuilder ?? throw new ArgumentNullException(nameof(promptBuilder));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Executes the complete component generation workflow
        /// </summary>
        public async Task<ComponentGenerationResult> GenerateComponentAsync(
            ComponentGenerationRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting component generation workflow for section {SectionId}",
                    request.Context.SectionId);

                var result = new ComponentGenerationResult
                {
                    SectionId = request.Context.SectionId,
                    SectionName = request.Context.SectionName,
                    StartedAt = DateTime.UtcNow
                };

                // Step 1: Check cache
                var cacheKey = _cacheService.GenerateComponentCacheKey(new ComponentRequest
                {
                    ReportId = request.ReportId,
                    SectionId = request.Context.SectionId,
                    Data = request.Data,
                    Options = new ComponentGenerationOptions
                    {
                        Framework = request.Context.Framework,
                        StyleFramework = request.Context.StyleFramework,
                        UseTypeScript = request.Context.UseTypeScript,
                        IncludeAccessibility = request.Context.IncludeAccessibility,
                        IncludeResponsiveDesign = request.Context.IncludeResponsiveDesign,
                        OptimizeForPerformance = request.Context.OptimizeForPerformance
                    }
                });

                var cachedResult = await _cacheService.GetCachedComponentAsync(cacheKey, cancellationToken);
                if (cachedResult != null && cachedResult.Success)
                {
                    _logger.LogInformation("Using cached component for section {SectionId}", request.Context.SectionId);
                    result.ComponentCode = cachedResult.Components.FirstOrDefault()?.ComponentCode ?? string.Empty;
                    result.Success = true;
                    result.CompletedAt = DateTime.UtcNow;
                    result.CacheHit = true;
                    return result;
                }

                // Step 2: Build generation prompt
                var prompt = await _promptBuilder.BuildComponentGenerationPromptAsync(
                    request.Context,
                    request.Data,
                    request.Template,
                    cancellationToken);

                // Step 3: Generate component with LLM
                _logger.LogDebug("Generating component code with LLM for section {SectionId}", request.Context.SectionId);
                var componentCode = await _llmClient.GenerateReactComponentAsync(prompt, request.Context, cancellationToken);

                if (string.IsNullOrEmpty(componentCode))
                {
                    result.Success = false;
                    result.Errors.Add("LLM returned empty component code");
                    result.CompletedAt = DateTime.UtcNow;
                    return result;
                }

                result.ComponentCode = componentCode;

                // Step 4: Validate generated component
                if (request.ValidateComponent)
                {
                    _logger.LogDebug("Validating generated component for section {SectionId}", request.Context.SectionId);
                    var validationResult = await ValidateComponentAsync(componentCode, request.Context, cancellationToken);
                    result.ValidationResult = validationResult;

                    if (!validationResult.IsValid && request.RequireValidComponent)
                    {
                        result.Success = false;
                        result.Errors.Add("Generated component failed validation");
                        result.CompletedAt = DateTime.UtcNow;
                        return result;
                    }
                }

                // Step 5: Optimize component if requested
                if (request.OptimizationTypes?.Any() == true)
                {
                    _logger.LogDebug("Optimizing component for section {SectionId}", request.Context.SectionId);
                    result.ComponentCode = await OptimizeComponentAsync(
                        result.ComponentCode,
                        request.OptimizationTypes,
                        request.Context,
                        cancellationToken);
                }

                // Step 6: Generate TypeScript definitions
                if (request.Context.UseTypeScript && request.GenerateTypeDefinitions)
                {
                    result.TypeDefinitions = await GenerateTypeDefinitionsAsync(
                        result.ComponentCode,
                        request.Context,
                        cancellationToken);
                }

                // Step 7: Cache the result
                if (result.Success)
                {
                    var componentResult = new ComponentResult
                    {
                        Success = true,
                        Components = new List<SectionComponent>
                        {
                            new SectionComponent
                            {
                                SectionId = request.Context.SectionId,
                                SectionName = request.Context.SectionName,
                                ComponentCode = result.ComponentCode,
                                TypeDefinitions = result.TypeDefinitions
                            }
                        }
                    };

                    await _cacheService.SetCachedComponentAsync(cacheKey, componentResult, cancellationToken);
                }

                result.Success = true;
                result.CompletedAt = DateTime.UtcNow;

                _logger.LogInformation("Component generation workflow completed successfully for section {SectionId} in {Duration}ms",
                    request.Context.SectionId, (result.CompletedAt - result.StartedAt).TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in component generation workflow for section {SectionId}",
                    request.Context.SectionId);

                return new ComponentGenerationResult
                {
                    SectionId = request.Context.SectionId,
                    SectionName = request.Context.SectionName,
                    Success = false,
                    Errors = new List<string> { $"Workflow error: {ex.Message}" },
                    StartedAt = DateTime.UtcNow,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// Validates a generated component
        /// </summary>
        private async Task<ComponentValidationResult> ValidateComponentAsync(
            string componentCode,
            ComponentGenerationContext context,
            CancellationToken cancellationToken)
        {
            try
            {
                // Validate with LLM
                var validationResult = await _llmClient.ValidateComponentAsync(componentCode, context.Framework, cancellationToken);

                // Cache the result if caching service supports it
                try
                {
                    // Note: Caching validation results would require additional cache service methods
                    // For now, we'll skip caching validation results
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache validation result");
                }

                return validationResult;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error validating component, returning basic validation");
                return new ComponentValidationResult
                {
                    IsValid = !componentCode.ToLower().Contains("error"),
                    QualityScore = 70
                };
            }
        }

        /// <summary>
        /// Optimizes a component for multiple criteria
        /// </summary>
        private async Task<string> OptimizeComponentAsync(
            string componentCode,
            List<string> optimizationTypes,
            ComponentGenerationContext context,
            CancellationToken cancellationToken)
        {
            var optimizedCode = componentCode;

            foreach (var optimizationType in optimizationTypes)
            {
                try
                {
                    _logger.LogDebug("Applying {OptimizationType} optimization", optimizationType);
                    optimizedCode = await _llmClient.OptimizeComponentAsync(optimizedCode, optimizationType, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to apply {OptimizationType} optimization, continuing with previous version",
                        optimizationType);
                }
            }

            return optimizedCode;
        }

        /// <summary>
        /// Generates TypeScript type definitions for a component
        /// </summary>
        private async Task<string> GenerateTypeDefinitionsAsync(
            string componentCode,
            ComponentGenerationContext context,
            CancellationToken cancellationToken)
        {
            try
            {
                // Extract types from the component code
                var typeDefinitions = ExtractTypeDefinitions(componentCode);

                if (string.IsNullOrEmpty(typeDefinitions))
                {
                    // Generate basic type definitions
                    typeDefinitions = GenerateBasicTypeDefinitions(context);
                }

                return await Task.FromResult(typeDefinitions);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error generating type definitions");
                return GenerateBasicTypeDefinitions(context);
            }
        }

        /// <summary>
        /// Extracts type definitions from component code
        /// </summary>
        private string ExtractTypeDefinitions(string componentCode)
        {
            var lines = componentCode.Split('\n');
            var typeDefinitions = new List<string>();

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("interface ") ||
                    trimmedLine.StartsWith("type ") ||
                    trimmedLine.StartsWith("enum "))
                {
                    typeDefinitions.Add(line);
                }
            }

            return string.Join("\n", typeDefinitions);
        }

        /// <summary>
        /// Generates basic type definitions for a component
        /// </summary>
        private string GenerateBasicTypeDefinitions(ComponentGenerationContext context)
        {
            var componentName = ToPascalCase(context.SectionName);

            return $@"interface {componentName}Props {{
  data: any;
  className?: string;
  [key: string]: any;
}}

interface {componentName}Data {{
  [key: string]: any;
}}";
        }

        /// <summary>
        /// Converts string to PascalCase
        /// </summary>
        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return "Component";

            var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new System.Text.StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    result.Append(char.ToUpper(word[0]));
                    if (word.Length > 1)
                    {
                        result.Append(word.Substring(1).ToLower());
                    }
                }
            }

            return result.Length > 0 ? result.ToString() : "Component";
        }
    }

    /// <summary>
    /// Request for component generation workflow
    /// </summary>
    public class ComponentGenerationRequest
    {
        public Guid ReportId { get; set; }
        public ComponentGenerationContext Context { get; set; } = new ComponentGenerationContext();
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public Template? Template { get; set; }
        public bool ValidateComponent { get; set; } = true;
        public bool RequireValidComponent { get; set; } = false;
        public bool GenerateTypeDefinitions { get; set; } = true;
        public List<string>? OptimizationTypes { get; set; }
    }

    /// <summary>
    /// Result of component generation workflow
    /// </summary>
    public class ComponentGenerationResult
    {
        public string SectionId { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public bool Success { get; set; } = false;
        public string ComponentCode { get; set; } = string.Empty;
        public string TypeDefinitions { get; set; } = string.Empty;
        public ComponentValidationResult? ValidationResult { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public DateTime StartedAt { get; set; }
        public DateTime CompletedAt { get; set; }
        public bool CacheHit { get; set; } = false;

        public TimeSpan Duration => CompletedAt - StartedAt;
    }
}
