import { NextRequest, NextResponse } from 'next/server';

// Mark this route as dynamic since it uses request.url
export const dynamic = 'force-dynamic';
import { 
  getSubscriptions, 
  getSubscriptionById, 
  getSubscriptionByTypeAndBillingCycle,
  getPopularSubscriptions
} from '@/lib/data/subscriptions';

/**
 * GET /api/v1/subscriptions
 * Get all subscriptions with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const type = searchParams.get('type') as 'basic' | 'professional' | 'enterprise' | null;
    const billingCycle = searchParams.get('billingCycle') as 'monthly' | 'quarterly' | 'annual' | null;
    const isPopular = searchParams.get('isPopular') === 'true';
    const sortBy = searchParams.get('_sort');
    const order = searchParams.get('_order') as 'asc' | 'desc' | null;
    const page = searchParams.get('_page') ? parseInt(searchParams.get('_page')!) : undefined;
    const limit = searchParams.get('_limit') ? parseInt(searchParams.get('_limit')!) : undefined;
    
    // Build query options
    const options: any = {
      sortBy: sortBy || undefined,
      order: order || undefined,
      page,
      limit
    };
    
    if (type) {
      options.type = type;
    }
    
    if (billingCycle) {
      options.billingCycle = billingCycle;
    }
    
    // Get subscriptions
    const subscriptions = await getSubscriptions(options);
    
    return NextResponse.json(subscriptions);
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    );
  }
}
