using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using HtmlAgilityPack;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Concrete implementation of IHtmlValidator using HtmlAgilityPack
    /// </summary>
    public class HtmlValidator : IHtmlValidator
    {
        private readonly ILogger<HtmlValidator> _logger;

        public HtmlValidator(ILogger<HtmlValidator> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Validates the HTML content for structure, syntax, and best practices
        /// </summary>
        public async Task<ValidationResult> ValidateAsync(string html, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Starting HTML validation");

            if (string.IsNullOrWhiteSpace(html))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { "HTML content is null or empty" }
                };
            }

            var errors = new List<string>();
            var warnings = new List<string>();

            try
            {
                // Simulate async operation
                await Task.Delay(1, cancellationToken);

                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // Check for parse errors
                if (doc.ParseErrors.Any())
                {
                    foreach (var error in doc.ParseErrors)
                    {
                        errors.Add($"Parse error at line {error.Line}, position {error.LinePosition}: {error.Reason}");
                    }
                }

                // Validate document structure
                ValidateDocumentStructure(doc, errors, warnings);

                // Validate HTML5 semantic structure
                ValidateSemanticStructure(doc, errors, warnings);

                // Validate accessibility
                ValidateAccessibility(doc, errors, warnings);

                // Validate CSS and styling
                ValidateStyling(doc, errors, warnings);

                var isValid = !errors.Any();

                _logger.LogDebug("HTML validation completed. Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}", 
                    isValid, errors.Count, warnings.Count);

                return new ValidationResult
                {
                    IsValid = isValid,
                    Errors = errors,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during HTML validation");
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { $"Validation error: {ex.Message}" }
                };
            }
        }

        private void ValidateDocumentStructure(HtmlDocument doc, List<string> errors, List<string> warnings)
        {
            // Check for DOCTYPE
            if (!doc.DocumentNode.ChildNodes.Any(n => n.NodeType == HtmlNodeType.Document))
            {
                warnings.Add("Missing DOCTYPE declaration");
            }

            // Check for html element
            var htmlNode = doc.DocumentNode.SelectSingleNode("//html");
            if (htmlNode == null)
            {
                errors.Add("Missing <html> element");
                return;
            }

            // Check for head element
            var headNode = htmlNode.SelectSingleNode("head");
            if (headNode == null)
            {
                errors.Add("Missing <head> element");
            }
            else
            {
                // Check for title
                var titleNode = headNode.SelectSingleNode("title");
                if (titleNode == null)
                {
                    warnings.Add("Missing <title> element in head");
                }

                // Check for meta charset
                var charsetMeta = headNode.SelectSingleNode("meta[@charset]");
                if (charsetMeta == null)
                {
                    warnings.Add("Missing charset meta tag");
                }

                // Check for viewport meta
                var viewportMeta = headNode.SelectSingleNode("meta[@name='viewport']");
                if (viewportMeta == null)
                {
                    warnings.Add("Missing viewport meta tag for responsive design");
                }
            }

            // Check for body element
            var bodyNode = htmlNode.SelectSingleNode("body");
            if (bodyNode == null)
            {
                errors.Add("Missing <body> element");
            }
        }

        private void ValidateSemanticStructure(HtmlDocument doc, List<string> errors, List<string> warnings)
        {
            // Check for proper heading hierarchy
            var headings = doc.DocumentNode.SelectNodes("//h1 | //h2 | //h3 | //h4 | //h5 | //h6");
            if (headings != null)
            {
                var h1Count = doc.DocumentNode.SelectNodes("//h1")?.Count ?? 0;
                if (h1Count == 0)
                {
                    warnings.Add("No H1 heading found - consider adding one for better SEO");
                }
                else if (h1Count > 1)
                {
                    warnings.Add("Multiple H1 headings found - consider using only one per page");
                }
            }

            // Check for semantic HTML5 elements
            var semanticElements = new[] { "header", "nav", "main", "section", "article", "aside", "footer" };
            var hasSemanticElements = semanticElements.Any(element => 
                doc.DocumentNode.SelectSingleNode($"//{element}") != null);

            if (!hasSemanticElements)
            {
                warnings.Add("Consider using HTML5 semantic elements (header, main, section, etc.) for better structure");
            }
        }

        private void ValidateAccessibility(HtmlDocument doc, List<string> errors, List<string> warnings)
        {
            // Check images for alt attributes
            var images = doc.DocumentNode.SelectNodes("//img");
            if (images != null)
            {
                foreach (var img in images)
                {
                    var altAttribute = img.GetAttributeValue("alt", null);
                    if (string.IsNullOrEmpty(altAttribute))
                    {
                        warnings.Add($"Image missing alt attribute: {img.OuterHtml.Substring(0, Math.Min(50, img.OuterHtml.Length))}...");
                    }
                }
            }

            // Check form inputs for labels
            var inputs = doc.DocumentNode.SelectNodes("//input[@type!='hidden'] | //textarea | //select");
            if (inputs != null)
            {
                foreach (var input in inputs)
                {
                    var id = input.GetAttributeValue("id", null);
                    var ariaLabel = input.GetAttributeValue("aria-label", null);
                    
                    if (!string.IsNullOrEmpty(id))
                    {
                        var label = doc.DocumentNode.SelectSingleNode($"//label[@for='{id}']");
                        if (label == null && string.IsNullOrEmpty(ariaLabel))
                        {
                            warnings.Add($"Form input missing associated label: {input.OuterHtml.Substring(0, Math.Min(50, input.OuterHtml.Length))}...");
                        }
                    }
                    else if (string.IsNullOrEmpty(ariaLabel))
                    {
                        warnings.Add($"Form input missing id and aria-label: {input.OuterHtml.Substring(0, Math.Min(50, input.OuterHtml.Length))}...");
                    }
                }
            }

            // Check for proper color contrast (basic check for inline styles)
            var elementsWithStyle = doc.DocumentNode.SelectNodes("//*[@style]");
            if (elementsWithStyle != null)
            {
                foreach (var element in elementsWithStyle)
                {
                    var style = element.GetAttributeValue("style", "");
                    if (style.Contains("color:") && !style.Contains("background"))
                    {
                        warnings.Add("Consider ensuring sufficient color contrast for accessibility");
                        break; // Only warn once
                    }
                }
            }
        }

        private void ValidateStyling(HtmlDocument doc, List<string> errors, List<string> warnings)
        {
            // Check for inline styles (should be minimal)
            var inlineStyles = doc.DocumentNode.SelectNodes("//*[@style]");
            if (inlineStyles != null && inlineStyles.Count > 10)
            {
                warnings.Add($"High number of inline styles ({inlineStyles.Count}) - consider using CSS classes");
            }

            // Check for deprecated elements
            var deprecatedElements = new[] { "font", "center", "big", "small", "tt" };
            foreach (var element in deprecatedElements)
            {
                var nodes = doc.DocumentNode.SelectNodes($"//{element}");
                if (nodes != null)
                {
                    warnings.Add($"Deprecated HTML element found: <{element}>. Consider using CSS for styling.");
                }
            }

            // Check for missing CSS
            var hasStyleElement = doc.DocumentNode.SelectSingleNode("//style") != null;
            var hasStylesheetLink = doc.DocumentNode.SelectSingleNode("//link[@rel='stylesheet']") != null;
            var hasInlineStyles = doc.DocumentNode.SelectSingleNode("//*[@style]") != null;

            if (!hasStyleElement && !hasStylesheetLink && !hasInlineStyles)
            {
                warnings.Add("No CSS styling detected - consider adding styles for better presentation");
            }
        }
    }
}
