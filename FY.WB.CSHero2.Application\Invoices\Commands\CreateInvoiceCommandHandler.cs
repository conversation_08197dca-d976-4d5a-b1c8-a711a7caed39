using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Invoices.Commands
{
    public class CreateInvoiceCommandHandler : IRequestHandler<CreateInvoiceCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateInvoiceCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateInvoiceCommand request, CancellationToken cancellationToken)
        {
            var entity = new Invoice
            {
                OrderNumber = Invoice.GenerateOrderNumber(request.Dto.Type, request.Dto.Date), // Generate OrderNumber
                Type = request.Dto.Type,
                Plans = request.Dto.Plans,
                Amount = request.Dto.Amount,
                Status = request.Dto.Status,
                Date = request.Dto.Date,
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };

            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create an Invoice.");
            }
            entity.TenantId = _currentUserService.TenantId.Value;

            _context.Invoices.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
