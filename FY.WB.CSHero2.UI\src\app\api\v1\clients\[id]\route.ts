import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/clients/[id]
 * Returns a specific client by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Clients/${id}`, {
      method: 'GET',
      headers,
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      return NextResponse.json(
        { error: `Failed to fetch client ${id} from backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const clientData = await backendResponse.json();
    return NextResponse.json(clientData);
  } catch (error) {
    console.error(`Error fetching client with ID ${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to fetch client: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/v1/clients/[id]
 * Updates a specific client
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Clients/${id}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      return NextResponse.json(
        { error: `Failed to update client ${id} via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const updatedClient = await backendResponse.json();
    return NextResponse.json(updatedClient);
  } catch (error) {
    console.error(`Error updating client with ID ${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to update client: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/clients/[id]
 * Deletes a specific client
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {};
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Clients/${id}`, {
      method: 'DELETE',
      headers,
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      // Handle 404 specifically if needed, otherwise a general error
      if (backendResponse.status === 404) {
          return NextResponse.json({ error: 'Client not found on backend' }, { status: 404 });
      }
      return NextResponse.json(
        { error: `Failed to delete client ${id} via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    // DELETE typically returns 204 No Content on success
    return new NextResponse(null, { status: backendResponse.status });
  } catch (error) {
    console.error(`Error deleting client with ID ${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to delete client: ${errorMessage}` },
      { status: 500 }
    );
  }
}
