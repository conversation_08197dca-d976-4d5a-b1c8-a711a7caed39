using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Forms.Dtos;
using FY.WB.CSHero2.Application.Forms.Queries;
using FY.WB.CSHero2.Application.Forms.Commands;
using FY.WB.CSHero2.Application.Common.Exceptions; // Added for custom exceptions
using FluentValidation; // Added for ValidationException

namespace FY.WB.CSHero2.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class FormsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<FormsController> _logger;

        public FormsController(IMediator mediator, ILogger<FormsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all forms with optional filtering, sorting, and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(List<FormDto>), 200)]
        public async Task<ActionResult<List<FormDto>>> GetForms(
            [FromQuery] string? search,
            [FromQuery] string? category,
            [FromQuery] string? priority,
            [FromQuery] string? tenantId,
            [FromQuery] string? sortBy,
            [FromQuery] string? sortOrder,
            [FromQuery] int? page,
            [FromQuery] int? limit)
        {
            try
            {
                _logger.LogInformation("Retrieving forms with search: {Search}, category: {Category}, priority: {Priority}",
                    search, category, priority);

                var parameters = new FormQueryParametersDto
                {
                    Search = search,
                    Category = category,
                    Priority = priority,
                    TenantId = !string.IsNullOrEmpty(tenantId) ? Guid.Parse(tenantId) : null,
                    SortBy = sortBy,
                    SortOrder = sortOrder,
                    Page = page,
                    Limit = limit
                };

                var query = new GetFormsQuery(parameters);
                var result = await _mediator.Send(query);

                _logger.LogInformation("Successfully retrieved {Count} forms", result.Count);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving forms");
                return StatusCode(500, new { message = "An error occurred while retrieving forms", details = ex.Message });
            }
        }

        /// <summary>
        /// Get a form by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(FormDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<FormDto>> GetForm(string id)
        {
            try
            {
                _logger.LogInformation("Retrieving form with ID: {FormId}", id);

                var query = new GetFormByIdQuery(Guid.Parse(id));
                var result = await _mediator.Send(query);

                _logger.LogInformation("Successfully retrieved form with ID: {FormId}", id);
                return Ok(result);
            }
            catch (Exception ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Form with ID {FormId} not found", id);
                return NotFound(new { message = ex.Message });
            }
            catch (FormatException)
            {
                _logger.LogWarning("Invalid form ID format: {FormId}", id);
                return BadRequest(new { message = "Invalid ID format. Please provide a valid GUID." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving form with ID: {FormId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the form", details = ex.Message });
            }
        }

        /// <summary>
        /// Create a new form
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(FormDto), 201)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<Guid>> CreateForm([FromBody] CreateFormRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new form with title: {FormTitle}", dto.Title);
            try
            {
                var command = new CreateFormCommand(dto);
                var formId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created form with ID: {FormId}", formId);
                // Returning the ID directly, or can use CreatedAtAction if GetForm returns the FormDto based on Guid ID
                return CreatedAtAction(nameof(GetForm), new { id = formId.ToString() }, new { id = formId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating form.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating form: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating form: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the form.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing form.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateForm(Guid id, [FromBody] UpdateFormRequestDto dto)
        {
            _logger.LogInformation("Attempting to update form with ID: {FormId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateFormCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated form with ID: {FormId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating form with ID: {FormId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Form with ID {FormId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update form with ID: {FormId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating form with ID {FormId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the form.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a form by its ID.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteForm(Guid id)
        {
            _logger.LogInformation("Attempting to delete form with ID: {FormId}", id);
            try
            {
                var command = new DeleteFormCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted form with ID: {FormId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Form with ID {FormId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete form with ID: {FormId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting form with ID {FormId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the form.", details = ex.Message });
            }
        }
    }
}
