import { NextRequest, NextResponse } from 'next/server';
import { getAdminTenant, updateAdminTenant } from '@/lib/data/admin';

/**
 * GET /api/v1/admin
 * Returns the admin tenant
 */
export async function GET(request: NextRequest) {
  try {
    const adminTenant = await getAdminTenant();

    if (!adminTenant) {
      return NextResponse.json(
        { error: 'Admin tenant not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(adminTenant);
  } catch (error) {
    console.error('Error fetching admin tenant:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin tenant' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/admin
 * Updates the admin tenant
 */
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    const updatedTenant = await updateAdminTenant(body);

    if (!updatedTenant) {
      return NextResponse.json(
        { error: 'Admin tenant not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedTenant);
  } catch (error) {
    console.error('Error updating admin tenant:', error);
    return NextResponse.json(
      { error: 'Failed to update admin tenant' },
      { status: 500 }
    );
  }
}
