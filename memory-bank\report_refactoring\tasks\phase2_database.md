# Phase 2: Database Migration

## Overview

This phase focuses on creating and applying database migrations to implement the new report structure schema. It also includes creating and executing a data migration script to convert existing JSON-based report content to the new structured format.

## Tasks

### Task 2.1: Create Database Migration

**Description:** Generate a new Entity Framework Core migration for the schema changes.

**Steps:**
1. Open a terminal in the project root directory
2. Run the EF Core migration command:
   ```
   dotnet ef migrations add AddReportStructureEntities --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
   ```
3. Review the generated migration file in `FY.WB.CSHero2.Infrastructure/Migrations/`
4. Verify that the migration includes:
   - Creation of `ReportSections` table
   - Creation of `ReportSectionFields` table
   - Any necessary index creation

**Acceptance Criteria:**
- Migration file generated successfully
- Migration includes creation of all necessary tables
- Migration includes creation of all necessary indexes
- Migration includes foreign key relationships

### Task 2.2: Create Data Migration Script

**Description:** Create a script to migrate existing report content from JSON format to the new structured format.

**Steps:**
1. Create a new class `ReportDataMigrationService.cs` in `FY.WB.CSHero2.Infrastructure/Persistence/Migrations/`
2. Implement a method to read all reports from the database
3. For each report:
   - Deserialize the JSON content from `ReportVersion.JsonData`
   - Extract sections from the JSON
   - Create new `ReportSection` entities
   - Extract fields from each section
   - Create new `ReportSectionField` entities
4. Save all changes to the database

**Code Example:**
```csharp
public class ReportDataMigrationService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ReportDataMigrationService> _logger;

    public ReportDataMigrationService(
        ApplicationDbContext context,
        ILogger<ReportDataMigrationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task MigrateReportDataAsync()
    {
        _logger.LogInformation("Starting report data migration...");
        
        // Get all reports with their current versions
        var reports = await _context.Reports
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .ToListAsync();
            
        _logger.LogInformation($"Found {reports.Count} reports to migrate");
        
        int migratedCount = 0;
        
        foreach (var report in reports)
        {
            try
            {
                var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
                if (currentVersion == null)
                {
                    _logger.LogWarning($"Report {report.Id} has no current version, skipping");
                    continue;
                }
                
                // Deserialize JSON data
                var jsonData = currentVersion.JsonData;
                if (string.IsNullOrEmpty(jsonData))
                {
                    _logger.LogWarning($"Report {report.Id} has empty JSON data, skipping");
                    continue;
                }
                
                var reportData = JsonSerializer.Deserialize<ReportContent>(jsonData);
                if (reportData?.sections == null || !reportData.sections.Any())
                {
                    _logger.LogWarning($"Report {report.Id} has no sections, skipping");
                    continue;
                }
                
                // Process sections
                int sectionOrder = 0;
                foreach (var sectionData in reportData.sections)
                {
                    var section = new ReportSection
                    {
                        Id = Guid.NewGuid(),
                        ReportId = report.Id,
                        Title = sectionData.title ?? sectionData["section-title"] ?? "Untitled Section",
                        Type = sectionData.type ?? "unknown",
                        Order = sectionOrder++
                    };
                    
                    _context.ReportSections.Add(section);
                    
                    // Process fields
                    if (sectionData.content != null)
                    {
                        int fieldOrder = 0;
                        foreach (var field in ExtractFields(sectionData.content))
                        {
                            var sectionField = new ReportSectionField
                            {
                                Id = Guid.NewGuid(),
                                SectionId = section.Id,
                                Name = field.Key,
                                Type = DetermineFieldType(field.Value),
                                Content = field.Value.ToString() ?? string.Empty,
                                Order = fieldOrder++
                            };
                            
                            _context.ReportSectionFields.Add(sectionField);
                        }
                    }
                }
                
                migratedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error migrating report {report.Id}: {ex.Message}");
            }
        }
        
        await _context.SaveChangesAsync();
        _logger.LogInformation($"Completed report data migration. Migrated {migratedCount} of {reports.Count} reports");
    }
    
    private Dictionary<string, object> ExtractFields(object content)
    {
        // Implementation to extract fields from content object
        // This will depend on the structure of your JSON data
    }
    
    private string DetermineFieldType(object value)
    {
        // Implementation to determine field type based on value
        // E.g., string, number, date, etc.
    }
}
```

**Acceptance Criteria:**
- Data migration service created
- Service correctly extracts sections from JSON
- Service correctly extracts fields from sections
- Service creates appropriate entities in the database
- Service handles errors gracefully
- Service logs progress and results

### Task 2.3: Register Data Migration Service

**Description:** Register the data migration service for dependency injection.

**Steps:**
1. Open `DependencyInjection.cs` in `FY.WB.CSHero2.Infrastructure/`
2. Add registration for the data migration service:
   ```csharp
   services.AddScoped<ReportDataMigrationService>();
   ```

**Acceptance Criteria:**
- Service registered for dependency injection

### Task 2.4: Create Migration Execution Command

**Description:** Create a command to execute the data migration.

**Steps:**
1. Create a new class `MigrateReportDataCommand.cs` in `FY.WB.CSHero2.Application/Reports/Commands/`
2. Implement the command and handler:
   ```csharp
   public class MigrateReportDataCommand : IRequest<int>
   {
   }
   
   public class MigrateReportDataCommandHandler : IRequestHandler<MigrateReportDataCommand, int>
   {
       private readonly ReportDataMigrationService _migrationService;
       
       public MigrateReportDataCommandHandler(ReportDataMigrationService migrationService)
       {
           _migrationService = migrationService;
       }
       
       public async Task<int> Handle(MigrateReportDataCommand request, CancellationToken cancellationToken)
       {
           return await _migrationService.MigrateReportDataAsync();
       }
   }
   ```

**Acceptance Criteria:**
- Command and handler created
- Handler uses the data migration service

### Task 2.5: Create Admin Endpoint for Migration

**Description:** Create an admin-only API endpoint to trigger the data migration.

**Steps:**
1. Open `ReportsController.cs` in `FY.WB.CSHero2/Controllers/`
2. Add a new endpoint for data migration:
   ```csharp
   [HttpPost("migrate-data")]
   [Authorize(Roles = "Admin")]
   [SwaggerOperation(Summary = "Migrate report data to new structure", OperationId = "MigrateReportData")]
   public async Task<ActionResult<int>> MigrateReportData()
   {
       try
       {
           var command = new MigrateReportDataCommand();
           var result = await _mediator.Send(command);
           return Ok(new { MigratedCount = result });
       }
       catch (Exception ex)
       {
           _logger.LogError(ex, "Error during report data migration");
           return StatusCode(500, new { message = "An error occurred during data migration", details = ex.Message });
       }
   }
   ```

**Acceptance Criteria:**
- Endpoint created with proper authorization
- Endpoint uses the migration command
- Endpoint returns appropriate responses

### Task 2.6: Apply Database Migration

**Description:** Apply the database migration to the development database.

**Steps:**
1. Open a terminal in the project root directory
2. Run the EF Core migration command:
   ```
   dotnet ef database update --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
   ```
3. Verify that the migration was applied successfully

**Acceptance Criteria:**
- Migration applied successfully
- New tables created in the database
- No errors during migration

### Task 2.7: Execute Data Migration

**Description:** Execute the data migration to convert existing report data.

**Steps:**
1. Start the application
2. Use Swagger UI to call the migration endpoint
3. Monitor the logs for progress and results
4. Verify that the data was migrated correctly

**Acceptance Criteria:**
- Data migration executed successfully
- Sections and fields created for existing reports
- No errors during migration
- Migration results logged properly

## Dependencies

- Phase 1: Domain Model Changes

## Deliverables

- Database migration for schema changes
- Data migration service
- Migration execution command
- Admin endpoint for triggering migration
- Migrated report data

## Estimated Effort

- Task 2.1: 1 hour
- Task 2.2: 4 hours
- Task 2.3: 0.5 hours
- Task 2.4: 1 hour
- Task 2.5: 1 hour
- Task 2.6: 0.5 hours
- Task 2.7: 1 hour

**Total: 9 hours**

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Data loss during migration | Create database backup before migration |
| Complex JSON structures | Implement robust parsing with error handling |
| Large volume of data | Process in batches with transaction management |
| Schema incompatibilities | Thorough testing with representative data |