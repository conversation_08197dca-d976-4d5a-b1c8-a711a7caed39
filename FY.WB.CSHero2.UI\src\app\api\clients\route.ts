import { NextRequest, NextResponse } from 'next/server';

/**
 * Redirect handler for legacy /api/clients endpoints
 * Forwards requests to the new /api/v1/clients endpoints for backward compatibility
 */
export async function GET(request: NextRequest) {
  // Extract the query string
  const { searchParams } = new URL(request.url);
  const queryString = searchParams.toString();
  
  // Construct the new URL with the v1 path
  const newUrl = `/api/v1/clients${queryString ? `?${queryString}` : ''}`;
  
  // Redirect to the new endpoint
  return NextResponse.redirect(new URL(newUrl, request.url));
}

/**
 * Forward POST requests to the v1 endpoint
 */
export async function POST(request: NextRequest) {
  // Forward to the v1 endpoint
  const newUrl = `/api/v1/clients`;
  return NextResponse.redirect(new URL(newUrl, request.url));
}
