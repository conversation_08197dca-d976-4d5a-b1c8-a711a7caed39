using FluentValidation;
using FY.WB.CSHero2.Application.Reports.Dtos;
using System;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class CreateReportCommandValidator : AbstractValidator<CreateReportCommand>
    {
        public CreateReportCommandValidator()
        {
            RuleFor(v => v.Dto.ReportNumber)
                // ReportNumber can be optional if auto-generated, otherwise NotEmpty()
                .MaximumLength(50).WithMessage("Report Number must not exceed 50 characters.");

            RuleFor(v => v.Dto.ClientId)
                .NotEmpty().WithMessage("Client ID is required.");

            RuleFor(v => v.Dto.ClientName)
                .NotEmpty().WithMessage("Client Name is required.")
                .MaximumLength(200).WithMessage("Client Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Name)
                .NotEmpty().WithMessage("Report Name is required.")
                .MaximumLength(200).WithMessage("Report Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Category)
                .NotEmpty().WithMessage("Category is required.")
                .MaximumLength(100).WithMessage("Category must not exceed 100 characters.");

            RuleFor(v => v.Dto.SlideCount)
                .GreaterThanOrEqualTo(0).WithMessage("Slide Count must be a non-negative number.");

            RuleFor(v => v.Dto.Status)
                .NotEmpty().WithMessage("Status is required.")
                .MaximumLength(50).WithMessage("Status must not exceed 50 characters.");

            RuleFor(v => v.Dto.Author)
                .NotEmpty().WithMessage("Author is required.")
                .MaximumLength(100).WithMessage("Author must not exceed 100 characters.");
        }
    }
}
