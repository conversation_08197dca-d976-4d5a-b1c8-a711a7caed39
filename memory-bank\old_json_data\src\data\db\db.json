{"today": {"metrics": {"totalCustomers": {"current": 150, "previousPeriod": 145, "trend": [140, 142, 145, 143, 147, 149, 150]}, "newCustomers": {"current": 5, "previousPeriod": 3, "trend": [2, 3, 2, 4, 3, 4, 5]}, "reportsCreated": {"current": 25, "previousPeriod": 20, "trend": [18, 20, 22, 21, 23, 24, 25]}, "revenue": {"current": 2500, "previousPeriod": 2200, "trend": [2100, 2200, 2300, 2250, 2400, 2450, 2500]}}}, "wtd": {"metrics": {"totalCustomers": {"current": 160, "previousPeriod": 150, "trend": [145, 148, 150, 153, 155, 158, 160]}, "newCustomers": {"current": 15, "previousPeriod": 12, "trend": [10, 11, 12, 13, 14, 14, 15]}, "reportsCreated": {"current": 120, "previousPeriod": 100, "trend": [95, 98, 102, 105, 110, 115, 120]}, "revenue": {"current": 12000, "previousPeriod": 10000, "trend": [9500, 10000, 10500, 11000, 11200, 11500, 12000]}}}, "mtd": {"metrics": {"totalCustomers": {"current": 200, "previousPeriod": 180, "trend": [170, 175, 180, 185, 190, 195, 200]}, "newCustomers": {"current": 45, "previousPeriod": 35, "trend": [30, 33, 36, 38, 40, 42, 45]}, "reportsCreated": {"current": 450, "previousPeriod": 400, "trend": [380, 390, 400, 410, 420, 435, 450]}, "revenue": {"current": 45000, "previousPeriod": 40000, "trend": [38000, 39000, 40000, 41000, 42000, 43500, 45000]}}}, "ytd": {"metrics": {"totalCustomers": {"current": 1200, "previousPeriod": 1000, "trend": [950, 1000, 1050, 1100, 1150, 1180, 1200]}, "newCustomers": {"current": 350, "previousPeriod": 300, "trend": [280, 290, 300, 310, 320, 335, 350]}, "reportsCreated": {"current": 5200, "previousPeriod": 4800, "trend": [4600, 4700, 4800, 4900, 5000, 5100, 5200]}, "revenue": {"current": 520000, "previousPeriod": 480000, "trend": [460000, 470000, 480000, 490000, 500000, 510000, 520000]}}}, "users": [{"id": 1, "email": "<EMAIL>", "name": "Admin User", "role": "admin", "created_at": "2024-01-01T00:00:00Z", "avatarUrl": null}, {"id": 2, "email": "<EMAIL>", "name": "Regular User", "role": "user", "created_at": "2024-01-02T00:00:00Z", "avatarUrl": null}], "clients": [{"id": "1", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "companyName": "TechCorp Solutions", "createdAt": "2024-01-15T08:00:00Z", "updatedAt": "2024-02-20T15:30:00Z", "phone": "+****************", "address": "123 Tech Street, Silicon Valley, CA", "companySize": "100-500", "industry": "Technology"}, {"id": "2", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "companyName": "Innovate Co", "createdAt": "2024-01-20T09:15:00Z", "updatedAt": "2024-02-19T14:20:00Z", "phone": "+****************", "address": "456 Innovation Ave, Boston, MA", "companySize": "50-100", "industry": "Consulting"}, {"id": "3", "name": "<PERSON>", "email": "<EMAIL>", "status": "inactive", "companyName": "Global Finance Ltd", "createdAt": "2024-01-25T10:30:00Z", "updatedAt": "2024-02-18T16:45:00Z", "phone": "+****************", "address": "789 Finance Blvd, New York, NY", "companySize": "1000+", "industry": "Finance"}, {"id": "4", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "companyName": "Health Plus", "createdAt": "2024-01-30T11:45:00Z", "updatedAt": "2024-02-17T13:15:00Z", "phone": "+****************", "address": "321 Healthcare Drive, Chicago, IL", "companySize": "500-1000", "industry": "Healthcare"}, {"id": "5", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "companyName": "Construct Co", "createdAt": "2024-02-01T13:00:00Z", "updatedAt": "2024-02-16T17:30:00Z", "phone": "+****************", "address": "654 Builder Street, Houston, TX", "companySize": "100-500", "industry": "Construction"}], "forms": [{"id": 1, "title": "Customer <PERSON><PERSON><PERSON>", "customerName": "<PERSON>", "email": "<EMAIL>", "category": "feedback", "priority": "high", "description": "Very satisfied with the service", "date": "2024-02-20T00:00:00Z"}, {"id": 2, "title": "Technical Issue", "customerName": "<PERSON>", "email": "<EMAIL>", "category": "complaint", "priority": "high", "description": "Having trouble with the dashboard", "date": "2024-02-21T00:00:00Z"}], "uploads": [{"id": 1, "filename": "report.pdf", "size": 1024576, "type": "application/pdf", "uploaded_at": "2024-02-20T00:00:00Z"}, {"id": 2, "filename": "data.xlsx", "size": 512288, "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "uploaded_at": "2024-02-21T00:00:00Z"}], "templates": [{"id": "example", "name": "Example Template", "description": "A simple example template with basic fields", "category": "Finance", "tags": ["example", "simple", "basic"], "thumbnailUrl": "/storage/images/desk.jpg", "sections": [{"id": "title-page", "title": "Title Page", "type": "cover"}, {"id": "content", "title": "Content", "type": "text"}], "fields": [{"id": "industry", "name": "Industry", "type": "String", "content": ""}, {"id": "clients", "name": "Clients", "type": "Table", "content": "", "config": {"rows": 1, "columns": 2}}]}], "tenants": [{"id": "1", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "createdAt": "2024-02-15T10:30:00Z", "updatedAt": "2024-03-10T14:45:00Z", "phone": "+****************", "company": "TechCorp Solutions", "subscription": "professional", "lastLogin": "2024-03-15T09:22:00Z", "paymentMethod": {"cardType": "Visa", "lastFourDigits": "4242", "expirationDate": "09/26", "securityMethod": "3D Secure"}, "billingAddress": {"street": "123 Tech Street", "city": "San Francisco", "state": "CA", "zipCode": "94105", "country": "United States"}, "billingCycle": "monthly", "nextBillingDate": "2025-04-15T00:00:00Z", "subscriptionStatus": "active"}, {"id": "2", "name": "<PERSON>", "email": "<EMAIL>", "status": "suspended", "createdAt": "2024-02-20T11:15:00Z", "updatedAt": "2024-03-12T16:30:00Z", "phone": "+****************", "company": "Health Plus", "subscription": "enterprise", "lastLogin": "2024-03-16T13:45:00Z", "paymentMethod": {"cardType": "Mastercard", "lastFourDigits": "8765", "expirationDate": "12/25", "securityMethod": "SMS Verification"}, "billingAddress": {"street": "456 Health Avenue", "city": "Chicago", "state": "IL", "zipCode": "60601", "country": "United States"}, "billingCycle": "annual", "nextBillingDate": "2026-02-20T00:00:00Z", "subscriptionStatus": "overdue"}, {"id": "3", "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "createdAt": "2024-03-05T09:15:00Z", "updatedAt": "2024-03-15T11:30:00Z", "phone": "+****************", "company": "Innovate Co", "subscription": "basic", "lastLogin": "2024-03-17T10:20:00Z", "paymentMethod": {"cardType": "American Express", "lastFourDigits": "3001", "expirationDate": "07/27", "securityMethod": "PIN Verification"}, "billingAddress": {"street": "789 Innovation Blvd", "city": "Boston", "state": "MA", "zipCode": "02110", "country": "United States"}, "billingCycle": "quarterly", "nextBillingDate": "2025-06-05T00:00:00Z", "subscriptionStatus": "active"}], "invoices": [{"id": "invoice-1", "tenantId": "1", "date": "2025-03-15", "type": "Invoice", "orderNumber": "INV-20250315-001", "plans": "Professional Plan - Monthly", "amount": "US$49.99", "status": "paid", "createdAt": "2025-03-15T00:00:00Z"}, {"id": "invoice-2", "tenantId": "1", "date": "2025-02-15", "type": "Invoice", "orderNumber": "INV-20250215-001", "plans": "Professional Plan - Monthly", "amount": "US$49.99", "status": "paid", "createdAt": "2025-02-15T00:00:00Z"}, {"id": "invoice-3", "tenantId": "1", "date": "2025-01-15", "type": "Invoice", "orderNumber": "INV-20250115-001", "plans": "Professional Plan - Monthly", "amount": "US$49.99", "status": "paid", "createdAt": "2025-01-15T00:00:00Z"}, {"id": "invoice-4", "tenantId": "2", "date": "2025-02-20", "type": "Invoice", "orderNumber": "INV-20250220-001", "plans": "Enterprise Plan - Annual", "amount": "US$5999.88", "status": "overdue", "createdAt": "2025-02-20T00:00:00Z"}, {"id": "invoice-5", "tenantId": "3", "date": "2025-03-05", "type": "Invoice", "orderNumber": "INV-20250305-001", "plans": "Basic Plan - Quarterly", "amount": "US$89.97", "status": "paid", "createdAt": "2025-03-05T00:00:00Z"}, {"id": "invoice-6", "tenantId": "3", "date": "2024-12-05", "type": "Invoice", "orderNumber": "INV-20241205-001", "plans": "Basic Plan - Quarterly", "amount": "US$89.97", "status": "paid", "createdAt": "2024-12-05T00:00:00Z"}, {"id": "invoice-7", "tenantId": "2", "date": "2025-03-10", "type": "Payment", "orderNumber": "PMT-20250310-001", "plans": "Manual payment for past due invoice", "amount": "US$2999.94", "status": "paid", "createdAt": "2025-03-10T00:00:00Z"}, {"id": "invoice-8", "tenantId": "1", "date": "2025-03-05", "type": "Refund", "orderNumber": "REF-20250305-001", "plans": "Partial refund for service outage", "amount": "US$16.66", "status": "paid", "createdAt": "2025-03-05T00:00:00Z"}], "reports": [{"reportId": "CSR-2025-001", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Q1 Customer Satisfaction Analysis", "category": "Satisfaction Survey", "slideCount": 12, "createdAt": "2025-01-15T10:30:00Z", "lastModified": "2025-01-18T14:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-002", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Support Ticket Resolution Time Analysis", "category": "Performance Metrics", "slideCount": 8, "createdAt": "2025-01-20T09:15:00Z", "lastModified": "2025-01-22T16:20:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-003", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Customer Feedback Implementation Plan", "category": "Action Plan", "slideCount": 15, "createdAt": "2025-01-25T13:45:00Z", "lastModified": "2025-02-10T11:30:00Z", "status": "In Progress", "author": "<PERSON>"}, {"reportId": "CSR-2025-004", "clientId": "CLIENT-003", "clientName": "Global Shipping Partners", "reportName": "Support Channel Effectiveness", "category": "Channel Analysis", "slideCount": 10, "createdAt": "2025-02-05T08:20:00Z", "lastModified": "2025-02-15T17:10:00Z", "status": "Under Review", "author": "<PERSON>"}, {"reportId": "CSR-2025-005", "clientId": "CLIENT-004", "clientName": "Pinnacle Healthcare", "reportName": "Customer Onboarding Experience", "category": "User Experience", "slideCount": 14, "createdAt": "2025-02-12T11:05:00Z", "lastModified": "2025-02-20T09:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-006", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Product Support Gap Analysis", "category": "Product Improvement", "slideCount": 18, "createdAt": "2025-02-18T14:30:00Z", "lastModified": "2025-02-22T16:15:00Z", "status": "Draft", "author": "<PERSON>"}]}