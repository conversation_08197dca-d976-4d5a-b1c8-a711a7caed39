"use client";

import { usePathname } from "next/navigation";
import { SidebarTemplate } from "./sidebar-template";
import { MainMenuTemplate } from "./main-menu-template";
import { PublicHeader } from "./public-header";
import { mainMenu } from "./menus/main-menu";
import { clientMenu } from "./menus/client-menu";
import { adminMenu } from "./menus/admin-menu";

export function Navigation() {
  const pathname = usePathname();

  // Determine which menu to show based on the current path
  const getContextualMenu = () => {
    if (pathname.startsWith("/admin")) return adminMenu;
    if (pathname.startsWith("/client")) return clientMenu;
    return mainMenu;
  };

  // Check if we're on a client or admin page
  const isClientOrAdmin = pathname.startsWith("/admin") || pathname.startsWith("/client");
  
  // Check if we're on the report editor page
  const isReportEditorPage = pathname.startsWith("/client/reports/editor");

  return (
    <>
      {/* Always show public header */}
      <PublicHeader />

      {/* Show sidebar only on client/admin pages, but not on the report editor page */}
      {isClientOrAdmin && !isReportEditorPage && (
        <SidebarTemplate key={pathname}>
          <MainMenuTemplate
            items={getContextualMenu()}
            pathname={pathname}
          />
        </SidebarTemplate>
      )}
    </>
  );
}
