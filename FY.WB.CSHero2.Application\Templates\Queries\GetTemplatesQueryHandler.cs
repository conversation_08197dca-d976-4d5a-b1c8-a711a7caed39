using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Templates.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Templates.Queries
{
    public class GetTemplatesQueryHandler : IRequestHandler<GetTemplatesQuery, PagedResult<TemplateDto>>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetTemplatesQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<PagedResult<TemplateDto>> Handle(GetTemplatesQuery request, CancellationToken cancellationToken)
        {
            var queryParameters = request.Parameters;
            var page = queryParameters.Page ?? 1;
            var limit = queryParameters.Limit ?? 10;

            IQueryable<Template> query = _context.Templates.AsNoTracking();

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(queryParameters.Search))
            {
                var searchTerm = queryParameters.Search.ToLower().Trim();
                query = query.Where(t =>
                    (t.Name != null && t.Name.ToLower().Contains(searchTerm)) ||
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.Category != null && t.Category.ToLower().Contains(searchTerm)) ||
                    (t.Tags != null && t.Tags.ToLower().Contains(searchTerm)) // Search in raw JSON tags string
                );
            }

            if (!string.IsNullOrWhiteSpace(queryParameters.Category))
            {
                query = query.Where(t => t.Category != null && t.Category.ToLower() == queryParameters.Category.ToLower());
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(queryParameters.SortBy))
            {
                var sortBy = queryParameters.SortBy.ToLower();
                var isDescending = queryParameters.SortOrder?.ToLower() == "desc";

                Expression<Func<Template, object>> keySelector = sortBy switch
                {
                    "name" => t => t.Name,
                    "category" => t => t.Category,
                    "createdat" => t => t.CreationTime,
                    _ => t => t.CreationTime // Default sort
                };

                query = isDescending ? query.OrderByDescending(keySelector) : query.OrderBy(keySelector);
            }
            else
            {
                query = query.OrderByDescending(t => t.CreationTime); // Default sort
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var templateEntities = await query
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync(cancellationToken);

            var templateDtos = templateEntities.Select(template =>
            {
                // Deserialize JSON properties
                var tags = JsonSerializer.Deserialize<List<string>>(template.Tags ?? "[]") ?? new List<string>();
                
                var sectionsDto = new List<TemplateSectionDto>();
                if (!string.IsNullOrWhiteSpace(template.Sections) && template.Sections != "[]")
                {
                    var sections = JsonSerializer.Deserialize<List<TemplateSection>>(template.Sections);
                    if (sections != null)
                    {
                        sectionsDto = sections.Select(s => new TemplateSectionDto { Id = s.Id, Title = s.Title, Type = s.Type }).ToList();
                    }
                }

                var fieldsDto = new List<TemplateFieldDto>();
                if (!string.IsNullOrWhiteSpace(template.Fields) && template.Fields != "[]")
                {
                    var fields = JsonSerializer.Deserialize<List<TemplateField>>(template.Fields);
                    if (fields != null)
                    {
                        fieldsDto = fields.Select(f => new TemplateFieldDto { 
                            Id = f.Id, 
                            Name = f.Name, 
                            Type = f.Type, 
                            Content = f.Content,
                            Config = f.Config?.RootElement.Clone() // Clone JsonElement for DTO stability
                        }).ToList();
                    }
                }

                return new TemplateDto
                {
                    Id = template.Id,
                    Name = template.Name,
                    Description = template.Description,
                    Category = template.Category,
                    ThumbnailUrl = template.ThumbnailUrl,
                    Tags = tags,
                    Sections = sectionsDto,
                    Fields = fieldsDto,
                    CreatedAt = template.CreationTime,
                    UpdatedAt = template.LastModificationTime,
                    TenantId = template.TenantId
                };
            }).ToList();

            return new PagedResult<TemplateDto>(templateDtos, page, limit, totalCount);
        }
    }
}
