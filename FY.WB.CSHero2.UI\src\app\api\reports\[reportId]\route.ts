import { NextRequest, NextResponse } from 'next/server';
import { API_BASE_URL } from '@/lib/constants'; // To call the backend API

/**
 * GET /api/reports/[reportId]
 * Returns a specific report by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } } // Changed id to reportId
) {
  try {
    const { reportId } = params;
    const authToken = request.cookies.get('authToken')?.value;

    if (!authToken) {
      return NextResponse.json({ message: 'Authentication token is missing' }, { status: 401 });
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Reports/${reportId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      if (backendResponse.status === 404) {
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }
      console.error(`Backend error fetching report ${reportId}: ${backendResponse.status} ${errorText}`);
      return NextResponse.json({ error: `Failed to fetch report: ${errorText || backendResponse.statusText}` }, { status: backendResponse.status });
    }
    
    const reportData = await backendResponse.json();
    return NextResponse.json(reportData);

  } catch (error) {
    console.error(`Error in BFF while fetching report ${params.reportId}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch report' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/reports/[reportId]
 * Updates a specific report
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { reportId: string } } // Changed id to reportId
) {
  try {
    const { reportId } = params;
    const body = await request.json();
    const authToken = request.cookies.get('authToken')?.value;

    if (!authToken) {
      return NextResponse.json({ message: 'Authentication token is missing' }, { status: 401 });
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Reports/${reportId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      if (backendResponse.status === 404) {
        return NextResponse.json({ error: 'Report not found for update' }, { status: 404 });
      }
      console.error(`Backend error updating report ${reportId}: ${backendResponse.status} ${errorText}`);
      return NextResponse.json({ error: `Failed to update report: ${errorText || backendResponse.statusText}` }, { status: backendResponse.status });
    }
    
    const updatedReportData = await backendResponse.json();
    return NextResponse.json(updatedReportData);

  } catch (error) {
    console.error(`Error in BFF while updating report ${params.reportId}:`, error);
    return NextResponse.json(
      { error: 'Failed to update report' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reports/[reportId]
 * Deletes a specific report
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { reportId: string } } // Changed id to reportId
) {
  try {
    const { reportId } = params;
    const authToken = request.cookies.get('authToken')?.value;

    if (!authToken) {
      return NextResponse.json({ message: 'Authentication token is missing' }, { status: 401 });
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/Reports/${reportId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      if (backendResponse.status === 404) {
        return NextResponse.json({ error: 'Report not found for deletion' }, { status: 404 });
      }
      console.error(`Backend error deleting report ${reportId}: ${backendResponse.status} ${errorText}`);
      return NextResponse.json({ error: `Failed to delete report: ${errorText || backendResponse.statusText}` }, { status: backendResponse.status });
    }
    
    // DELETE typically returns 204 No Content on success, or 200/202 if it returns the deleted resource or status.
    // If backend returns no body on successful delete (204), then backendResponse.text() or .json() will fail.
    if (backendResponse.status === 204) {
        return new NextResponse(null, { status: 204 });
    }
    // If backend returns something (e.g. the deleted object or a success message)
    // const deletedData = await backendResponse.json(); 
    // return NextResponse.json(deletedData);
    return new NextResponse(null, { status: backendResponse.status });


  } catch (error) {
    console.error(`Error in BFF while deleting report ${params.reportId}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete report' },
      { status: 500 }
    );
  }
}
