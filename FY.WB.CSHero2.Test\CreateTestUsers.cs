using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Test
{
    public static class CreateTestUsers
    {
        private static readonly JsonSerializerOptions _jsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public static async Task Main(string[] args)
        {
            Console.WriteLine("Creating test users for integration tests...");
            
            string baseUrl = "http://localhost:5056";
            var client = new HttpClient { BaseAddress = new Uri(baseUrl) };

            try
            {
                // First check if the API is running
                var healthResponse = await client.GetAsync("/api/health");
                if (!healthResponse.IsSuccessStatusCode)
                {
                    Console.WriteLine($"API is not running at {baseUrl}. Please start the API first.");
                    return;
                }

                // Create tenant 1 user (Health Plus)
                await RegisterUser(client, 
                    "<EMAIL>", 
                    "Test123!", 
                    "Health Plus", 
                    "https://healthplus.org");

                // Create tenant 2 user (TechFusion)
                await RegisterUser(client, 
                    "<EMAIL>", 
                    "Test123!", 
                    "TechFusion Inc", 
                    "https://techfusion.example.com");

                Console.WriteLine("Test users created successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating test users: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
            finally
            {
                client.Dispose();
            }
        }

        private static async Task RegisterUser(HttpClient client, string email, string password, string companyName, string companyUrl)
        {
            Console.WriteLine($"Registering user: {email}");

            var registerModel = new
            {
                Email = email,
                Password = password,
                CompanyName = companyName,
                CompanyUrl = companyUrl
            };

            var content = new StringContent(
                JsonSerializer.Serialize(registerModel, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync("/api/auth/register", content);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<object>(_jsonOptions);
                Console.WriteLine($"User {email} registered successfully: {JsonSerializer.Serialize(result, _jsonOptions)}");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Failed to register user {email}: {response.StatusCode}");
                Console.WriteLine($"Error: {errorContent}");
            }
        }
    }
}
