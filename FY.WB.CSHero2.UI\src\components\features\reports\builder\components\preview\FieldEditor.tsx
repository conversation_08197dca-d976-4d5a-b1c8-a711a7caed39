"use client";

import { useState, useRef } from "react";
import { Upload, Table as TableIcon, Pencil, ChevronDown, ChevronUp, Wand2, Check } from "lucide-react";
import { Field, FieldType } from "../../types"; // Added FieldType to import

interface FieldEditorProps {
  fieldName: string;
  fieldValue: any;
  isEditable: boolean;
  onFieldUpdate: (fieldName: string, value: any) => void;
  onMetadataUpdate?: (fieldName: string, updates: { name?: string; description?: string }) => void;
  fieldDescription?: string;
  displayName?: string;
}

export function FieldEditor({ 
  fieldName, 
  fieldValue, 
  isEditable, 
  onFieldUpdate, 
  onMetadataUpdate,
  fieldDescription = '',
  displayName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/([A-Z])/g, ' $1')
}: FieldEditorProps) {
  const [tableDialogOpen, setTableDialogOpen] = useState(false);
  const [textDialogOpen, setTextDialogOpen] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [nameValue, setNameValue] = useState(displayName);
  const [showDescription, setShowDescription] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState(fieldDescription);
  
  // Define field type enum from imported types
  // type FieldType = 'String' | 'Number' | 'Date' | 'TextArea' | 'Table' | 'Image'; // Already in ../../types
  
  // Determine field type based on value
  const getFieldType = (): FieldType | 'UnhandledObject' => { // Allow internal 'UnhandledObject'
    if (typeof fieldValue === 'string') {
      // Check if it's a JSON string representing a table
      try {
        const parsed = JSON.parse(fieldValue);
        if (Array.isArray(parsed) && parsed.every(row => Array.isArray(row))) {
          return 'Table';
        }
      } catch (e) {
        // Not a valid JSON string, continue with other checks
      }
      
      // Check if it's a date string
      if (/^\d{4}-\d{2}-\d{2}/.test(fieldValue)) {
        return 'Date';
      }
      
      // Check if it's a long text or contains newlines
      if (fieldValue.length > 100 || fieldValue.includes('\n')) {
        return 'TextArea';
      }
      
      return 'String';
    }
    
    if (typeof fieldValue === 'number') {
      return 'Number';
    }
    
    if (Array.isArray(fieldValue)) { // Handles actual arrays
      return 'Table';
    }

    if (typeof fieldValue === 'object' && fieldValue !== null) { // Handles non-array objects
        return 'UnhandledObject';
    }
    
    return 'String'; // Fallback for other primitives (null, undefined, boolean)
  };
  
  const determinedFieldType = getFieldType(); // Use a different variable name to avoid conflict if FieldType is used elsewhere directly
  
  // Parse table data if needed
  const parseTableData = () => {
    if (determinedFieldType === 'Table') { // Use determinedFieldType
      if (typeof fieldValue === 'string') {
        try {
          return JSON.parse(fieldValue);
        } catch (e) {
          console.error('Failed to parse table data:', e);
          return [['']];
        }
      }
      
      if (Array.isArray(fieldValue)) {
        return fieldValue;
      }
    }
    
    return [['']];
  };
  
  const [tableData, setTableData] = useState<string[][]>(parseTableData);
  
  // Handle table cell change
  const handleTableCellChange = (rowIndex: number, colIndex: number, value: string) => {
    const newData = [...tableData];
    newData[rowIndex][colIndex] = value;
    setTableData(newData);
    
    onFieldUpdate(fieldName, JSON.stringify(newData));
  };
  
  // Handle text change
  const handleTextChange = (value: string) => {
    onFieldUpdate(fieldName, value);
  };
  
  // Handle field name editing
  const handleNameSave = () => {
    if (onMetadataUpdate && nameValue.trim()) {
      onMetadataUpdate(fieldName, { name: nameValue });
    }
    setIsEditingName(false);
  };

  // Handle description toggle
  const handleToggleDescription = () => {
    setShowDescription(!showDescription);
  };

  // Create a ref for the textarea
  const descriptionRef = useRef<HTMLTextAreaElement>(null);

  // Handle description change
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    e.stopPropagation();
    // We don't update state on every keystroke anymore
  };

  // Handle description save
  const handleSaveDescription = () => {
    if (onMetadataUpdate && descriptionRef.current) {
      onMetadataUpdate(fieldName, { description: descriptionRef.current.value });
    }
    setShowDescription(false);
  };

  // Handle enhance description
  const handleEnhanceDescription = () => {
    // Here we'd typically call an AI enhancement function
    // For now, just add some placeholder text if empty
    if (descriptionRef.current && !descriptionRef.current.value) {
      descriptionRef.current.value = "This field contains important data that will be used to generate insights in the report.";
    }
  };

  // Field header with name editing and description controls
  const FieldHeader = () => (
    <div className="mb-3">
      <div className="flex items-center justify-between">
        {isEditingName && isEditable && onMetadataUpdate ? (
          <div className="flex items-center flex-1">
            <input
              type="text"
              value={nameValue}
              onChange={(e) => setNameValue(e.target.value)}
              onBlur={handleNameSave}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleNameSave();
                if (e.key === 'Escape') {
                  setIsEditingName(false);
                  setNameValue(displayName);
                }
              }}
              className="flex-1 p-1 border rounded mr-2"
              autoFocus
            />
            <button
              className="p-1 rounded hover:bg-gray-200"
              onClick={handleNameSave}
            >
              <Check className="h-4 w-4" />
            </button>
          </div>
        ) : (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <div className="font-medium text-primary">{displayName}</div>
              {isEditable && onMetadataUpdate && (
                <button 
                  className="ml-2 p-1 rounded hover:bg-gray-200 text-gray-500"
                  onClick={() => setIsEditingName(true)}
                >
                  <Pencil className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* Description teaser when not expanded - only show if onMetadataUpdate is provided */}
      {onMetadataUpdate && !showDescription && fieldDescription && (
        <div 
          className="mt-1 mb-2 text-sm text-gray-400 italic cursor-pointer hover:text-gray-600 flex items-center"
          onClick={() => isEditable && handleToggleDescription()}
        >
          <span>
            {fieldDescription.length > 60 
              ? `${fieldDescription.substring(0, 60)}...` 
              : fieldDescription}
          </span>
          {isEditable && (
            <Pencil className="h-3 w-3 ml-1 text-gray-400" />
          )}
        </div>
      )}
      {onMetadataUpdate && !showDescription && !fieldDescription && isEditable && (
        <div 
          className="mt-1 mb-2 text-sm text-gray-400 italic cursor-pointer hover:text-gray-600 flex items-center"
          onClick={handleToggleDescription}
        >
          <span>Click to add a description for AI...</span>
          <Pencil className="h-3 w-3 ml-1 text-gray-400" />
        </div>
      )}

      {/* Expanded description editor */}
      {isEditable && showDescription && onMetadataUpdate && (
        <div className="p-3" onClick={(e) => e.stopPropagation()}>
          <textarea 
            className="w-full p-2 border rounded text-sm mb-2"
            rows={3}
            defaultValue={descriptionValue}
            onChange={handleDescriptionChange}
            onClick={(e) => e.stopPropagation()}
            ref={descriptionRef}
            placeholder="Describe this field for the AI that will generate the report..."
          />
          <div className="flex justify-end space-x-2">
            <button
              className="px-2 py-1 text-xs border rounded hover:bg-gray-50"
              onClick={(e) => {
                e.stopPropagation();
                setShowDescription(false);
              }}
            >
              Cancel
            </button>
            <button
              className="px-2 py-1 text-xs border rounded hover:bg-gray-50 flex items-center"
              onClick={(e) => {
                e.stopPropagation();
                handleEnhanceDescription();
              }}
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Enhance
            </button>
            <button
              className="px-2 py-1 text-xs bg-primary text-white rounded"
              onClick={(e) => {
                e.stopPropagation();
                handleSaveDescription();
              }}
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
  
  // Always render the header, but conditionally enable editing based on onMetadataUpdate
  const renderWithHeader = (content: React.ReactNode) => (
    <div className="space-y-1">
      <FieldHeader />
      {content}
    </div>
  );
  
  // Render based on field type
  switch (determinedFieldType) { // Use determinedFieldType
    case 'String':
      return renderWithHeader(
        isEditable ? (
          <input 
            type="text" 
            className="w-full px-3 py-2 border rounded-md"
            placeholder={`Enter ${displayName}`}
            value={fieldValue ?? ''} // Use nullish coalescing
            onChange={(e) => handleTextChange(e.target.value)}
          />
        ) : (
          <div className="bg-gray-50 p-2 rounded text-sm text-gray-700">
            {fieldValue ?? ''}  {/* Use nullish coalescing */}
          </div>
        )
      );
      
    case 'Number':
      return renderWithHeader(
        isEditable ? (
          <input 
            type="number" 
            className="w-full px-3 py-2 border rounded-md"
            placeholder={`Enter ${displayName}`}
            value={fieldValue ?? ''} // Use nullish coalescing
            onChange={(e) => handleTextChange(e.target.value)}
          />
        ) : (
          <div className="bg-gray-50 p-2 rounded text-sm text-gray-700">
            {fieldValue ?? ''}  {/* Use nullish coalescing */}
          </div>
        )
      );
      
    case 'Date':
      return renderWithHeader(
        isEditable ? (
          <input 
            type="date" 
            className="w-full px-3 py-2 border rounded-md"
            value={fieldValue ?? ''} // Use nullish coalescing
            onChange={(e) => handleTextChange(e.target.value)}
          />
        ) : (
          <div className="bg-gray-50 p-2 rounded text-sm text-gray-700">
            {fieldValue ?? ''}  {/* Use nullish coalescing */}
          </div>
        )
      );
      
    case 'TextArea':
      return renderWithHeader(
        <div className={`border rounded-md ${isEditable ? 'cursor-pointer' : ''}`} onClick={() => isEditable && setTextDialogOpen(true)}>
          <div className="p-4 max-h-[200px] overflow-y-auto prose prose-sm max-w-none">
            {fieldValue ? (
              <div dangerouslySetInnerHTML={{ __html: fieldValue }} />
            ) : (
              <p className="text-gray-400 italic">
                {isEditable ? `Click to add content for ${displayName}...` : 'No content'}
              </p>
            )}
          </div>

          {textDialogOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
                <h3 className="text-lg font-medium mb-4">Edit {displayName}</h3>
                <textarea
                  className="w-full px-3 py-2 border rounded-md min-h-[300px]"
                  value={fieldValue ?? ''} // Use nullish coalescing
                  onChange={(e) => handleTextChange(e.target.value)}
                  placeholder={`Enter ${displayName}...`}
                />
                <div className="flex justify-end mt-4">
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={() => setTextDialogOpen(false)}
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      );
      
    case 'Table':
      return renderWithHeader(
        <>
          <div
            className={`border rounded-md p-4 ${isEditable ? 'cursor-pointer hover:bg-gray-50' : ''}`}
            onClick={() => isEditable && setTableDialogOpen(true)}
          >
            <div className="overflow-auto max-h-[200px]">
              <table className="w-full border-collapse">
                <tbody>
                  {tableData.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, colIndex) => (
                        <td key={colIndex} className="border p-2 text-sm">
                          {cell || '—'}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {isEditable && (
              <div className="mt-2 text-center text-sm text-gray-500">
                Click to edit table data
              </div>
            )}
          </div>

          {tableDialogOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
                <h3 className="text-lg font-medium mb-4">Edit Table: {displayName}</h3>
                <div className="py-4 overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        <th className="border p-2 bg-gray-50"></th>
                        {tableData[0]?.map((_, colIndex) => (
                          <th key={colIndex} className="border p-2 bg-gray-50">Column {colIndex + 1}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          <td className="border p-2 bg-gray-50 font-medium">Row {rowIndex + 1}</td>
                          {row.map((cell, colIndex) => (
                            <td key={colIndex} className="border p-1">
                              <input
                                type="text"
                                value={cell}
                                onChange={(e) => handleTableCellChange(rowIndex, colIndex, e.target.value)}
                                className="w-full px-2 py-1 border rounded-md min-w-[100px]"
                              />
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="flex justify-end mt-4">
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={() => setTableDialogOpen(false)}
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      );
      
    case 'Image':
      return renderWithHeader(
        <div className="border-2 border-dashed rounded-md p-8 text-center">
          <div className="flex flex-col items-center">
            <Upload className="h-10 w-10 text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-2">
              {isEditable 
                ? 'Drag and drop an image here or click to select' 
                : 'Image placeholder (not implemented)'}
            </p>
            {isEditable && (
              <button className="px-4 py-2 border rounded-md hover:bg-gray-50">
                Select Image
              </button>
            )}
          </div>
        </div>
      );
      
    default:
      return renderWithHeader(
        <div className="bg-gray-50 p-2 rounded text-sm text-gray-700">
          {typeof fieldValue === 'object' && fieldValue !== null // Explicitly check for object before stringify
            ? JSON.stringify(fieldValue) 
            : String(fieldValue ?? '')} {/* Use nullish coalescing for other types */}
        </div>
      );
  }
}
