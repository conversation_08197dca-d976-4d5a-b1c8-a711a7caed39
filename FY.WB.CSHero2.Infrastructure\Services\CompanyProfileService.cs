using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using Microsoft.Extensions.Configuration;

namespace FY.WB.CSHero2.Infrastructure.Services
{
    public class CompanyProfileService : ICompanyProfileService
    {
        private readonly IConfiguration _configuration;

        public CompanyProfileService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public CompanyProfileDto GetCompanyProfile()
        {
            var companyProfile = new CompanyProfileDto();
            _configuration.GetSection("CompanyProfile").Bind(companyProfile);
            return companyProfile;
        }
    }
}
