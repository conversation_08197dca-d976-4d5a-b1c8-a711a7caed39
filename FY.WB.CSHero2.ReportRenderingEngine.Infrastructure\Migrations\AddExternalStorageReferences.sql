-- Migration: Add External Storage References to Report Rendering Engine Entities
-- Description: Adds columns to support hybrid storage architecture with CosmosDB and Blob Storage
-- Date: 2025-05-30

BEGIN TRANSACTION;

-- Add external storage references to ReportVersion table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ReportVersions') AND name = 'StyleDocumentId')
BEGIN
    ALTER TABLE ReportVersions 
    ADD StyleDocumentId NVARCHAR(255) NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ReportVersions') AND name = 'DataBlobPath')
BEGIN
    ALTER TABLE ReportVersions 
    ADD DataBlobPath NVARCHAR(500) NULL;
END

-- Add external storage references to ComponentDefinition table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComponentDefinitions') AND name = 'StyleDocumentId')
BEGIN
    ALTER TABLE ComponentDefinitions 
    ADD StyleDocumentId NVARCHAR(255) NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComponentDefinitions') AND name = 'AssetBlobPaths')
BEGIN
    ALTER TABLE ComponentDefinitions 
    ADD AssetBlobPaths NVARCHAR(MAX) NULL; -- JSON array of blob paths
END

-- Add external storage references to Template table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Templates') AND name = 'StyleDocumentId')
BEGIN
    ALTER TABLE Templates 
    ADD StyleDocumentId NVARCHAR(255) NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Templates') AND name = 'DefaultDataBlobPath')
BEGIN
    ALTER TABLE Templates 
    ADD DefaultDataBlobPath NVARCHAR(500) NULL;
END

-- Create indexes for performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('ReportVersions') AND name = 'IX_ReportVersions_StyleDocumentId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_ReportVersions_StyleDocumentId 
    ON ReportVersions (StyleDocumentId) 
    WHERE StyleDocumentId IS NOT NULL;
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('ComponentDefinitions') AND name = 'IX_ComponentDefinitions_StyleDocumentId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_ComponentDefinitions_StyleDocumentId 
    ON ComponentDefinitions (StyleDocumentId) 
    WHERE StyleDocumentId IS NOT NULL;
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Templates') AND name = 'IX_Templates_StyleDocumentId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Templates_StyleDocumentId 
    ON Templates (StyleDocumentId) 
    WHERE StyleDocumentId IS NOT NULL;
END

-- Add comments to document the new columns
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Reference to the style document ID in CosmosDB for this report version', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'ReportVersions', 
    @level2type = N'COLUMN', @level2name = N'StyleDocumentId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Path to the report data blob in Azure Blob Storage', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'ReportVersions', 
    @level2type = N'COLUMN', @level2name = N'DataBlobPath';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Reference to the style document ID in CosmosDB for this component', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'ComponentDefinitions', 
    @level2type = N'COLUMN', @level2name = N'StyleDocumentId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'JSON array of asset blob paths in Azure Blob Storage for this component', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'ComponentDefinitions', 
    @level2type = N'COLUMN', @level2name = N'AssetBlobPaths';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Reference to the style document ID in CosmosDB for this template', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'Templates', 
    @level2type = N'COLUMN', @level2name = N'StyleDocumentId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Path to the default data blob in Azure Blob Storage for this template', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'Templates', 
    @level2type = N'COLUMN', @level2name = N'DefaultDataBlobPath';

COMMIT TRANSACTION;

PRINT 'External storage references migration completed successfully.';