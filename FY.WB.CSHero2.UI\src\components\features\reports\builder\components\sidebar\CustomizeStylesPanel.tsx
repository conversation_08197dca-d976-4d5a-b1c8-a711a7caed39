"use client";

import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import { 
  StyleOptions, 
  DocumentLayoutOptions, 
  TypographyOptions, 
  StructureOptions, 
  ContentFormattingOptions, 
  VisualElementOptions 
} from "../../types";
import { DocumentLayoutSection } from "./styles/DocumentLayoutSection";
import { TypographySection } from "./styles/TypographySection";
import { StructureSection } from "./styles/StructureSection";
import { ContentFormattingSection } from "./styles/ContentFormattingSection";
import { VisualElementsSection } from "./styles/VisualElementsSection";

interface CustomizeStylesPanelProps {
  styles: StyleOptions;
  onUpdateStyle: (field: string, value: any) => void;
}

type CategoryType = "layout" | "typography" | "structure" | "content" | "visual" | null;

export function CustomizeStylesPanel({ styles, onUpdateStyle }: CustomizeStylesPanelProps) {
  const [activeCategory, setActiveCategory] = useState<CategoryType>(null);

  // Initialize style objects if not present, but don't add default values
  // This allows AI maximum creativity when generating content
  const initializedStyles: StyleOptions = {
    ...styles,
    layout: styles.layout || {} as DocumentLayoutOptions,
    typographyOptions: styles.typographyOptions || {} as TypographyOptions,
    structure: styles.structure || {} as StructureOptions,
    content: styles.content || {} as ContentFormattingOptions,
    visual: styles.visual || {} as VisualElementOptions
  };

  const handleUpdateNestedStyle = (category: string, field: string, value: any): void => {
    // Log the update for debugging
    console.log(`Updating ${category}.${field} to:`, value);
    
    const currentCategory = initializedStyles[category as keyof StyleOptions] as Record<string, any> || {};
    const updatedCategory = {
      ...currentCategory,
      [field]: value
    };
    
    // Pass the object directly instead of stringifying it
    onUpdateStyle(category, updatedCategory);
  };

  const handleBackToMainMenu = () => {
    setActiveCategory(null);
  };

  // Main category menu
  if (activeCategory === null) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-3">
          <button
            className="p-4 border rounded-md flex items-center justify-between hover:border-primary"
            onClick={() => setActiveCategory("layout")}
          >
            <div className="text-left">
              <span className="font-medium">Layout</span>
              <p className="text-sm text-gray-500 mt-1">Page size, margins, columns, and more</p>
            </div>
            <span className="text-gray-400">→</span>
          </button>
          
          <button
            className="p-4 border rounded-md flex items-center justify-between hover:border-primary"
            onClick={() => setActiveCategory("typography")}
          >
            <div className="text-left">
              <span className="font-medium">Typography</span>
              <p className="text-sm text-gray-500 mt-1">Fonts, sizes, spacing, and text styles</p>
            </div>
            <span className="text-gray-400">→</span>
          </button>
          
          <button
            className="p-4 border rounded-md flex items-center justify-between hover:border-primary"
            onClick={() => setActiveCategory("structure")}
          >
            <div className="text-left">
              <span className="font-medium">Structure</span>
              <p className="text-sm text-gray-500 mt-1">Headings, numbering, and document organization</p>
            </div>
            <span className="text-gray-400">→</span>
          </button>
          
          <button
            className="p-4 border rounded-md flex items-center justify-between hover:border-primary"
            onClick={() => setActiveCategory("content")}
          >
            <div className="text-left">
              <span className="font-medium">Content</span>
              <p className="text-sm text-gray-500 mt-1">Text, lists, tables, and citations</p>
            </div>
            <span className="text-gray-400">→</span>
          </button>
          
          <button
            className="p-4 border rounded-md flex items-center justify-between hover:border-primary"
            onClick={() => setActiveCategory("visual")}
          >
            <div className="text-left">
              <span className="font-medium">Visual</span>
              <p className="text-sm text-gray-500 mt-1">Colors, charts, images, and branding</p>
            </div>
            <span className="text-gray-400">→</span>
          </button>
        </div>
        
        
        
      </div>
    );
  }

  // Category-specific content
  return (
    <div className="space-y-6">
      {/* Back button and header */}
      <div className="flex items-center mb-4">
        <button 
          onClick={handleBackToMainMenu}
          className="mr-3 p-2 rounded-full hover:border-primary"
          aria-label="Back to main menu"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        
        <div>
          <h3 className="font-medium text-lg">
            {activeCategory === "layout" && "Layout Options"}
            {activeCategory === "typography" && "Typography Options"}
            {activeCategory === "structure" && "Structure Options"}
            {activeCategory === "content" && "Content Formatting"}
            {activeCategory === "visual" && "Visual Elements"}
          </h3>
          <p className="text-sm text-gray-500">
            {activeCategory === "layout" && "Configure page size, margins, columns, and more"}
            {activeCategory === "typography" && "Configure fonts, sizes, spacing, and text styles"}
            {activeCategory === "structure" && "Configure headings, numbering, and document organization"}
            {activeCategory === "content" && "Configure text, lists, tables, and citations"}
            {activeCategory === "visual" && "Configure colors, charts, images, and branding"}
          </p>
        </div>
      </div>

      {/* Category-specific options */}
      {activeCategory === "layout" && (
        <DocumentLayoutSection 
          layout={initializedStyles.layout} 
          onUpdate={(field: string, value: any) => handleUpdateNestedStyle("layout", field, value)} 
        />
      )}
      
      {activeCategory === "typography" && (
        <TypographySection 
          typography={initializedStyles.typographyOptions} 
          onUpdate={(field: string, value: any) => handleUpdateNestedStyle("typographyOptions", field, value)} 
        />
      )}
      
      {activeCategory === "structure" && (
        <StructureSection 
          structure={initializedStyles.structure} 
          onUpdate={(field: string, value: any) => handleUpdateNestedStyle("structure", field, value)} 
        />
      )}
      
      {activeCategory === "content" && (
        <ContentFormattingSection 
          content={initializedStyles.content} 
          onUpdate={(field: string, value: any) => handleUpdateNestedStyle("content", field, value)} 
        />
      )}
      
      {activeCategory === "visual" && (
        <VisualElementsSection 
          visual={initializedStyles.visual} 
          onUpdate={(field: string, value: any) => handleUpdateNestedStyle("visual", field, value)} 
        />
      )}
    </div>
  );
}
