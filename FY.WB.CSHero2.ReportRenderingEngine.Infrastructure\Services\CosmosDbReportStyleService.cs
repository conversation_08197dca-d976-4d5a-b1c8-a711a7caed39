using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// CosmosDB implementation of the report style service
    /// </summary>
    public class CosmosDbReportStyleService : IReportStyleService
    {
        private readonly CosmosClient _cosmosClient;
        private readonly Container _container;
        private readonly ILogger<CosmosDbReportStyleService> _logger;
        private readonly CosmosDbOptions _options;

        public CosmosDbReportStyleService(
            CosmosClient cosmosClient,
            IOptions<CosmosDbOptions> options,
            ILogger<CosmosDbReportStyleService> logger)
        {
            _cosmosClient = cosmosClient ?? throw new ArgumentNullException(nameof(cosmosClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var database = _cosmosClient.GetDatabase(_options.DatabaseName);
            _container = database.GetContainer(_options.Containers.ReportStyles);
        }

        public async Task<ReportStyleDocument?> GetStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);

                var response = await _container.ReadItemAsync<ReportStyleDocument>(
                    styleDocumentId,
                    new PartitionKey(tenantId.ToString()),
                    cancellationToken: cancellationToken);

                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogDebug("Style document {StyleDocumentId} not found for tenant {TenantId}", styleDocumentId, tenantId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                throw;
            }
        }

        public async Task<ReportStyleDocument?> GetReportStyleAsync(Guid reportVersionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateReportStyleId(reportVersionId);
            return await GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public async Task<ReportStyleDocument?> GetTemplateStyleAsync(Guid templateId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateTemplateStyleId(templateId);
            return await GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public async Task<string> SaveStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Saving style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);

                // Ensure partition key is set
                style.PartitionKey = style.TenantId.ToString();
                style.CreatedAt = DateTime.UtcNow;
                style.LastModified = DateTime.UtcNow;

                var response = await _container.CreateItemAsync(
                    style,
                    new PartitionKey(style.PartitionKey),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully saved style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                return response.Resource.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                throw;
            }
        }

        public async Task<bool> UpdateStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);

                // Ensure partition key is set and update timestamp
                style.PartitionKey = style.TenantId.ToString();
                style.LastModified = DateTime.UtcNow;
                style.Version++;

                var response = await _container.ReplaceItemAsync(
                    style,
                    style.Id,
                    new PartitionKey(style.PartitionKey),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully updated style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                return response.StatusCode == System.Net.HttpStatusCode.OK;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Style document {StyleId} not found for update in tenant {TenantId}", style.Id, style.TenantId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                throw;
            }
        }

        public async Task<bool> DeleteStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);

                var response = await _container.DeleteItemAsync<ReportStyleDocument>(
                    styleDocumentId,
                    new PartitionKey(tenantId.ToString()),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully deleted style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                return response.StatusCode == System.Net.HttpStatusCode.NoContent;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Style document {StyleDocumentId} not found for deletion in tenant {TenantId}", styleDocumentId, tenantId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                throw;
            }
        }

        public async Task<List<ReportStyleDocument>> GetStylesByTenantAsync(Guid tenantId, string? styleType = null, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting styles for tenant {TenantId} with type filter {StyleType}", tenantId, styleType);

                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId AND c.isActive = true" +
                    (string.IsNullOrEmpty(styleType) ? "" : " AND c.styleType = @styleType"))
                    .WithParameter("@tenantId", tenantId.ToString());

                if (!string.IsNullOrEmpty(styleType))
                {
                    queryDefinition = queryDefinition.WithParameter("@styleType", styleType);
                }

                var results = new List<ReportStyleDocument>();
                using var iterator = _container.GetItemQueryIterator<ReportStyleDocument>(
                    queryDefinition,
                    requestOptions: new QueryRequestOptions
                    {
                        PartitionKey = new PartitionKey(tenantId.ToString())
                    });

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }

                _logger.LogDebug("Found {Count} styles for tenant {TenantId}", results.Count, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting styles for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<string> CopyStyleForReportVersionAsync(string sourceStyleId, Guid targetReportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Copying style {SourceStyleId} to report version {TargetReportVersionId} for tenant {TenantId}", 
                    sourceStyleId, targetReportVersionId, tenantId);

                // Get the source style
                var sourceStyle = await GetStyleAsync(sourceStyleId, tenantId, cancellationToken);
                if (sourceStyle == null)
                {
                    throw new InvalidOperationException($"Source style {sourceStyleId} not found");
                }

                // Create a copy for the new report version
                var newStyle = new ReportStyleDocument
                {
                    Id = ReportStyleDocument.CreateReportStyleId(targetReportVersionId),
                    PartitionKey = tenantId.ToString(),
                    ReportVersionId = targetReportVersionId,
                    TemplateId = null,
                    TenantId = tenantId,
                    StyleType = "report",
                    HtmlContent = sourceStyle.HtmlContent,
                    CssStyles = sourceStyle.CssStyles,
                    InlineStyles = new Dictionary<string, object>(sourceStyle.InlineStyles),
                    ComponentStyles = sourceStyle.ComponentStyles.ToDictionary(
                        kvp => kvp.Key,
                        kvp => new ComponentStyle
                        {
                            ComponentId = kvp.Value.ComponentId,
                            ComponentType = kvp.Value.ComponentType,
                            CssClasses = new List<string>(kvp.Value.CssClasses),
                            InlineStyles = new Dictionary<string, string>(kvp.Value.InlineStyles),
                            CustomCss = kvp.Value.CustomCss,
                            Layout = new ComponentLayout
                            {
                                Width = kvp.Value.Layout.Width,
                                Height = kvp.Value.Layout.Height,
                                Margin = kvp.Value.Layout.Margin,
                                Padding = kvp.Value.Layout.Padding,
                                Display = kvp.Value.Layout.Display,
                                Position = kvp.Value.Layout.Position
                            }
                        }),
                    Metadata = new StyleMetadata
                    {
                        Framework = sourceStyle.Metadata.Framework,
                        FrameworkVersion = sourceStyle.Metadata.FrameworkVersion,
                        Theme = sourceStyle.Metadata.Theme,
                        ColorScheme = sourceStyle.Metadata.ColorScheme,
                        Tags = new List<string>(sourceStyle.Metadata.Tags),
                        CustomProperties = new Dictionary<string, object>(sourceStyle.Metadata.CustomProperties),
                        Breakpoints = new Dictionary<string, string>(sourceStyle.Metadata.Breakpoints)
                    },
                    CreatedBy = userId,
                    LastModifiedBy = userId,
                    Version = 1,
                    IsActive = true
                };

                return await SaveStyleAsync(newStyle, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying style {SourceStyleId} to report version {TargetReportVersionId}", 
                    sourceStyleId, targetReportVersionId);
                throw;
            }
        }

        public async Task<string> CreateStyleFromTemplateAsync(Guid templateId, Guid reportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating style from template {TemplateId} for report version {ReportVersionId} in tenant {TenantId}", 
                    templateId, reportVersionId, tenantId);

                // Get the template style
                var templateStyle = await GetTemplateStyleAsync(templateId, tenantId, cancellationToken);
                if (templateStyle == null)
                {
                    throw new InvalidOperationException($"Template style for template {templateId} not found");
                }

                // Use the copy method to create a report style from the template
                return await CopyStyleForReportVersionAsync(templateStyle.Id, reportVersionId, tenantId, userId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating style from template {TemplateId} for report version {ReportVersionId}", 
                    templateId, reportVersionId);
                throw;
            }
        }

        public async Task<List<ReportStyleDocument>> SearchStylesAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Searching styles for tenant {TenantId} with term {SearchTerm}", tenantId, searchTerm);

                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId AND c.isActive = true AND " +
                    "(CONTAINS(LOWER(c.metadata.theme), LOWER(@searchTerm)) OR " +
                    "ARRAY_CONTAINS(c.metadata.tags, @searchTerm, true))")
                    .WithParameter("@tenantId", tenantId.ToString())
                    .WithParameter("@searchTerm", searchTerm);

                var results = new List<ReportStyleDocument>();
                using var iterator = _container.GetItemQueryIterator<ReportStyleDocument>(
                    queryDefinition,
                    requestOptions: new QueryRequestOptions
                    {
                        PartitionKey = new PartitionKey(tenantId.ToString())
                    });

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }

                _logger.LogDebug("Found {Count} styles matching search term {SearchTerm} for tenant {TenantId}", 
                    results.Count, searchTerm, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching styles for tenant {TenantId} with term {SearchTerm}", tenantId, searchTerm);
                throw;
            }
        }

        public async Task<List<ReportStyleDocument>> GetStylesByFrameworkAsync(Guid tenantId, string framework, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting styles for tenant {TenantId} with framework {Framework}", tenantId, framework);

                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId AND c.isActive = true AND " +
                    "LOWER(c.metadata.framework) = LOWER(@framework)")
                    .WithParameter("@tenantId", tenantId.ToString())
                    .WithParameter("@framework", framework);

                var results = new List<ReportStyleDocument>();
                using var iterator = _container.GetItemQueryIterator<ReportStyleDocument>(
                    queryDefinition,
                    requestOptions: new QueryRequestOptions
                    {
                        PartitionKey = new PartitionKey(tenantId.ToString())
                    });

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }

                _logger.LogDebug("Found {Count} styles with framework {Framework} for tenant {TenantId}", 
                    results.Count, framework, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting styles by framework {Framework} for tenant {TenantId}", framework, tenantId);
                throw;
            }
        }

        public async Task<StyleValidationResult> ValidateStyleAsync(ReportStyleDocument style)
        {
            await Task.CompletedTask; // Placeholder for async signature

            var result = new StyleValidationResult { IsValid = true };

            // Basic validation rules
            if (string.IsNullOrEmpty(style.Id))
            {
                result.Errors.Add("Style document ID is required");
                result.IsValid = false;
            }

            if (style.TenantId == Guid.Empty)
            {
                result.Errors.Add("Tenant ID is required");
                result.IsValid = false;
            }

            if (string.IsNullOrEmpty(style.StyleType))
            {
                result.Errors.Add("Style type is required");
                result.IsValid = false;
            }

            if (style.StyleType == "report" && !style.ReportVersionId.HasValue)
            {
                result.Errors.Add("Report version ID is required for report styles");
                result.IsValid = false;
            }

            if (style.StyleType == "template" && !style.TemplateId.HasValue)
            {
                result.Errors.Add("Template ID is required for template styles");
                result.IsValid = false;
            }

            // CSS validation warnings
            if (string.IsNullOrEmpty(style.CssStyles) && style.InlineStyles.Count == 0)
            {
                result.Warnings.Add("No CSS styles or inline styles defined");
            }

            return result;
        }
    }
}