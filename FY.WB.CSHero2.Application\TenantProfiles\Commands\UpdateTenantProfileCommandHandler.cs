using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.TenantProfiles.Commands
{
    public class UpdateTenantProfileCommandHandler : IRequestHandler<UpdateTenantProfileCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateTenantProfileCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateTenantProfileCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.TenantProfiles
                .FirstOrDefaultAsync(tp => tp.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(TenantProfile), request.Dto.Id);
            }

            // Verify that the tenant profile belongs to the current user's tenant
            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                // Or if an admin is allowed to update any tenant profile, add role check here
                throw new ForbiddenAccessException("User is not authorized to update this tenant profile.");
            }

            entity.Name = request.Dto.Name;
            entity.Email = request.Dto.Email;
            entity.Status = request.Dto.Status;
            entity.Phone = request.Dto.Phone;
            entity.Company = request.Dto.Company;
            entity.Subscription = request.Dto.Subscription;
            entity.BillingCycle = request.Dto.BillingCycle;
            entity.NextBillingDate = request.Dto.NextBillingDate;
            entity.SubscriptionStatus = request.Dto.SubscriptionStatus;

            if (request.Dto.PaymentMethod != null)
            {
                entity.SetPaymentMethod(request.Dto.PaymentMethod);
            }
            else // If null is passed, clear the payment method
            {
                entity.PaymentMethod = "{}"; 
            }

            if (request.Dto.BillingAddress != null)
            {
                entity.SetBillingAddress(request.Dto.BillingAddress);
            }
            else // If null is passed, clear the billing address
            {
                entity.BillingAddress = "{}";
            }

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
