import { NextRequest, NextResponse } from 'next/server';

/**
 * Compatibility layer for non-versioned API routes
 * Redirects to the versioned API routes
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const url = new URL(request.url);
  url.pathname = url.pathname.replace('/api/tenants', '/api/v1/tenants');
  return NextResponse.redirect(url);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const url = new URL(request.url);
  url.pathname = url.pathname.replace('/api/tenants', '/api/v1/tenants');
  return NextResponse.redirect(url);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const url = new URL(request.url);
  url.pathname = url.pathname.replace('/api/tenants', '/api/v1/tenants');
  return NextResponse.redirect(url);
}
