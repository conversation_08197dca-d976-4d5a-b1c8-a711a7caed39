# Multi-Storage Report Structure Architecture

## Overview

This document provides architectural diagrams and explanations for the multi-storage approach to the report structure. The architecture distributes report data across three specialized storage systems:

1. **SQL Database**: Store report metadata and style selections
2. **Azure Blob Storage**: Store rendered Next.js components with HTML and CSS
3. **Azure Cosmos DB**: Store report data (sections and fields) as JSON

## System Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WebApp["Web Application"]
        MobileApp["Mobile Application"]
        API["API Clients"]
    end

    subgraph "API Layer"
        ReportsController["Reports Controller"]
        ReportDataController["Report Data Controller"]
        ReportRenderingController["Report Rendering Controller"]
    end

    subgraph "Application Layer"
        ReportService["Report Service"]
        ReportDataService["Report Data Service"]
        ReportRenderingService["Report Rendering Service"]
        MigrationService["Data Migration Service"]
    end

    subgraph "Repository Layer"
        ReportMetadataRepo["Report Metadata Repository"]
        ReportDataRepo["Report Data Repository"]
        ReportComponentsRepo["Report Components Repository"]
    end

    subgraph "Storage Layer"
        SQL[(SQL Database)]
        CosmosDB[(Cosmos DB)]
        BlobStorage[(Blob Storage)]
    end

    WebApp --> ReportsController
    WebApp --> ReportDataController
    WebApp --> ReportRenderingController
    MobileApp --> ReportsController
    MobileApp --> ReportDataController
    MobileApp --> ReportRenderingController
    API --> ReportsController
    API --> ReportDataController
    API --> ReportRenderingController

    ReportsController --> ReportService
    ReportDataController --> ReportDataService
    ReportRenderingController --> ReportRenderingService
    ReportsController --> MigrationService

    ReportService --> ReportMetadataRepo
    ReportService --> ReportDataRepo
    ReportService --> ReportComponentsRepo
    ReportDataService --> ReportMetadataRepo
    ReportDataService --> ReportDataRepo
    ReportRenderingService --> ReportMetadataRepo
    ReportRenderingService --> ReportDataRepo
    ReportRenderingService --> ReportComponentsRepo
    MigrationService --> ReportMetadataRepo
    MigrationService --> ReportDataRepo
    MigrationService --> ReportComponentsRepo

    ReportMetadataRepo --> SQL
    ReportDataRepo --> CosmosDB
    ReportComponentsRepo --> BlobStorage

    classDef controller fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:2px;
    classDef repository fill:#bfb,stroke:#333,stroke-width:2px;
    classDef storage fill:#fbb,stroke:#333,stroke-width:2px;
    classDef client fill:#ddd,stroke:#333,stroke-width:2px;

    class ReportsController,ReportDataController,ReportRenderingController controller;
    class ReportService,ReportDataService,ReportRenderingService,MigrationService service;
    class ReportMetadataRepo,ReportDataRepo,ReportComponentsRepo repository;
    class SQL,CosmosDB,BlobStorage storage;
    class WebApp,MobileApp,API client;
```

## Data Flow Diagrams

### Report Creation Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as API Layer
    participant Service as Report Service
    participant SQLRepo as Report Metadata Repository
    participant CosmosRepo as Report Data Repository
    participant BlobRepo as Report Components Repository
    participant SQL as SQL Database
    participant Cosmos as Cosmos DB
    participant Blob as Blob Storage

    Client->>API: Create Report Request
    API->>Service: CreateReportAsync()
    
    Service->>CosmosRepo: CreateReportDataAsync()
    CosmosRepo->>Cosmos: Create Document
    Cosmos-->>CosmosRepo: Document ID
    CosmosRepo-->>Service: Document ID
    
    Service->>SQLRepo: CreateReportAsync()
    Note right of Service: Include Document ID reference
    SQLRepo->>SQL: Insert Report, Version, Style
    SQL-->>SQLRepo: Report ID
    SQLRepo-->>Service: Report ID
    
    Service-->>API: Report ID
    API-->>Client: Report Created Response
```

### Report Retrieval Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as API Layer
    participant Service as Report Service
    participant SQLRepo as Report Metadata Repository
    participant CosmosRepo as Report Data Repository
    participant BlobRepo as Report Components Repository
    participant SQL as SQL Database
    participant Cosmos as Cosmos DB
    participant Blob as Blob Storage

    Client->>API: Get Report Request
    API->>Service: GetReportAsync()
    
    Service->>SQLRepo: GetReportAsync()
    SQLRepo->>SQL: Select Report, Version, Style
    SQL-->>SQLRepo: Report Data
    SQLRepo-->>Service: Report Entity
    
    Service->>CosmosRepo: GetReportDataAsync()
    Note right of Service: Use Document ID from Report
    CosmosRepo->>Cosmos: Read Document
    Cosmos-->>CosmosRepo: Report Data Document
    CosmosRepo-->>Service: Report Data
    
    Service->>BlobRepo: GetComponentsMetadataAsync()
    Note right of Service: Use Blob ID from Report
    BlobRepo->>Blob: Get Metadata
    Blob-->>BlobRepo: Components Metadata
    BlobRepo-->>Service: Components Metadata
    
    Service-->>API: Combined Report DTO
    API-->>Client: Report Response
```

### Report Rendering Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as API Layer
    participant RenderService as Report Rendering Service
    participant DataService as Report Data Service
    participant SQLRepo as Report Metadata Repository
    participant CosmosRepo as Report Data Repository
    participant BlobRepo as Report Components Repository
    participant SQL as SQL Database
    participant Cosmos as Cosmos DB
    participant Blob as Blob Storage

    Client->>API: Render Report Request
    API->>RenderService: RenderReportAsync()
    
    RenderService->>SQLRepo: GetReportAsync()
    SQLRepo->>SQL: Select Report, Version, Style
    SQL-->>SQLRepo: Report Data
    SQLRepo-->>RenderService: Report Entity
    
    RenderService->>CosmosRepo: GetReportDataAsync()
    CosmosRepo->>Cosmos: Read Document
    Cosmos-->>CosmosRepo: Report Data Document
    CosmosRepo-->>RenderService: Report Data
    
    RenderService->>BlobRepo: GetAllComponentsAsync()
    BlobRepo->>Blob: Get Components
    Blob-->>BlobRepo: Component Files
    BlobRepo-->>RenderService: Component Definitions
    
    RenderService->>RenderService: Render Components with Data
    
    RenderService-->>API: Rendered HTML/CSS
    API-->>Client: Rendered Report
```

### Data Migration Flow

```mermaid
sequenceDiagram
    participant Admin as Administrator
    participant API as API Layer
    participant MigrationService as Data Migration Service
    participant SQLRepo as Report Metadata Repository
    participant CosmosRepo as Report Data Repository
    participant BlobRepo as Report Components Repository
    participant SQL as SQL Database
    participant Cosmos as Cosmos DB
    participant Blob as Blob Storage

    Admin->>API: Migrate Report Data Request
    API->>MigrationService: MigrateReportDataAsync()
    
    MigrationService->>SQLRepo: GetReportsAsync()
    SQLRepo->>SQL: Select All Reports with Versions
    SQL-->>SQLRepo: Reports Data
    SQLRepo-->>MigrationService: Report Entities
    
    loop For Each Report
        MigrationService->>MigrationService: Extract Sections and Fields
        
        MigrationService->>CosmosRepo: CreateReportDataAsync()
        CosmosRepo->>Cosmos: Create Document
        Cosmos-->>CosmosRepo: Document ID
        CosmosRepo-->>MigrationService: Document ID
        
        MigrationService->>MigrationService: Extract Components
        
        MigrationService->>BlobRepo: SaveComponentsAsync()
        BlobRepo->>Blob: Upload Components
        Blob-->>BlobRepo: Blob ID
        BlobRepo-->>MigrationService: Blob ID
        
        MigrationService->>SQLRepo: UpdateReportAsync()
        Note right of MigrationService: Update with Document and Blob IDs
        SQLRepo->>SQL: Update Report and Version
        SQL-->>SQLRepo: Success
        SQLRepo-->>MigrationService: Success
    end
    
    MigrationService-->>API: Migration Results
    API-->>Admin: Migration Complete Response
```

## Component Diagrams

### Domain Model

```mermaid
classDiagram
    class Report {
        +Guid Id
        +string Name
        +string Description
        +Guid? TenantId
        +Guid? CreatorId
        +DateTime CreatedAt
        +DateTime? LastModifiedAt
        +Guid? LastModifiedById
        +bool IsPublished
        +bool IsArchived
        +string DataDocumentId
        +string ComponentsBlobId
    }
    
    class ReportVersion {
        +Guid Id
        +Guid ReportId
        +int VersionNumber
        +string VersionName
        +Guid? CreatorId
        +DateTime CreatedAt
        +bool IsCurrent
        +bool IsPublished
        +string DataDocumentId
        +string ComponentsBlobId
    }
    
    class ReportStyle {
        +Guid Id
        +Guid ReportId
        +string Theme
        +string ColorScheme
        +string Typography
        +string Spacing
        +string LayoutOptionsJson
        +string TypographyOptionsJson
        +string StructureOptionsJson
        +string ContentOptionsJson
        +string VisualOptionsJson
    }
    
    class ReportData {
        +string Id
        +string ReportId
        +string VersionId
        +int VersionNumber
        +List~ReportSection~ Sections
        +ReportDataMetadata Metadata
    }
    
    class ReportSection {
        +string Id
        +string Title
        +string Type
        +int Order
        +List~ReportSectionField~ Fields
        +Dictionary~string,object~ Metadata
    }
    
    class ReportSectionField {
        +string Id
        +string Name
        +string Type
        +string Content
        +int Order
        +Dictionary~string,object~ Metadata
    }
    
    class ComponentsMetadata {
        +string ReportId
        +string VersionId
        +List~ComponentMetadata~ Components
        +DateTime CreatedAt
        +string CreatedBy
    }
    
    class ComponentMetadata {
        +string Id
        +string Name
        +string SectionId
        +string FileName
        +List~string~ Imports
        +List~string~ Props
    }
    
    class ComponentDefinition {
        +string Id
        +string Name
        +string SectionId
        +string Code
        +List~string~ Imports
        +List~string~ Props
    }
    
    Report "1" -- "many" ReportVersion : has
    Report "1" -- "1" ReportStyle : has
    Report "1" -- "many" ReportData : references
    ReportVersion "1" -- "1" ReportData : references
    ReportData "1" -- "many" ReportSection : contains
    ReportSection "1" -- "many" ReportSectionField : contains
    Report "1" -- "many" ComponentsMetadata : references
    ReportVersion "1" -- "1" ComponentsMetadata : references
    ComponentsMetadata "1" -- "many" ComponentMetadata : contains
    ComponentMetadata "1" -- "1" ComponentDefinition : describes
```

### Repository Layer

```mermaid
classDiagram
    class IReportMetadataRepository {
        <<interface>>
        +Task~Report~ GetReportAsync(Guid reportId)
        +Task~IEnumerable~Report~~ GetReportsAsync(Guid? tenantId)
        +Task~Guid~ CreateReportAsync(Report report)
        +Task UpdateReportAsync(Report report)
        +Task DeleteReportAsync(Guid reportId)
        +Task~ReportVersion~ GetReportVersionAsync(Guid versionId)
        +Task~IEnumerable~ReportVersion~~ GetReportVersionsAsync(Guid reportId)
        +Task~Guid~ CreateReportVersionAsync(ReportVersion version)
        +Task UpdateReportVersionAsync(ReportVersion version)
        +Task DeleteReportVersionAsync(Guid versionId)
        +Task~ReportStyle~ GetReportStyleAsync(Guid reportId)
        +Task~Guid~ CreateReportStyleAsync(ReportStyle style)
        +Task UpdateReportStyleAsync(ReportStyle style)
    }
    
    class IReportDataRepository {
        <<interface>>
        +Task~ReportData~ GetReportDataAsync(string documentId)
        +Task~ReportData~ GetReportDataByReportIdAsync(string reportId, string versionId)
        +Task~string~ CreateReportDataAsync(ReportData data)
        +Task UpdateReportDataAsync(ReportData data)
        +Task DeleteReportDataAsync(string documentId)
        +Task~ReportSection~ GetReportSectionAsync(string documentId, string sectionId)
        +Task AddReportSectionAsync(string documentId, ReportSection section)
        +Task UpdateReportSectionAsync(string documentId, ReportSection section)
        +Task DeleteReportSectionAsync(string documentId, string sectionId)
        +Task~ReportSectionField~ GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId)
        +Task AddReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field)
        +Task UpdateReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field)
        +Task DeleteReportSectionFieldAsync(string documentId, string sectionId, string fieldId)
    }
    
    class IReportComponentsRepository {
        <<interface>>
        +Task~string~ SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable~ComponentDefinition~ components)
        +Task~ComponentsMetadata~ GetComponentsMetadataAsync(string blobId)
        +Task~ComponentDefinition~ GetComponentAsync(string blobId, string componentName)
        +Task~IEnumerable~ComponentDefinition~~ GetAllComponentsAsync(string blobId)
        +Task DeleteComponentsAsync(string blobId)
    }
    
    class ReportMetadataRepository {
        -IApplicationDbContext _dbContext
        -ILogger~ReportMetadataRepository~ _logger
        +Task~Report~ GetReportAsync(Guid reportId)
        +Task~IEnumerable~Report~~ GetReportsAsync(Guid? tenantId)
        +Task~Guid~ CreateReportAsync(Report report)
        +Task UpdateReportAsync(Report report)
        +Task DeleteReportAsync(Guid reportId)
        +Task~ReportVersion~ GetReportVersionAsync(Guid versionId)
        +Task~IEnumerable~ReportVersion~~ GetReportVersionsAsync(Guid reportId)
        +Task~Guid~ CreateReportVersionAsync(ReportVersion version)
        +Task UpdateReportVersionAsync(ReportVersion version)
        +Task DeleteReportVersionAsync(Guid versionId)
        +Task~ReportStyle~ GetReportStyleAsync(Guid reportId)
        +Task~Guid~ CreateReportStyleAsync(ReportStyle style)
        +Task UpdateReportStyleAsync(ReportStyle style)
    }
    
    class ReportDataRepository {
        -CosmosClient _cosmosClient
        -Container _container
        -ILogger~ReportDataRepository~ _logger
        +Task~ReportData~ GetReportDataAsync(string documentId)
        +Task~ReportData~ GetReportDataByReportIdAsync(string reportId, string versionId)
        +Task~string~ CreateReportDataAsync(ReportData data)
        +Task UpdateReportDataAsync(ReportData data)
        +Task DeleteReportDataAsync(string documentId)
        +Task~ReportSection~ GetReportSectionAsync(string documentId, string sectionId)
        +Task AddReportSectionAsync(string documentId, ReportSection section)
        +Task UpdateReportSectionAsync(string documentId, ReportSection section)
        +Task DeleteReportSectionAsync(string documentId, string sectionId)
        +Task~ReportSectionField~ GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId)
        +Task AddReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field)
        +Task UpdateReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field)
        +Task DeleteReportSectionFieldAsync(string documentId, string sectionId, string fieldId)
    }
    
    class ReportComponentsRepository {
        -BlobServiceClient _blobServiceClient
        -string _containerName
        -ILogger~ReportComponentsRepository~ _logger
        +Task~string~ SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable~ComponentDefinition~ components)
        +Task~ComponentsMetadata~ GetComponentsMetadataAsync(string blobId)
        +Task~ComponentDefinition~ GetComponentAsync(string blobId, string componentName)
        +Task~IEnumerable~ComponentDefinition~~ GetAllComponentsAsync(string blobId)
        +Task DeleteComponentsAsync(string blobId)
    }
    
    IReportMetadataRepository <|.. ReportMetadataRepository : implements
    IReportDataRepository <|.. ReportDataRepository : implements
    IReportComponentsRepository <|.. ReportComponentsRepository : implements
```

### Service Layer

```mermaid
classDiagram
    class IReportService {
        <<interface>>
        +Task~ReportDto~ GetReportAsync(Guid reportId)
        +Task~IEnumerable~ReportDto~~ GetReportsAsync(Guid? tenantId)
        +Task~Guid~ CreateReportAsync(CreateReportDto createReportDto)
        +Task UpdateReportAsync(UpdateReportDto updateReportDto)
        +Task DeleteReportAsync(Guid reportId)
        +Task~ReportVersionDto~ GetReportVersionAsync(Guid versionId)
        +Task~IEnumerable~ReportVersionDto~~ GetReportVersionsAsync(Guid reportId)
        +Task~Guid~ CreateReportVersionAsync(CreateReportVersionDto createVersionDto)
        +Task UpdateReportVersionAsync(UpdateReportVersionDto updateVersionDto)
        +Task DeleteReportVersionAsync(Guid versionId)
        +Task~ReportStyleDto~ GetReportStyleAsync(Guid reportId)
        +Task~Guid~ CreateReportStyleAsync(CreateReportStyleDto createStyleDto)
        +Task UpdateReportStyleAsync(UpdateReportStyleDto updateStyleDto)
    }
    
    class IReportDataService {
        <<interface>>
        +Task~ReportDataDto~ GetReportDataAsync(Guid reportId, Guid versionId)
        +Task~string~ CreateReportDataAsync(CreateReportDataDto createDataDto)
        +Task UpdateReportDataAsync(UpdateReportDataDto updateDataDto)
        +Task DeleteReportDataAsync(Guid reportId, Guid versionId)
        +Task~ReportSectionDto~ GetReportSectionAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~IEnumerable~ReportSectionDto~~ GetReportSectionsAsync(Guid reportId, Guid versionId)
        +Task~string~ AddReportSectionAsync(Guid reportId, Guid versionId, CreateReportSectionDto createSectionDto)
        +Task UpdateReportSectionAsync(Guid reportId, Guid versionId, UpdateReportSectionDto updateSectionDto)
        +Task DeleteReportSectionAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~ReportSectionFieldDto~ GetReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId)
        +Task~IEnumerable~ReportSectionFieldDto~~ GetReportSectionFieldsAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~string~ AddReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, CreateReportSectionFieldDto createFieldDto)
        +Task UpdateReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, UpdateReportSectionFieldDto updateFieldDto)
        +Task DeleteReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId)
    }
    
    class IReportRenderingService {
        <<interface>>
        +Task~string~ RenderReportAsync(Guid reportId, Guid versionId)
        +Task~IEnumerable~ComponentDefinitionDto~~ GetReportComponentsAsync(Guid reportId, Guid versionId)
        +Task~ComponentDefinitionDto~ GetReportComponentAsync(Guid reportId, Guid versionId, string componentName)
        +Task~byte[]~ ExportReportAsPdfAsync(Guid reportId, Guid versionId)
        +Task~byte[]~ ExportReportAsHtmlAsync(Guid reportId, Guid versionId)
    }
    
    class IReportDataMigrationService {
        <<interface>>
        +Task~int~ MigrateReportDataAsync()
        +Task~MigrationStatusDto~ GetMigrationStatusAsync()
        +Task~bool~ ValidateMigrationAsync(Guid reportId)
        +Task~bool~ RollbackMigrationAsync(Guid reportId)
    }
    
    class ReportService {
        -IReportMetadataRepository _metadataRepository
        -IReportDataRepository _dataRepository
        -IReportComponentsRepository _componentsRepository
        -IMapper _mapper
        -ILogger~ReportService~ _logger
        +Task~ReportDto~ GetReportAsync(Guid reportId)
        +Task~IEnumerable~ReportDto~~ GetReportsAsync(Guid? tenantId)
        +Task~Guid~ CreateReportAsync(CreateReportDto createReportDto)
        +Task UpdateReportAsync(UpdateReportDto updateReportDto)
        +Task DeleteReportAsync(Guid reportId)
        +Task~ReportVersionDto~ GetReportVersionAsync(Guid versionId)
        +Task~IEnumerable~ReportVersionDto~~ GetReportVersionsAsync(Guid reportId)
        +Task~Guid~ CreateReportVersionAsync(CreateReportVersionDto createVersionDto)
        +Task UpdateReportVersionAsync(UpdateReportVersionDto updateVersionDto)
        +Task DeleteReportVersionAsync(Guid versionId)
        +Task~ReportStyleDto~ GetReportStyleAsync(Guid reportId)
        +Task~Guid~ CreateReportStyleAsync(CreateReportStyleDto createStyleDto)
        +Task UpdateReportStyleAsync(UpdateReportStyleDto updateStyleDto)
    }
    
    class ReportDataService {
        -IReportMetadataRepository _metadataRepository
        -IReportDataRepository _dataRepository
        -IMapper _mapper
        -ILogger~ReportDataService~ _logger
        +Task~ReportDataDto~ GetReportDataAsync(Guid reportId, Guid versionId)
        +Task~string~ CreateReportDataAsync(CreateReportDataDto createDataDto)
        +Task UpdateReportDataAsync(UpdateReportDataDto updateDataDto)
        +Task DeleteReportDataAsync(Guid reportId, Guid versionId)
        +Task~ReportSectionDto~ GetReportSectionAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~IEnumerable~ReportSectionDto~~ GetReportSectionsAsync(Guid reportId, Guid versionId)
        +Task~string~ AddReportSectionAsync(Guid reportId, Guid versionId, CreateReportSectionDto createSectionDto)
        +Task UpdateReportSectionAsync(Guid reportId, Guid versionId, UpdateReportSectionDto updateSectionDto)
        +Task DeleteReportSectionAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~ReportSectionFieldDto~ GetReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId)
        +Task~IEnumerable~ReportSectionFieldDto~~ GetReportSectionFieldsAsync(Guid reportId, Guid versionId, string sectionId)
        +Task~string~ AddReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, CreateReportSectionFieldDto createFieldDto)
        +Task UpdateReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, UpdateReportSectionFieldDto updateFieldDto)
        +Task DeleteReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId)
    }
    
    class ReportRenderingService {
        -IReportMetadataRepository _metadataRepository
        -IReportDataRepository _dataRepository
        -IReportComponentsRepository _componentsRepository
        -IMapper _mapper
        -ILogger~ReportRenderingService~ _logger
        +Task~string~ RenderReportAsync(Guid reportId, Guid versionId)
        +Task~IEnumerable~ComponentDefinitionDto~~ GetReportComponentsAsync(Guid reportId, Guid versionId)
        +Task~ComponentDefinitionDto~ GetReportComponentAsync(Guid reportId, Guid versionId, string componentName)
        +Task~byte[]~ ExportReportAsPdfAsync(Guid reportId, Guid versionId)
        +Task~byte[]~ ExportReportAsHtmlAsync(Guid reportId, Guid versionId)
    }
    
    class ReportDataMigrationService {
        -IApplicationDbContext _sqlContext
        -IReportDataRepository _cosmosRepository
        -IReportComponentsRepository _blobRepository
        -ILogger~ReportDataMigrationService~ _logger
        +Task~int~ MigrateReportDataAsync()
        +Task~MigrationStatusDto~ GetMigrationStatusAsync()
        +Task~bool~ ValidateMigrationAsync(Guid reportId)
        +Task~bool~ RollbackMigrationAsync(Guid reportId)
    }
    
    IReportService <|.. ReportService : implements
    IReportDataService <|.. ReportDataService : implements
    IReportRenderingService <|.. ReportRenderingService : implements
    IReportDataMigrationService <|.. ReportDataMigrationService : implements
```

## Storage Structure

### SQL Database Schema

```mermaid
erDiagram
    Reports {
        Guid Id PK
        string Name
        string Description
        Guid TenantId FK
        Guid CreatorId FK
        DateTime CreatedAt
        DateTime LastModifiedAt
        Guid LastModifiedById FK
        bool IsPublished
        bool IsArchived
        string DataDocumentId
        string ComponentsBlobId
    }
    
    ReportVersions {
        Guid Id PK
        Guid ReportId FK
        int VersionNumber
        string VersionName
        Guid CreatorId FK
        DateTime CreatedAt
        bool IsCurrent
        bool IsPublished
        string DataDocumentId
        string ComponentsBlobId
    }
    
    ReportStyles {
        Guid Id PK
        Guid ReportId FK
        string Theme
        string ColorScheme
        string Typography
        string Spacing
        string LayoutOptionsJson
        string TypographyOptionsJson
        string StructureOptionsJson
        string ContentOptionsJson
        string VisualOptionsJson
    }
    
    TenantProfiles {
        Guid Id PK
        string Name
        string Domain
    }
    
    ApplicationUsers {
        Guid Id PK
        string UserName
        string Email
        Guid TenantId FK
    }
    
    Reports ||--o{ ReportVersions : "has"
    Reports ||--|| ReportStyles : "has"
    Reports }o--|| TenantProfiles : "belongs to"
    Reports }o--|| ApplicationUsers : "created by"
    Reports }o--|| ApplicationUsers : "modified by"
    ReportVersions }o--|| ApplicationUsers : "created by"
```

### Cosmos DB Document Structure

```json
{
  "id": "report-data-guid",
  "reportId": "guid",
  "versionId": "guid",
  "versionNumber": 1,
  "sections": [
    {
      "id": "section-guid",
      "title": "Executive Summary",
      "type": "text",
      "order": 0,
      "fields": [
        {
          "id": "field-guid",
          "name": "summary",
          "type": "richtext",
          "content": "<p>This is the executive summary...</p>",
          "order": 0,
          "metadata": {
            "wordCount": 150,
            "lastEdited": "2025-05-15T10:30:00Z"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "backgroundColor": "#f5f5f5"
      }
    },
    {
      "id": "section-guid-2",
      "title": "Financial Results",
      "type": "chart",
      "order": 1,
      "fields": [
        {
          "id": "field-guid-2",
          "name": "quarterlyRevenue",
          "type": "chart-data",
          "content": "{\"labels\":[\"Q1\",\"Q2\",\"Q3\",\"Q4\"],\"datasets\":[{\"label\":\"Revenue\",\"data\":[10000,12000,15000,20000]}]}",
          "order": 0,
          "metadata": {
            "chartType": "bar",
            "dataSource": "financial-system"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "chartHeight": 400
      }
    }
  ],
  "metadata": {
    "createdAt": "2025-05-15T10:00:00Z",
    "createdBy": "user-guid",
    "lastModifiedAt": "2025-05-15T11:30:00Z",
    "lastModifiedBy": "user-guid"
  }
}
```

### Blob Storage Structure

```
reports/
├── {reportId}/
│   ├── {versionId}/
│   │   ├── metadata.json
│   │   ├── components/
│   │   │   ├── ExecutiveSummary.tsx
│   │   │   ├── FinancialResults.tsx
│   │   │   ├── KeyMetrics.tsx
│   │   │   └── Conclusion.tsx
│   │   └── assets/
│   │       ├── logo.png
│   │       └── chart-background.svg
│   └── {versionId-2}/
│       ├── metadata.json
│       ├── components/
│       │   ├── ExecutiveSummary.tsx
│       │   ├── FinancialResults.tsx
│       │   ├── KeyMetrics.tsx
│       │   └── Conclusion.tsx
│       └── assets/
│           ├── logo.png
│           └── chart-background.svg
└── {reportId-2}/
    └── ...
```

## Deployment Architecture

```m