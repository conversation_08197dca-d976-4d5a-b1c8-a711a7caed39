namespace FY.WB.CSHero2.Application.Uploads.Dtos
{
    public class UploadQueryParametersDto
    {
        public string? Search { get; set; } // Search by Filename
        public string? ContentType { get; set; } // Filter by exact MIME type e.g., "application/pdf" or "image/jpeg"
        public string? SortBy { get; set; } // e.g., "Filename", "Size", "ContentType", "CreatedAt"
        public string? SortOrder { get; set; } // "asc" or "desc"
        public int? Page { get; set; } = 1;
        public int? Limit { get; set; } = 10;
    }
}
