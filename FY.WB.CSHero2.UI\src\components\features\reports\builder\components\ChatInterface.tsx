"use client";

import { useState, useRef, useEffect } from "react";
import { ChatInterfaceProps } from "../types";

export function ChatInterface({
  messages,
  onSendMessage,
  activeSection,
  sections,
  onSectionChange
}: ChatInterfaceProps) {
  const [message, setMessage] = useState<string>("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const handleSendMessage = () => {
    if (message.trim()) {
      onSendMessage(message, activeSection || undefined);
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full bg-white border-l border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-medium">Chat Interface</h3>
        <p className="text-sm text-gray-500">Refine your report with AI assistance</p>
      </div>

      {/* Section Selector */}
      <div className="p-4 border-b border-gray-200">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Active Section Context
        </label>
        <select
          className="w-full p-2 border border-gray-300 rounded-md"
          value={activeSection || ""}
          onChange={(e) => onSectionChange(e.target.value || null)}
        >
          <option value="">All Sections</option>
          {sections.map((section) => (
            <option key={section.id} value={section.id}>
              {section.title}
            </option>
          ))}
        </select>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start a conversation to refine your report.</p>
          </div>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              className={`p-3 rounded-lg max-w-[80%] ${
                msg.sender === "user"
                  ? "bg-blue-100 ml-auto"
                  : "bg-gray-100"
              }`}
            >
              <div className="text-sm">
                {msg.content}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {new Date(msg.timestamp).toLocaleTimeString()}
                {msg.sectionId && (
                  <span className="ml-2 bg-gray-200 px-2 py-0.5 rounded-full text-xs">
                    {sections.find(s => s.id === msg.sectionId)?.title || "Unknown Section"}
                  </span>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex">
          <textarea
            className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Type your message here..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            rows={3}
          />
          <button
            className="bg-primary text-white px-4 rounded-r-md hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            onClick={handleSendMessage}
          >
            Send
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Press Enter to send. Use Shift+Enter for a new line.
        </p>
      </div>
    </div>
  );
}
