import { NextRequest, NextResponse } from 'next/server';
import { join } from 'path';
import fs from 'fs';

/**
 * GET /api/storage/[...path]
 * Serves static files from the storage directory
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const pathElements = Array.isArray(params.path) ? params.path : [params.path];
    const filePath = join(process.cwd(), 'public', 'storage', ...pathElements);

    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Determine content type based on file extension
    const extension = filePath.split('.').pop()?.toLowerCase();
    let contentType = 'application/octet-stream';

    switch (extension) {
      case 'html':
        contentType = 'text/html';
        break;
      case 'css':
        contentType = 'text/css';
        break;
      case 'js':
        contentType = 'application/javascript';
        break;
      case 'json':
        contentType = 'application/json';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
    }

    // Read the file and return it with the appropriate content type
    const fileContent = fs.readFileSync(filePath);
    
    return new NextResponse(fileContent, {
      headers: {
        'Content-Type': contentType
      }
    });
  } catch (error) {
    console.error('Error serving static file:', error);
    return NextResponse.json(
      { error: 'Failed to serve file' },
      { status: 500 }
    );
  }
}
