﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddReportSectionEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReportVersions_CreatedAt",
                table: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_ComponentDefinitions_GeneratedAt",
                table: "ComponentDefinitions");

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "Templates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataBlobPath",
                table: "ReportVersions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDataInBlob",
                table: "ReportVersions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "ReportVersions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AssetBlobPath",
                table: "ComponentDefinitions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StyleDocumentId",
                table: "ComponentDefinitions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ReportSections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSections_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReportSectionFields",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SectionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSectionFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSectionFields_ReportSections_SectionId",
                        column: x => x.SectionId,
                        principalTable: "ReportSections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreationTime",
                table: "ReportVersions",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_CreationTime",
                table: "ComponentDefinitions",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSectionFields_SectionId",
                table: "ReportSectionFields",
                column: "SectionId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSectionFields_SectionId_Order",
                table: "ReportSectionFields",
                columns: new[] { "SectionId", "Order" });

            migrationBuilder.CreateIndex(
                name: "IX_ReportSections_ReportId",
                table: "ReportSections",
                column: "ReportId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSections_ReportId_Order",
                table: "ReportSections",
                columns: new[] { "ReportId", "Order" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReportSectionFields");

            migrationBuilder.DropTable(
                name: "ReportSections");

            migrationBuilder.DropIndex(
                name: "IX_ReportVersions_CreationTime",
                table: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_ComponentDefinitions_CreationTime",
                table: "ComponentDefinitions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "DataBlobPath",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "IsDataInBlob",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "AssetBlobPath",
                table: "ComponentDefinitions");

            migrationBuilder.DropColumn(
                name: "StyleDocumentId",
                table: "ComponentDefinitions");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreatedAt",
                table: "ReportVersions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_GeneratedAt",
                table: "ComponentDefinitions",
                column: "GeneratedAt");
        }
    }
}
