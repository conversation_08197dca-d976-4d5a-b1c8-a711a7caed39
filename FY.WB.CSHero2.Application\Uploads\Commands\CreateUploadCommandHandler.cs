using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Uploads.Commands
{
    public class CreateUploadCommandHandler : IRequestHandler<CreateUploadCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateUploadCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateUploadCommand request, CancellationToken cancellationToken)
        {
            var entity = new Upload
            {
                Filename = request.Dto.Filename,
                Size = request.Dto.Size,
                ContentType = request.Dto.ContentType,
                StoragePath = request.Dto.StoragePath,
                StorageProvider = request.Dto.StorageProvider,
                ExternalUrl = request.Dto.ExternalUrl,
                Checksum = request.Dto.Checksum,
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };
            
            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create an Upload.");
            }
            entity.TenantId = _currentUserService.TenantId.Value;

            _context.Uploads.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
