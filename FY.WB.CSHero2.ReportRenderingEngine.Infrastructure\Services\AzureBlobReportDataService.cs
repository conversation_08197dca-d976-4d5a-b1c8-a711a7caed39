using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Azure Blob Storage implementation of the report data service
    /// </summary>
    public class AzureBlobReportDataService : IReportDataBlobService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly BlobContainerClient _containerClient;
        private readonly ILogger<AzureBlobReportDataService> _logger;
        private readonly BlobStorageOptions _options;

        public AzureBlobReportDataService(
            BlobServiceClient blobServiceClient,
            IOptions<BlobStorageOptions> options,
            ILogger<AzureBlobReportDataService> logger)
        {
            _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _containerClient = _blobServiceClient.GetBlobContainerClient(_options.ContainerName);
        }

        public async Task<Dictionary<string, object>> GetReportDataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DownloadContentAsync(cancellationToken);

                var jsonContent = response.Value.Content.ToString();
                var data = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

                _logger.LogDebug("Successfully retrieved report data from blob {BlobPath}", blobPath);
                return data ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<Stream> GetReportDataStreamAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data stream from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.OpenReadAsync(cancellationToken: cancellationToken);

                _logger.LogDebug("Successfully opened stream for blob {BlobPath}", blobPath);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data stream from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<string> GetReportDataJsonAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data JSON from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DownloadContentAsync(cancellationToken);

                var jsonContent = response.Value.Content.ToString();
                _logger.LogDebug("Successfully retrieved JSON data from blob {BlobPath}", blobPath);
                return jsonContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data JSON from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<string> SaveReportDataAsync(Guid tenantId, Guid reportId, Guid versionId, Dictionary<string, object> data, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
                _logger.LogDebug("Saving report data to blob {BlobPath}", blobPath);

                var jsonData = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
                await SaveReportDataJsonAsync(tenantId, reportId, versionId, jsonData, cancellationToken);

                _logger.LogInformation("Successfully saved report data to blob {BlobPath}", blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving report data for tenant {TenantId}, report {ReportId}, version {VersionId}", 
                    tenantId, reportId, versionId);
                throw;
            }
        }

        public async Task<string> SaveReportDataStreamAsync(Guid tenantId, Guid reportId, Guid versionId, Stream dataStream, string contentType = "application/json", CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
                _logger.LogDebug("Saving report data stream to blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);

                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = contentType
                    },
                    Metadata = new Dictionary<string, string>
                    {
                        ["tenantId"] = tenantId.ToString(),
                        ["reportId"] = reportId.ToString(),
                        ["versionId"] = versionId.ToString(),
                        ["uploadedAt"] = DateTime.UtcNow.ToString("O")
                    }
                };

                await blobClient.UploadAsync(dataStream, uploadOptions, cancellationToken);

                _logger.LogInformation("Successfully saved report data stream to blob {BlobPath}", blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving report data stream for tenant {TenantId}, report {ReportId}, version {VersionId}", 
                    tenantId, reportId, versionId);
                throw;
            }
        }

        public async Task<string> SaveReportDataJsonAsync(Guid tenantId, Guid reportId, Guid versionId, string jsonData, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
                _logger.LogDebug("Saving report data JSON to blob {BlobPath}", blobPath);

                using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonData));
                return await SaveReportDataStreamAsync(tenantId, reportId, versionId, stream, _options.DefaultContentType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving report data JSON for tenant {TenantId}, report {ReportId}, version {VersionId}", 
                    tenantId, reportId, versionId);
                throw;
            }
        }

        public async Task<bool> UpdateReportDataAsync(string blobPath, Dictionary<string, object> data, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report data in blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);

                // Check if blob exists
                var exists = await blobClient.ExistsAsync(cancellationToken);
                if (!exists.Value)
                {
                    _logger.LogWarning("Blob {BlobPath} does not exist for update", blobPath);
                    return false;
                }

                var jsonData = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
                using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonData));

                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = _options.DefaultContentType
                    }
                };

                await blobClient.UploadAsync(stream, uploadOptions, cancellationToken);

                _logger.LogInformation("Successfully updated report data in blob {BlobPath}", blobPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report data in blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<bool> DeleteReportDataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report data from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);

                if (response.Value)
                {
                    _logger.LogInformation("Successfully deleted report data from blob {BlobPath}", blobPath);
                }
                else
                {
                    _logger.LogWarning("Blob {BlobPath} did not exist for deletion", blobPath);
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report data from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.ExistsAsync(cancellationToken);
                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if blob {BlobPath} exists", blobPath);
                throw;
            }
        }

        public async Task<long?> GetSizeAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var exists = await blobClient.ExistsAsync(cancellationToken);
                
                if (!exists.Value)
                {
                    return null;
                }

                var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
                return properties.Value.ContentLength;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting size of blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<BlobMetadata?> GetMetadataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobPath);
                var exists = await blobClient.ExistsAsync(cancellationToken);
                
                if (!exists.Value)
                {
                    return null;
                }

                var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
                var props = properties.Value;

                return new BlobMetadata
                {
                    BlobPath = blobPath,
                    Size = props.ContentLength,
                    ContentType = props.ContentType ?? string.Empty,
                    CreatedAt = props.CreatedOn.DateTime,
                    LastModified = props.LastModified.DateTime,
                    ETag = props.ETag.ToString(),
                    Properties = new Dictionary<string, string>(props.Metadata)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting metadata for blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<string> CopyReportDataAsync(string sourceBlobPath, Guid tenantId, Guid reportId, Guid versionId, CancellationToken cancellationToken = default)
        {
            try
            {
                var targetBlobPath = GenerateReportDataPath(tenantId, reportId, versionId);
                _logger.LogDebug("Copying report data from {SourceBlobPath} to {TargetBlobPath}", sourceBlobPath, targetBlobPath);

                var sourceBlobClient = _containerClient.GetBlobClient(sourceBlobPath);
                var targetBlobClient = _containerClient.GetBlobClient(targetBlobPath);

                var copyOperation = await targetBlobClient.StartCopyFromUriAsync(sourceBlobClient.Uri, cancellationToken: cancellationToken);
                await copyOperation.WaitForCompletionAsync(cancellationToken);

                _logger.LogInformation("Successfully copied report data from {SourceBlobPath} to {TargetBlobPath}", 
                    sourceBlobPath, targetBlobPath);
                return targetBlobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying report data from {SourceBlobPath} to tenant {TenantId}, report {ReportId}, version {VersionId}", 
                    sourceBlobPath, tenantId, reportId, versionId);
                throw;
            }
        }

        public async Task<List<string>> ListReportDataAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Listing report data for tenant {TenantId}", tenantId);

                var prefix = $"tenants/{tenantId}/reports/";
                var results = new List<string>();

                await foreach (var blobItem in _containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken))
                {
                    if (blobItem.Name.EndsWith("/data.json"))
                    {
                        results.Add(blobItem.Name);
                    }
                }

                _logger.LogDebug("Found {Count} report data blobs for tenant {TenantId}", results.Count, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing report data for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<List<string>> ListReportVersionDataAsync(Guid tenantId, Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Listing report version data for tenant {TenantId}, report {ReportId}", tenantId, reportId);

                var prefix = $"tenants/{tenantId}/reports/{reportId}/versions/";
                var results = new List<string>();

                await foreach (var blobItem in _containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken))
                {
                    if (blobItem.Name.EndsWith("/data.json"))
                    {
                        results.Add(blobItem.Name);
                    }
                }

                _logger.LogDebug("Found {Count} version data blobs for tenant {TenantId}, report {ReportId}", 
                    results.Count, tenantId, reportId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing report version data for tenant {TenantId}, report {ReportId}", tenantId, reportId);
                throw;
            }
        }

        public async Task<string> SaveAssetAsync(Guid tenantId, Guid reportId, Guid versionId, string assetName, Stream assetStream, string contentType, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateAssetPath(tenantId, reportId, versionId, assetName);
                _logger.LogDebug("Saving asset {AssetName} to blob {BlobPath}", assetName, blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);

                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = contentType
                    },
                    Metadata = new Dictionary<string, string>
                    {
                        ["tenantId"] = tenantId.ToString(),
                        ["reportId"] = reportId.ToString(),
                        ["versionId"] = versionId.ToString(),
                        ["assetName"] = assetName,
                        ["uploadedAt"] = DateTime.UtcNow.ToString("O")
                    }
                };

                await blobClient.UploadAsync(assetStream, uploadOptions, cancellationToken);

                _logger.LogInformation("Successfully saved asset {AssetName} to blob {BlobPath}", assetName, blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving asset {AssetName} for tenant {TenantId}, report {ReportId}, version {VersionId}", 
                    assetName, tenantId, reportId, versionId);
                throw;
            }
        }

        public async Task<Stream> GetAssetAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting asset from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.OpenReadAsync(cancellationToken: cancellationToken);

                _logger.LogDebug("Successfully opened asset stream for blob {BlobPath}", blobPath);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting asset from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public async Task<bool> DeleteAssetAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting asset from blob {BlobPath}", blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);

                if (response.Value)
                {
                    _logger.LogInformation("Successfully deleted asset from blob {BlobPath}", blobPath);
                }
                else
                {
                    _logger.LogWarning("Asset blob {BlobPath} did not exist for deletion", blobPath);
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting asset from blob {BlobPath}", blobPath);
                throw;
            }
        }

        public string GenerateReportDataPath(Guid tenantId, Guid reportId, Guid versionId)
        {
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/data.json";
        }

        public string GenerateAssetPath(Guid tenantId, Guid reportId, Guid versionId, string assetName)
        {
            // Sanitize asset name to ensure it's safe for blob storage
            var sanitizedAssetName = assetName.Replace(" ", "_").Replace("\\", "/");
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/assets/{sanitizedAssetName}";
        }
    }
}