import { BaseCommand } from '../../../shared/src/commands/user.commands.js';
import { NEXT_API_BASE_URL } from '@/lib/constants';

class CommandServiceClass {
  private baseUrl: string;

  constructor() {
    this.baseUrl = NEXT_API_BASE_URL;
  }

  async send<TResponse>(command: BaseCommand): Promise<TResponse> {
    const response = await fetch(`${this.baseUrl}/commands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.getAuthHeader() || {}),
      },
      body: JSON.stringify(command),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Command execution failed');
    }

    return response.json();
  }

  private getAuthHeader(): Record<string, string> | null {
    if (typeof window === 'undefined') return null;

    const token = localStorage.getItem('auth_token');
    return token ? { Authorization: `Bearer ${token}` } : null;
  }
}

export const CommandService = new CommandServiceClass();
