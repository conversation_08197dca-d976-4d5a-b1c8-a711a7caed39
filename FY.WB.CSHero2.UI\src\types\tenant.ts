export interface Tenant {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'suspended';
  createdAt: string;
  updatedAt: string;
  phone?: string;
  company?: string;
  subscription?: 'basic' | 'professional' | 'enterprise';
  lastLogin?: string;
  // Billing related fields
  paymentMethod?: {
    cardType?: string;
    lastFourDigits?: string;
    expirationDate?: string;
    securityMethod?: string;
  };
  billingAddress?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  billingCycle?: 'monthly' | 'quarterly' | 'annual';
  nextBillingDate?: string;
  subscriptionStatus?: 'active' | 'overdue' | 'canceled';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
