import { NextResponse } from 'next/server';
import { getYTDMetrics } from '@/lib/data/metrics';

/**
 * GET /api/ytd/metrics
 * Returns year-to-date metrics data
 */
export async function GET() {
  try {
    const metrics = await getYTDMetrics();
    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching year-to-date metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}
