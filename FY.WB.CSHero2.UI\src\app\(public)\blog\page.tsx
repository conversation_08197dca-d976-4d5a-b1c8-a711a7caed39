"use client";

import { motion } from "framer-motion";
import { Calendar, Clock, ChevronRight, Tag } from "lucide-react";
import Link from "next/link";

const blogPosts = [
  {
    id: 1,
    title: "Maximizing Customer Service Efficiency with AI-Powered Reports",
    excerpt: "Learn how AI can transform your customer service reporting workflow and help identify key trends and insights automatically.",
    date: "Feb 18, 2024",
    readTime: "5 min read",
    tags: ["AI", "Customer Service", "Reporting"],
    gradient: "gradient-primary-secondary",
    slug: "maximizing-customer-service-efficiency"
  },
  // More posts will be added later
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function BlogPage() {
  return (
    <div className="gradient-primary-secondary">
      <div className="container mx-auto px-4 py-24 md:px-16">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl font-bold mb-4 text-white">Blog</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Insights, updates, and best practices for customer service reporting
          </p>
        </motion.div>

        {/* Featured Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-16"
        >
          {["All Posts", "AI & Automation", "Best Practices", "Case Studies", "Product Updates"].map((category) => (
            <button
              key={category}
              className="px-4 py-2 rounded-full bg-white text-[rgb(var(--primary))] hover:bg-[rgb(var(--primary))] hover:text-white transition-colors"
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Blog Posts Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto"
        >
          {blogPosts.map((post) => (
            <motion.div
              key={post.id}
              variants={itemVariants}
              className="group relative"
              whileHover={{ y: -5 }}
            >
              <Link href={`/blog/${post.slug}`}>
                <div className="h-full bg-white rounded-lg shadow-lg p-8 transition-shadow duration-300 group-hover:shadow-xl">
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-600"
                      >
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                  <h2 className={`text-2xl font-bold mb-4 ${post.gradient} bg-clip-text text-transparent group-hover:underline`}>
                    {post.title}
                  </h2>
                  <p className="text-gray-600 mb-6">{post.excerpt}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span className="mr-4">{post.date}</span>
                    <Clock className="w-4 h-4 mr-1" />
                    <span>{post.readTime}</span>
                  </div>
                  <div className="absolute bottom-8 right-8 opacity-0 transform translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all">
                    <ChevronRight className="w-5 h-5 text-[rgb(var(--primary))]" />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 p-8 rounded-lg bg-white shadow-lg max-w-3xl mx-auto text-center"
        >
          <h2 className="text-2xl font-bold mb-4 text-[rgb(var(--primary))]">Stay Updated</h2>
          <p className="text-gray-600 mb-6">
            Subscribe to our newsletter for the latest insights and updates.
          </p>
          <div className="flex gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(var(--primary))] focus:border-transparent"
            />
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-2 rounded-lg gradient-primary-tertiary text-white font-semibold hover:opacity-90 transition-opacity"
            >
              Subscribe
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
