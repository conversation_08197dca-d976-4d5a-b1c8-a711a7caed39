'use client';

import { Shield } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tenant } from '@/types/tenant';

interface SubscriptionDetailsCardProps {
  formData: {
    billingCycle: string;
    nextBillingDate: string;
    subscriptionStatus: string;
    subscription: string;
  };
  setFormData: (formData: any) => void;
  tenant: Tenant;
}

export function SubscriptionDetailsCard({ formData, setFormData, tenant }: SubscriptionDetailsCardProps) {
  return (
    <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
      <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Subscription Details</h2>
      <div className="space-y-6">
        {/* Subscription Type and Status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-start gap-3">
            <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
              <Shield className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Subscription</p>
              <Select
                value={formData.subscription || tenant?.subscription || 'basic'}
                onValueChange={(value) => setFormData({ ...formData, subscription: value })}
              >
                <SelectTrigger id="subscription" className="border-0 p-0 h-auto text-lg font-medium">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Select
            value={formData.subscriptionStatus}
            onValueChange={(value) => setFormData({ ...formData, subscriptionStatus: value })}
          >
            <SelectTrigger id="subscriptionStatus" className={`h-7 px-3 rounded-full text-xs font-medium ${
              formData.subscriptionStatus === 'active'
                ? 'bg-emerald-100/80 text-emerald-700 border-0' 
                : formData.subscriptionStatus === 'overdue'
                ? 'bg-amber-100/80 text-amber-700 border-0'
                : formData.subscriptionStatus === 'canceled'
                ? 'bg-red-100/80 text-red-700 border-0'
                : 'bg-emerald-100/80 text-emerald-700 border-0'
            }`}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">active</SelectItem>
              <SelectItem value="overdue">overdue</SelectItem>
              <SelectItem value="canceled">canceled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label htmlFor="billingCycle" className="text-sm font-medium text-muted-foreground">
              Billing Cycle
            </label>
            <Select
              value={formData.billingCycle}
              onValueChange={(value) => setFormData({ ...formData, billingCycle: value })}
            >
              <SelectTrigger id="billingCycle">
                <SelectValue placeholder="Select billing cycle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="annual">Annual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="nextBillingDate" className="text-sm font-medium text-muted-foreground">
              Next Billing Date
            </label>
            <Input
              id="nextBillingDate"
              type="date"
              value={formData.nextBillingDate}
              onChange={(e) => setFormData({ ...formData, nextBillingDate: e.target.value })}
            />
          </div>
        </div>
      </div>
    </Card>
  );
}
