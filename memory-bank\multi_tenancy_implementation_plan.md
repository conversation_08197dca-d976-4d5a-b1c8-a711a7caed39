# ABP-Inspired Multi-Tenancy Implementation Plan

**Version:** 1.1
**Date:** 2025-05-12
**Related Documents:** `projectbrief.md`, `systemPatterns.md`, `techContext.md`, `authentication_guide.md`, `activeContext.md`

## 1. Overall Goal

To implement a robust multi-tenancy solution using Finbuckle, drawing inspiration from the entity and multi-tenancy patterns found in the ABP Framework. This involves establishing a clear entity hierarchy with built-in support for multi-tenancy (`TenantId`), auditing, and soft-delete functionality, and integrating this with Finbuckle for tenant resolution and data isolation. This will address current authentication/authorization issues by ensuring authenticated requests can be correctly associated with a tenant.

## 2. Key Decisions Made

*   **User-Tenant Model:** `ApplicationUser` will have a direct `TenantId` property (single primary tenant per user).
*   **Auditing/Soft Delete:** Full auditing (creation, modification, deletion timestamps and user IDs) and soft delete (`IsDeleted` flag) will be included in the base entity design.
*   **Key Types:** `Guid` will be used for all entity primary keys and for `TenantId`.
*   **Tenant Resolution:** JWT claim-based resolution (e.g., "tenant_id") is the preferred method for Finbuckle.

## 3. Phases and Tasks

### Phase 1: Define Core Entity Abstractions (Completed)

*   **Objective:** Create a set of base interfaces and classes that provide common properties and behaviors for domain entities.
*   **Location:** `FY.WB.CSHero2.Domain/Entities/Core/`
*   **Interfaces Created:**
    *   `IEntity<TId>`: Defines a contract for entities with a primary key.
    *   `IMultiTenant`: Defines a contract for entities that are tenant-specific (`TenantId`).
    *   `ICreationAuditedObject`: Defines properties for tracking entity creation (`CreationTime`, `CreatorId`).
    *   `IModificationAuditedObject`: Defines properties for tracking entity modification (`LastModificationTime`, `LastModifierId`).
    *   `IDeletionAuditedObject`: Defines properties for tracking entity deletion (`DeletionTime`, `DeleterId`).
    *   `ISoftDelete`: Defines a property for soft-delete functionality (`IsDeleted`).
*   **Base Classes Created:**
    *   `Entity<TId>`: Implements `IEntity<TId>`. Base for all entities.
    *   `AuditedEntity<TId>`: Inherits `Entity<TId>`, implements `ICreationAuditedObject`, `IModificationAuditedObject`.
    *   `FullAuditedMultiTenantEntity<TId>`: Inherits `AuditedEntity<TId>`, implements `IMultiTenant`, `ISoftDelete`, `IDeletionAuditedObject`. This will be the primary base for most domain entities.

### Phase 2: Integrate Abstractions into Domain Entities (Current Focus)

*   **Objective:** Modify existing domain entities to inherit from the new base classes/interfaces.
*   **Tasks:**
    1.  Update `Client` entity to inherit from `FullAuditedMultiTenantEntity<Guid>`.
    2.  Update `ApplicationUser` entity:
        *   It already inherits from `IdentityUser`. It will directly implement `IMultiTenant`, `ISoftDelete`, `ICreationAuditedObject`, `IModificationAuditedObject`, `IDeletionAuditedObject`.
        *   Add `public virtual Guid? TenantId { get; set; }` and other auditing/soft-delete properties.
    3.  Update `AppTenantInfo` entity (likely to inherit from `AuditedEntity<Guid>` or similar, as tenants themselves are not usually tenant-specific but are audited).
    4.  Update other domain entities (e.g., `Report`) as necessary.
    5.  Generate and apply new EF Core migrations after entity changes.

### Phase 3: Enhance Finbuckle Configuration (Partially Completed - Manual Workaround Active)

*   **Objective:** Configure Finbuckle for effective tenant resolution and data isolation.
*   **Tasks:**
    1.  [✅] Configure Finbuckle in `MultiTenancyConfiguration.cs` to use `WithClaimStrategy("tenant_id")`, `WithRouteStrategy()`, and `WithInMemoryStore()`.
    2.  [✅] Resolved startup hang by removing incorrect `ITenantInfo` registration from `MultiTenancyConfiguration.cs`.
    3.  [🚧] **CS0308 Workaround Active:** The `ApplicationDbContext` still inherits from standard `IdentityDbContext`. Multi-tenancy data isolation is handled manually:
        *   [✅] Global query filters for `IMultiTenant` entities are applied in `ApplicationDbContext.OnModelCreating`.
        *   [✅] `TenantId` is set for new `IMultiTenant` entities in `ApplicationDbContext.ApplyMultiTenancyAndAuditInfo` (called by `SaveChanges`/`SaveChangesAsync`).
    4.  [✅] Ensure `AppTenantInfo` is correctly defined. Currently using `InMemoryStore`. (Future: Consider EF Core store).

### Phase 4: Implement User-Tenant Association and Authentication Flow (Completed)

*   **Objective:** Ensure user login associates the user with their `TenantId` and includes it in the JWT.
*   **Tasks:**
    1.  [✅] **User Registration (`AuthController.Register`):**
        *   Dynamically creates a new `AppTenantInfo` for the registering company.
        *   Adds the new tenant to the Finbuckle `InMemoryStore`.
        *   Assigns the new `TenantId` to the `ApplicationUser` being created.
    2.  [✅] **Login Process (`AuthController.Login`):**
        *   After credential validation, retrieves the user (including `TenantId`).
        *   Adds the `TenantId` as a custom claim ("tenant_id") to the JWT.
        *   Adds `IsAdmin` claim to the JWT.
    3.  [ ] **Frontend `AuthContext`:** (Needs verification) Ensure it correctly handles/stores `TenantId` and potentially `IsAdmin` from the login response/JWT.

### Phase 5: Adapt Backend Data Access and Business Logic (Completed - Manual Implementation)

*   **Objective:** Ensure all data operations are correctly scoped by `TenantId` and audit properties are set.
*   **Tasks:**
    1.  [✅] **Data Access (`ApplicationDbContext`):**
        *   Global query filters automatically handle `WHERE TenantId = currentTenantId` for `IMultiTenant` entities.
        *   `ApplyMultiTenancyAndAuditInfo` method (called by `SaveChanges`) handles setting `TenantId` on new entities and updating all audit properties (`CreationTime`, `CreatorId`, `LastModificationTime`, `LastModifierId`, `DeletionTime`, `DeleterId`, `IsDeleted`).
    2.  [✅] `ICurrentUserService` is injected into `ApplicationDbContext` to retrieve the current `UserId` and `TenantId` for auditing and filtering purposes.

### Phase 6: Update API Controllers

*   **Objective:** Ensure API controllers are tenant-aware.
*   **Tasks:**
    1.  Protect controllers/actions dealing with tenant-specific data with `[Authorize]`.
    2.  Rely on Finbuckle (via JWT claim) and the DbContext/service layer to handle tenant scoping.

### Phase 7: Update Frontend API Calls

*   **Objective:** Ensure frontend sends authenticated requests that allow backend tenant resolution.
*   **Tasks:**
    1.  The `authToken` (JWT) cookie, containing the `tenant_id` claim, should be automatically sent by the browser.
    2.  The BFF should not need to modify URLs for tenant information if claim-based resolution is used effectively on the backend.
    3.  Improve frontend error handling for 401/403 responses.

### Phase 8: Testing

*   **Objective:** Thoroughly test the multi-tenancy and auditing implementation.
*   **Tasks:**
    1.  Test user registration with tenant assignment.
    2.  Test login and JWT claim for `tenant_id`.
    3.  Test data isolation between tenants for all relevant entities.
    4.  Verify audit properties are correctly populated on entity CUD operations.
    5.  Verify soft delete functionality.
