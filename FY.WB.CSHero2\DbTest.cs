using System;
using Microsoft.Data.SqlClient;

namespace FY.WB.CSHero2
{
    public static class DbTest
    {
        public static void TestConnection(string connectionString)
        {
            Console.WriteLine("Testing direct connection to SQL Server...");
            Console.WriteLine($"Connection string: {connectionString}");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("Connection successful!");

                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT @@VERSION";
                        var result = command.ExecuteScalar()?.ToString();
                        Console.WriteLine($"SQL Server version: {result}");
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error connecting to database: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }
    }
}
