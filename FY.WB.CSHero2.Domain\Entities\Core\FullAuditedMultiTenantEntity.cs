using System;

namespace FY.WB.CSHero2.Domain.Entities.Core
{
    public abstract class FullAuditedMultiTenantEntity<TId> : AuditedEntity<TId>,
                                                              IMultiTenant,
                                                              ISoftDelete,
                                                              IDeletionAuditedObject
    {
        // IMultiTenant
        public virtual Guid? TenantId { get; set; }

        // ISoftDelete
        public virtual bool IsDeleted { get; set; }

        // IDeletionAuditedObject
        public virtual Guid? DeleterId { get; set; }
        public virtual DateTime? DeletionTime { get; set; }

        protected FullAuditedMultiTenantEntity()
        {
        }

        protected FullAuditedMultiTenantEntity(TId id) : base(id)
        {
        }
    }
}