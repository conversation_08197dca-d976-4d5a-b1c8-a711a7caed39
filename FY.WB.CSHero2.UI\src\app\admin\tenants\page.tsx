'use client';

import { useState } from 'react';
import { TenantTable } from '@/components/features/tenants/tenant-table';
import { TenantForm } from '@/components/features/tenants/tenant-form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { createTenant } from '@/lib/api';
import { Tenant } from '@/types/tenant';
import { PageHeader } from '@/components/layouts/page-header';

export default function TenantsPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const handleCreateTenant = async (data: Partial<Tenant>) => {
    await createTenant(data);
    setDialogOpen(false);
    setRefreshKey(k => k + 1);
  };

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <PageHeader
              title="Tenants"
              description="Manage users who access the client login and use the report builder"
              onButtonClick={() => setDialogOpen(true)}
              btnName="New Tenant"
              placeHolder="Search tenants..."
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
            />

      <div className="bg-card rounded-lg border">
        <TenantTable key={refreshKey} searchQuery={searchQuery} />

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent className="bg-card max-w-lg bg-white">
            <DialogHeader className="space-y-1">
              <DialogTitle className="text-2xl font-bold text-[rgb(var(--primary))]">Create New Tenant</DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Add a new tenant user who can access the client login
              </DialogDescription>
            </DialogHeader>
            <TenantForm
              onSubmit={handleCreateTenant}
              onCancel={() => setDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
