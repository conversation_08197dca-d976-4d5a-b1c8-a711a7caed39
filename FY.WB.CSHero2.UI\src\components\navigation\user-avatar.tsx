"use client";

import { LogOut, User } from "lucide-react";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getCurrentUser, logout } from "@/lib/api";
import { useToast } from "@/components/providers/toast-provider";

interface User {
  name?: string;
  avatarUrl?: string;
}

export function UserAvatar() {
  const { toast } = useToast();
  const { data: user } = useQuery<User>({
    queryKey: ["currentUser"],
    queryFn: getCurrentUser,
  });

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Get user initials for avatar
  const getInitials = () => {
    if (!user?.name) return "U";
    return user.name
      .split(" ")
      .map((part: string) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="h-10 w-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center text-sm font-medium text-white">
          {user?.avatarUrl ? (
            <div className="relative h-full w-full rounded-full overflow-hidden">
              <Image 
                src={user.avatarUrl} 
                alt={user?.name || "User"} 
                className="object-cover"
                fill
                sizes="32px"
              />
            </div>
          ) : (
            getInitials()
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem className="cursor-pointer" disabled={!user}>
          <User className="mr-2 h-4 w-4" />
          Profile
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="cursor-pointer text-red-600 focus:text-red-600" onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
