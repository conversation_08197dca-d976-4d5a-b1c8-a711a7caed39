using System;
using System.Collections.Generic;
using System.Text.Json;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents a generated React component for a specific section of a report
    /// </summary>
    public class ComponentDefinition : AuditedEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the report version this component belongs to
        /// </summary>
        public Guid ReportVersionId { get; set; }

        /// <summary>
        /// Navigation property to the parent report version
        /// </summary>
        public virtual ReportVersion ReportVersion { get; set; } = null!;

        /// <summary>
        /// Identifier of the section this component represents
        /// </summary>
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the section for display purposes
        /// </summary>
        public string SectionName { get; set; } = string.Empty;

        /// <summary>
        /// Generated React component code (TypeScript/JSX)
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// TypeScript type definitions for the component
        /// </summary>
        public string TypeDefinitions { get; set; } = string.Empty;

        /// <summary>
        /// JSON array of import statements required by the component
        /// </summary>
        public string ImportsJson { get; set; } = string.Empty;

        /// <summary>
        /// JSON serialized metadata about the component (props, styling, etc.)
        /// </summary>
        public string MetadataJson { get; set; } = string.Empty;

        /// <summary>
        /// When this component was generated (use CreationTime from base class)
        /// </summary>
        public DateTime GeneratedAt
        {
            get => CreationTime;
            set => CreationTime = value;
        }

        /// <summary>
        /// Who generated this component (use CreatorId from base class)
        /// </summary>
        public Guid GeneratedBy
        {
            get => CreatorId ?? Guid.Empty;
            set => CreatorId = value;
        }

        /// <summary>
        /// Hash of the component code for change detection
        /// </summary>
        public string ComponentHash { get; set; } = string.Empty;

        /// <summary>
        /// Size of the component code in bytes
        /// </summary>
        public long ComponentSize { get; set; }

        /// <summary>
        /// Whether this component compiled successfully
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Validation errors if component is not valid
        /// </summary>
        public string ValidationErrors { get; set; } = string.Empty;

        /// <summary>
        /// Framework used for component generation (NextJS, React, etc.)
        /// </summary>
        public string Framework { get; set; } = "NextJS";

        /// <summary>
        /// Style framework used (TailwindCSS, CSS Modules, etc.)
        /// </summary>
        public string StyleFramework { get; set; } = "TailwindCSS";

        /// <summary>
        /// Path to Azure Blob Storage for component assets (images, charts, etc.)
        /// </summary>
        public string? AssetBlobPath { get; set; }

        /// <summary>
        /// Reference to CosmosDB style document for component-specific styles
        /// </summary>
        public string? StyleDocumentId { get; set; }

        /// <summary>
        /// Gets the import statements as a list
        /// </summary>
        public List<string> GetImports()
        {
            if (string.IsNullOrEmpty(ImportsJson))
                return new List<string>();

            try
            {
                return JsonSerializer.Deserialize<List<string>>(ImportsJson) ?? new List<string>();
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// Sets the import statements from a list
        /// </summary>
        public void SetImports(List<string> imports)
        {
            ImportsJson = JsonSerializer.Serialize(imports ?? new List<string>());
        }

        /// <summary>
        /// Gets the component metadata as a dictionary
        /// </summary>
        public Dictionary<string, object> GetMetadata()
        {
            if (string.IsNullOrEmpty(MetadataJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the component metadata from a dictionary
        /// </summary>
        public void SetMetadata(Dictionary<string, object> metadata)
        {
            MetadataJson = JsonSerializer.Serialize(metadata ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Calculates and sets the component hash for change detection
        /// </summary>
        public void CalculateHash()
        {
            var content = $"{ComponentCode}{TypeDefinitions}{ImportsJson}";
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(content));
            ComponentHash = Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// Sets the component code and calculates size and hash
        /// </summary>
        public void SetComponentCode(string code)
        {
            ComponentCode = code ?? string.Empty;
            ComponentSize = System.Text.Encoding.UTF8.GetByteCount(ComponentCode);
            CalculateHash();
        }

        /// <summary>
        /// Checks if the component has changed compared to another component
        /// </summary>
        public bool HasChangedFrom(ComponentDefinition other)
        {
            return ComponentHash != other.ComponentHash;
        }

        /// <summary>
        /// Gets the full component file content including imports and exports
        /// </summary>
        public string GetFullComponentContent()
        {
            var imports = GetImports();
            var importsSection = imports.Count > 0 ? string.Join("\n", imports) + "\n\n" : "";

            return $"{importsSection}{ComponentCode}";
        }

        /// <summary>
        /// Validates the component code syntax
        /// </summary>
        public void ValidateComponent()
        {
            try
            {
                // Basic validation - check for required React component structure
                if (string.IsNullOrEmpty(ComponentCode))
                {
                    IsValid = false;
                    ValidationErrors = "Component code is empty";
                    return;
                }

                if (!ComponentCode.Contains("export") || !ComponentCode.Contains("return"))
                {
                    IsValid = false;
                    ValidationErrors = "Component must have export and return statements";
                    return;
                }

                IsValid = true;
                ValidationErrors = string.Empty;
            }
            catch (Exception ex)
            {
                IsValid = false;
                ValidationErrors = ex.Message;
            }
        }

        /// <summary>
        /// Creates a copy of this component definition for a new version
        /// </summary>
        public ComponentDefinition CreateCopy(Guid newReportVersionId)
        {
            return new ComponentDefinition
            {
                Id = Guid.NewGuid(),
                ReportVersionId = newReportVersionId,
                SectionId = SectionId,
                SectionName = SectionName,
                ComponentCode = ComponentCode,
                TypeDefinitions = TypeDefinitions,
                ImportsJson = ImportsJson,
                MetadataJson = MetadataJson,
                CreationTime = DateTime.UtcNow,
                CreatorId = CreatorId,
                ComponentHash = ComponentHash,
                ComponentSize = ComponentSize,
                IsValid = IsValid,
                ValidationErrors = ValidationErrors,
                Framework = Framework,
                StyleFramework = StyleFramework
            };
        }
    }
}
