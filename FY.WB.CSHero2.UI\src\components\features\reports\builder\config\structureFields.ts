import { FieldConfig, SectionConfig, StyleSectionConfig } from './styleFieldConfig';

// Heading Levels Fields
const headingLevelsFields: FieldConfig[] = [
  {
    name: "count",
    label: "Number of Levels",
    type: "select",
    defaultValue: 3,
    options: [
      { value: "1", label: "1 level" },
      { value: "2", label: "2 levels" },
      { value: "3", label: "3 levels" },
      { value: "4", label: "4 levels" },
      { value: "5", label: "5 levels" },
      { value: "6", label: "6 levels" }
    ]
  },
  {
    name: "distinction",
    label: "Hierarchy Visual Distinction",
    type: "select",
    defaultValue: "size",
    options: [
      { value: "size", label: "Size-based (larger to smaller)" },
      { value: "weight", label: "Weight-based (bolder to lighter)" },
      { value: "color", label: "Color-based" },
      { value: "combined", label: "Combined approaches" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Numbering Scheme Fields
const numberingSchemeFields: FieldConfig[] = [
  {
    name: "style",
    label: "Style",
    type: "select",
    defaultValue: "decimal",
    options: [
      { value: "none", label: "None" },
      { value: "decimal", label: "Decimal (1.1.1)" },
      { value: "legal", label: "Legal (I.A.1)" },
      { value: "bullet", label: "Bullet-based" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "depth",
    label: "Depth",
    type: "select",
    defaultValue: "all",
    options: [
      { value: "all", label: "All levels" },
      { value: "1", label: "Top level only" },
      { value: "2", label: "Top 2 levels" },
      { value: "3", label: "Top 3 levels" },
      { value: "custom", label: "Custom rule" }
    ]
  },
  {
    name: "format",
    label: "Format",
    type: "select",
    defaultValue: "number-only",
    options: [
      { value: "number-only", label: "Number only" },
      { value: "number-with-text", label: "Number with text (e.g., \"Section 1.2\")" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "delimiter",
    label: "Delimiter",
    type: "select",
    defaultValue: "period",
    options: [
      { value: "period", label: "Period (1.1)" },
      { value: "hyphen", label: "Hyphen (1-1)" },
      { value: "space", label: "Space (1 1)" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Heading Styles Fields - H1
const h1StyleFields: FieldConfig[] = [
  {
    name: "h1Font",
    label: "Font",
    type: "select",
    defaultValue: "primary",
    options: [
      { value: "primary", label: "Primary" },
      { value: "secondary", label: "Secondary" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h1Weight",
    label: "Weight",
    type: "select",
    defaultValue: "bold",
    options: [
      { value: "regular", label: "Regular" },
      { value: "bold", label: "Bold" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h1Color",
    label: "Color",
    type: "select",
    defaultValue: "primary",
    options: [
      { value: "primary", label: "Primary" },
      { value: "secondary", label: "Secondary" },
      { value: "black", label: "Black" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h1SpacingBefore",
    label: "Spacing Before",
    type: "select",
    defaultValue: "24pt",
    options: [
      { value: "0pt", label: "None" },
      { value: "12pt", label: "12pt" },
      { value: "18pt", label: "18pt" },
      { value: "24pt", label: "24pt" },
      { value: "36pt", label: "36pt" }
    ]
  },
  {
    name: "h1SpacingAfter",
    label: "Spacing After",
    type: "select",
    defaultValue: "12pt",
    options: [
      { value: "0pt", label: "None" },
      { value: "6pt", label: "6pt" },
      { value: "12pt", label: "12pt" },
      { value: "18pt", label: "18pt" },
      { value: "24pt", label: "24pt" }
    ]
  },
  {
    name: "h1Border",
    label: "Border",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "below", label: "Below" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Heading Styles Fields - H2
const h2StyleFields: FieldConfig[] = [
  {
    name: "h2Font",
    label: "Font",
    type: "select",
    defaultValue: "primary",
    options: [
      { value: "primary", label: "Primary" },
      { value: "secondary", label: "Secondary" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h2Weight",
    label: "Weight",
    type: "select",
    defaultValue: "bold",
    options: [
      { value: "regular", label: "Regular" },
      { value: "bold", label: "Bold" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h2Color",
    label: "Color",
    type: "select",
    defaultValue: "primary",
    options: [
      { value: "primary", label: "Primary" },
      { value: "secondary", label: "Secondary" },
      { value: "black", label: "Black" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Heading Styles Fields - H3
const h3StyleFields: FieldConfig[] = [
  {
    name: "h3Font",
    label: "Font",
    type: "select",
    defaultValue: "primary",
    options: [
      { value: "primary", label: "Primary" },
      { value: "secondary", label: "Secondary" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h3Weight",
    label: "Weight",
    type: "select",
    defaultValue: "bold",
    options: [
      { value: "regular", label: "Regular" },
      { value: "bold", label: "Bold" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Table of Contents Fields
const tableOfContentsFields: FieldConfig[] = [
  {
    name: "depth",
    label: "Depth",
    type: "select",
    defaultValue: "all",
    options: [
      { value: "all", label: "Include all levels" },
      { value: "1", label: "Include level 1 only" },
      { value: "2", label: "Include levels 1-2" },
      { value: "3", label: "Include levels 1-3" }
    ]
  },
  {
    name: "style",
    label: "Style",
    type: "select",
    defaultValue: "simple",
    options: [
      { value: "simple", label: "Simple" },
      { value: "hierarchical", label: "Hierarchical" },
      { value: "boxed", label: "Boxed" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "dotLeaders",
    label: "Dot Leaders",
    type: "select",
    defaultValue: "dots",
    options: [
      { value: "none", label: "None" },
      { value: "dots", label: "Dots" },
      { value: "line", label: "Line" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "pageNumbers",
    label: "Show page numbers",
    type: "checkbox",
    defaultValue: true
  },
  {
    name: "links",
    label: "Links",
    type: "select",
    defaultValue: "hyperlinked",
    options: [
      { value: "none", label: "None" },
      { value: "hyperlinked", label: "Hyperlinked (for digital formats)" }
    ]
  }
];

// Section Cover Pages Fields
const sectionCoverPagesFields: FieldConfig[] = [
  {
    name: "usage",
    label: "Usage",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "major", label: "Major sections only" },
      { value: "all", label: "All sections" },
      { value: "custom", label: "Custom rule" }
    ]
  },
  {
    name: "style",
    label: "Style",
    type: "select",
    defaultValue: "minimal",
    options: [
      { value: "minimal", label: "Minimal" },
      { value: "full-page", label: "Full-page color" },
      { value: "image", label: "Image-based" },
      { value: "typography", label: "Typography-focused" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "numbering",
    label: "Numbering",
    type: "select",
    defaultValue: "continue",
    options: [
      { value: "continue", label: "Continue page numbering" },
      { value: "restart", label: "Restart for each section" },
      { value: "none", label: "No numbers" }
    ]
  }
];

// Define sections
const sections: SectionConfig[] = [
  {
    id: "headingLevels",
    title: "Heading Levels",
    fields: headingLevelsFields,
    defaultExpanded: true
  },
  {
    id: "numberingScheme",
    title: "Numbering Scheme",
    fields: numberingSchemeFields
  },
  {
    id: "h1Style",
    title: "H1 Style",
    fields: h1StyleFields
  },
  {
    id: "h2Style",
    title: "H2 Style",
    fields: h2StyleFields
  },
  {
    id: "h3Style",
    title: "H3 Style",
    fields: h3StyleFields
  },
  {
    id: "tableOfContents",
    title: "Table of Contents",
    fields: tableOfContentsFields
  },
  {
    id: "sectionCoverPages",
    title: "Section Cover Pages",
    fields: sectionCoverPagesFields
  }
];

// Export the complete configuration
export const structureConfig: StyleSectionConfig = {
  sections
};
