-- Add foreign key from ApplicationUser.TenantId to TenantProfile.Id
ALTER TABLE [AspNetUsers]
ADD CONSTRAINT [FK_AspNetUsers_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE NO ACTION;

-- Create an index on TenantId in AspNetUsers for better performance
CREATE INDEX [IX_AspNetUsers_TenantId] ON [AspNetUsers] ([TenantId]);

-- Add foreign keys for all entities inheriting from FullAuditedMultiTenantEntity to TenantProfile.Id

-- Clients
ALTER TABLE [Clients]
ADD CONSTRAINT [FK_Clients_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;

-- Forms
ALTER TABLE [Forms]
ADD CONSTRAINT [FK_Forms_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;

-- Invoices
ALTER TABLE [Invoices]
ADD CONSTRAINT [FK_Invoices_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;

-- Reports
ALTER TABLE [Reports]
ADD CONSTRAINT [FK_Reports_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;

-- Templates
ALTER TABLE [Templates]
ADD CONSTRAINT [FK_Templates_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;

-- Uploads
ALTER TABLE [Uploads]
ADD CONSTRAINT [FK_Uploads_TenantProfiles_TenantId]
FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id])
ON DELETE CASCADE;
