using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Invoices.Dtos;
using FY.WB.CSHero2.Application.Invoices.Queries;
using FY.WB.CSHero2.Application.Invoices.Commands;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Exceptions;
using FluentValidation;

namespace FY.WB.CSHero2.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class InvoicesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<InvoicesController> _logger; // Added for logging

        public InvoicesController(IMediator mediator, ILogger<InvoicesController> logger) // Added logger
        {
            _mediator = mediator;
            _logger = logger; // Added logger
        }

        /// <summary>
        /// Get all invoices with optional filtering, sorting, and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResult<InvoiceDto>), 200)] // Updated to PagedResult
        public async Task<ActionResult<PagedResult<InvoiceDto>>> GetInvoices( // Updated to PagedResult
            [FromQuery] string? search,
            [FromQuery] string? type,
            [FromQuery] string? status,
            // Removed tenantId from parameters, should be handled by CurrentUserService
            [FromQuery] string? sortBy,
            [FromQuery] string? sortOrder,
            [FromQuery] int? page,
            [FromQuery] int? limit)
        {
            var parameters = new InvoiceQueryParametersDto
            {
                Search = search,
                Type = type,
                Status = status,
                // TenantId removed
                SortBy = sortBy,
                SortOrder = sortOrder,
                Page = page,
                Limit = limit
            };

            var query = new GetInvoicesQuery(parameters);
            var result = await _mediator.Send(query); // This query handler must return PagedResult<InvoiceDto>
            
            return Ok(result);
        }

        /// <summary>
        /// Get an invoice by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(InvoiceDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<InvoiceDto>> GetInvoice(string id)
        {
            try
            {
                var query = new GetInvoiceByIdQuery(Guid.Parse(id));
                var result = await _mediator.Send(query);
                
                return Ok(result);
            }
            catch (Exception ex) when (ex.Message.Contains("not found"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (FormatException)
            {
                return BadRequest(new { message = "Invalid ID format. Please provide a valid GUID." });
            }
        }
        // Removed GetInvoicesByTenantId as it's redundant with automatic tenant filtering

        /// <summary>
        /// Creates a new invoice.
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), 201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new invoice of type: {InvoiceType}", dto.Type);
            try
            {
                var command = new CreateInvoiceCommand(dto);
                var invoiceId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created invoice with ID: {InvoiceId}", invoiceId);
                return CreatedAtAction(nameof(GetInvoice), new { id = invoiceId.ToString() }, new { id = invoiceId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating invoice.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating invoice: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex) // Catch specific exception from Invoice.GenerateOrderNumber
            {
                _logger.LogWarning(ex, "Argument exception while creating invoice: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the invoice.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing invoice.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateInvoice(Guid id, [FromBody] UpdateInvoiceRequestDto dto)
        {
            _logger.LogInformation("Attempting to update invoice with ID: {InvoiceId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateInvoiceCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated invoice with ID: {InvoiceId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating invoice with ID: {InvoiceId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Invoice with ID {InvoiceId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update invoice with ID: {InvoiceId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice with ID {InvoiceId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the invoice.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes an invoice by its ID.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteInvoice(Guid id)
        {
            _logger.LogInformation("Attempting to delete invoice with ID: {InvoiceId}", id);
            try
            {
                var command = new DeleteInvoiceCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted invoice with ID: {InvoiceId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Invoice with ID {InvoiceId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete invoice with ID: {InvoiceId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice with ID {InvoiceId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the invoice.", details = ex.Message });
            }
        }
    }
}
