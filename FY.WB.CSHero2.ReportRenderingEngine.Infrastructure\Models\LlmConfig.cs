namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Models
{
    /// <summary>
    /// Configuration for LLM clients
    /// </summary>
    public class LlmConfig
    {
        /// <summary>
        /// The provider of the LLM service (e.g., "openai", "anthropic")
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// The model to use (e.g., "gpt-4-turbo", "claude-3-opus")
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// The API key for authenticating with the LLM service
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// The endpoint URL for the LLM service
        /// </summary>
        public string ApiEndpoint { get; set; } = string.Empty;

        /// <summary>
        /// The maximum number of retries for failed API calls
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// The timeout in seconds for API calls
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// The maximum number of tokens to generate
        /// </summary>
        public int MaxTokens { get; set; } = 4000;
    }
}
