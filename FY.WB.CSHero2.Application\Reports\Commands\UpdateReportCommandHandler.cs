using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class UpdateReportCommandHandler : IRequestHandler<UpdateReportCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateReportCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateReportCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Reports
                .FirstOrDefaultAsync(r => r.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Report), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this report.");
            }

            // Update only allowed fields as per UpdateReportRequestDto and entity's UpdateDetails method
            entity.UpdateDetails(
                request.Dto.Name,
                request.Dto.Category,
                request.Dto.SlideCount,
                request.Dto.Status,
                request.Dto.Author
            );
            // Or, if more granular control is needed:
            // entity.Name = request.Dto.Name;
            // entity.Category = request.Dto.Category;
            // entity.SlideCount = request.Dto.SlideCount;
            // entity.Status = request.Dto.Status;
            // entity.Author = request.Dto.Author;

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
