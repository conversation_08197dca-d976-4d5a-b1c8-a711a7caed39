import { Invoice, InvoiceCreateInput, InvoiceUpdateInput } from '@/types/invoice';
import { NEXT_NEXT_API_BASE_URL } from '@/lib/constants';

/**
 * Get all invoices with optional filtering, sorting, and pagination
 */
export async function getInvoices(params?: {
  search?: string;
  type?: 'Invoice' | 'Credit' | 'Refund' | 'Payment';
  status?: 'paid' | 'pending' | 'overdue' | 'canceled';
  tenantId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.search) queryParams.append('q', params.search);
    if (params.type) queryParams.append('type', params.type);
    if (params.status) queryParams.append('status', params.status);
    if (params.tenantId) queryParams.append('tenantId', params.tenantId);
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  const response = await fetch(`${NEXT_API_BASE_URL}/v1/invoices?${queryParams}`);
  if (!response.ok) {
    throw new Error('Failed to fetch invoices');
  }
  const data = await response.json();
  return {
    data,
    headers: response.headers
  };
}

/**
 * Get a single invoice by ID
 */
export async function getInvoiceById(id: string): Promise<Invoice> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/invoices/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch invoice');
  }
  return response.json();
}

/**
 * Get all invoices for a specific tenant
 */
export async function getInvoicesByTenantId(tenantId: string): Promise<Invoice[]> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/tenants/${tenantId}/invoices`);
  if (!response.ok) {
    throw new Error('Failed to fetch tenant invoices');
  }
  return response.json();
}

/**
 * Create a new invoice
 */
export async function createInvoice(data: InvoiceCreateInput): Promise<Invoice> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/invoices`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create invoice');
  }
  return response.json();
}

/**
 * Update an existing invoice
 */
export async function updateInvoice(id: string, data: InvoiceUpdateInput): Promise<Invoice> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/invoices/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update invoice');
  }
  return response.json();
}

/**
 * Delete an invoice
 */
export async function deleteInvoice(id: string): Promise<void> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/invoices/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete invoice');
  }
}
