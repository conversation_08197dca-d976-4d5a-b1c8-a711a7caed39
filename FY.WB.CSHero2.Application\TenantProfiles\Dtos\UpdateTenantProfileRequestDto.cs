using System;
using FY.WB.CSHero2.Domain.Entities; // Required for PaymentMethodInfo and BillingAddressInfo

namespace FY.WB.CSHero2.Application.TenantProfiles.Dtos
{
    public class UpdateTenantProfileRequestDto
    {
        public Guid Id { get; set; } // Id of the TenantProfile to update
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Subscription { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public DateTime NextBillingDate { get; set; }
        public string SubscriptionStatus { get; set; } = string.Empty;
        public PaymentMethodInfo? PaymentMethod { get; set; }
        public BillingAddressInfo? BillingAddress { get; set; }
    }
}
