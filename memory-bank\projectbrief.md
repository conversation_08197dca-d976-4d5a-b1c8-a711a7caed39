# Project Brief: SaaS Template - ASP.NET Core API & Next.js Frontend

**Version:** 0.1
**Date:** 2025-05-09

## 1. Project Goal

To create a reusable and robust template for building Software-as-a-Service (SaaS) applications. The template will feature an ASP.NET Core 8 Web API backend and a Next.js (TypeScript, Tailwind CSS) frontend. Key features include multi-tenancy support (using Finbuckle), user authentication and authorization with ASP.NET Core Identity and JWTs, and a structured frontend with distinct layouts for public, client (authenticated user), and tenant-specific areas.

## 2. Core Requirements

### 2.1. Backend (ASP.NET Core API - `UN_WB_TemlateProject`)
-   **Framework:** ASP.NET Core 8.
-   **Database:** SQL Server (using Entity Framework Core).
-   **Authentication:** ASP.NET Core Identity for user management, JWT Bearer tokens for API authentication.
    -   Custom `ApplicationUser` with fields: `Email`, `Password` (handled by Identity), `CompanyName` (string, nullable), `CompanyUrl` (string, nullable).
-   **Multi-tenancy:**
    -   Utilize Finbuckle.MultiTenant library.
    -   Define a tenant entity (`AppTenantInfo`) with `Id`, `Identifier`, `Name`, `ConnectionString`.
    -   Implement a tenant resolution strategy (e.g., route-based: `/api/{tenant}/...`).
    -   Initially use an in-memory store for tenants, with plans to move to an EF Core store.
    -   The `ApplicationDbContext` should eventually be multi-tenant aware (currently blocked by CS0308, temporarily using standard `IdentityDbContext`).
-   **API Endpoints:**
    -   `POST /api/Auth/register`: User registration.
    -   `POST /api/Auth/login`: User login, returns JWT.
-   **Database Migrations:** Entity Framework Core migrations, applied automatically on startup.
-   **Swagger/OpenAPI:** Configured for API documentation and testing, including JWT authorization.

### 2.2. Frontend (Next.js - `un_wb_templateproject_ui`)
-   **Framework:** Next.js (App Router, TypeScript, Tailwind CSS, ESLint).
-   **Layouts:**
    -   `PublicLayout`: For publicly accessible pages (e.g., home, login, register).
    -   `ClientLayout`: For general authenticated user areas (e.g., dashboard).
    -   `TenantLayout`: For pages specific to a tenant context, displaying tenant information.
-   **Authentication:**
    -   Connect to the ASP.NET Core API for identity services using JWTs.
    -   Implement client-side authentication context (`AuthContext`) to manage user state and tokens.
    -   Login page (`/login`).
    -   Registration page (`/register`).
    -   Securely store JWT (HttpOnly cookie preferred, set via Next.js BFF API route).
    -   Route protection for client and tenant areas (using Next.js Middleware).
-   **API Communication:**
    -   Utilize Next.js API routes as a Backend-For-Frontend (BFF) to proxy requests to the ASP.NET Core API, especially for authentication to handle HttpOnly cookies.

### 2.3. Deployment (Future Consideration)
-   Target deployment: Azure Static Web Apps (for Next.js frontend) + Azure App Service (for ASP.NET Core API) + Azure SQL Database (or similar).

## 3. Scope

-   **In Scope (Current Phase):**
    -   Setup ASP.NET Core API with Identity, basic JWT auth, and initial (non-EF Core) Finbuckle setup.
    -   Create Next.js frontend with basic layouts, routing, login/registration pages, and client-side auth context.
    -   Establish API communication between Next.js (via BFF) and ASP.NET Core API for auth.
-   **Out of Scope (For Now / Future):**
    -   Full resolution of the CS0308 error preventing `MultiTenantIdentityDbContext`.
    -   EF Core store for Finbuckle tenants.
    -   Advanced multi-tenancy features (per-tenant branding, complex tenant resolution).
    -   Specific business logic beyond authentication and user/tenant structure.
    -   Comprehensive UI/UX design.
    -   Automated tests (unit, integration, e2e).
    -   Deployment scripts/pipelines.

## 4. Key Technologies
-   ASP.NET Core 8
-   Entity Framework Core 8
-   SQL Server (LocalDB for development)
-   ASP.NET Core Identity
-   JWT (JSON Web Tokens)
-   Finbuckle.MultiTenant
-   Next.js (App Router)
-   TypeScript
-   Tailwind CSS
-   ESLint
-   Swagger/OpenAPI

## 5. Success Criteria (Initial)
-   ASP.NET Core API builds and runs.
-   Users can be registered and can log in via the API, receiving a JWT.
-   Next.js frontend builds and runs.
-   Users can register and log in via the Next.js UI, with tokens handled.
-   Protected routes in Next.js redirect unauthenticated users.
-   Basic tenant resolution (e.g., via route) is demonstrable in the API.
