using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using FY.WB.CSHero2.Application.Common.Interfaces;

namespace FY.WB.CSHero2.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "RequireAdmin")] // Require admin policy for all endpoints in this controller
    public class AdminController : ControllerBase
    {
        private readonly ILogger<AdminController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public AdminController(
            ILogger<AdminController> logger,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _currentUserService = currentUserService;
        }

        [HttpGet]
        public IActionResult GetAdminInfo()
        {
            _logger.LogInformation("Admin info requested by user {UserId}", _currentUserService.UserId);
            
            return Ok(new
            {
                message = "You have access to admin resources",
                userId = _currentUserService.UserId,
                isAdmin = _currentUserService.IsAdmin
            });
        }

        [HttpGet("validate")]
        public IActionResult ValidateAdminAccess()
        {
            _logger.LogInformation("Admin validation requested by user {UserId}", _currentUserService.UserId);
            
            if (!_currentUserService.IsAdmin)
            {
                _logger.LogWarning("Non-admin user {UserId} attempted to access admin resources", _currentUserService.UserId);
                return Forbid();
            }
            
            return Ok(new
            {
                isValid = true,
                message = "You have valid admin access"
            });
        }
    }
}
