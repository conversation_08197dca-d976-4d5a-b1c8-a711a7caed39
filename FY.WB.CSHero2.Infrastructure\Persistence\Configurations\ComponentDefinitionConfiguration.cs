using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ComponentDefinition entity
    /// </summary>
    public class ComponentDefinitionConfiguration : IEntityTypeConfiguration<ComponentDefinition>
    {
        public void Configure(EntityTypeBuilder<ComponentDefinition> builder)
        {
            // Table configuration
            builder.ToTable("ComponentDefinitions");

            // Primary key
            builder.HasKey(cd => cd.Id);

            // Properties
            builder.Property(cd => cd.Id)
                .IsRequired()
                .ValueGeneratedOnAdd();

            builder.Property(cd => cd.ReportVersionId)
                .IsRequired();

            builder.Property(cd => cd.SectionId)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(cd => cd.SectionName)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(cd => cd.ComponentCode)
                .HasColumnType("nvarchar(max)")
                .IsRequired();

            builder.Property(cd => cd.TypeDefinitions)
                .HasColumnType("nvarchar(max)")
                .IsRequired();

            builder.Property(cd => cd.ImportsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("[]");

            builder.Property(cd => cd.MetadataJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");

            // GeneratedAt and GeneratedBy are handled by the base AuditedEntity configuration

            builder.Property(cd => cd.ComponentHash)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(cd => cd.ComponentSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(cd => cd.IsValid)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(cd => cd.ValidationErrors)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("");

            builder.Property(cd => cd.Framework)
                .HasMaxLength(50)
                .IsRequired()
                .HasDefaultValue("NextJS");

            builder.Property(cd => cd.StyleFramework)
                .HasMaxLength(50)
                .IsRequired()
                .HasDefaultValue("TailwindCSS");

            // Indexes
            builder.HasIndex(cd => new { cd.ReportVersionId, cd.SectionId })
                .IsUnique()
                .HasDatabaseName("IX_ComponentDefinitions_ReportVersionId_SectionId");

            builder.HasIndex(cd => cd.ReportVersionId)
                .HasDatabaseName("IX_ComponentDefinitions_ReportVersionId");

            builder.HasIndex(cd => cd.SectionId)
                .HasDatabaseName("IX_ComponentDefinitions_SectionId");

            builder.HasIndex(cd => cd.CreationTime)
                .HasDatabaseName("IX_ComponentDefinitions_CreationTime");

            builder.HasIndex(cd => cd.IsValid)
                .HasDatabaseName("IX_ComponentDefinitions_IsValid");

            builder.HasIndex(cd => cd.ComponentHash)
                .HasDatabaseName("IX_ComponentDefinitions_ComponentHash");

            // Relationships
            builder.HasOne(cd => cd.ReportVersion)
                .WithMany(rv => rv.ComponentDefinitions)
                .HasForeignKey(cd => cd.ReportVersionId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
