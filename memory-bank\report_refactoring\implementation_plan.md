# Multi-Storage Report Structure Implementation Plan

## Overview

This document provides a detailed implementation plan for refactoring the report structure to use multiple storage mechanisms:

1. **SQL Database**: Store report metadata and style selections
2. **Azure Blob Storage**: Store rendered Next.js components with HTML and CSS
3. **Azure Cosmos DB**: Store report data (sections and fields) as JSON

## Implementation Phases

### Phase 1: Infrastructure Setup (Week 1)

#### Task 1.1: Set up Azure Resources

1. **Create Azure Cosmos DB Account**
   - Create a new Cosmos DB account with SQL API
   - Create a database named "ReportData"
   - Create a container named "Reports" with partition key "/id"
   - Configure throughput (start with 400 RU/s)
   - Set up indexing policy for efficient queries

2. **Create Azure Blob Storage Account**
   - Create a new Storage Account
   - Create a container named "report-components"
   - Configure access tier (Hot)
   - Set up CORS policy for web access
   - Configure lifecycle management for version retention

3. **Configure Connection Strings**
   - Add connection strings to application configuration
   - Set up Key Vault for secure storage of connection strings
   - Configure application to use Key Vault references

#### Task 1.2: Update SQL Database Schema

1. **Create New Entities**
   - Create `ReportStyle` entity
   - Update `Report` entity with storage references
   - Update `ReportVersion` entity with storage references

2. **Create Entity Configurations**
   - Create `ReportStyleConfiguration` class
   - Update `ReportConfiguration` class
   - Update `ReportVersionConfiguration` class

3. **Generate and Apply Migration**
   - Create EF Core migration
   - Review migration SQL script
   - Apply migration to development database

#### Task 1.3: Implement Storage Repositories

1. **Implement SQL Repository**
   - Create `ReportMetadataRepository` class
   - Implement CRUD operations for reports, versions, and styles
   - Add unit tests for repository methods

2. **Implement Cosmos DB Repository**
   - Create `ReportDataRepository` class
   - Implement document operations
   - Add unit tests for repository methods

3. **Implement Blob Storage Repository**
   - Create `ReportComponentsRepository` class
   - Implement blob operations
   - Add unit tests for repository methods

### Phase 2: Data Models and DTOs (Week 1-2)

#### Task 2.1: Create Data Models

1. **Create Cosmos DB Models**
   - Create `ReportData` class
   - Create `ReportSection` class
   - Create `ReportSectionField` class
   - Create serialization helpers

2. **Create Blob Storage Models**
   - Create `ComponentsMetadata` class
   - Create `ComponentDefinition` class
   - Create serialization helpers

3. **Create DTOs**
   - Create `ReportDto` class
   - Create `ReportVersionDto` class
   - Create `ReportStyleDto` class
   - Create `ReportDataDto` class
   - Create `ReportSectionDto` class
   - Create `ReportSectionFieldDto` class
   - Create `ComponentDefinitionDto` class
   - Create mapping profiles for AutoMapper

### Phase 3: Service Implementation (Week 2)

#### Task 3.1: Implement Core Services

1. **Implement Report Service**
   - Create `ReportService` class implementing `IReportService`
   - Implement CRUD operations for reports
   - Implement version management methods
   - Add unit tests for service methods

2. **Implement Report Data Service**
   - Create `ReportDataService` class implementing `IReportDataService`
   - Implement CRUD operations for report data
   - Implement section and field management methods
   - Add unit tests for service methods

3. **Implement Report Rendering Service**
   - Create `ReportRenderingService` class implementing `IReportRenderingService`
   - Implement rendering methods
   - Implement component management methods
   - Implement export methods
   - Add unit tests for service methods

#### Task 3.2: Implement CQRS Commands and Queries

1. **Create Commands**
   - Create `CreateReportCommand` and handler
   - Create `UpdateReportCommand` and handler
   - Create `DeleteReportCommand` and handler
   - Create `CreateReportVersionCommand` and handler
   - Create `UpdateReportStyleCommand` and handler
   - Create `UpdateReportDataCommand` and handler
   - Create `RenderReportCommand` and handler

2. **Create Queries**
   - Create `GetReportQuery` and handler
   - Create `GetReportVersionQuery` and handler
   - Create `GetReportStyleQuery` and handler
   - Create `GetReportDataQuery` and handler
   - Create `GetReportSectionQuery` and handler
   - Create `GetReportComponentsQuery` and handler

3. **Register Services and Handlers**
   - Update dependency injection configuration
   - Register repositories
   - Register services
   - Register command and query handlers

### Phase 4: Data Migration (Week 2-3)

#### Task 4.1: Create Migration Service

1. **Implement Migration Logic**
   - Create `ReportDataMigrationService` class
   - Implement method to read existing reports from SQL
   - Implement method to extract sections and fields from JSON
   - Implement method to create Cosmos DB documents
   - Implement method to extract component definitions
   - Implement method to store components in Blob Storage
   - Implement method to update SQL records with references

2. **Create Migration Command**
   - Create `MigrateReportDataCommand` and handler
   - Implement migration orchestration logic
   - Add progress tracking and logging

3. **Create Migration Endpoint**
   - Add migration endpoint to `ReportsController`
   - Implement authorization and validation
   - Add Swagger documentation

#### Task 4.2: Test Migration

1. **Create Test Data**
   - Create representative test reports
   - Ensure variety of section and field types
   - Include edge cases

2. **Run Migration Tests**
   - Create integration tests for migration
   - Test with small dataset
   - Test with large dataset
   - Test error handling and recovery

3. **Validate Migration Results**
   - Verify SQL data integrity
   - Verify Cosmos DB document structure
   - Verify Blob Storage content
   - Verify cross-references

### Phase 5: API Implementation (Week 3)

#### Task 5.1: Update Controllers

1. **Update Reports Controller**
   - Update CRUD endpoints to use new services
   - Add endpoints for version management
   - Add endpoints for style management
   - Add Swagger documentation

2. **Create Report Data Controller**
   - Create `ReportDataController` class
   - Implement endpoints for report data management
   - Implement endpoints for section management
   - Implement endpoints for field management
   - Add Swagger documentation

3. **Create Report Rendering Controller**
   - Create `ReportRenderingController` class
   - Implement endpoints for rendering
   - Implement endpoints for component management
   - Implement endpoints for export
   - Add Swagger documentation

#### Task 5.2: Implement API Tests

1. **Create Controller Tests**
   - Create unit tests for controller methods
   - Create integration tests for API endpoints
   - Test authorization and validation

2. **Create End-to-End Tests**
   - Create tests for complete workflows
   - Test cross-storage operations
   - Test error handling and recovery

3. **Document API**
   - Update API documentation
   - Create Postman collection
   - Create example requests and responses

### Phase 6: Frontend Integration (Week 3-4)

#### Task 6.1: Update Frontend Models

1. **Update TypeScript Interfaces**
   - Update `Report` interface
   - Update `ReportVersion` interface
   - Create `ReportStyle` interface
   - Create `ReportData` interface
   - Create `ReportSection` interface
   - Create `ReportSectionField` interface
   - Create `ComponentDefinition` interface

2. **Update API Service Functions**
   - Update report management functions
   - Add version management functions
   - Add style management functions
   - Add data management functions
   - Add rendering functions
   - Add component management functions

3. **Create Data Transformation Helpers**
   - Create functions to transform API data to UI models
   - Create functions to transform UI models to API data
   - Create validation helpers

#### Task 6.2: Update UI Components

1. **Update Report Editor**
   - Update report metadata editing
   - Update style editing
   - Update section management
   - Update field management
   - Update rendering preview

2. **Create New Components**
   - Create component viewer
   - Create version history viewer
   - Create export options dialog

3. **Update Existing Components**
   - Update report table
   - Update report details
   - Update section editor
   - Update field editor

### Phase 7: Testing and Optimization (Week 4)

#### Task 7.1: Performance Testing

1. **Create Performance Test Suite**
   - Create tests for SQL operations
   - Create tests for Cosmos DB operations
   - Create tests for Blob Storage operations
   - Create tests for cross-storage operations

2. **Run Performance Tests**
   - Test with small dataset
   - Test with medium dataset
   - Test with large dataset
   - Test with concurrent users

3. **Optimize Performance**
   - Identify bottlenecks
   - Implement caching
   - Optimize queries
   - Implement batch operations

#### Task 7.2: Security Testing

1. **Create Security Test Suite**
   - Test authentication and authorization
   - Test data isolation
   - Test input validation
   - Test error handling

2. **Run Security Tests**
   - Test with different user roles
   - Test with invalid inputs
   - Test with malicious inputs
   - Test error responses

3. **Address Security Issues**
   - Fix authorization issues
   - Implement additional validation
   - Improve error handling
   - Add security headers

#### Task 7.3: Final Integration Testing

1. **Create Integration Test Suite**
   - Test complete workflows
   - Test edge cases
   - Test error scenarios
   - Test recovery scenarios

2. **Run Integration Tests**
   - Test in development environment
   - Test in staging environment
   - Test with production-like data
   - Test with multiple users

3. **Fix Issues and Optimize**
   - Address integration issues
   - Optimize workflows
   - Improve error handling
   - Enhance user experience

### Phase 8: Deployment and Documentation (Week 4)

#### Task 8.1: Prepare Deployment

1. **Create Deployment Scripts**
   - Create SQL migration script
   - Create Azure resource deployment script
   - Create application deployment script
   - Create rollback script

2. **Create Deployment Documentation**
   - Document deployment steps
   - Document configuration options
   - Document troubleshooting steps
   - Document rollback procedure

3. **Create Monitoring Setup**
   - Set up SQL monitoring
   - Set up Cosmos DB monitoring
   - Set up Blob Storage monitoring
   - Set up application monitoring

#### Task 8.2: Create User Documentation

1. **Update API Documentation**
   - Document new endpoints
   - Document request and response formats
   - Document authentication and authorization
   - Document error responses

2. **Create Developer Guide**
   - Document architecture
   - Document data models
   - Document service interfaces
   - Document extension points

3. **Create User Guide**
   - Document UI workflows
   - Document feature usage
   - Document best practices
   - Document troubleshooting

#### Task 8.3: Deploy to Production

1. **Deploy to Staging**
   - Deploy Azure resources
   - Deploy database changes
   - Deploy application
   - Run smoke tests

2. **Validate Staging Deployment**
   - Verify functionality
   - Verify performance
   - Verify security
   - Verify monitoring

3. **Deploy to Production**
   - Schedule deployment window
   - Execute deployment plan
   - Verify deployment
   - Monitor post-deployment

## Technical Implementation Details

### Azure Cosmos DB Setup

```csharp
// Add to Startup.cs
services.AddSingleton(sp => 
{
    var connectionString = Configuration["CosmosDb:ConnectionString"];
    return new CosmosClient(connectionString, new CosmosClientOptions
    {
        SerializerOptions = new CosmosSerializationOptions
        {
            PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
        }
    });
});

services.AddScoped<IReportDataRepository, ReportDataRepository>();
```

### Azure Blob Storage Setup

```csharp
// Add to Startup.cs
services.AddSingleton(sp => 
{
    var connectionString = Configuration["BlobStorage:ConnectionString"];
    return new BlobServiceClient(connectionString);
});

services.AddScoped<IReportComponentsRepository, ReportComponentsRepository>();
```

### Entity Framework Configuration

```csharp
// ReportStyleConfiguration.cs
public class ReportStyleConfiguration : IEntityTypeConfiguration<ReportStyle>
{
    public void Configure(EntityTypeBuilder<ReportStyle> builder)
    {
        builder.ToTable("ReportStyles");
        builder.HasKey(s => s.Id);
        
        builder.Property(s => s.ReportId).IsRequired();
        builder.Property(s => s.Theme).HasMaxLength(50);
        builder.Property(s => s.ColorScheme).HasMaxLength(50);
        builder.Property(s => s.Typography).HasMaxLength(50);
        builder.Property(s => s.Spacing).HasMaxLength(50);
        
        builder.Property(s => s.LayoutOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.TypographyOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.StructureOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.ContentOptionsJson).HasColumnType("nvarchar(max)");
        builder.Property(s => s.VisualOptionsJson).HasColumnType("nvarchar(max)");
        
        builder.HasOne(s => s.Report)
            .WithOne()
            .HasForeignKey<ReportStyle>(s => s.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(s => s.ReportId)
            .IsUnique()
            .HasDatabaseName("IX_ReportStyles_ReportId");
    }
}
```

### Cosmos DB Repository Implementation

```csharp
public class ReportDataRepository : IReportDataRepository
{
    private readonly CosmosClient _cosmosClient;
    private readonly Container _container;
    private readonly ILogger<ReportDataRepository> _logger;
    
    public ReportDataRepository(
        CosmosClient cosmosClient, 
        IConfiguration configuration,
        ILogger<ReportDataRepository> logger)
    {
        _cosmosClient = cosmosClient;
        _logger = logger;
        
        var databaseName = configuration["CosmosDb:DatabaseName"];
        var containerName = configuration["CosmosDb:ContainerName"];
        _container = _cosmosClient.GetContainer(databaseName, containerName);
    }
    
    public async Task<ReportData> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _container.ReadItemAsync<ReportData>(
                documentId, 
                new PartitionKey(documentId),
                cancellationToken: cancellationToken);
                
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            _logger.LogWarning("Report data document {DocumentId} not found", documentId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving report data document {DocumentId}", documentId);
            throw;
        }
    }
    
    public async Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _container.CreateItemAsync(
                data,
                new PartitionKey(data.Id),
                cancellationToken: cancellationToken);
                
            _logger.LogInformation("Created report data document {DocumentId}", data.Id);
            return data.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report data document {DocumentId}", data.Id);
            throw;
        }
    }
    
    // Other methods implementation...
}
```

### Blob Storage Repository Implementation

```csharp
public class ReportComponentsRepository : IReportComponentsRepository
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    private readonly ILogger<ReportComponentsRepository> _logger;
    
    public ReportComponentsRepository(
        BlobServiceClient blobServiceClient, 
        IConfiguration configuration,
        ILogger<ReportComponentsRepository> logger)
    {
        _blobServiceClient = blobServiceClient;
        _logger = logger;
        _containerName = configuration["BlobStorage:ContainerName"];
    }
    
    public async Task<string> SaveComponentsAsync(
        Guid reportId, 
        Guid versionId, 
        IEnumerable<ComponentDefinition> components, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var blobId = $"reports/{reportId}/{versionId}";
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Ensure container exists
            await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
            
            // Create metadata
            var metadata = new ComponentsMetadata
            {
                ReportId = reportId.ToString(),
                VersionId = versionId.ToString(),
                Components = components.Select(c => new ComponentMetadata
                {
                    Id = c.Id,
                    Name = c.Name,
                    SectionId = c.SectionId,
                    FileName = $"{c.Name}.tsx",
                    Imports = c.Imports,
                    Props = c.Props
                }).ToList()
            };
            
            // Save metadata
            var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            
            var metadataBlobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");
            using var metadataStream = new MemoryStream(Encoding.UTF8.GetBytes(metadataJson));
            await metadataBlobClient.UploadAsync(metadataStream, overwrite: true, cancellationToken: cancellationToken);
            
            // Save components
            foreach (var component in components)
            {
                var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                await componentBlobClient.UploadAsync(componentStream, overwrite: true, cancellationToken: cancellationToken);
            }
            
            _logger.LogInformation("Saved {ComponentCount} components for report {ReportId}, version {VersionId}", 
                components.Count(), reportId, versionId);
                
            return blobId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving components for report {ReportId}, version {VersionId}", 
                reportId, versionId);
            throw;
        }
    }
    
    // Other methods implementation...
}
```

### Migration Service Implementation

```csharp
public class ReportDataMigrationService
{
    private readonly IApplicationDbContext _sqlContext;
    private readonly IReportDataRepository _cosmosRepository;
    private readonly IReportComponentsRepository _blobRepository;
    private readonly ILogger<ReportDataMigrationService> _logger;
    
    public ReportDataMigrationService(
        IApplicationDbContext sqlContext,
        IReportDataRepository cosmosRepository,
        IReportComponentsRepository blobRepository,
        ILogger<ReportDataMigrationService> logger)
    {
        _sqlContext = sqlContext;
        _cosmosRepository = cosmosRepository;
        _blobRepository = blobRepository;
        _logger = logger;
    }
    
    public async Task<int> MigrateReportDataAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting report data migration");
        
        // Get all reports with their current versions
        var reports = await _sqlContext.Reports
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .ThenInclude(v => v.ComponentDefinitions)
            .ToListAsync(cancellationToken);
            
        _logger.LogInformation("Found {ReportCount} reports to migrate", reports.Count);
        
        int migratedCount = 0;
        
        foreach (var report in reports)
        {
            try
            {
                var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
                if (currentVersion == null)
                {
                    _logger.LogWarning("Report {ReportId} has no current version, skipping", report.Id);
                    continue;
                }
                
                // Migrate report data to Cosmos DB
                var reportData = await MigrateReportDataToCosmosDbAsync(report, currentVersion, cancellationToken);
                
                // Migrate components to Blob Storage
                var blobId = await MigrateComponentsToBlobStorageAsync(report, currentVersion, cancellationToken);
                
                // Update SQL references
                report.DataDocumentId = reportData.Id;
                report.ComponentsBlobId = blobId;
                currentVersion.DataDocumentId = reportData.Id;
                currentVersion.ComponentsBlobId = blobId;
                
                await _sqlContext.SaveChangesAsync(cancellationToken);
                
                migratedCount++;
                _logger.LogInformation("Successfully migrated report {ReportId}", report.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating report {ReportId}", report.Id);
            }
        }
        
        _logger.LogInformation("Completed report data migration. Migrated {MigratedCount} of {TotalCount} reports", 
            migratedCount, reports.Count);
            
        return migratedCount;
    }
    
    private async Task<ReportData> MigrateReportDataToCosmosDbAsync(
        Report report, 
        ReportVersion version, 
        CancellationToken cancellationToken)
    {
        // Extract sections and fields from JSON data
        var jsonData = version.JsonData;
        if (string.IsNullOrEmpty(jsonData))
        {
            _logger.LogWarning("Report {ReportId} has empty JSON data", report.Id);
            jsonData = "{}";
        }
        
        var reportContent = JsonSerializer.Deserialize<ReportContent>(jsonData, 
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
        var sections = new List<ReportSection>();
        
        if (reportContent?.Sections != null)
        {
            int sectionOrder = 0;
            foreach (var sectionData in reportContent.Sections)
            {
                var section = new ReportSection
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = sectionData.Title ?? sectionData.GetProperty("section-title")?.GetString() ?? "Untitled Section",
                    Type = sectionData.Type ?? "unknown",
                    Order = sectionOrder++,
                    Fields = new List<ReportSectionField>()
                };
                
                if (sectionData.Content != null)
                {
                    int fieldOrder = 0;
                    foreach (var field in ExtractFields(sectionData.Content))
                    {
                        var sectionField = new ReportSectionField
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = field.Key,
                            Type = DetermineFieldType(field.Value),
                            Content = field.Value.ToString() ?? string.Empty,
                            Order = fieldOrder++
                        };
                        
                        section.Fields.Add(sectionField);
                    }
                }
                
                sections.Add(section);
            }
        }
        
        var reportData = new ReportData
        {
            Id = $"report-data-{Guid.NewGuid()}",
            ReportId = report.Id.ToString(),
            VersionId = version.Id.ToString(),
            VersionNumber = version.VersionNumber,
            Sections = sections,
            Metadata = new ReportDataMetadata
            {
                CreatedAt = DateTime.UtcNow,
                CreatedBy = version.CreatorId?.ToString() ?? "system",
                LastModifiedAt = DateTime.UtcNow,
                LastModifiedBy = version.CreatorId?.ToString() ?? "system"
            }
        };
        
        await _cosmosRepository.CreateReportDataAsync(reportData, cancellationToken);
        
        return reportData;
    }
    
    private async Task<string> MigrateComponentsToBlobStorageAsync(
        Report report, 
        ReportVersion version, 
        CancellationToken cancellationToken)
    {
        var components = new List<ComponentDefinition>();
        
        foreach (var componentDef in version.ComponentDefinitions)
        {
            var component = new ComponentDefinition
            {
                Id = componentDef.Id.ToString(),
                Name = componentDef.SectionName.Replace(" ", ""),
                SectionId = componentDef.SectionId,
                Code = componentDef.ComponentCode,
                Imports = JsonSerializer.Deserialize<List<string>>(componentDef.ImportsJson) ?? new List<string>(),
                Props = ExtractProps(componentDef.ComponentCode)
            };
            
            components.Add(component);
        }
        
        return await _blobRepository.SaveComponentsAsync(report.Id, version.Id, components, cancellationToken);
    }
    
    private Dictionary<string, object> ExtractFields(object content)
    {
        // Implementation to extract fields from content object
        // This will depend on the structure of your JSON data
    }
    
    private string DetermineFieldType(object value)
    {
        // Implementation to determine field type based on value
        // E.g., string, number, date, etc.
    }
    
    private List<string> ExtractProps(string componentCode)
    {
        // Implementation to extract props from component code
        // This will depend on the structure of your component code
    }
}
```

## Conclusion

This implementation plan provides a detailed roadmap for refactoring the report structure to use multiple storage mechanisms. By following this plan, the team can systematically implement the changes while minimizing disruption to existing functionality. The plan includes specific technical details for each phase, ensuring that the implementation is consistent and follows best practices.