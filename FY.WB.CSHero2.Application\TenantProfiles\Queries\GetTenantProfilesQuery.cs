using MediatR;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.TenantProfiles.Dtos;

namespace FY.WB.CSHero2.Application.TenantProfiles.Queries
{
    public class GetTenantProfilesQuery : IRequest<PagedResult<TenantProfileDto>>
    {
        public TenantProfileQueryParametersDto Parameters { get; }

        public GetTenantProfilesQuery(TenantProfileQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
