using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Infrastructure.Services;
using FY.WB.CSHero2.Infrastructure.Repositories;
using FY.WB.CSHero2.Infrastructure.Configuration;
using FY.WB.CSHero2.Infrastructure.Interfaces;
using FY.WB.CSHero2.Application.Common.Interfaces;
using System;

namespace FY.WB.CSHero2.Infrastructure
{
    /// <summary>
    /// Dependency injection configuration for Infrastructure layer
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// Adds Infrastructure services to the dependency injection container
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Database Context
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);

                    sqlOptions.CommandTimeout(30);
                });

                // Enable sensitive data logging in development
                if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
                {
                    options.EnableSensitiveDataLogging();
                }

                // Enable detailed errors in development
                if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
                {
                    options.EnableDetailedErrors();
                }
            });

            // Register the simplified tenant-aware factory (no EF Core factory needed)
            services.AddScoped<ITenantDbContextFactory, SimpleTenantDbContextFactory>();

            // Register DbContext as IApplicationDbContext
            services.AddScoped<IApplicationDbContext>(provider =>
                provider.GetRequiredService<ApplicationDbContext>());

            // Core Infrastructure Services
            services.AddScoped<ICurrentUserService, CurrentUserService>();
            services.AddScoped<CompanyProfileService>();

            // Cosmos DB Configuration and Services
            services.Configure<CosmosDbOptions>(configuration.GetSection(CosmosDbOptions.SectionName));
            services.Configure<BlobStorageOptions>(configuration.GetSection(BlobStorageOptions.SectionName));
            services.AddSingleton<ICosmosDbService, CosmosDbService>();
            services.AddScoped<IReportDataService, ReportDataService>();

            // Report Rendering Engine Infrastructure
            services.AddReportRenderingEngineInfrastructure(configuration);

            return services;
        }

        /// <summary>
        /// Adds Report Rendering Engine Infrastructure services
        /// </summary>
        private static IServiceCollection AddReportRenderingEngineInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Repository Services
            services.AddScoped<ReportRenderingEngineRepository>();

            // Caching Services
            services.AddMemoryCache();
            services.AddScoped<ComponentCacheService>();

            // HTTP Client for LLM Service
            services.AddHttpClient<LLMService>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(60);
                client.DefaultRequestHeaders.Add("User-Agent", "FY.WB.CSHero2.ReportRenderingEngine/1.0");
            });

            // LLM Service
            services.AddScoped<LLMService>();

            // Background Services (if needed)
            // services.AddHostedService<ComponentCacheCleanupService>();

            return services;
        }

        /// <summary>
        /// Adds distributed caching (Redis) for production environments
        /// Note: Requires Microsoft.Extensions.Caching.StackExchangeRedis package
        /// </summary>
        public static IServiceCollection AddDistributedCaching(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // For now, just use in-memory cache
            // To enable Redis, install Microsoft.Extensions.Caching.StackExchangeRedis package
            services.AddMemoryCache();

            return services;
        }

        /// <summary>
        /// Adds health checks for infrastructure components
        /// Note: Requires additional health check packages for full functionality
        /// </summary>
        public static IServiceCollection AddInfrastructureHealthChecks(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            var healthChecksBuilder = services.AddHealthChecks();

            // Basic health check - can be extended with specific packages
            // To add SQL Server health checks: Install AspNetCore.HealthChecks.SqlServer
            // To add Redis health checks: Install AspNetCore.HealthChecks.Redis
            // To add URL health checks: Install AspNetCore.HealthChecks.Uris

            return services;
        }

        /// <summary>
        /// Configures Entity Framework for development environment
        /// </summary>
        public static IServiceCollection AddDevelopmentEntityFramework(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                options.UseSqlServer(connectionString);

                // Development-specific configurations
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                options.LogTo(Console.WriteLine);
            });

            return services;
        }

        /// <summary>
        /// Configures Entity Framework for production environment
        /// </summary>
        public static IServiceCollection AddProductionEntityFramework(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    // Production-specific configurations
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 5,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);

                    sqlOptions.CommandTimeout(60);
                });

                // Disable sensitive data logging in production
                options.EnableSensitiveDataLogging(false);
                options.EnableDetailedErrors(false);
            });

            return services;
        }

        /// <summary>
        /// Adds monitoring and telemetry services
        /// Note: Requires Microsoft.ApplicationInsights.AspNetCore package for Application Insights
        /// </summary>
        public static IServiceCollection AddInfrastructureMonitoring(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Application Insights can be added by installing Microsoft.ApplicationInsights.AspNetCore
            // services.AddApplicationInsightsTelemetry();

            // Custom telemetry services
            // services.AddScoped<ITelemetryService, TelemetryService>();

            return services;
        }

        /// <summary>
        /// Adds security services for infrastructure
        /// </summary>
        public static IServiceCollection AddInfrastructureSecurity(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Data protection - basic configuration
            services.AddDataProtection();
            // To persist keys to database: Install Microsoft.AspNetCore.DataProtection.EntityFrameworkCore
            // .PersistKeysToDbContext<ApplicationDbContext>();

            // Rate limiting (if needed)
            // services.AddRateLimiting(configuration);

            return services;
        }
    }
}
