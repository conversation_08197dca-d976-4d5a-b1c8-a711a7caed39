import { NextRequest, NextResponse } from 'next/server';

interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'report' | 'client' | 'template' | 'setting' | 'page';
  url: string;
  category: string;
  score: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const type = searchParams.get('type'); // Optional filter by type
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ results: [] });
    }

    const searchQuery = query.toLowerCase().trim();
    
    // Mock search results - replace with actual API calls to your backend
    const allResults: SearchResult[] = [
      // Reports
      {
        id: 'report-1',
        title: 'Customer Service Report Q4 2024',
        description: 'Quarterly performance analysis and metrics',
        type: 'report',
        url: '/client/reports/1',
        category: 'Reports',
        score: 0.9
      },
      {
        id: 'report-2',
        title: 'Monthly Performance Dashboard',
        description: 'Monthly KPI tracking and analysis',
        type: 'report',
        url: '/client/reports/2',
        category: 'Reports',
        score: 0.8
      },
      {
        id: 'report-3',
        title: 'Annual Customer Satisfaction Survey',
        description: 'Comprehensive customer feedback analysis',
        type: 'report',
        url: '/client/reports/3',
        category: 'Reports',
        score: 0.7
      },
      
      // Clients
      {
        id: 'client-1',
        title: 'ABC Corporation',
        description: 'Enterprise client - Technology sector',
        type: 'client',
        url: '/client/clients/1',
        category: 'Clients',
        score: 0.9
      },
      {
        id: 'client-2',
        title: 'XYZ Industries',
        description: 'Manufacturing client - Industrial sector',
        type: 'client',
        url: '/client/clients/2',
        category: 'Clients',
        score: 0.8
      },
      {
        id: 'client-3',
        title: 'Global Services Inc',
        description: 'Service provider - Financial sector',
        type: 'client',
        url: '/client/clients/3',
        category: 'Clients',
        score: 0.7
      },
      
      // Templates
      {
        id: 'template-1',
        title: 'Monthly Report Template',
        description: 'Standard monthly reporting template with KPIs',
        type: 'template',
        url: '/client/reports/templates/1',
        category: 'Templates',
        score: 0.9
      },
      {
        id: 'template-2',
        title: 'Customer Feedback Template',
        description: 'Template for customer satisfaction surveys',
        type: 'template',
        url: '/client/reports/templates/2',
        category: 'Templates',
        score: 0.8
      },
      {
        id: 'template-3',
        title: 'Performance Dashboard Template',
        description: 'Executive dashboard template for performance metrics',
        type: 'template',
        url: '/client/reports/templates/3',
        category: 'Templates',
        score: 0.7
      },
      
      // Settings/Pages
      {
        id: 'setting-1',
        title: 'Account Settings',
        description: 'Manage your account preferences and profile',
        type: 'setting',
        url: '/client/settings/account',
        category: 'Settings',
        score: 0.6
      },
      {
        id: 'setting-2',
        title: 'Notification Settings',
        description: 'Configure email and push notifications',
        type: 'setting',
        url: '/client/settings/notifications',
        category: 'Settings',
        score: 0.6
      },
      {
        id: 'page-1',
        title: 'Dashboard',
        description: 'Main dashboard with overview and quick actions',
        type: 'page',
        url: '/client/dashboard',
        category: 'Navigation',
        score: 0.5
      },
      {
        id: 'page-2',
        title: 'Reports Gallery',
        description: 'Browse and manage all your reports',
        type: 'page',
        url: '/client/reports',
        category: 'Navigation',
        score: 0.5
      }
    ];

    // Filter and score results based on search query
    let filteredResults = allResults.filter(item => {
      const titleMatch = item.title.toLowerCase().includes(searchQuery);
      const descriptionMatch = item.description?.toLowerCase().includes(searchQuery) || false;
      const categoryMatch = item.category.toLowerCase().includes(searchQuery);
      
      return titleMatch || descriptionMatch || categoryMatch;
    });

    // Apply type filter if specified
    if (type && type !== 'all') {
      filteredResults = filteredResults.filter(item => item.type === type);
    }

    // Calculate relevance scores
    filteredResults = filteredResults.map(item => {
      let score = 0;
      
      // Title exact match gets highest score
      if (item.title.toLowerCase() === searchQuery) {
        score += 1.0;
      } else if (item.title.toLowerCase().startsWith(searchQuery)) {
        score += 0.8;
      } else if (item.title.toLowerCase().includes(searchQuery)) {
        score += 0.6;
      }
      
      // Description match
      if (item.description?.toLowerCase().includes(searchQuery)) {
        score += 0.4;
      }
      
      // Category match
      if (item.category.toLowerCase().includes(searchQuery)) {
        score += 0.3;
      }
      
      return { ...item, score };
    });

    // Sort by relevance score (descending) and limit results
    const results = filteredResults
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return NextResponse.json({ 
      results,
      total: filteredResults.length,
      query: searchQuery
    });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
