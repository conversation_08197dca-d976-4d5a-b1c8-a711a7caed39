'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/providers/toast-provider';
import { Client } from '@/types/client';

interface ClientFormProps {
  onSubmit: (data: Partial<Client>) => Promise<void>;
  onCancel: () => void;
}

export function ClientForm({ onSubmit, onCancel }: ClientFormProps) {
  interface FormData {
    name: string; // contact person's name
    companyName: string;
    email: string;
    phone: string;
    address: string;
    industry: string;
    companySize: string;
    status: 'active';
  }

  const [formData, setFormData] = useState<FormData>({
    name: '',
    companyName: '',
    email: '',
    phone: '',
    address: '',
    industry: '',
    companySize: '',
    status: 'active',
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      await onSubmit({
        name: formData.name,
        companyName: formData.companyName,
        email: formData.email,
        phone: formData.phone || undefined,
        address: formData.address || undefined,
        industry: formData.industry || undefined,
        companySize: formData.companySize || undefined,
        status: formData.status
      });
      toast({
        title: 'Success',
        description: 'Client created successfully',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to create client',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 py-4">
      <div className="space-y-2.5">
        <label htmlFor="name" className="text-sm font-medium text-muted-foreground">
          Contact Person
        </label>
        <Input
          id="name"
          placeholder="Enter contact person's name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="contact" className="text-sm font-medium text-muted-foreground">
          Company
        </label>
        <Input
          id="companyName"
          placeholder="Enter company name"
          value={formData.companyName || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
          required
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="email" className="text-sm font-medium text-muted-foreground">
          Email
        </label>
        <Input
          id="email"
          type="email"
          placeholder="Enter email address"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          required
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="phone" className="text-sm font-medium text-muted-foreground">
          Phone
        </label>
        <Input
          id="phone"
          type="tel"
          placeholder="Enter phone number (optional)"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="address" className="text-sm font-medium text-muted-foreground">
          Address
        </label>
        <Input
          id="address"
          placeholder="Enter address (optional)"
          value={formData.address}
          onChange={(e) => setFormData({ ...formData, address: e.target.value })}
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="industry" className="text-sm font-medium text-muted-foreground">
          Industry
        </label>
        <Input
          id="industry"
          placeholder="Enter industry (optional)"
          value={formData.industry}
          onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="companySize" className="text-sm font-medium text-muted-foreground">
          Company Size
        </label>
        <Input
          id="companySize"
          placeholder="Enter company size (e.g., 1-10, 11-50, 51-200, 201-500, 500+)"
          value={formData.companySize}
          onChange={(e) => setFormData({ ...formData, companySize: e.target.value })}
        />
      </div>

      <div className="flex justify-end gap-3 pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading} className="text-white">
          {loading ? 'Creating...' : 'Create Client'}
        </Button>
      </div>
    </form>
  );
}
