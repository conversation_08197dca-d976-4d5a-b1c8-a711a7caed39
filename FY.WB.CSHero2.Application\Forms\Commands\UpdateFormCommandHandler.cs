using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Forms.Commands
{
    public class UpdateFormCommandHandler : IRequestHandler<UpdateFormCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateFormCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateFormCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Forms
                .FirstOrDefaultAsync(f => f.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Form), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this form.");
            }

            entity.Title = request.Dto.Title;
            entity.CustomerName = request.Dto.CustomerName;
            entity.Email = request.Dto.Email;
            entity.Category = request.Dto.Category;
            entity.Priority = request.Dto.Priority;
            entity.Description = request.Dto.Description;
            // Date is not updated as per DTO design

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
