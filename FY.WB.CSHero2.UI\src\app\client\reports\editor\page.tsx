"use client";

import { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReportEditor } from "@/components/features/reports/builder";

function ReportEditorContent() {
  const searchParams = useSearchParams();
  const reportId = searchParams.get('id');
  const templateId = searchParams.get('template') || 'professional';
  const styleId = searchParams.get('style') || undefined;

  return (
    <ReportEditor 
      reportId={reportId || undefined} 
      templateId={templateId}
      styleId={styleId}
    />
  );
}

export default function ReportEditorPage() {
  return (
    <Suspense fallback={<div className="p-8 text-center">Loading editor...</div>}>
      <ReportEditorContent />
    </Suspense>
  );
}
