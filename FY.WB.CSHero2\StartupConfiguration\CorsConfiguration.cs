using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace FY.WB.CSHero2.StartupConfiguration
{
    public static class CorsConfiguration
    {
        public const string NextJsCorsPolicy = "NextJsCorsPolicy";

        public static IServiceCollection AddCorsConfiguration(this IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddPolicy(NextJsCorsPolicy, policy =>
                    policy.AllowAnyOrigin()
                          .AllowAnyHeader()
                          .AllowAnyMethod());
            });

            return services;
        }

        public static IApplicationBuilder UseCorsConfiguration(this IApplicationBuilder app)
        {
            app.UseCors(NextJsCorsPolicy);
            return app;
        }
    }
}
