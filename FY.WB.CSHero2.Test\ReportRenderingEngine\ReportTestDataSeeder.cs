using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine
{
    /// <summary>
    /// Seeds test data for ReportRenderingEngine integration tests
    /// </summary>
    public class ReportTestDataSeeder
    {
        // Test data references
        public Guid TestTenantId { get; private set; }
        public Guid TestUserId { get; private set; }
        public Template TestTemplate { get; private set; } = null!;
        public Report TestReport { get; private set; } = null!;
        public ReportVersion TestReportVersion { get; private set; } = null!;
        public ComponentDefinition TestComponent { get; private set; } = null!;
        public ReportStyleDocument TestStyleDocument { get; private set; } = null!;

        /// <summary>
        /// Seeds comprehensive test data across SQL Server, CosmosDB, and Blob Storage
        /// </summary>
        public async Task SeedTestDataAsync(
            ApplicationDbContext sqlContext,
            IReportStyleService styleService,
            IReportDataBlobService blobService)
        {
            // Initialize test identifiers
            TestTenantId = Guid.Parse("00000000-0000-0000-0001-000000000001");
            TestUserId = Guid.Parse("00000000-0000-0000-0002-000000000001");

            // 1. Seed SQL Server data
            await SeedSqlServerDataAsync(sqlContext);

            // 2. Seed CosmosDB styles
            await SeedCosmosDbStylesAsync(styleService);

            // 3. Seed Blob Storage data
            await SeedBlobStorageDataAsync(blobService);

            // 4. Update SQL entities with references to external storage
            await LinkExternalStorageReferencesAsync(sqlContext, styleService, blobService);
        }

        private async Task SeedSqlServerDataAsync(ApplicationDbContext sqlContext)
        {
            // Create test tenant (if not exists)
            var existingTenant = await sqlContext.Tenants
                .FirstOrDefaultAsync(t => t.Id == TestTenantId);

            if (existingTenant == null)
            {
                var testTenant = new Tenant(
                    TestTenantId,
                    "Test Tenant",
                    "test-tenant",
                    "<EMAIL>"
                );
                testTenant.CreatorId = TestUserId;
                sqlContext.Tenants.Add(testTenant);
            }

            // Create test client
            var testClient = new Client(
                Guid.NewGuid(),
                "Test Client Corp",
                "<EMAIL>",
                "+1-555-0123"
            );
            testClient.TenantId = TestTenantId;
            testClient.CreatorId = TestUserId;
            sqlContext.Clients.Add(testClient);

            // Create test template
            TestTemplate = new Template(
                Guid.NewGuid(),
                "Financial Report Template",
                "Comprehensive financial reporting template",
                "Financial",
                "https://example.com/thumbnails/financial-template.png",
                new[] { "financial", "quarterly", "annual" },
                new[]
                {
                    new TemplateSection { Id = "header", Title = "Report Header", Type = "header" },
                    new TemplateSection { Id = "financials", Title = "Financial Data", Type = "data" },
                    new TemplateSection { Id = "charts", Title = "Charts & Graphs", Type = "visualization" },
                    new TemplateSection { Id = "footer", Title = "Report Footer", Type = "footer" }
                },
                new[]
                {
                    new TemplateField { Id = "title", Name = "Report Title", Type = "text", Content = "Quarterly Financial Report" },
                    new TemplateField { Id = "date", Name = "Report Date", Type = "date", Content = DateTime.UtcNow.ToString("yyyy-MM-dd") },
                    new TemplateField { Id = "revenue", Name = "Revenue", Type = "currency", Content = "0" },
                    new TemplateField { Id = "expenses", Name = "Expenses", Type = "currency", Content = "0" }
                }
            );
            TestTemplate.TenantId = TestTenantId;
            TestTemplate.CreatorId = TestUserId;
            TestTemplate.IsPublic = true;
            sqlContext.Templates.Add(TestTemplate);

            // Create test report
            TestReport = new Report(
                Guid.NewGuid(),
                $"TST-{DateTime.UtcNow:yyyyMMdd}-001",
                testClient.Id,
                testClient.Name,
                "Q4 2024 Financial Report",
                "Financial",
                4,
                "Draft",
                "Test Author"
            );
            TestReport.TenantId = TestTenantId;
            TestReport.CreatorId = TestUserId;
            TestReport.TemplateId = TestTemplate.Id;
            TestReport.ReportType = "Template-based";
            sqlContext.Reports.Add(TestReport);

            // Save to get IDs
            await sqlContext.SaveChangesAsync();

            // Create test report version
            TestReportVersion = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = TestReport.Id,
                VersionNumber = 1,
                Description = "Initial version",
                CreationTime = DateTime.UtcNow,
                CreatorId = TestUserId,
                IsCurrent = true,
                JsonData = """
                {
                    "header.title": "Q4 2024 Financial Report",
                    "header.date": "2024-12-31",
                    "financials.revenue": 1250000,
                    "financials.expenses": 980000,
                    "financials.profit": 270000,
                    "charts.revenueChart": {
                        "type": "line",
                        "data": [1000000, 1100000, 1200000, 1250000],
                        "labels": ["Q1", "Q2", "Q3", "Q4"]
                    }
                }
                """,
                JsonDataSize = 0 // Will be calculated
            };
            TestReportVersion.JsonDataSize = System.Text.Encoding.UTF8.GetByteCount(TestReportVersion.JsonData);
            sqlContext.ReportVersions.Add(TestReportVersion);

            // Create test component definition
            TestComponent = new ComponentDefinition
            {
                Id = Guid.NewGuid(),
                ReportVersionId = TestReportVersion.Id,
                SectionId = "financials",
                SectionName = "Financial Overview",
                ComponentCode = """
                export const FinancialsSection = ({ data }) => {
                  return (
                    <section className="financials-section p-6 bg-white rounded-lg shadow">
                      <h2 className="text-2xl font-bold mb-4">Financial Overview</h2>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="metric-card">
                          <h3 className="text-lg font-semibold">Revenue</h3>
                          <p className="text-3xl text-green-600">${data.revenue?.toLocaleString()}</p>
                        </div>
                        <div className="metric-card">
                          <h3 className="text-lg font-semibold">Expenses</h3>
                          <p className="text-3xl text-red-600">${data.expenses?.toLocaleString()}</p>
                        </div>
                        <div className="metric-card">
                          <h3 className="text-lg font-semibold">Profit</h3>
                          <p className="text-3xl text-blue-600">${data.profit?.toLocaleString()}</p>
                        </div>
                      </div>
                    </section>
                  );
                };
                """,
                TypeDefinitions = """
                interface FinancialsData {
                  revenue: number;
                  expenses: number;
                  profit: number;
                }
                
                interface FinancialsSectionProps {
                  data: FinancialsData;
                }
                """,
                CreationTime = DateTime.UtcNow,
                CreatorId = TestUserId,
                Framework = "NextJS",
                StyleFramework = "TailwindCSS",
                IsValid = true
            };

            TestComponent.SetImports(new List<string>
            {
                "import React from 'react';",
                "import { MetricCard } from '@/components/ui/metric-card';"
            });

            TestComponent.SetMetadata(new Dictionary<string, object>
            {
                ["responsive"] = true,
                ["theme"] = "default",
                ["section"] = "financials"
            });

            TestComponent.SetComponentCode(TestComponent.ComponentCode);
            sqlContext.ComponentDefinitions.Add(TestComponent);

            // Update report with current version
            TestReport.CurrentVersionId = TestReportVersion.Id;

            await sqlContext.SaveChangesAsync();
        }

        private async Task SeedCosmosDbStylesAsync(IReportStyleService styleService)
        {
            // Create template style document
            var templateStyleDoc = new ReportStyleDocument
            {
                Id = ReportStyleDocument.CreateTemplateStyleId(TestTemplate.Id),
                PartitionKey = TestTenantId.ToString(),
                TemplateId = TestTemplate.Id,
                TenantId = TestTenantId,
                StyleType = "template",
                HtmlContent = """
                <div class="report-template financial-template">
                    <header class="report-header">
                        <h1 class="report-title">{{title}}</h1>
                        <div class="report-date">{{date}}</div>
                    </header>
                    <main class="report-content">
                        <section class="financials-section">
                            <h2>Financial Overview</h2>
                            <div class="metrics-grid">
                                <div class="metric-card revenue">
                                    <span class="metric-label">Revenue</span>
                                    <span class="metric-value">{{revenue}}</span>
                                </div>
                                <div class="metric-card expenses">
                                    <span class="metric-label">Expenses</span>
                                    <span class="metric-value">{{expenses}}</span>
                                </div>
                            </div>
                        </section>
                    </main>
                </div>
                """,
                CssStyles = """
                .report-template {
                    font-family: 'Inter', sans-serif;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                }
                
                .report-header {
                    border-bottom: 2px solid #e5e7eb;
                    padding-bottom: 1rem;
                    margin-bottom: 2rem;
                }
                
                .report-title {
                    font-size: 2.5rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin: 0;
                }
                
                .report-date {
                    font-size: 1.125rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                }
                
                .metrics-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    margin-top: 1.5rem;
                }
                
                .metric-card {
                    background: white;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border-left: 4px solid #3b82f6;
                }
                
                .metric-card.revenue {
                    border-left-color: #10b981;
                }
                
                .metric-card.expenses {
                    border-left-color: #ef4444;
                }
                
                .metric-label {
                    display: block;
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: #6b7280;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
                
                .metric-value {
                    display: block;
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-top: 0.5rem;
                }
                """,
                CreatedBy = TestUserId,
                LastModifiedBy = TestUserId
            };

            templateStyleDoc.Metadata.Framework = "TailwindCSS";
            templateStyleDoc.Metadata.Theme = "financial";
            templateStyleDoc.Metadata.Tags.AddRange(new[] { "financial", "template", "corporate" });

            await styleService.SaveStyleAsync(templateStyleDoc);

            // Create report-specific style document
            TestStyleDocument = new ReportStyleDocument
            {
                Id = ReportStyleDocument.CreateReportStyleId(TestReportVersion.Id),
                PartitionKey = TestTenantId.ToString(),
                ReportVersionId = TestReportVersion.Id,
                TenantId = TestTenantId,
                StyleType = "report",
                HtmlContent = templateStyleDoc.HtmlContent,
                CssStyles = templateStyleDoc.CssStyles + """
                
                /* Report-specific overrides */
                .report-template {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                }
                
                .financials-section h2 {
                    color: #1e40af;
                    border-bottom: 2px solid #3b82f6;
                    padding-bottom: 0.5rem;
                }
                """,
                CreatedBy = TestUserId,
                LastModifiedBy = TestUserId
            };

            TestStyleDocument.Metadata.Framework = "TailwindCSS";
            TestStyleDocument.Metadata.Theme = "financial-custom";
            TestStyleDocument.Metadata.Tags.AddRange(new[] { "financial", "q4-2024", "custom" });

            // Add component-specific styles
            TestStyleDocument.ComponentStyles["financials"] = new ComponentStyle
            {
                ComponentId = "financials",
                ComponentType = "data-section",
                CssClasses = new List<string> { "financials-section", "p-6", "bg-white", "rounded-lg", "shadow" },
                CustomCss = """
                .financials-section .metric-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    transition: all 0.2s ease-in-out;
                }
                """
            };

            await styleService.SaveStyleAsync(TestStyleDocument);
        }

        private async Task SeedBlobStorageDataAsync(IReportDataBlobService blobService)
        {
            // Create comprehensive test report data
            var reportData = new Dictionary<string, object>
            {
                ["header.title"] = "Q4 2024 Financial Report",
                ["header.subtitle"] = "Comprehensive Financial Analysis",
                ["header.date"] = "2024-12-31",
                ["header.author"] = "Test Author",
                ["header.company"] = "Test Client Corp",
                
                ["financials.revenue"] = 1250000,
                ["financials.expenses"] = 980000,
                ["financials.profit"] = 270000,
                ["financials.profitMargin"] = 21.6,
                ["financials.growth"] = 15.2,
                
                ["charts.revenueChart"] = new
                {
                    type = "line",
                    title = "Quarterly Revenue Trend",
                    data = new[] { 1000000, 1100000, 1200000, 1250000 },
                    labels = new[] { "Q1 2024", "Q2 2024", "Q3 2024", "Q4 2024" },
                    colors = new[] { "#3b82f6", "#10b981", "#f59e0b", "#ef4444" }
                },
                
                ["charts.expenseChart"] = new
                {
                    type = "pie",
                    title = "Expense Breakdown",
                    data = new[] { 400000, 300000, 180000, 100000 },
                    labels = new[] { "Operations", "Marketing", "R&D", "Other" },
                    colors = new[] { "#ef4444", "#f59e0b", "#3b82f6", "#6b7280" }
                },
                
                ["summary.highlights"] = new[]
                {
                    "Revenue increased by 15.2% compared to Q3 2024",
                    "Profit margin improved to 21.6%",
                    "Operating expenses reduced by 5%",
                    "Strong performance in all business segments"
                },
                
                ["summary.risks"] = new[]
                {
                    "Market volatility may impact Q1 2025",
                    "Supply chain challenges continue",
                    "Increased competition in core markets"
                },
                
                ["metadata.generatedAt"] = DateTime.UtcNow,
                ["metadata.version"] = "1.0",
                ["metadata.dataSource"] = "ERP System",
                ["metadata.lastUpdated"] = DateTime.UtcNow.AddHours(-2)
            };

            // Save report data to blob storage
            var blobPath = await blobService.SaveReportDataAsync(
                TestTenantId, 
                TestReport.Id, 
                TestReportVersion.Id, 
                reportData);

            Console.WriteLine($"Seeded report data to blob: {blobPath}");
        }

        private async Task LinkExternalStorageReferencesAsync(
            ApplicationDbContext sqlContext,
            IReportStyleService styleService,
            IReportDataBlobService blobService)
        {
            // Update template with style document reference
            TestTemplate.StyleDocumentId = ReportStyleDocument.CreateTemplateStyleId(TestTemplate.Id);

            // Update report version with style and blob references
            TestReportVersion.StyleDocumentId = TestStyleDocument.Id;
            TestReportVersion.DataBlobPath = blobService.GenerateReportDataPath(
                TestTenantId, 
                TestReport.Id, 
                TestReportVersion.Id);
            TestReportVersion.IsDataInBlob = true;

            // Update component with style reference
            TestComponent.StyleDocumentId = TestStyleDocument.Id;

            // Save changes
            await sqlContext.SaveChangesAsync();

            Console.WriteLine("Linked external storage references to SQL entities");
        }

        /// <summary>
        /// Creates additional test data for specific test scenarios
        /// </summary>
        public async Task SeedAdditionalTestDataAsync(
            ApplicationDbContext sqlContext,
            IReportStyleService styleService,
            IReportDataBlobService blobService,
            int reportCount = 3)
        {
            for (int i = 1; i <= reportCount; i++)
            {
                // Create additional test report
                var additionalReport = new Report(
                    Guid.NewGuid(),
                    $"TST-{DateTime.UtcNow:yyyyMMdd}-{i:D3}",
                    TestReport.ClientId,
                    TestReport.ClientName,
                    $"Test Report {i}",
                    "Test Category",
                    2,
                    i % 2 == 0 ? "Published" : "Draft",
                    "Test Author"
                );
                additionalReport.TenantId = TestTenantId;
                additionalReport.CreatorId = TestUserId;
                additionalReport.TemplateId = TestTemplate.Id;
                sqlContext.Reports.Add(additionalReport);

                await sqlContext.SaveChangesAsync();

                // Create version for the additional report
                var additionalVersion = new ReportVersion
                {
                    Id = Guid.NewGuid(),
                    ReportId = additionalReport.Id,
                    VersionNumber = 1,
                    Description = $"Initial version of test report {i}",
                    CreationTime = DateTime.UtcNow.AddDays(-i),
                    CreatorId = TestUserId,
                    IsCurrent = true,
                    JsonData = $@"{{
                        ""header.title"": ""Test Report {i}"",
                        ""header.date"": ""{DateTime.UtcNow.AddDays(-i):yyyy-MM-dd}"",
                        ""test.value"": {i * 1000}
                    }}"
                };
                additionalVersion.JsonDataSize = System.Text.Encoding.UTF8.GetByteCount(additionalVersion.JsonData);
                sqlContext.ReportVersions.Add(additionalVersion);

                // Create style document for the additional report
                var additionalStyle = await styleService.CreateStyleFromTemplateAsync(
                    TestTemplate.Id,
                    additionalVersion.Id,
                    TestTenantId,
                    TestUserId);

                // Save test data to blob
                var testData = new Dictionary<string, object>
                {
                    ["header.title"] = $"Test Report {i}",
                    ["header.date"] = DateTime.UtcNow.AddDays(-i).ToString("yyyy-MM-dd"),
                    ["test.value"] = i * 1000,
                    ["test.category"] = "Additional Test Data"
                };

                var blobPath = await blobService.SaveReportDataAsync(
                    TestTenantId,
                    additionalReport.Id,
                    additionalVersion.Id,
                    testData);

                // Update version with references
                additionalVersion.StyleDocumentId = additionalStyle;
                additionalVersion.DataBlobPath = blobPath;
                additionalVersion.IsDataInBlob = true;

                // Update report with current version
                additionalReport.CurrentVersionId = additionalVersion.Id;

                await sqlContext.SaveChangesAsync();
            }

            Console.WriteLine($"Seeded {reportCount} additional test reports");
        }
    }
}