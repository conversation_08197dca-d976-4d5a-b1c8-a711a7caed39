import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';

export interface Client {
  id: string;
  name: string;
  email: string;
  status: string;
  companyName: string;
  createdAt: string;
  updatedAt: string;
  phone: string;
  address: string;
  companySize: string;
  industry: string;
}

/**
 * Get all clients with optional filtering, sorting, and pagination
 * @param options Query options for filtering, sorting, and pagination
 * @returns Array of clients
 */
export async function getClients(options: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
} = {}): Promise<Client[]> {
  const { query, limit, page, sortBy, order } = options;
  
  const data = await readJsonFile<any>();
  
  return processArray(data.clients, {
    query,
    sortBy,
    order,
    page: page ? parseInt(page.toString()) : undefined,
    limit: limit ? parseInt(limit.toString()) : undefined
  });
}

/**
 * Get a client by ID
 * @param id Client ID
 * @returns Client object or null if not found
 */
export async function getClientById(id: string): Promise<Client | null> {
  const data = await readJsonFile<any>();
  const client = data.clients.find((c: Client) => c.id === id);
  return client || null;
}

/**
 * Create a new client
 * @param client Client data
 * @returns Created client
 */
export async function createClient(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Promise<Client> {
  const data = await readJsonFile<any>();
  
  const newClient: Client = {
    ...client,
    id: generateId(data.clients),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  data.clients.push(newClient);
  
  await writeJsonFile('db.json', data);
  
  return newClient;
}

/**
 * Update a client
 * @param id Client ID
 * @param updates Client data to update
 * @returns Updated client or null if not found
 */
export async function updateClient(
  id: string,
  updates: Partial<Omit<Client, 'id' | 'createdAt'>>
): Promise<Client | null> {
  const data = await readJsonFile<any>();
  
  const clientIndex = data.clients.findIndex((c: Client) => c.id === id);
  if (clientIndex === -1) return null;
  
  const updatedClient: Client = {
    ...data.clients[clientIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  data.clients[clientIndex] = updatedClient;
  
  await writeJsonFile('db.json', data);
  
  return updatedClient;
}

/**
 * Delete a client
 * @param id Client ID
 * @returns True if client was deleted, false if not found
 */
export async function deleteClient(id: string): Promise<boolean> {
  const data = await readJsonFile<any>();
  
  const clientIndex = data.clients.findIndex((c: Client) => c.id === id);
  if (clientIndex === -1) return false;
  
  data.clients.splice(clientIndex, 1);
  
  await writeJsonFile('db.json', data);
  
  return true;
}
