import { BaseQuery } from '../../../shared/src/queries/user.queries.js';
import { NEXT_API_BASE_URL } from '@/lib/constants';

class QueryServiceClass {
  private baseUrl: string;

  constructor() {
    this.baseUrl = NEXT_API_BASE_URL;
  }

  async send<TResponse>(query: BaseQuery): Promise<TResponse> {
    const queryString = new URLSearchParams({
      type: query.type,
      payload: JSON.stringify(query.payload),
    }).toString();

    const response = await fetch(`${this.baseUrl}/queries?${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(this.getAuthHeader() || {}),
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Query execution failed');
    }

    return response.json();
  }

  private getAuthHeader(): Record<string, string> | null {
    if (typeof window === 'undefined') return null;

    const token = localStorage.getItem('auth_token');
    return token ? { Authorization: `Bearer ${token}` } : null;
  }
}

export const QueryService = new QueryServiceClass();
