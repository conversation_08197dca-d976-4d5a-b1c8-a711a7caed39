"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

interface LayoutRendererProps {
  layout: any;
  data: any;
  dataType: any;
}

export function LayoutRenderer({
  layout,
  data,
  dataType
}: LayoutRendererProps) {
  if (!layout || !layout.components || layout.components.length === 0) {
    return (
      <div className="p-8 text-center bg-gray-50 rounded-lg">
        <p className="text-gray-500">No layout selected or no components to render.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      <div className="text-center mb-8">
        <div className="text-xl font-medium mb-1">{layout.name || "Report Layout"}</div>
        <p className="text-sm text-gray-500">{layout.description || "Preview of selected layout"}</p>
      </div>

      {layout.components.map((component: any, index: number) => (
        <div key={index} className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">{component.title || `Component ${index + 1}`}</h3>
          
          {component.type === "table" && (
            <div className="overflow-x-auto">
              <div className="inline-block min-w-full align-middle">
                <div className="overflow-hidden border rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {dataType.fields.map((field: any) => (
                          <th
                            key={field.id}
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            {field.label}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        {dataType.fields.map((field: any) => (
                          <td
                            key={field.id}
                            className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                          >
                            {field.type === 'number' ? Math.floor(Math.random() * 100) : 'Sample data'}
                          </td>
                        ))}
                      </tr>
                      <tr>
                        {dataType.fields.map((field: any) => (
                          <td
                            key={field.id}
                            className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                          >
                            {field.type === 'number' ? Math.floor(Math.random() * 100) : 'Sample data'}
                          </td>
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
          
          {component.type === "bar-chart" && (
            <div className="h-64 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="text-center">
                <BarChart className="h-12 w-12 mx-auto text-blue-500 mb-2" />
                <p className="text-gray-500">Bar Chart Placeholder</p>
                <p className="text-xs text-gray-400 mt-1">This is a placeholder for a real chart implementation</p>
              </div>
            </div>
          )}
          
          {component.type === "line-chart" && (
            <div className="h-64 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="text-center">
                <LineChart className="h-12 w-12 mx-auto text-green-500 mb-2" />
                <p className="text-gray-500">Line Chart Placeholder</p>
                <p className="text-xs text-gray-400 mt-1">This is a placeholder for a real chart implementation</p>
              </div>
            </div>
          )}
          
          {component.type === "pie-chart" && (
            <div className="h-64 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="text-center">
                <PieChart className="h-12 w-12 mx-auto text-purple-500 mb-2" />
                <p className="text-gray-500">Pie Chart Placeholder</p>
                <p className="text-xs text-gray-400 mt-1">This is a placeholder for a real chart implementation</p>
              </div>
            </div>
          )}
          
          {component.type === "metric-cards" && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Metric {item}</dt>
                      <dd className="mt-1 text-3xl font-semibold text-gray-900">{Math.floor(Math.random() * 1000)}</dd>
                    </dl>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {component.type === "comparison-chart" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="h-48 flex items-center justify-center bg-gray-100 rounded-lg">
                <BarChart className="h-8 w-8 text-blue-500" />
              </div>
              <div className="h-48 flex items-center justify-center bg-gray-100 rounded-lg">
                <BarChart className="h-8 w-8 text-green-500" />
              </div>
            </div>
          )}
          
          {!["table", "bar-chart", "line-chart", "pie-chart", "metric-cards", "comparison-chart"].includes(component.type) && (
            <div className="h-32 flex items-center justify-center bg-gray-100 rounded-lg">
              <p className="text-gray-500">Unknown component type: {component.type}</p>
            </div>
          )}
        </div>
      ))}
      
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-700">
          <strong>Note:</strong> This is a placeholder component. In a real implementation, 
          actual charts and data visualizations would be rendered with real data.
        </p>
      </div>
    </div>
  );
}
