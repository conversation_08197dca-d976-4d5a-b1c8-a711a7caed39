using FY.WB.CSHero2.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;
using System; // Required for Guid

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class DeleteClientCommandHandler : IRequestHandler<DeleteClientCommand, bool>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public DeleteClientCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<bool> Handle(DeleteClientCommand request, CancellationToken cancellationToken)
        {
            if (!Guid.TryParse(request.Id, out var clientId))
            {
                return false; // Or throw an ArgumentException
            }

            var query = _context.Clients.AsQueryable();

            // Filter by tenant ID if available
            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(c => c.TenantId == _currentUserService.TenantId);
            }

            var clientEntity = await query.FirstOrDefaultAsync(c => c.Id == clientId, cancellationToken);

            if (clientEntity is null)
            {
                return false; // Client not found
            }

            // Instead of removing the client, mark it as deleted
            clientEntity.SetStatus("Deleted");
            await _context.SaveChangesAsync(cancellationToken);

            return true;
        }
    }
}
