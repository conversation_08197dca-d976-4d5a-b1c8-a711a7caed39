using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Test
{
    public class LoginTest
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Testing login for integration tests...");
            
            string baseUrl = "http://localhost:5056";
            var client = new HttpClient { BaseAddress = new Uri(baseUrl) };
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            try
            {
                // First check if the API is running
                var healthResponse = await client.GetAsync("/api/health");
                if (!healthResponse.IsSuccessStatusCode)
                {
                    Console.WriteLine($"API is not running at {baseUrl}. Please start the API first.");
                    return;
                }

                // Test login for tenant 1
                await TestLogin(client, "<EMAIL>", "Test123!", jsonOptions);
                
                // Test login for tenant 2
                await TestLogin(client, "<EMAIL>", "Test123!", jsonOptions);
                
                // Test login for admin
                await TestLogin(client, "<EMAIL>", "AdminPass123!", jsonOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing login: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
            finally
            {
                client.Dispose();
            }
        }

        private static async Task TestLogin(HttpClient client, string email, string password, JsonSerializerOptions jsonOptions)
        {
            Console.WriteLine($"Testing login for: {email}");

            var loginModel = new
            {
                Email = email,
                Password = password
            };

            var content = new StringContent(
                JsonSerializer.Serialize(loginModel, jsonOptions),
                Encoding.UTF8,
                "application/json");

            try
            {
                var response = await client.PostAsync("/api/auth/login", content);
                
                var responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Response: {responseBody}");
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonSerializer.Deserialize<LoginResponse>(responseBody, jsonOptions);
                    Console.WriteLine($"Login successful for {email}. Token: {result?.Token?.Substring(0, 20)}...");
                }
                else
                {
                    Console.WriteLine($"Login failed for {email}: {response.StatusCode}");
                    Console.WriteLine($"Error: {responseBody}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception during login for {email}: {ex.Message}");
            }

            Console.WriteLine();
        }

        private class LoginResponse
        {
            public string? Token { get; set; }
            public bool? Success { get; set; }
            public string? UserId { get; set; }
            public string? Email { get; set; }
            public string? TenantId { get; set; }
            public bool? IsAdmin { get; set; }
        }
    }
}
