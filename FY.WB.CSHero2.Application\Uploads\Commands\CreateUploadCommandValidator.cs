using FluentValidation;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using System; // Added for Uri

namespace FY.WB.CSHero2.Application.Uploads.Commands
{
    public class CreateUploadCommandValidator : AbstractValidator<CreateUploadCommand>
    {
        public CreateUploadCommandValidator()
        {
            RuleFor(v => v.Dto.Filename)
                .NotEmpty().WithMessage("Filename is required.")
                .MaximumLength(255).WithMessage("Filename must not exceed 255 characters.");

            RuleFor(v => v.Dto.Size)
                .GreaterThan(0).WithMessage("Size must be greater than 0.");

            RuleFor(v => v.Dto.ContentType)
                .NotEmpty().WithMessage("Content Type is required.")
                .MaximumLength(100).WithMessage("Content Type must not exceed 100 characters.");

            RuleFor(v => v.Dto.StoragePath)
                .NotEmpty().WithMessage("Storage Path is required.")
                .MaximumLength(1024).WithMessage("Storage Path must not exceed 1024 characters.");

            RuleFor(v => v.Dto.StorageProvider)
                .MaximumLength(100).WithMessage("Storage Provider must not exceed 100 characters.");

            RuleFor(v => v.Dto.ExternalUrl)
                .MaximumLength(2048).WithMessage("External URL must not exceed 2048 characters.")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
                .When(v => !string.IsNullOrEmpty(v.Dto.ExternalUrl))
                .WithMessage("External URL must be a valid URL.");
                
            RuleFor(v => v.Dto.Checksum)
                .MaximumLength(128).WithMessage("Checksum must not exceed 128 characters.");
        }
    }
}
