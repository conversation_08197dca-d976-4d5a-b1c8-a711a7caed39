import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/tenants/[id]/invoices
 * Returns all invoices for a specific tenant
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Extract tenantId outside the try block so it's accessible in the catch block
  const tenantId = params.id;
  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID is required' },
      { status: 400 }
    );
  }

  try {

    console.log(`Fetching invoices for tenant: ${tenantId}`);

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      console.log(`Fetching invoices for tenant ${tenantId} from backend: ${API_BASE_URL}/api/Invoices/tenant/${tenantId}`);
      
      // Call the backend API with timeout
      const backendResponse = await fetch(`${API_BASE_URL}/api/Invoices/tenant/${tenantId}`, {
        method: 'GET',
        headers,
        cache: 'no-store',
        signal: controller.signal, // Add the abort signal
      });

      if (!backendResponse.ok) {
        const errorBody = await backendResponse.text();
        console.error(`Backend API error: ${backendResponse.status} ${errorBody}`);
        return NextResponse.json(
          { error: `Failed to fetch invoices from backend: ${backendResponse.status} ${errorBody}` },
          { status: backendResponse.status }
        );
      }

      const invoices = await backendResponse.json();
      console.log(`Successfully fetched ${Array.isArray(invoices) ? invoices.length : 'unknown number of'} invoices for tenant ${tenantId} from backend API`);
      return NextResponse.json(invoices);
    } finally {
      clearTimeout(timeoutId); // Clear the timeout to prevent memory leaks
    }
  } catch (error) {
    // Check if it's an abort error (timeout)
    if (error instanceof Error && error.name === 'AbortError') {
      console.error(`Fetch timeout after 10 seconds when fetching invoices for tenant ${tenantId}`);
      return NextResponse.json(
        { error: 'Request timed out after 10 seconds' },
        { status: 504 } // Gateway Timeout
      );
    }

    console.error(`Error fetching invoices for tenant: ${error}`);
    return NextResponse.json(
      { 
        error: 'Failed to fetch tenant invoices',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
