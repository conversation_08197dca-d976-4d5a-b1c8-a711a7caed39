using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Invoices.Commands
{
    public class UpdateInvoiceCommandHandler : IRequestHandler<UpdateInvoiceCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateInvoiceCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(UpdateInvoiceCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Invoices
                .FirstOrDefaultAsync(i => i.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Invoice), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this invoice.");
            }

            // Update only allowed fields as per UpdateInvoiceRequestDto and entity's UpdateDetails method
            entity.UpdateDetails(
                request.Dto.Plans,
                request.Dto.Amount,
                request.Dto.Status
            );
            // Or, if more granular control is needed:
            // entity.Plans = request.Dto.Plans;
            // entity.Amount = request.Dto.Amount;
            // entity.Status = request.Dto.Status;

            // LastModificationTime and LastModifierId will be set by DbContext interceptor or ApplyMultiTenancyAndAuditInfo

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
