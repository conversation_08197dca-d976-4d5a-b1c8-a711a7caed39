# Technical Context: SaaS Template

**Version:** 0.1
**Date:** 2025-05-09
**Related Documents:** `projectbrief.md`, `systemPatterns.md`

## 1. Backend Technology Stack (`UN_WB_TemlateProject`)

-   **Framework:** ASP.NET Core 8
    -   Web API project template.
-   **Language:** C#
-   **Database ORM:** Entity Framework Core 8 (version 8.0.15)
    -   Provider: `Microsoft.EntityFrameworkCore.SqlServer` (8.0.15)
    -   Tools: `Microsoft.EntityFrameworkCore.Tools` (8.0.15) for migrations.
    -   Design: `Microsoft.EntityFrameworkCore.Design` (implicitly referenced).
-   **Identity & Authentication:**
    -   `Microsoft.AspNetCore.Identity.EntityFrameworkCore` (8.0.15) for user and role stores.
    -   `Microsoft.AspNetCore.Authentication.JwtBearer` (8.0.4) for JWT validation.
    -   JWTs generated using `System.IdentityModel.Tokens.Jwt` and `Microsoft.IdentityModel.Tokens`.
-   **Multi-tenancy:**
    -   `Finbuckle.MultiTenant` (9.1.3) - Core library.
    -   `Finbuckle.MultiTenant.AspNetCore` (9.1.3) - ASP.NET Core integration (strategies, middleware).
    -   `Finbuckle.MultiTenant.EntityFrameworkCore` (9.1.3) - EF Core integration (Note: Currently using manual DbContext filtering due to past CS0308 issue, not `MultiTenantIdentityDbContext`).
-   **API Documentation:**
    -   `Swashbuckle.AspNetCore` (6.6.2) for Swagger/OpenAPI generation.
-   **Dependency Injection:**
    -   Standard ASP.NET Core DI. Startup hang issue related to `ITenantInfo` resolved by removing incorrect registration.
-   **Configuration:**
    -   `appsettings.json` and environment-specific versions (e.g., `appsettings.Development.json`).
    -   User Secrets for sensitive data during development (though JWT key is currently in `appsettings.json` for template simplicity).
-   **Development Environment:**
    -   .NET SDK 8.x.
    -   LocalDB for SQL Server development.
    -   Build tool: `dotnet` CLI.

## 2. Frontend Technology Stack (`un_wb_templateproject_ui`)

-   **Framework:** Next.js (version 15.3.2, App Router)
    -   Initialized with `create-next-app`.
-   **Language:** TypeScript
-   **Styling:** Tailwind CSS
    -   `tailwindcss`, `@tailwindcss/postcss`.
    -   `postcss.config.mjs`, `tailwind.config.ts`.
-   **Linting:** ESLint
    -   `eslint`, `eslint-config-next`, `@eslint/eslintrc`.
    -   `eslint.config.mjs`.
-   **State Management (Client-Side):** React Context API (`AuthContext`).
-   **Routing:** Next.js App Router (file-system based).
-   **API Communication:**
    -   `fetch` API for making requests to Next.js API Routes (BFF).
    -   Next.js API Routes (`src/app/api/...`) for proxying to the backend and handling cookies.
-   **Package Manager:** npm (as per `create-next-app` initialization).
-   **Development Server:** `next dev` (with Turbopack enabled).

## 3. Database (`UN_WB_TemplateDb` on LocalDB)

-   **Provider:** SQL Server (LocalDB instance: `(localdb)\mssqllocaldb`).
-   **Schema:** Managed by EF Core Migrations.
    -   Initial migration creates tables for ASP.NET Core Identity (`AspNetUsers`, `AspNetRoles`, etc.).
-   **Connection String:** Defined in `appsettings.json` (`ConnectionStrings:DefaultConnection`).

## 4. Key Tool Usage Patterns & Conventions

### 4.1. ASP.NET Core API
-   **Service Registration:** Services (DbContext, Identity, Authentication, CORS, Finbuckle) are registered in `Program.cs` using `builder.Services`.
-   **Middleware Pipeline:** Middleware (CORS, MultiTenant, Authentication, Authorization, Swagger) is configured in `Program.cs` using `app.Use...()`. Order is important.
-   **Controllers:** API endpoints are defined in classes inheriting from `ControllerBase` within the `Controllers` directory.
-   **Data Models:**
    -   Entity classes (e.g., `ApplicationUser`, `AppTenantInfo`) are in the `Data` directory.
    -   DTOs/ViewModels (e.g., `RegisterModel`, `LoginModel` in `AuthController`) are often defined as nested classes within controllers or in separate DTO folders for larger applications.

### 4.2. Next.js Frontend
-   **Project Structure:**
    -   `src/` directory for all source code.
    -   `src/app/` for App Router specific files (pages, layouts).
    -   `src/app/(layouts)/` for shared layout components.
    -   `src/app/(public|client|tenant)/` for route groups.
    -   `src/context/` for React Context providers.
    -   `src/middleware.ts` for Next.js middleware.
-   **Component Types:**
    -   Server Components by default for pages and layouts in App Router.
    -   Client Components (`'use client';` directive) for components requiring interactivity, hooks (e.g., `useState`, `useEffect`, `useParams`, `useRouter`), or browser APIs.
-   **Import Alias:** `@/*` configured to point to `src/*`.
-   **API Routes (BFF):** Located in `src/app/api/...`, using `route.ts` files for handlers (e.g., `POST`).

## 5. Current Blocker Details (CS0308)

-   **Startup Hang (Resolved):** A previous issue causing the application to hang during startup due to a circular dependency in `ITenantInfo` registration was resolved by removing the faulty `AddScoped` call in `MultiTenancyConfiguration.cs`.
-   **CS0308 Issue with `MultiTenantIdentityDbContext` (Resolved/Workaround):** This compilation error was previously resolved by implementing necessary auditing interfaces and moving `AppTenantInfo`. The current implementation uses a standard `IdentityDbContext` and handles multi-tenant filtering manually within the DbContext (`OnModelCreating` and `SaveChanges`). Finbuckle is used for tenant resolution (Claim/Route strategies) and managing the `InMemoryStore`, but not for automatic DbContext filtering via its specialized DbContext types.
