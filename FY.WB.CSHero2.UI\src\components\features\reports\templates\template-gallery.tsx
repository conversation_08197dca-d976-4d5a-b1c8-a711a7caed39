"use client";

import { useState, useEffect } from "react";
import { Search, Filter, Check, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

// Define the template interfaces
export interface StyleTemplate {
  id: string;
  name: string;
  thumbnailUrl: string;
  templateUrl: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnailUrl?: string;
  sections?: any[];
  pages?: any[];
  templateData?: any; // For backward compatibility
  chartData?: any[]; // Chart data for preview
  fields?: any[]; // Fields for the example template
}

// Define the categories for filtering
const CATEGORIES = [
  "All",
  "Finance",
  "Project Management",
  "Marketing",
  "Sales",
  "Human Resources",
  "Operations"
];

interface TemplateGalleryProps {
  onSelectTemplate: (template: ReportTemplate) => void;
  onSelectStyleTemplate?: (styleTemplate: StyleTemplate) => void;
}

export function TemplateGallery({ onSelectTemplate, onSelectStyleTemplate }: TemplateGalleryProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [styleTemplates, setStyleTemplates] = useState<StyleTemplate[]>([]);
  const [selectedStyleTemplate, setSelectedStyleTemplate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [carouselIndex, setCarouselIndex] = useState(0);

  // Fetch style templates
  useEffect(() => {
    const fetchStyleTemplates = async () => {
      try {
        // Create style templates based on the files in frontend/public/assets/style-templates
        const templates: StyleTemplate[] = [
          {
            id: "minimal",
            name: "Minimal",
            thumbnailUrl: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/minimal/minimal-style-template.html"
          },
          {
            id: "modern",
            name: "Modern",
            thumbnailUrl: "/assets/style-templates/modern/modern-style-template.jpg",
            templateUrl: "/assets/style-templates/modern/innocloud-revised-report.html"
          },
          {
            id: "professional",
            name: "Professional",
            thumbnailUrl: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/professional/professional-style-template.html"
          }
        ];
        
        setStyleTemplates(templates);
      } catch (err) {
        console.error('Error fetching style templates:', err);
      }
    };

    fetchStyleTemplates();
  }, []);

  // Fetch templates from the API
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        console.log('Fetching templates...');
        const response = await fetch('/assets/mock-data/db.json');
        if (!response.ok) {
          throw new Error(`Failed to fetch templates: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Templates API response:', data);

        // Extract templates from the db.json file
        let templatesArray: ReportTemplate[] = [];
        if (data.templates && Array.isArray(data.templates)) {
          console.log('Found templates in db.json');
          templatesArray = data.templates;
        } else {
          console.log('No templates found in db.json');
        }
        
        console.log('Templates array before processing:', templatesArray);
        
        // Transform the data to match the expected format if needed
        const processedTemplates = templatesArray.map(template => {
          // Create templateData from pages if it doesn't exist
          if (!template.templateData && template.pages) {
            return {
              ...template,
              templateData: {
                pages: template.pages
              }
            };
          }
          return template;
        });
        
        console.log('Processed templates:', processedTemplates);
        setTemplates(processedTemplates);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError('Failed to load templates. Please try again later.');
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Filter templates based on search query and category
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory =
      selectedCategory === "All" || template.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Handle template selection
  const handleSelectTemplate = (template: ReportTemplate) => {
    setSelectedTemplate(template.id);
    onSelectTemplate(template);
  };

  // Handle style template selection
  const handleSelectStyleTemplate = (template: StyleTemplate) => {
    setSelectedStyleTemplate(template.id);
    if (onSelectStyleTemplate) {
      onSelectStyleTemplate(template);
    }
  };

  // Handle carousel navigation
  const nextSlide = () => {
    setCarouselIndex((prevIndex) => 
      prevIndex === styleTemplates.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCarouselIndex((prevIndex) => 
      prevIndex === 0 ? styleTemplates.length - 1 : prevIndex - 1
    );
  };

  // Calculate all templates for carousel with proper ordering
  const orderedStyleTemplates = styleTemplates.length > 0 
    ? [
        ...styleTemplates.slice(carouselIndex),
        ...styleTemplates.slice(0, carouselIndex)
      ]
    : [];

  return (
    <div className="space-y-6">
      {/* Style Templates Carousel */}
      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-4">Style Templates</h3>
        <div className="relative">
          <div className="flex justify-between items-center mb-2">
            <button 
              onClick={prevSlide}
              className="p-2 rounded-full hover:bg-gray-100"
              aria-label="Previous style template"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button 
              onClick={nextSlide}
              className="p-2 rounded-full hover:bg-gray-100"
              aria-label="Next style template"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
          
          <div className="relative overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out" 
              style={{ transform: `translateX(0%)` }}
            >
              {orderedStyleTemplates.map((template, index) => (
              <div
                key={template.id}
                className={`w-1/3 px-2 flex-shrink-0 transition-all duration-300 ${
                  index >= 3 ? 'opacity-0' : 'opacity-100'
                }`}
                style={{ transform: index >= 3 ? 'scale(0.8)' : 'scale(1)' }}
              >
                <div 
                  className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${
                    selectedStyleTemplate === template.id
                      ? "border-primary ring-2 ring-primary"
                      : "border-gray-200 hover:border-blue-300"
                  }`}
                  onClick={() => handleSelectStyleTemplate(template)}
                >
                  <div className="relative h-32 bg-gray-100">
                    <img
                      src={template.thumbnailUrl}
                      alt={`${template.name} style template thumbnail`}
                      className="object-cover w-full h-full"
                    />
                    
                    {/* Selected indicator */}
                    {selectedStyleTemplate === template.id && (
                      <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                        <Check className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                  
                  <div className="p-2 text-center">
                    <h4 className="font-medium text-sm">{template.name}</h4>
                  </div>
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </div>

      {/* Report Templates section */}
      <h2 className="text-xl font-semibold mb-4 mt-8">Report Templates</h2>
      <div className="flex flex-col md:flex-row gap-4 mb-4">
        {/* Search bar */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search templates..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Category filter */}
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <select
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-2 focus:ring-primary"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            {CATEGORIES.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Templates grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12 border border-dashed border-red-300 rounded-lg">
          <p className="text-red-500">{error}</p>
          <p className="text-sm text-gray-400 mt-1">Please try refreshing the page.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${
                selectedTemplate === template.id
                  ? "border-primary ring-2 ring-primary"
                  : "border-gray-200 hover:border-blue-300"
              }`}
              onClick={() => handleSelectTemplate(template)}
            >
              <div className="p-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium text-lg">{template.name}</h3>
                  
                  {/* Selected indicator */}
                  {selectedTemplate === template.id && (
                    <div className="bg-primary text-white rounded-full p-1">
                      <Check className="h-4 w-4" />
                    </div>
                  )}
                </div>
                
                {/* Category badge */}
                <div className="inline-block bg-gray-800 text-white text-xs px-2 py-1 rounded mt-1 mb-2">
                  {template.category}
                </div>
                
                <p className="text-gray-500 text-sm mb-3 line-clamp-2">
                  {template.description}
                </p>
                
                {/* Pages included */}
                {template.pages && template.pages.length > 0 && (
                  <div className="mt-2 mb-3">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                      Pages Included:
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {template.pages.slice(0, 3).map((page: any, index: number) => (
                        <li key={index} className="flex items-center">
                          <Check className="h-3 w-3 text-green-500 mr-1" />
                          {page.name || `Page ${index + 1}`}
                        </li>
                      ))}
                      {template.pages.length > 3 && (
                        <li className="text-xs text-gray-400">
                          +{template.pages.length - 3} more pages
                        </li>
                      )}
                    </ul>
                  </div>
                )}
                
                {/* Sections included */}
                {template.sections && template.sections.length > 0 && (
                  <div className="mt-2 mb-3">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                      Sections Included:
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {template.sections.map((section: any, index: number) => (
                        <li key={index} className="flex items-center">
                          <Check className="h-3 w-3 text-green-500 mr-1" />
                          {section.title || section['section-title'] || `Section ${index + 1}`}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                <div className="mt-2 flex flex-wrap gap-1">
                  {template.tags.map((tag) => (
                    <span
                      key={tag}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
          
          {filteredTemplates.length === 0 && !loading && !error && (
            <div className="text-center py-12 border border-dashed border-gray-300 rounded-lg col-span-full">
              <p className="text-gray-500">No templates found matching your criteria.</p>
              <p className="text-sm text-gray-400 mt-1">Try adjusting your search or filter.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
