using FY.WB.CSHero2.Application.Clients.Dtos;
using MediatR; // Assuming MediatR is or will be added

namespace FY.WB.CSHero2.Application.Clients.Queries
{
    public record PagedResult<T>(List<T> Items, int Page, int PageSize, int TotalCount)
    {
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
        public bool HasPreviousPage => Page > 1;
        public bool HasNextPage => Page < TotalPages;
    }

    public class GetClientsQuery : IRequest<PagedResult<ClientDto>>
    {
        public ClientQueryParametersDto Parameters { get; }

        public GetClientsQuery(ClientQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
