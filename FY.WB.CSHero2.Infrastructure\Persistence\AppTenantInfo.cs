using Finbuckle.MultiTenant.Abstractions;
using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Infrastructure.Persistence
{
    public class AppTenantInfo : ITenantInfo, 
                                 ICreationAuditedObject, 
                                 IModificationAuditedObject
                                 // Not typically soft-deleted or deletion-audited in the same way as other entities
    {
        // ITenantInfo properties
        public string? Id { get; set; } // Keep as string for Finbuckle compatibility
        public string? Identifier { get; set; }
        public string? Name { get; set; }
        public string? ConnectionString { get; set; }
        
        // Additional property for ApplicationUser.TenantId compatibility
        public Guid TenantGuid => !string.IsNullOrEmpty(Id) ? Guid.Parse(Id) : Guid.Empty;

        // ICreationAuditedObject
        public DateTime CreationTime { get; set; }
        public Guid? CreatorId { get; set; } // Assuming Guid for user IDs

        // IModificationAuditedObject
        public DateTime? LastModificationTime { get; set; }
        public Guid? LastModifierId { get; set; }
        
        public AppTenantInfo()
        {
            // Audit properties will be set by DbContext or services
        }
    }
}