using System.Collections.Generic;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities
{
    /// <summary>
    /// Metadata defining the structure of a document template
    /// </summary>
    public class DocumentTemplateMetadata : Entity<System.Guid>
    {
        /// <summary>
        /// Style information for the document
        /// </summary>
        public ReportStyle Style { get; set; } = new ReportStyle();

        /// <summary>
        /// Sections that make up the document template
        /// </summary>
        public List<ReportSection> Sections { get; set; } = new List<ReportSection>();
    }
}
