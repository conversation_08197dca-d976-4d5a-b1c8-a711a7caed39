# Check status of Azure resources for Report Rendering Engine
param(
    [string]$ResourceGroup = "rg_CSHero",
    [string]$CosmosAccount = "cshero-cosmosdb",
    [string]$StorageAccount = "csherostorage",
    [string]$DatabaseName = "ReportRenderingEngine"
)

Write-Host "=== Azure Resources Status Check ===" -ForegroundColor Green
Write-Host ""

try {
    # Check Resource Group
    Write-Host "Checking Resource Group: $ResourceGroup" -ForegroundColor Cyan
    $rg = az group show --name $ResourceGroup 2>$null | ConvertFrom-Json
    if ($rg) {
        Write-Host "Resource Group exists: $($rg.location)" -ForegroundColor Green
    } else {
        Write-Host "Resource Group not found" -ForegroundColor Red
        return
    }

    # Check Cosmos DB Account
    Write-Host "Checking Cosmos DB Account: $CosmosAccount" -ForegroundColor Cyan
    $cosmos = az cosmosdb show --name $CosmosAccount --resource-group $ResourceGroup 2>$null | ConvertFrom-Json
    if ($cosmos) {
        Write-Host "Cosmos DB Account exists: $($cosmos.provisioningState)" -ForegroundColor Green
        Write-Host "Endpoint: $($cosmos.documentEndpoint)" -ForegroundColor White
        
        # Check Database
        Write-Host "Checking Database: $DatabaseName" -ForegroundColor Cyan
        $database = az cosmosdb sql database show --account-name $CosmosAccount --resource-group $ResourceGroup --name $DatabaseName 2>$null | ConvertFrom-Json
        if ($database) {
            Write-Host "Database exists" -ForegroundColor Green
            
            # Check Containers
            Write-Host "Checking Containers..." -ForegroundColor Cyan
            $containers = az cosmosdb sql container list --account-name $CosmosAccount --resource-group $ResourceGroup --database-name $DatabaseName | ConvertFrom-Json
            if ($containers) {
                foreach ($container in $containers) {
                    Write-Host "Container: $($container.name)" -ForegroundColor Green
                }
            }
        } else {
            Write-Host "Database not found" -ForegroundColor Red
        }
    } else {
        Write-Host "Cosmos DB Account not found" -ForegroundColor Red
    }

    # Check Storage Account
    Write-Host "Checking Storage Account: $StorageAccount" -ForegroundColor Cyan
    $storage = az storage account show --name $StorageAccount --resource-group $ResourceGroup 2>$null | ConvertFrom-Json
    if ($storage) {
        Write-Host "Storage Account exists: $($storage.provisioningState)" -ForegroundColor Green
        Write-Host "SKU: $($storage.sku.name)" -ForegroundColor White
        
        # Check Blob Containers
        Write-Host "Checking Blob Containers..." -ForegroundColor Cyan
        $blobContainers = az storage container list --account-name $StorageAccount --auth-mode login 2>$null | ConvertFrom-Json
        if ($blobContainers) {
            foreach ($container in $blobContainers) {
                Write-Host "Blob Container: $($container.name)" -ForegroundColor Green
            }
        } else {
            Write-Host "No blob containers found or access denied" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Storage Account not found" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "Status check complete!" -ForegroundColor Green

}
catch {
    Write-Host "Error during status check: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
