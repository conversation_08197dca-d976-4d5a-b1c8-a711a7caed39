import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface StepProps {
  children: ReactNode;
  completed?: boolean;
  active?: boolean;
}

export const Step = ({ 
  children, 
  completed = false, 
  active = false 
}: StepProps) => {
  return (
    <div className="flex flex-col items-center">
      <div 
        className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border",
          {
            "bg-primary text-primary-foreground border-primary": active,
            "bg-primary/20 text-primary border-primary/20": completed && !active,
            "bg-muted text-muted-foreground border-muted": !completed && !active
          }
        )}
      >
        {completed ? '✓' : ''}
      </div>
      <div className="mt-2">{children}</div>
    </div>
  );
};

interface StepLabelProps {
  children: ReactNode;
}

export const StepLabel = ({ children }: StepLabelProps) => {
  return (
    <span className="text-sm font-medium">{children}</span>
  );
};

interface StepperProps {
  activeStep: number;
  children: ReactNode;
  className?: string;
}

export const Stepper = ({ 
  activeStep, 
  children, 
  className 
}: StepperProps) => {
  // Clone children to add active and completed props
  const childrenArray = React.Children.toArray(children);
  
  const steps = childrenArray.map((child, index) => {
    if (React.isValidElement<StepProps>(child)) {
      return React.cloneElement(child, {
        ...child.props,
        active: index === activeStep,
        completed: index < activeStep,
      } as StepProps);
    }
    return child;
  });

  return (
    <div className={cn("flex justify-between items-center", className)}>
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          {step}
          {index < childrenArray.length - 1 && (
            <div 
              className={cn(
                "flex-1 h-0.5 mx-2", 
                {
                  "bg-primary": index < activeStep,
                  "bg-muted": index >= activeStep
                }
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
