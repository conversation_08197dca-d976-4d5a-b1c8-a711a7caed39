using FluentValidation;
using FY.WB.CSHero2.Application.Reports.Dtos;
using System;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class UpdateReportCommandValidator : AbstractValidator<UpdateReportCommand>
    {
        public UpdateReportCommandValidator()
        {
            RuleFor(v => v.Dto.Id)
                .NotEmpty().WithMessage("Id is required.");

            RuleFor(v => v.Dto.Name)
                .NotEmpty().WithMessage("Report Name is required.")
                .MaximumLength(200).WithMessage("Report Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Category)
                .NotEmpty().WithMessage("Category is required.")
                .MaximumLength(100).WithMessage("Category must not exceed 100 characters.");

            RuleFor(v => v.Dto.SlideCount)
                .GreaterThanOrEqualTo(0).WithMessage("Slide Count must be a non-negative number.");

            RuleFor(v => v.Dto.Status)
                .NotEmpty().WithMessage("Status is required.")
                .MaximumLength(50).WithMessage("Status must not exceed 50 characters.");

            RuleFor(v => v.Dto.Author)
                .NotEmpty().WithMessage("Author is required.")
                .MaximumLength(100).WithMessage("Author must not exceed 100 characters.");
        }
    }
}
