using System;

namespace FY.WB.CSHero2.Application.Reports.Dtos
{
    public class CreateReportRequestDto
    {
        public string ReportNumber { get; set; } = string.Empty; // Can be auto-generated or provided
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty; // Could be denormalized or fetched via ClientId
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
    }
}
