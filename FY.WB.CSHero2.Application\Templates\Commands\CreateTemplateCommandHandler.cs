using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Templates.Commands
{
    public class CreateTemplateCommandHandler : IRequestHandler<CreateTemplateCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateTemplateCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateTemplateCommand request, CancellationToken cancellationToken)
        {
            var entity = new Template
            {
                Name = request.Dto.Name,
                Description = request.Dto.Description,
                Category = request.Dto.Category,
                ThumbnailUrl = request.Dto.ThumbnailUrl
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };

            if (request.Dto.Tags != null && request.Dto.Tags.Any())
            {
                entity.SetTags(request.Dto.Tags);
            }

            if (request.Dto.Sections != null && request.Dto.Sections.Any())
            {
                entity.SetSections(request.Dto.Sections);
            }

            if (request.Dto.Fields != null && request.Dto.Fields.Any())
            {
                entity.SetFields(request.Dto.Fields);
            }
            
            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create a Template.");
            }
            entity.TenantId = _currentUserService.TenantId.Value;

            _context.Templates.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
