using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Infrastructure.Interfaces;
using FY.WB.CSHero2.Application.Common.Interfaces;

namespace FY.WB.CSHero2.Infrastructure.Services;

/// <summary>
/// Tenant-aware DbContext factory that creates ApplicationDbContext instances
/// with proper tenant isolation and thread safety
/// </summary>
public class TenantDbContextFactory : ITenantDbContextFactory
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TenantDbContextFactory> _logger;

    public TenantDbContextFactory(
        IDbContextFactory<ApplicationDbContext> factory,
        IServiceProvider serviceProvider,
        ILogger<TenantDbContextFactory> logger)
    {
        _factory = factory ?? throw new ArgumentNullException(nameof(factory));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Creates a new ApplicationDbContext instance with current tenant context
    /// </summary>
    public ApplicationDbContext CreateDbContext()
    {
        try
        {
            // Create a new scope to get fresh instances of scoped services
            using var scope = _serviceProvider.CreateScope();
            
            // Get the current user service and tenant info from the new scope
            var currentUserService = scope.ServiceProvider.GetRequiredService<ICurrentUserService>();
            var tenantAccessor = scope.ServiceProvider.GetService<IMultiTenantContextAccessor<AppTenantInfo>>();
            var tenantInfo = tenantAccessor?.MultiTenantContext?.TenantInfo;

            // Create the DbContext with the factory
            var context = _factory.CreateDbContext();
            
            // Manually inject the dependencies that would normally be injected by DI
            // This is necessary because the factory creates the context outside of normal DI scope
            var contextType = context.GetType();
            var currentUserField = contextType.GetField("_currentUserService", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var tenantInfoField = contextType.GetField("_tenantInfo", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            currentUserField?.SetValue(context, currentUserService);
            tenantInfoField?.SetValue(context, tenantInfo);

            _logger.LogDebug("Created new DbContext instance for tenant: {TenantId}", 
                tenantInfo?.Id ?? "Unknown");

            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create DbContext instance");
            throw;
        }
    }

    /// <summary>
    /// Creates a new ApplicationDbContext instance for a specific tenant
    /// </summary>
    public ApplicationDbContext CreateDbContext(string tenantId)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            
            var currentUserService = scope.ServiceProvider.GetRequiredService<ICurrentUserService>();
            
            // Create a mock tenant info for the specified tenant
            var tenantInfo = new AppTenantInfo
            {
                Id = tenantId,
                Identifier = tenantId,
                Name = $"Tenant-{tenantId}"
            };

            var context = _factory.CreateDbContext();
            
            var contextType = context.GetType();
            var currentUserField = contextType.GetField("_currentUserService", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var tenantInfoField = contextType.GetField("_tenantInfo", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            currentUserField?.SetValue(context, currentUserService);
            tenantInfoField?.SetValue(context, tenantInfo);

            _logger.LogDebug("Created new DbContext instance for specific tenant: {TenantId}", tenantId);

            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create DbContext instance for tenant: {TenantId}", tenantId);
            throw;
        }
    }
}

/// <summary>
/// Simple tenant info implementation for factory usage
/// </summary>