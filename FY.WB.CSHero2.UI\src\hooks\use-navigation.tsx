"use client";

import { createContext, useContext, useState } from "react";

interface NavigationContextType {
  isExpanded: boolean;
  toggleExpanded?: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isExpanded, setIsExpanded] = useState(true);
  
  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
  };

  return (
    <NavigationContext.Provider value={{ isExpanded, toggleExpanded }}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
}
