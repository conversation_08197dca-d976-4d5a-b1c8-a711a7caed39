using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Clients.Queries;
using MediatR;
using FluentValidation;
using FluentValidation.AspNetCore;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Infrastructure.Services;
using System.Threading.Tasks;
using System.Threading;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("NextJsCorsPolicy", policy =>
        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
});

// Multi-tenancy
builder.Services.AddMultiTenant<AppTenantInfo>()
    .WithClaimStrategy("tenant_id")
    .WithInMemoryStore(); // Removed .WithRouteStrategy()

// DbContext
builder.Services.AddDbContext<IApplicationDbContext, ApplicationDbContext>(options =>
    options.UseSqlServer(configuration.GetConnectionString("DefaultConnection"), 
        sqlOptions => sqlOptions.CommandTimeout(30))); // Add timeout

// Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole<Guid>>(opts =>
    opts.SignIn.RequireConfirmedAccount = false)
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();

// JWT Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = configuration["Jwt:Issuer"],
        ValidAudience = configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(configuration["Jwt:Key"] ?? string.Empty))
    };
});

// MVC, Swagger, MediatR, Validation
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(GetClientsQuery).Assembly));
builder.Services.AddValidatorsFromAssemblyContaining<GetClientsQuery>();
builder.Services.AddFluentValidationAutoValidation();

// HttpContext and current user
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<ITenantInfo>(sp => sp.GetService<ITenantInfo>() ?? throw new InvalidOperationException("ITenantInfo not available"));

var app = builder.Build();

// Middleware
app.UseCors("NextJsCorsPolicy");
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
else
{
    app.UseHttpsRedirection();
}
app.UseMultiTenant();
app.UseAuthentication();
app.UseAuthorization();

// Database migration and seeding - emergency mode
app.Logger.LogInformation("Initializing database - EMERGENCY MODE (skipping actual database operations)");

// Emergency mode - skip actual database operations entirely
app.Logger.LogInformation("Database operations have been skipped due to connection issues.");
app.Logger.LogWarning("Application will proceed but database functionality will not be available.");

// The application will continue to start up without connecting to the database

// Routes
app.MapControllers();
app.MapGet("/api/ping", () =>
{
    app.Logger.LogInformation("Ping endpoint called");
    return Results.Ok(new { message = "pong", timestamp = DateTime.UtcNow });
});

app.Logger.LogInformation("API server starting up...");
app.Run();

// Non-static class for logging - must come after top-level statements
public class DbMigrationLogger {}
