"use client";

import { StructureOptions } from "../../../types";
import { structureConfig } from "../../../config/structureFields";
import { FormSection } from "../../ui/FormSection";
import { FormField } from "../../ui/FormField";
import { getSectionValues } from "../../../utils/formUtils";

interface StructureSectionProps {
  structure?: StructureOptions;
  onUpdate: (field: string, value: any) => void;
}

export function StructureSection({ structure = {}, onUpdate }: StructureSectionProps) {
  const handleNestedUpdate = (category: string, field: string, value: any): void => {
    // Special handling for heading styles
    if (category.startsWith("h") && category.endsWith("Style")) {
      const headingLevel = category.replace("Style", "");
      const fieldName = field.replace(headingLevel, "").toLowerCase();
      
      const currentHeadingStyles = structure.headingStyles || {};
      const currentLevelStyle = currentHeadingStyles[headingLevel as keyof typeof currentHeadingStyles] || {};
      
      onUpdate("headingStyles", {
        ...currentHeadingStyles,
        [headingLevel]: {
          ...currentLevelStyle,
          [fieldName]: value
        }
      });
      return;
    }
    
    // Default handling for other fields
    const currentCategory = structure[category as keyof StructureOptions] as Record<string, any> || {};
    const updatedCategory = {
      ...currentCategory,
      [field]: value
    };
    
    onUpdate(category, updatedCategory);
  };

  // Helper function to get values for a specific section
  const getValuesForSection = (sectionId: string): Record<string, any> => {
    if (sectionId === "h1Style") {
      // Get H1 style values
      const h1Style = structure.headingStyles?.h1 || {};
      return {
        h1Font: h1Style.font,
        h1Weight: h1Style.weight,
        h1Color: h1Style.color,
        h1SpacingBefore: h1Style.spacingBefore,
        h1SpacingAfter: h1Style.spacingAfter,
        h1Border: h1Style.border
      };
    } else if (sectionId === "h2Style") {
      // Get H2 style values
      const h2Style = structure.headingStyles?.h2 || {};
      return {
        h2Font: h2Style.font,
        h2Weight: h2Style.weight,
        h2Color: h2Style.color
      };
    } else if (sectionId === "h3Style") {
      // Get H3 style values
      const h3Style = structure.headingStyles?.h3 || {};
      return {
        h3Font: h3Style.font,
        h3Weight: h3Style.weight
      };
    }
    
    // For other sections, get values directly
    return getSectionValues(structure, sectionId);
  };

  return (
    <div className="space-y-4">
      {structureConfig.sections.map(section => {
        const sectionValues = getValuesForSection(section.id);
        
        return (
          <FormSection 
            key={section.id}
            title={section.title}
            defaultExpanded={section.defaultExpanded}
          >
            {section.fields.map(field => (
              <FormField
                key={field.name}
                field={field}
                value={sectionValues[field.name]}
                onChange={(value) => handleNestedUpdate(section.id, field.name, value)}
                parentValues={sectionValues}
              />
            ))}
          </FormSection>
        );
      })}
    </div>
  );
}
