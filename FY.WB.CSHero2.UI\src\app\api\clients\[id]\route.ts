import { NextRequest, NextResponse } from 'next/server';

/**
 * Redirect handler for legacy /api/clients/[id] endpoints
 * Forwards requests to the new /api/v1/clients/[id] endpoints for backward compatibility
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Construct the new URL with the v1 path
  const newUrl = `/api/v1/clients/${params.id}`;
  
  // Redirect to the new endpoint
  return NextResponse.redirect(new URL(newUrl, request.url));
}

/**
 * Forward PATCH/PUT requests to the v1 endpoint
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Forward to the v1 endpoint
  const newUrl = `/api/v1/clients/${params.id}`;
  return NextResponse.redirect(new URL(newUrl, request.url));
}

/**
 * Forward DELETE requests to the v1 endpoint
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Forward to the v1 endpoint
  const newUrl = `/api/v1/clients/${params.id}`;
  return NextResponse.redirect(new URL(newUrl, request.url));
}
