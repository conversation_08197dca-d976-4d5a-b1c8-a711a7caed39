using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Reports.Dtos;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Reports.Queries
{
    public class GetReportByIdQueryHandler : IRequestHandler<GetReportByIdQuery, ReportDto?>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetReportByIdQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<ReportDto?> Handle(GetReportByIdQuery request, CancellationToken cancellationToken)
        {
            var query = _context.Reports.AsQueryable();

            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(r => r.TenantId == _currentUserService.TenantId);
            }

            var report = await query
                .Where(r => r.Id == request.Id)
                .Select(r => new ReportDto
                {
                    Id = r.Id,
                    ReportNumber = r.ReportNumber,
                    ClientId = r.ClientId,
                    ClientName = r.ClientName,
                    Name = r.Name,
                    Category = r.Category,
                    SlideCount = r.SlideCount,
                    Status = r.Status,
                    Author = r.Author,
                    CreationTime = r.CreationTime,
                    LastModificationTime = r.LastModificationTime
                })
                .FirstOrDefaultAsync(cancellationToken);

            return report;
        }
    }
}
