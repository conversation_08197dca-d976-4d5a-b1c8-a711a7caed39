using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Information about a tenant for seeding purposes
    /// </summary>
    public class TenantInfo
    {
        public Guid TenantId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Subscription { get; set; } = string.Empty;
    }

    /// <summary>
    /// Service for resolving tenant information from the SQL database
    /// </summary>
    public interface ITenantResolutionService
    {
        /// <summary>
        /// Gets all seeded tenants from the SQL database
        /// </summary>
        Task<List<TenantInfo>> GetSeededTenantsAsync();

        /// <summary>
        /// Gets a TenantId by company name
        /// </summary>
        Task<Guid?> GetTenantIdByCompanyNameAsync(string companyName);

        /// <summary>
        /// Gets a mapping of company names to TenantIds
        /// </summary>
        Task<Dictionary<string, Guid>> GetCompanyToTenantMappingAsync();
    }

    /// <summary>
    /// Implementation of tenant resolution service that reads from SQL database
    /// </summary>
    public class TenantResolutionService : ITenantResolutionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TenantResolutionService> _logger;

        public TenantResolutionService(
            ApplicationDbContext context,
            ILogger<TenantResolutionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Gets all seeded tenants from the SQL database
        /// </summary>
        public async Task<List<TenantInfo>> GetSeededTenantsAsync()
        {
            _logger.LogInformation("Retrieving seeded tenant data from SQL database");

            try
            {
                var tenants = await _context.TenantProfiles
                    .IgnoreQueryFilters() // Bypass multi-tenant filtering for seeding
                    .Where(tp => tp.Status == "active" && tp.TenantId.HasValue) // Only active tenants with valid TenantId
                    .Select(tp => new TenantInfo
                    {
                        TenantId = tp.TenantId!.Value, // Use null-forgiving operator since we filtered for HasValue
                        CompanyName = tp.Company,
                        ContactName = tp.Name,
                        Email = tp.Email,
                        Status = tp.Status,
                        Subscription = tp.Subscription
                    })
                    .ToListAsync();

                _logger.LogInformation("Retrieved {TenantCount} active tenants from SQL database", tenants.Count);

                foreach (var tenant in tenants)
                {
                    _logger.LogDebug("Found tenant: {TenantId} - {CompanyName}", tenant.TenantId, tenant.CompanyName);
                }

                return tenants;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve tenant data from SQL database");
                throw;
            }
        }

        /// <summary>
        /// Gets a TenantId by company name (case-insensitive)
        /// </summary>
        public async Task<Guid?> GetTenantIdByCompanyNameAsync(string companyName)
        {
            if (string.IsNullOrWhiteSpace(companyName))
            {
                _logger.LogWarning("Company name is null or empty");
                return null;
            }

            try
            {
                var normalizedCompanyName = companyName.ToLowerInvariant().Trim();
                _logger.LogDebug("Looking up TenantId for company: {CompanyName}", companyName);

                var tenantProfile = await _context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(tp => tp.Company.ToLower().Trim() == normalizedCompanyName && tp.Status == "active")
                    .FirstOrDefaultAsync();

                if (tenantProfile != null)
                {
                    _logger.LogDebug("Found TenantId {TenantId} for company {CompanyName}", tenantProfile.TenantId, companyName);
                    return tenantProfile.TenantId;
                }
                else
                {
                    _logger.LogWarning("No TenantId found for company: {CompanyName}", companyName);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to lookup TenantId for company: {CompanyName}", companyName);
                return null;
            }
        }

        /// <summary>
        /// Gets a mapping of company names (normalized) to TenantIds
        /// </summary>
        public async Task<Dictionary<string, Guid>> GetCompanyToTenantMappingAsync()
        {
            _logger.LogInformation("Creating company name to TenantId mapping");

            try
            {
                var tenants = await GetSeededTenantsAsync();
                var mapping = tenants.ToDictionary(
                    t => t.CompanyName.ToLowerInvariant().Trim(),
                    t => t.TenantId
                );

                _logger.LogInformation("Created mapping for {CompanyCount} companies", mapping.Count);

                foreach (var kvp in mapping)
                {
                    _logger.LogDebug("Mapping: '{CompanyName}' -> {TenantId}", kvp.Key, kvp.Value);
                }

                return mapping;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create company to tenant mapping");
                throw;
            }
        }
    }
}