import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers'; // To access cookies
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/clients
 * Returns a list of clients with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const limit = searchParams.get('_limit');
    const page = searchParams.get('_page');
    const sortBy = searchParams.get('_sort') || undefined;
    const order = searchParams.get('_order') as 'asc' | 'desc' | undefined;

    // Forward all original searchParams to the backend
    const queryString = searchParams.toString();

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making request to backend: ${API_BASE_URL}/api/Clients${queryString ? `?${queryString}` : ''}`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/Clients${queryString ? `?${queryString}` : ''}`, {
      method: 'GET',
      headers,
      cache: 'no-store', // Disable caching to ensure fresh data
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to fetch clients from backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const clientsData = await backendResponse.json();
    console.log(`Successfully fetched clients from backend: ${JSON.stringify(clientsData).substring(0, 100)}...`);
    return NextResponse.json(clientsData);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch clients', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/clients
 * Creates a new client
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making POST request to backend: ${API_BASE_URL}/api/Clients`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/Clients`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to create client via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const newClient = await backendResponse.json();
    console.log(`Successfully created client: ${JSON.stringify(newClient).substring(0, 100)}...`);
    return NextResponse.json(newClient, { status: backendResponse.status }); // Use backend status, likely 201
  } catch (error) {
    console.error('Error creating client:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to create client: ${errorMessage}` },
      { status: 500 }
    );
  }
}
