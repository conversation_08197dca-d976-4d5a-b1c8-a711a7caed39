using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.TenantProfiles.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.TenantProfiles.Queries
{
    public class GetTenantProfilesQueryHandler : IRequestHandler<GetTenantProfilesQuery, PagedResult<TenantProfileDto>>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetTenantProfilesQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<PagedResult<TenantProfileDto>> Handle(GetTenantProfilesQuery request, CancellationToken cancellationToken)
        {
            var queryParameters = request.Parameters;
            var page = queryParameters.Page ?? 1;
            var limit = queryParameters.Limit ?? 10;

            IQueryable<TenantProfile> query = _context.TenantProfiles.AsNoTracking();
            List<TenantProfileDto> tenantProfileDtos;
            int totalCount;

            if (_currentUserService.IsAdmin)
            {
                query = query.IgnoreQueryFilters(); // Admin sees all

                // Apply filtering for Admin
                if (!string.IsNullOrWhiteSpace(queryParameters.Search))
                {
                    var searchTerm = queryParameters.Search.ToLower().Trim();
                    query = query.Where(tp =>
                        (tp.Name != null && tp.Name.ToLower().Contains(searchTerm)) ||
                        (tp.Company != null && tp.Company.ToLower().Contains(searchTerm)) ||
                        (tp.Email != null && tp.Email.ToLower().Contains(searchTerm))
                    );
                }
                if (!string.IsNullOrWhiteSpace(queryParameters.Status))
                {
                    query = query.Where(tp => tp.Status != null && tp.Status.ToLower() == queryParameters.Status.ToLower());
                }
                if (!string.IsNullOrWhiteSpace(queryParameters.Subscription))
                {
                    query = query.Where(tp => tp.Subscription != null && tp.Subscription.ToLower() == queryParameters.Subscription.ToLower());
                }

                // Apply sorting for Admin
                if (!string.IsNullOrWhiteSpace(queryParameters.SortBy))
                {
                    var sortBy = queryParameters.SortBy.ToLower();
                    var isDescending = queryParameters.SortOrder?.ToLower() == "desc";
                    Expression<Func<TenantProfile, object>> keySelector = sortBy switch
                    {
                        "name" => tp => tp.Name,
                        "company" => tp => tp.Company,
                        "subscription" => tp => tp.Subscription,
                        "createdat" => tp => tp.CreationTime,
                        _ => tp => tp.CreationTime
                    };
                    query = isDescending ? query.OrderByDescending(keySelector) : query.OrderBy(keySelector);
                }
                else
                {
                    query = query.OrderByDescending(tp => tp.CreationTime);
                }

                totalCount = await query.CountAsync(cancellationToken);
                var adminTenantProfiles = await query
                    .Skip((page - 1) * limit)
                    .Take(limit)
                    .ToListAsync(cancellationToken);
                
                tenantProfileDtos = adminTenantProfiles.Select(MapToDto).ToList();
            }
            else // Regular user
            {
                var userTenantId = _currentUserService.TenantId;
                if (userTenantId == null)
                {
                    // Should not happen for an authenticated non-admin user, but handle defensively
                    return new PagedResult<TenantProfileDto>(new List<TenantProfileDto>(), 1, limit, 0);
                }

                // Regular user sees only their own TenantProfile.
                // The TenantProfile.TenantId should match the user's current tenant context.
                var userTenantProfile = await query
                    .FirstOrDefaultAsync(tp => tp.TenantId == userTenantId, cancellationToken); 
                    // The global query filter in ApplicationDbContext already filters by tp.TenantId == userTenantId
                    // So, we are essentially looking for the one profile that matches.
                    // If TenantProfile.Id is the actual link to AppTenantInfo.Id, then it should be:
                    // .FirstOrDefaultAsync(tp => tp.Id == userTenantId, cancellationToken);
                    // Based on TenantProfile being FullAuditedMultiTenantEntity<Guid>, its own TenantId field is used for multi-tenancy.

                if (userTenantProfile != null)
                {
                    tenantProfileDtos = new List<TenantProfileDto> { MapToDto(userTenantProfile) };
                    totalCount = 1;
                }
                else
                {
                    tenantProfileDtos = new List<TenantProfileDto>();
                    totalCount = 0;
                }
                // For a single item, page and limit are less relevant but PagedResult expects them.
                page = 1; 
            }
            
            return new PagedResult<TenantProfileDto>(tenantProfileDtos, page, limit, totalCount);
        }

        private TenantProfileDto MapToDto(TenantProfile tenantProfile)
        {
            PaymentMethodInfoDto? paymentMethodDto = null;
            if (!string.IsNullOrWhiteSpace(tenantProfile.PaymentMethod) && tenantProfile.PaymentMethod != "{}")
            {
                try { paymentMethodDto = JsonSerializer.Deserialize<PaymentMethodInfoDto>(tenantProfile.PaymentMethod); }
                catch (JsonException) { /* Log error or handle as needed */ }
            }

            BillingAddressInfoDto? billingAddressDto = null;
            if (!string.IsNullOrWhiteSpace(tenantProfile.BillingAddress) && tenantProfile.BillingAddress != "{}")
            {
                try { billingAddressDto = JsonSerializer.Deserialize<BillingAddressInfoDto>(tenantProfile.BillingAddress); }
                catch (JsonException) { /* Log error or handle as needed */ }
            }

            return new TenantProfileDto
            {
                Id = tenantProfile.Id,
                TenantId = tenantProfile.TenantId, // This is TenantProfile.TenantId (FK to AppTenantInfo)
                Name = tenantProfile.Name,
                Email = tenantProfile.Email,
                Status = tenantProfile.Status,
                Phone = tenantProfile.Phone,
                Company = tenantProfile.Company,
                Subscription = tenantProfile.Subscription,
                LastLoginTime = tenantProfile.LastLoginTime,
                BillingCycle = tenantProfile.BillingCycle,
                NextBillingDate = tenantProfile.NextBillingDate,
                SubscriptionStatus = tenantProfile.SubscriptionStatus,
                PaymentMethod = paymentMethodDto,
                BillingAddress = billingAddressDto,
                CreatedAt = tenantProfile.CreationTime,
                UpdatedAt = tenantProfile.LastModificationTime
            };
        }
    }
}
