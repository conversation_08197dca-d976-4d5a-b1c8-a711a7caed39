import { NextRequest, NextResponse } from 'next/server';

/**
 * Compatibility layer for non-versioned API routes
 * Redirects to versioned API routes
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const newUrl = new URL(url.toString());
  newUrl.pathname = newUrl.pathname.replace('/api/invoices', '/api/v1/invoices');
  
  return NextResponse.redirect(newUrl);
}

export async function POST(request: NextRequest) {
  const url = new URL(request.url);
  const newUrl = new URL(url.toString());
  newUrl.pathname = newUrl.pathname.replace('/api/invoices', '/api/v1/invoices');
  
  return NextResponse.redirect(newUrl);
}
