"use client";

import { motion } from "framer-motion";
import { Search, ArrowRight } from "lucide-react";

const sections = [
  {
    title: "Getting Started",
    items: [
      { title: "Quick Start Guide", href: "/docs/quick-start" },
      { title: "Installation", href: "/docs/installation" },
      { title: "Basic Concepts", href: "/docs/concepts" }
    ],
    gradient: "gradient-primary-secondary"
  },
  {
    title: "Core Features",
    items: [
      { title: "Report Builder", href: "/docs/report-builder" },
      { title: "Data Import", href: "/docs/data-import" },
      { title: "Templates", href: "/docs/templates" },
      { title: "Export Options", href: "/docs/export" }
    ],
    gradient: "gradient-secondary-tertiary"
  },
  {
    title: "API Reference",
    items: [
      { title: "Authentication", href: "/docs/api/auth" },
      { title: "Endpoints", href: "/docs/api/endpoints" },
      { title: "Rate Limits", href: "/docs/api/rate-limits" },
      { title: "Error Handling", href: "/docs/api/errors" }
    ],
    gradient: "gradient-primary-tertiary"
  },
  {
    title: "Integrations",
    items: [
      { title: "OpenAI GPT", href: "/docs/integrations/openai" },
      { title: "Custom APIs", href: "/docs/integrations/custom-apis" },
      { title: "Export Services", href: "/docs/integrations/export" }
    ],
    gradient: "gradient-primary-secondary"
  }
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function DocsPage() {
  return (
    <div className="gradient-primary-secondary">
      <div className="container mx-auto px-4 py-24 md:px-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4 text-white">Documentation</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Everything you need to know about using the CS-Hero
          </p>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-2xl mx-auto mb-16"
        >
          <div className="relative">
            <input
              type="text"
              placeholder="Search documentation..."
              className="w-full px-6 py-4 rounded-lg bg-white border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[rgb(var(--tertiary))] shadow-sm"
            />
            <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>
        </motion.div>

        {/* Documentation Sections */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto"
        >
          {sections.map((section) => (
            <motion.div
              key={section.title}
              variants={itemVariants}
              className="relative"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-8">
                <h2 className={`text-2xl font-bold mb-6 ${section.gradient} bg-clip-text text-transparent`}>
                  {section.title}
                </h2>
                <ul className="space-y-4">
                  {section.items.map((item) => (
                    <li key={item.title}>
                      <a
                        href={item.href}
                        className="group flex items-center justify-between text-gray-600 hover:text-[rgb(var(--primary))] transition-colors"
                      >
                        <span>{item.title}</span>
                        <ArrowRight className="w-4 h-4 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all" />
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 p-8 rounded-lg bg-white shadow-lg max-w-3xl mx-auto text-center"
        >
          <h2 className="text-2xl font-bold mb-4 text-[rgb(var(--primary))]">Need More Help?</h2>
          <p className="text-gray-600 mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <a
            href="/contact"
            className="inline-flex items-center px-6 py-3 rounded-lg gradient-primary-tertiary text-white font-semibold hover:opacity-90 transition-opacity"
          >
            Contact Support
          </a>
        </motion.div>
      </div>
    </div>
  );
}
