using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Service for resolving seeded data from the database for blob storage seeding
    /// </summary>
    public class SeededDataResolutionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SeededDataResolutionService> _logger;

        public SeededDataResolutionService(
            ApplicationDbContext context,
            ILogger<SeededDataResolutionService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets all seeded tenants from the database
        /// </summary>
        /// <returns>List of seeded tenant information</returns>
        public async Task<List<SeededTenantInfo>> GetSeededTenantsAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving seeded tenants from database");

                var tenants = await _context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(t => !t.IsDeleted)
                    .Select(t => new SeededTenantInfo
                    {
                        TenantId = t.Id,
                        CompanyName = t.Company,
                        ContactName = t.Name
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {TenantCount} seeded tenants", tenants.Count);
                return tenants;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seeded tenants");
                throw;
            }
        }

        /// <summary>
        /// Gets all seeded reports from the database
        /// </summary>
        /// <returns>List of seeded report information</returns>
        public async Task<List<SeededReportInfo>> GetSeededReportsAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving seeded reports from database");

                var reports = await _context.Reports
                    .IgnoreQueryFilters()
                    .Include(r => r.Client)
                    .Where(r => !r.IsDeleted)
                    .Where(r => r.TenantId.HasValue) // Only include reports with valid TenantId
                    .Select(r => new SeededReportInfo
                    {
                        ReportId = r.Id,
                        ReportNumber = r.ReportNumber,
                        ReportName = r.Name,
                        Category = r.Category,
                        TenantId = r.TenantId!.Value, // Safe to use ! since we filtered for HasValue
                        ClientId = r.ClientId,
                        ClientName = r.Client != null ? r.Client.CompanyName : "Unknown Client",
                        Status = r.Status,
                        Author = r.Author,
                        SlideCount = r.SlideCount,
                        CreationTime = r.CreationTime
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {ReportCount} seeded reports", reports.Count);
                return reports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seeded reports");
                throw;
            }
        }

        /// <summary>
        /// Gets seeded reports grouped by tenant
        /// </summary>
        /// <returns>Dictionary of tenant ID to list of reports</returns>
        public async Task<Dictionary<Guid, List<SeededReportInfo>>> GetSeededReportsByTenantAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving seeded reports grouped by tenant");

                var reports = await GetSeededReportsAsync();
                var groupedReports = reports
                    .GroupBy(r => r.TenantId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                _logger.LogInformation("Grouped {ReportCount} reports across {TenantCount} tenants",
                    reports.Count, groupedReports.Count);

                return groupedReports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seeded reports by tenant");
                throw;
            }
        }

        /// <summary>
        /// Gets seeded reports for a specific tenant
        /// </summary>
        /// <param name="tenantId">The tenant ID</param>
        /// <returns>List of reports for the tenant</returns>
        public async Task<List<SeededReportInfo>> GetSeededReportsForTenantAsync(Guid tenantId)
        {
            try
            {
                _logger.LogInformation("Retrieving seeded reports for tenant {TenantId}", tenantId);

                var reports = await _context.Reports
                    .IgnoreQueryFilters()
                    .Include(r => r.Client)
                    .Where(r => !r.IsDeleted && r.TenantId == tenantId)
                    .Select(r => new SeededReportInfo
                    {
                        ReportId = r.Id,
                        ReportNumber = r.ReportNumber,
                        ReportName = r.Name,
                        Category = r.Category,
                        TenantId = r.TenantId!.Value, // Safe since we're filtering by tenantId
                        ClientId = r.ClientId,
                        ClientName = r.Client != null ? r.Client.CompanyName : "Unknown Client",
                        Status = r.Status,
                        Author = r.Author,
                        SlideCount = r.SlideCount,
                        CreationTime = r.CreationTime
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {ReportCount} seeded reports for tenant {TenantId}",
                    reports.Count, tenantId);

                return reports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seeded reports for tenant {TenantId}", tenantId);
                throw;
            }
        }

        /// <summary>
        /// Gets summary statistics about seeded data
        /// </summary>
        /// <returns>Summary of seeded data</returns>
        public async Task<SeededDataSummary> GetSeededDataSummaryAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving seeded data summary");

                var tenantCount = await _context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(t => !t.IsDeleted)
                    .CountAsync();

                var reportCount = await _context.Reports
                    .IgnoreQueryFilters()
                    .Where(r => !r.IsDeleted)
                    .CountAsync();

                var clientCount = await _context.Clients
                    .IgnoreQueryFilters()
                    .Where(c => !c.IsDeleted)
                    .CountAsync();

                var reportsByCategory = await _context.Reports
                    .IgnoreQueryFilters()
                    .Where(r => !r.IsDeleted)
                    .GroupBy(r => r.Category)
                    .Select(g => new { Category = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.Category, x => x.Count);

                var summary = new SeededDataSummary
                {
                    TenantCount = tenantCount,
                    ReportCount = reportCount,
                    ClientCount = clientCount,
                    ReportsByCategory = reportsByCategory
                };

                _logger.LogInformation("Seeded data summary: {TenantCount} tenants, {ReportCount} reports, {ClientCount} clients",
                    tenantCount, reportCount, clientCount);

                return summary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving seeded data summary");
                throw;
            }
        }

        /// <summary>
        /// Validates that required seeded data exists for blob storage seeding
        /// </summary>
        /// <returns>True if data is ready for blob seeding</returns>
        public async Task<bool> ValidateSeededDataForBlobSeedingAsync()
        {
            try
            {
                _logger.LogInformation("Validating seeded data for blob storage seeding");

                var tenants = await GetSeededTenantsAsync();
                var reports = await GetSeededReportsAsync();

                var isValid = tenants.Any() && reports.Any();

                if (isValid)
                {
                    _logger.LogInformation("Seeded data validation passed: {TenantCount} tenants, {ReportCount} reports",
                        tenants.Count, reports.Count);
                }
                else
                {
                    _logger.LogWarning("Seeded data validation failed: {TenantCount} tenants, {ReportCount} reports",
                        tenants.Count, reports.Count);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating seeded data for blob seeding");
                return false;
            }
        }
    }

    /// <summary>
    /// Summary of seeded data in the database
    /// </summary>
    public class SeededDataSummary
    {
        public int TenantCount { get; set; }
        public int ReportCount { get; set; }
        public int ClientCount { get; set; }
        public Dictionary<string, int> ReportsByCategory { get; set; } = new();
    }
}
