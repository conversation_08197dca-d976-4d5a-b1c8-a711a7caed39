'use client';

import { motion } from "framer-motion";

export default function UsersPage() {
  return (
    <div className="container mx-auto py-12 max-w-inner px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">User Management</h1>
            <p className="text-xl text-gray-600">
              Manage system users and their permissions
            </p>
          </div>
        </div>
      </motion.div>
      
      <div className="bg-card rounded-lg border p-6">
        <p className="text-gray-600">Manage system users and their permissions.</p>
      </div>
    </div>
  );
}
