'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useToast } from '@/components/providers/toast-provider';
import { Tenant } from '@/types/tenant';

interface TenantFormProps {
  onSubmit: (data: Partial<Tenant>) => Promise<void>;
  onCancel: () => void;
}

export function TenantForm({ onSubmit, onCancel }: TenantFormProps) {
  interface FormData {
    name: string;
    email: string;
    phone: string;
    subscription: 'basic' | 'professional' | 'enterprise';
    status: 'active' | 'suspended';
  }

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    subscription: 'basic',
    status: 'active',
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      await onSubmit({
        name: formData.name,
        email: formData.email,
        phone: formData.phone || undefined,
        subscription: formData.subscription || undefined,
        status: formData.status
      });
      toast({
        title: 'Success',
        description: 'Tenant created successfully',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to create tenant',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 py-4">
      <div className="space-y-2.5">
        <label htmlFor="name" className="text-sm font-medium text-muted-foreground">
          Full Name
        </label>
        <Input
          id="name"
          placeholder="Enter tenant's full name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="email" className="text-sm font-medium text-muted-foreground">
          Email
        </label>
        <Input
          id="email"
          type="email"
          placeholder="Enter email address"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          required
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="phone" className="text-sm font-medium text-muted-foreground">
          Phone
        </label>
        <Input
          id="phone"
          type="tel"
          placeholder="Enter phone number (optional)"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
        />
      </div>

      <div className="space-y-2.5">
        <label htmlFor="subscription" className="text-sm font-medium text-muted-foreground">
          Subscription
        </label>
        <Select
          value={formData.subscription}
          onValueChange={(value: 'basic' | 'professional' | 'enterprise') => 
            setFormData({ ...formData, subscription: value })
          }
        >
          <SelectTrigger id="subscription">
            <SelectValue placeholder="Select subscription type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="basic">Basic</SelectItem>
            <SelectItem value="professional">Professional</SelectItem>
            <SelectItem value="enterprise">Enterprise</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-3 pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading} className="text-white">
          {loading ? 'Creating...' : 'Create Tenant'}
        </Button>
      </div>
    </form>
  );
}
