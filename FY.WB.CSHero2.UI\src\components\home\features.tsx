"use client";

import { motion } from "framer-motion";
import {
  LayoutTemplate,
  Sparkles,
  FileOutput,
  Users,
} from "lucide-react";

const features = [
  {
    title: "Visual Report Builder",
    description: "Drag and drop elements to create stunning reports. Customize layouts, charts, and content with ease.",
    icon: LayoutTemplate,
    gradient: "gradient-primary-secondary",
  },
  {
    title: "AI-Enhanced Insights",
    description: "Leverage AI to automatically generate insights and identify trends in your customer service data.",
    icon: Sparkles,
    gradient: "gradient-secondary-tertiary",
  },
  {
    title: "Multi-format Export",
    description: "Export your reports in multiple formats including PDF, PowerPoint, and more. Share with stakeholders easily.",
    icon: FileOutput,
    gradient: "gradient-primary-tertiary",
  },
  {
    title: "Team Collaboration",
    description: "Work together with your team. Share templates, provide feedback, and maintain consistency.",
    icon: Users,
    gradient: "gradient-primary-secondary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export function Features() {
  return (
    <section className="py-24 bg-gray-50 rounded-round md:px-16">
      <div className="container mx-auto px-2 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">
            Powerful Features for Better Reports
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Everything you need to transform your customer service data into actionable insights
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={cardVariants}
              whileHover={{ y: -5 }}
              className="relative group"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-6 transition-shadow duration-300 group-hover:shadow-xl">
                <div className={`w-12 h-12 rounded-lg ${feature.gradient} flex items-center justify-center mb-4`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
