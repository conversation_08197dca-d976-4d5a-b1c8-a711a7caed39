using System;
using System.Collections.Generic;
using System.Linq;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Result of React component validation
    /// </summary>
    public class ComponentValidationResult
    {
        /// <summary>
        /// Whether the component is valid
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Overall quality score (0-100)
        /// </summary>
        public int QualityScore { get; set; } = 100;

        /// <summary>
        /// Performance score (0-100)
        /// </summary>
        public int PerformanceScore { get; set; } = 100;

        /// <summary>
        /// Accessibility score (0-100)
        /// </summary>
        public int AccessibilityScore { get; set; } = 100;

        /// <summary>
        /// Maintainability score (0-100)
        /// </summary>
        public int MaintainabilityScore { get; set; } = 100;

        /// <summary>
        /// Best practices score (0-100)
        /// </summary>
        public int BestPracticesScore { get; set; } = 100;

        /// <summary>
        /// Security score (0-100)
        /// </summary>
        public int SecurityScore { get; set; } = 100;

        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<ValidationWarning> Warnings { get; set; } = new List<ValidationWarning>();

        /// <summary>
        /// List of improvement suggestions
        /// </summary>
        public List<ValidationSuggestion> Suggestions { get; set; } = new List<ValidationSuggestion>();

        /// <summary>
        /// Detected dependencies
        /// </summary>
        public List<string> DetectedDependencies { get; set; } = new List<string>();

        /// <summary>
        /// Estimated bundle size impact
        /// </summary>
        public string EstimatedBundleSize { get; set; } = "Unknown";

        /// <summary>
        /// Complexity metrics
        /// </summary>
        public ComponentComplexityMetrics Complexity { get; set; } = new ComponentComplexityMetrics();

        /// <summary>
        /// Validation timestamp
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Framework-specific validation results
        /// </summary>
        public Dictionary<string, object> FrameworkSpecific { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Adds an error to the validation result
        /// </summary>
        public void AddError(string code, string message, int? line = null, int? column = null)
        {
            Errors.Add(new ValidationError
            {
                Code = code,
                Message = message,
                Line = line,
                Column = column,
                Severity = "Error"
            });
            IsValid = false;
        }

        /// <summary>
        /// Adds a warning to the validation result
        /// </summary>
        public void AddWarning(string code, string message, string suggestion = "")
        {
            Warnings.Add(new ValidationWarning
            {
                Code = code,
                Message = message,
                Suggestion = suggestion
            });
        }

        /// <summary>
        /// Adds a suggestion to the validation result
        /// </summary>
        public void AddSuggestion(string type, string message, int priority = 1)
        {
            Suggestions.Add(new ValidationSuggestion
            {
                Type = type,
                Message = message,
                Priority = priority
            });
        }

        /// <summary>
        /// Gets a summary of the validation result
        /// </summary>
        public string GetSummary()
        {
            var status = IsValid ? "Valid" : "Invalid";
            var errorCount = Errors.Count;
            var warningCount = Warnings.Count;
            var suggestionCount = Suggestions.Count;

            return $"{status} - Quality: {QualityScore}%, Errors: {errorCount}, Warnings: {warningCount}, Suggestions: {suggestionCount}";
        }

        /// <summary>
        /// Checks if the component has any critical issues
        /// </summary>
        public bool HasCriticalIssues()
        {
            return !IsValid || 
                   Errors.Any(e => e.Severity == "Critical") || 
                   QualityScore < 50;
        }
    }

    /// <summary>
    /// Validation error details
    /// </summary>
    public class ValidationError
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Severity { get; set; } = "Error";
        public int? Line { get; set; }
        public int? Column { get; set; }
        public string Rule { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validation warning details
    /// </summary>
    public class ValidationWarning
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Suggestion { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int? Line { get; set; }
        public int? Column { get; set; }
    }

    /// <summary>
    /// Validation suggestion details
    /// </summary>
    public class ValidationSuggestion
    {
        public string Type { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int Priority { get; set; } = 1;
        public string Category { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public string Effort { get; set; } = string.Empty;
    }

    /// <summary>
    /// Component complexity metrics
    /// </summary>
    public class ComponentComplexityMetrics
    {
        public int CyclomaticComplexity { get; set; } = 1;
        public int LinesOfCode { get; set; } = 0;
        public int NumberOfProps { get; set; } = 0;
        public int NumberOfHooks { get; set; } = 0;
        public int NumberOfEffects { get; set; } = 0;
        public int NestingDepth { get; set; } = 0;
        public int NumberOfDependencies { get; set; } = 0;
        public bool HasStateManagement { get; set; } = false;
        public bool HasSideEffects { get; set; } = false;
        public bool HasAsyncOperations { get; set; } = false;
    }
}
