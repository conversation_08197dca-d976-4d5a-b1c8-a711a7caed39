using FluentValidation;
using FY.WB.CSHero2.Application.Clients.Dtos;

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class CreateClientCommandValidator : AbstractValidator<CreateClientCommand>
    {
        public CreateClientCommandValidator()
        {
            RuleFor(v => v.ClientDto.Name)
                .NotEmpty().WithMessage("Name is required.")
                .MaximumLength(100).WithMessage("Name must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Email)
                .NotEmpty().WithMessage("Email is required.")
                .EmailAddress().WithMessage("A valid email is required.")
                .MaximumLength(100).WithMessage("Email must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Status)
                .NotEmpty().WithMessage("Status is required.")
                .MaximumLength(50).WithMessage("Status must not exceed 50 characters.");

            RuleFor(v => v.ClientDto.CompanyName)
                .NotEmpty().WithMessage("Company name is required.")
                .MaximumLength(100).WithMessage("Company name must not exceed 100 characters.");

            RuleFor(v => v.ClientDto.Phone)
                .MaximumLength(20).WithMessage("Phone must not exceed 20 characters.");

            RuleFor(v => v.ClientDto.Address)
                .MaximumLength(200).WithMessage("Address must not exceed 200 characters.");
            
            RuleFor(v => v.ClientDto.CompanySize)
                .MaximumLength(50).WithMessage("Company size must not exceed 50 characters.");

            RuleFor(v => v.ClientDto.Industry)
                .MaximumLength(100).WithMessage("Industry must not exceed 100 characters.");
        }
    }
}
