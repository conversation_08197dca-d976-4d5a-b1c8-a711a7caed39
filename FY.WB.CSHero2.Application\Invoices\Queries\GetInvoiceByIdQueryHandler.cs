using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Invoices.Dtos;
using FY.WB.CSHero2.Domain.Entities; // Required for Invoice entity
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Invoices.Queries
{
    public class GetInvoiceByIdQueryHandler : IRequestHandler<GetInvoiceByIdQuery, InvoiceDto?>
    {
        private readonly IApplicationDbContext _context;
        // IMapper removed, will map manually

        public GetInvoiceByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<InvoiceDto?> Handle(GetInvoiceByIdQuery request, CancellationToken cancellationToken)
        {
            var invoice = await _context.Invoices
                .AsNoTracking()
                // .Include(i => i.TenantProfile) // Include if TenantProfile details are needed in DTO
                .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);

            if (invoice == null)
            {
                return null; // Or throw a NotFoundException
            }

            // Manual mapping
            return new InvoiceDto
            {
                Id = invoice.Id,
                OrderNumber = invoice.OrderNumber,
                Type = invoice.Type,
                Plans = invoice.Plans,
                Amount = invoice.Amount,
                Status = invoice.Status,
                Date = invoice.Date,
                CreatedAt = invoice.CreationTime,
                UpdatedAt = invoice.LastModificationTime,
                TenantId = invoice.TenantId
                // TenantName = invoice.TenantProfile?.Name // If TenantProfile was included
            };
        }
    }
}
