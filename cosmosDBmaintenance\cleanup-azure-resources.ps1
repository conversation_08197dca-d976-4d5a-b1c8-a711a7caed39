# Cleanup Azure resources for Report Rendering Engine
# WARNING: This will delete resources and data!

param(
    [string]$ResourceGroup = "rg_CSHero",
    [string]$StorageAccount = "csherostorage",
    [switch]$Force,
    [switch]$StorageOnly
)

Write-Host "=== Azure Resources Cleanup ===" -ForegroundColor Red
Write-Host "⚠️  WARNING: This will delete Azure resources and data!" -ForegroundColor Yellow
Write-Host ""

if (-not $Force) {
    $confirm = Read-Host "Are you sure you want to proceed? Type 'DELETE' to confirm"
    if ($confirm -ne "DELETE") {
        Write-Host "Cleanup cancelled." -ForegroundColor Green
        exit 0
    }
}

try {
    if ($StorageOnly) {
        # Delete only storage account
        Write-Host "🗑️  Deleting Storage Account: $StorageAccount" -ForegroundColor Yellow
        az storage account delete --name $StorageAccount --resource-group $ResourceGroup --yes
        Write-Host "✅ Storage Account deleted" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Full cleanup not implemented for safety" -ForegroundColor Yellow
        Write-Host "To delete Cosmos DB, use Azure Portal or manual az commands" -ForegroundColor White
        Write-Host "This prevents accidental deletion of the main Cosmos DB account" -ForegroundColor White
    }

} catch {
    Write-Host "❌ Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
