using System.Xml.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models
{
    /// <summary>
    /// Represents a field in the render request
    /// </summary>
    public class RenderField
    {
        /// <summary>
        /// Unique identifier for the field
        /// </summary>
        [XmlAttribute("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the field
        /// </summary>
        [XmlAttribute("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The data type of the field
        /// </summary>
        [XmlAttribute("type")]
        public string Type { get; set; } = "text";

        /// <summary>
        /// Description of the field
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// The value of the field
        /// </summary>
        public string Value { get; set; } = string.Empty;
    }
}
