using System.Collections.Generic;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities
{
    /// <summary>
    /// Represents a section in the document template metadata
    /// </summary>
    public class ReportSection
    {
        /// <summary>
        /// Unique identifier for the section
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the section
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the section
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Fields contained within this section
        /// </summary>
        public List<ReportField> Fields { get; set; } = new List<ReportField>();
    }
}
