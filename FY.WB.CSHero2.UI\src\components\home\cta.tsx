"use client";

import { motion } from "framer-motion";
import { Spark<PERSON> } from "lucide-react";

export function CTA() {
  return (
    <section className="py-24 relative overflow-hidden md:px-16">
      {/* Background gradient */}
      <div className="absolute inset-0 gradient-primary-tertiary opacity-90" />

      {/* Content */}
      <div className="relative container mx-auto px-2 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center text-white"
        >
          <Sparkles className="w-12 h-12 mx-auto mb-6 text-white" />
          <h2 className="text-4xl font-bold mb-6">
            Ready to Transform Your Customer Service Reporting?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of teams who have already improved their reporting workflow
          </p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <a
                href="/client/reports/create"
                className="inline-flex items-center px-8 py-4 rounded-lg bg-white text-[rgb(var(--primary))] font-semibold hover:bg-white/90 transition-colors"
              >
                Start Creating Reports
                <svg
                  className="ml-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </a>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-4 rounded-lg bg-white/10 text-white font-semibold hover:bg-white/20 transition-colors"
              >
                Contact Sales
              </a>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {[
            { number: "10K+", label: "Reports Generated" },
            { number: "98%", label: "Customer Satisfaction" },
            { number: "24/7", label: "Expert Support" },
          ].map((stat) => (
            <div
              key={stat.label}
              className="text-center text-white"
            >
              <div className="text-4xl font-bold mb-2">{stat.number}</div>
              <div className="text-white/80">{stat.label}</div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
