using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore; // Required for FirstOrDefaultAsync and AnyAsync

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class CreateReportCommandHandler : IRequestHandler<CreateReportCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateReportCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateReportCommand request, CancellationToken cancellationToken)
        {
            // Validate ClientId
            if (!await _context.Clients.AnyAsync(c => c.Id == request.Dto.ClientId && c.TenantId == _currentUserService.TenantId, cancellationToken))
            {
                // Or throw a more specific validation exception if preferred
                throw new InvalidOperationException($"Client with ID {request.Dto.ClientId} not found for the current tenant.");
            }

            var entity = new Report
            {
                ReportNumber = request.Dto.ReportNumber, // Consider auto-generation logic if needed
                ClientId = request.Dto.ClientId,
                ClientName = request.Dto.ClientName, // Consider fetching from Client entity if not denormalized
                Name = request.Dto.Name,
                Category = request.Dto.Category,
                SlideCount = request.Dto.SlideCount,
                Status = request.Dto.Status,
                Author = request.Dto.Author,
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };

            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create a Report.");
            }
            entity.TenantId = _currentUserService.TenantId.Value;

            _context.Reports.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
