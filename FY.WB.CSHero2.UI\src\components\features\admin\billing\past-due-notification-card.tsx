'use client';

import { useState, useEffect } from 'react';
import { AlertTriangle, ExternalLink } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Invoice } from '@/types/invoice';
import { getInvoices } from '@/lib/api-invoices';
import { getTenantById } from '@/lib/api';

export function PastDueNotificationCard() {
  const [pastDueInvoices, setPastDueInvoices] = useState<(Invoice & { tenantName?: string })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPastDueInvoices = async () => {
      try {
        setLoading(true);
        
        // Get all invoices with status 'overdue'
        const { data: allInvoices } = await getInvoices({ status: 'overdue' });
        
        // Create a map to store tenant data to avoid duplicate API calls
        const tenantMap = new Map<string, string>();
        
        // Fetch tenant data for each invoice
        const invoicesWithTenantNames = await Promise.all(
          allInvoices.map(async (invoice: Invoice) => {
            try {
              // Check if we already have this tenant's data
              if (!tenantMap.has(invoice.tenantId)) {
                const tenant = await getTenantById(invoice.tenantId);
                tenantMap.set(invoice.tenantId, tenant.name);
              }
              
              const tenantName = tenantMap.get(invoice.tenantId);
              return {
                ...invoice,
                tenantName: tenantName || 'Unknown Tenant'
              };
            } catch (err) {
              console.error(`Error fetching tenant ${invoice.tenantId}:`, err);
              return {
                ...invoice,
                tenantName: 'Unknown Tenant'
              };
            }
          })
        );
        
        setPastDueInvoices(invoicesWithTenantNames);
        setError(null);
      } catch (err) {
        console.error('Error fetching past due invoices:', err);
        setError('Failed to fetch past due invoices');
      } finally {
        setLoading(false);
      }
    };

    fetchPastDueInvoices();
  }, []);

  if (loading) {
    return (
      <Card className="p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
        <div className="flex items-center justify-center h-24">
          <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
        <div className="p-4 text-red-600">
          {error}
        </div>
      </Card>
    );
  }

  if (pastDueInvoices.length === 0) {
    return (
      <Card className="p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
        <div className="flex items-center gap-3 text-emerald-600">
          <div className="p-2 bg-emerald-50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-check">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </div>
          <div>
            <h3 className="font-medium">All Invoices Current</h3>
            <p className="text-sm text-emerald-600/80">No past due invoices found</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 bg-gradient-to-b from-amber-50/50 to-amber-50/30 border-amber-200 shadow-sm hover:shadow-md transition-all duration-200 mb-8">
      <div className="flex items-start gap-3">
        <div className="p-2 bg-amber-100 rounded-full mt-1">
          <AlertTriangle className="h-5 w-5 text-amber-600" />
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-amber-800">Past Due Invoices</h3>
          <p className="text-sm text-amber-700 mb-4">
            {pastDueInvoices.length} {pastDueInvoices.length === 1 ? 'invoice is' : 'invoices are'} past due
          </p>
          
          <div className="space-y-3">
            {pastDueInvoices.slice(0, 3).map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between bg-white p-3 rounded-md border border-amber-200">
                <div>
                  <div className="font-medium text-sm">{invoice.tenantName}</div>
                  <div className="text-xs text-muted-foreground">
                    {invoice.orderNumber} • {invoice.date} • {invoice.amount}
                  </div>
                </div>
                <Link 
                  href={`/admin/tenants/${invoice.tenantId}`}
                  className="text-xs font-medium text-primary hover:text-primary/80 hover:underline transition-colors flex items-center gap-1"
                >
                  View <ExternalLink className="h-3 w-3" />
                </Link>
              </div>
            ))}
            
            {pastDueInvoices.length > 3 && (
              <div className="text-center mt-2">
                <Button 
                  variant="link" 
                  className="text-amber-700 hover:text-amber-800"
                  onClick={() => window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })}
                >
                  View all {pastDueInvoices.length} past due invoices
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
