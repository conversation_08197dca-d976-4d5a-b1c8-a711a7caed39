<DesignBrief>
    <ProjectInformation>
        <ClientName>InnoCloud</ClientName>
        <ProjectTitle>Executive KPI Dashboard Report</ProjectTitle>
        <ProjectDescription>A comprehensive design brief for a sleek, professional dashboard report focusing on executive-level key performance indicators with modern data visualization and actionable insights.</ProjectDescription>
        <DeliveryDate>2025-05-15</DeliveryDate>
    </ProjectInformation>
    
    <BrandIdentity>
        <ColorPalette>
            <PrimaryColors>
                <Color name="Deep Teal" hex="#1D3B4A" rgb="29,59,74" />
                <Color name="Slate Gray" hex="#8599A5" rgb="133,153,165" />
                <Color name="Clean White" hex="#FFFFFF" rgb="255,255,255" />
            </PrimaryColors>
            <SecondaryColors>
                <Color name="Accent Blue" hex="#3E7BFA" rgb="62,123,250" />
                <Color name="Success Green" hex="#2AC769" rgb="42,199,105" />
                <Color name="Alert Orange" hex="#FF7E42" rgb="255,126,66" />
                <Color name="Warning Yellow" hex="#FFCB47" rgb="255,203,71" />
                <Color name="Light Gray" hex="#F0F4F7" rgb="240,244,247" />
                <Color name="Dark Gray" hex="#2A3B47" rgb="42,59,71" />
            </SecondaryColors>
        </ColorPalette>
        
        <Typography>
            <PrimaryFont name="Poppins">
                <Weights>400,500,600,700</Weights>
                <Usage>Main headings, key metrics, and section titles</Usage>
            </PrimaryFont>
            <SecondaryFont name="Inter">
                <Weights>400,500,600</Weights>
                <Usage>Body text, data labels, and descriptive content</Usage>
            </SecondaryFont>
            <FontSizes>
                <Size type="Heading1">32px</Size>
                <Size type="Heading2">24px</Size>
                <Size type="Heading3">20px</Size>
                <Size type="BodyText">16px</Size>
                <Size type="DataLabels">14px</Size>
                <Size type="Caption">12px</Size>
                <Size type="FinePrint">10px</Size>
            </FontSizes>
            <LineHeight>
                <Compact>1.2</Compact>
                <Standard>1.5</Standard>
                <Expanded>1.8</Expanded>
            </LineHeight>
        </Typography>
    </BrandIdentity>
    
    <DesignElements>
        <Spacing>
            <BaseUnit>8px</BaseUnit>
            <Scale>Micro (0.5x), Small (1x), Medium (2x), Large (3x), XLarge (4x), XXLarge (6x)</Scale>
        </Spacing>
        <GridSystem>
            <Columns>12</Columns>
            <Gutter>32px</Gutter>
            <Margins>40px</Margins>
            <Breakpoints>
                <Mobile>375px - 767px</Mobile>
                <Tablet>768px - 1199px</Tablet>
                <Desktop>1200px - 1599px</Desktop>
                <LargeDesktop>1600px+</LargeDesktop>
            </Breakpoints>
        </GridSystem>
        
        <Components>
            <Charts>
                <Style>Modern, minimalist with subtle gradients and clear data points</Style>
                <Labels font="Inter" weight="Regular" size="14px" />
                <Types>Area charts, bar charts, line graphs, heat maps, gauge charts, sparklines</Types>
            </Charts>
            <Tables>
                <Header background="#1D3B4A" textColor="#FFFFFF" fontWeight="SemiBold" />
                <Row background="#FFFFFF" textColor="#2A3B47" fontWeight="Regular" />
                <AlternateRow background="#F7FAFC" textColor="#2A3B47" fontWeight="Regular" />
                <Border color="#E6EBF0" width="1px" />
            </Tables>
            <Cards>
                <Style background="#FFFFFF" borderRadius="12px" padding="24px" />
                <Shadow>0px 4px 12px rgba(0,0,0,0.08)</Shadow>
            </Cards>
        </Components>
    </DesignElements>
    
    <DocumentStructure>
        <Sections>
            <Section name="Cover/Title Page" pages="1" />
            <Section name="Executive Overview" pages="1" />
            <Section name="Performance Highlights" pages="1-2" />
            <Section name="Financial Dashboard" pages="2" />
        </Sections>
    </DocumentStructure>
    
    <Guidelines>
        <ContentGuidelines>
            <Tone>Analytical, strategic, insight-focused</Tone>
            <Language>Precise, executive-level with industry-specific terminology</Language>
            <Emphasis>Performance trends, strategic implications, decision-support data</Emphasis>
        </ContentGuidelines>
        <DataVisualizationGuidelines>
            <InsightDriven>Each visualization should tell a clear story at a glance</InsightDriven>
        </DataVisualizationGuidelines>
    </Guidelines>
    
    <Deliverables>
        <File type="High-Resolution Print PDF" format="300dpi, CMYK" />
        <File type="Interactive Digital Dashboard" format="PDF with navigation links" />
    </Deliverables>
</DesignBrief>
