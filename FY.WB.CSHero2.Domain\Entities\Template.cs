using System;
using System.Collections.Generic;
using System.Text.Json;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents a reusable report template that can be cloned by users to create report instances
    /// </summary>
    public class Template : FullAuditedMultiTenantEntity<Guid>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;

        /// <summary>
        /// Whether this template is available to all users (public) or only to the creator (private)
        /// </summary>
        public bool IsPublic { get; set; }

        /// <summary>
        /// JSON serialized default styling and theme information
        /// </summary>
        public string DefaultStyleJson { get; set; } = string.Empty;

        /// <summary>
        /// Reference to CosmosDB style document for default template styling
        /// </summary>
        public string? StyleDocumentId { get; set; }

        /// <summary>
        /// Number of times this template has been cloned/used
        /// </summary>
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// Template version for tracking changes
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// Whether this template is currently active and available for use
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Estimated time to complete a report using this template (in minutes)
        /// </summary>
        public int EstimatedCompletionTimeMinutes { get; set; } = 30;

        // Store tags as a JSON array string
        private string _tags = "[]";
        public string Tags
        {
            get => _tags;
            set => _tags = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        // Store sections as a JSON array string
        private string _sections = "[]";
        public string Sections
        {
            get => _sections;
            set => _sections = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        // Store fields as a JSON array string
        private string _fields = "[]";
        public string Fields
        {
            get => _fields;
            set => _fields = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        public Template() : base() { }

        public Template(
            Guid id,
            string name,
            string description,
            string category,
            string thumbnailUrl,
            IEnumerable<string>? tags = null,
            IEnumerable<TemplateSection>? sections = null,
            IEnumerable<TemplateField>? fields = null)
            : base(id)
        {
            Name = name;
            Description = description;
            Category = category;
            ThumbnailUrl = thumbnailUrl;

            if (tags != null)
                SetTags(tags);

            if (sections != null)
                SetSections(sections);

            if (fields != null)
                SetFields(fields);
        }

        public void UpdateDetails(
            string name,
            string description,
            string category,
            string thumbnailUrl)
        {
            Name = name;
            Description = description;
            Category = category;
            ThumbnailUrl = thumbnailUrl;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetTags(IEnumerable<string> tags)
        {
            Tags = JsonSerializer.Serialize(tags);
        }

        public IEnumerable<string> GetTags()
        {
            return JsonSerializer.Deserialize<IEnumerable<string>>(Tags) ?? Array.Empty<string>();
        }

        public void SetSections(IEnumerable<TemplateSection> sections)
        {
            Sections = JsonSerializer.Serialize(sections);
        }

        public IEnumerable<TemplateSection> GetSections()
        {
            return JsonSerializer.Deserialize<IEnumerable<TemplateSection>>(Sections) ?? Array.Empty<TemplateSection>();
        }

        public void SetFields(IEnumerable<TemplateField> fields)
        {
            Fields = JsonSerializer.Serialize(fields);
        }

        public IEnumerable<TemplateField> GetFields()
        {
            return JsonSerializer.Deserialize<IEnumerable<TemplateField>>(Fields) ?? Array.Empty<TemplateField>();
        }

        /// <summary>
        /// Navigation property to reports created from this template
        /// </summary>
        public virtual ICollection<Report> Reports { get; set; } = new List<Report>();

        /// <summary>
        /// Increments the usage count when template is cloned
        /// </summary>
        public void IncrementUsage()
        {
            UsageCount++;
        }

        /// <summary>
        /// Checks if the template can be edited by the current user
        /// </summary>
        public bool CanBeEditedBy(Guid userId, bool isAdmin = false)
        {
            return isAdmin || CreatorId == userId;
        }

        /// <summary>
        /// Checks if the template can be viewed by the current user
        /// </summary>
        public bool CanBeViewedBy(Guid? tenantId, bool isAdmin = false)
        {
            return IsPublic || isAdmin || TenantId == tenantId;
        }
    }

    public class TemplateSection
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }

    public class TemplateField
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public JsonDocument? Config { get; set; }
    }
}
