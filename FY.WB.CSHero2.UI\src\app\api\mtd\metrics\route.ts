import { NextResponse } from 'next/server';
import { getMTDMetrics } from '@/lib/data/metrics';

/**
 * GET /api/mtd/metrics
 * Returns month-to-date metrics data
 */
export async function GET() {
  try {
    const metrics = await getMTDMetrics();
    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching month-to-date metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}
