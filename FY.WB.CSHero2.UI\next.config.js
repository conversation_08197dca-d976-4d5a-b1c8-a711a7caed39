/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        // Redirect v1 paths to our internal API routes
        source: '/v1/:path*',
        destination: '/api/v1/:path*',
      },
      // Keep the storage path for static files
      process.env.NODE_ENV === 'development' ? {
        source: '/storage/:path*',
        destination: 'http://localhost:3010/storage/:path*',
      } : {
        source: '/storage/:path*',
        destination: '/api/storage/:path*',
      },
    ].filter(Boolean);
  },
  images: {
    domains: ['localhost'],
    unoptimized: process.env.NODE_ENV === 'development',
    dangerouslyAllowSVG: true,
  },
};

module.exports = nextConfig;
