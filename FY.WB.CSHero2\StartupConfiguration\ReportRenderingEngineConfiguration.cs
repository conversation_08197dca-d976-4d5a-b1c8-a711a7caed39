using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;

namespace FY.WB.CSHero2.StartupConfiguration
{
    /// <summary>
    /// Configuration for the Report Rendering Engine in the main API project
    /// </summary>
    public static class ReportRenderingEngineConfiguration
    {
        /// <summary>
        /// Adds Report Rendering Engine services to the dependency injection container
        /// </summary>
        public static IServiceCollection AddReportRenderingEngineConfiguration(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Add the Report Rendering Engine services
            services.AddReportRenderingEngine(configuration);

            return services;
        }
    }
}
