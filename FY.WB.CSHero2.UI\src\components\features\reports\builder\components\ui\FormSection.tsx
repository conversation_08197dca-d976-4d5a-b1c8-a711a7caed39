"use client";

import { useState } from "react";

interface FormSectionProps {
  title: string;
  defaultExpanded?: boolean;
  children: React.ReactNode;
  className?: string;
}

/**
 * A collapsible form section component
 */
export function FormSection({ 
  title, 
  defaultExpanded = false, 
  children,
  className = ""
}: FormSectionProps) {
  const [expanded, setExpanded] = useState(defaultExpanded);
  
  return (
    <div className={`border rounded-lg overflow-hidden ${!expanded ? "hover:border-primary" : ""} ${className}`}>
      <button
        className={`w-full p-4 text-left font-medium flex justify-between items-center ${expanded ? "bg-tertiary" : "bg-gray-50"}`}
        onClick={() => setExpanded(!expanded)}
        type="button"
      >
        <span>{title}</span>
        <span>{expanded ? "−" : "+"}</span>
      </button>
      
      {expanded && (
        <div className="p-4 space-y-4">
          {children}
        </div>
      )}
    </div>
  );
}
