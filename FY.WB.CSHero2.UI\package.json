{"name": "prompt-library-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.66.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.7", "html2pdf.js": "^0.10.3", "lucide-react": "^0.475.0", "next": "14.2.29", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.17", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.2.29", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}}