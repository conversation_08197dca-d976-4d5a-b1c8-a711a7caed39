"use client";

import { motion } from "framer-motion";
import { Calendar, Clock, Tag, ArrowLeft, Share2, Bookmark } from "lucide-react";
import Link from "next/link";

export default function BlogPostPage() {
  return (
    <div className="gradient-primary-secondary min-h-screen">
      <div className="container mx-auto px-4 py-24">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <Link
            href="/blog"
            className="inline-flex items-center text-white hover:text-gray-200 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Blog
          </Link>
        </motion.div>

        {/* Article Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto text-center mb-12"
        >
          <div className="flex flex-wrap justify-center gap-2 mb-6">
            {["AI", "Customer Service", "Reporting"].map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white text-[rgb(var(--primary))]"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
          <h1 className="text-4xl font-bold mb-6 text-white">
            Maximizing Customer Service Efficiency with AI-Powered Reports
          </h1>
          <div className="flex items-center justify-center text-gray-300 text-sm">
            <Calendar className="w-4 h-4 mr-1" />
            <span className="mr-4">Feb 18, 2024</span>
            <Clock className="w-4 h-4 mr-1" />
            <span>5 min read</span>
          </div>
        </motion.div>

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-12"
        >
          {/* Article Actions */}
          <div className="flex justify-end gap-4 mb-8">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Share2 className="w-5 h-5 text-gray-600" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Bookmark className="w-5 h-5 text-gray-600" />
            </motion.button>
          </div>

          {/* Article Body */}
          <div className="prose prose-lg max-w-none">
            <p>
              In today's fast-paced business environment, customer service teams are handling more interactions than ever before. The challenge isn't just managing these interactions—it's understanding them, learning from them, and improving based on them. This is where AI-powered reporting comes into play, transforming raw data into actionable insights.
            </p>

            <h2>The Power of AI in Customer Service Reporting</h2>
            <p>
              Traditional reporting methods often require hours of manual data analysis, leaving little time for actually implementing improvements. AI changes this paradigm by automatically:
            </p>
            <ul>
              <li>Identifying common customer issues and trends</li>
              <li>Analyzing sentiment across customer interactions</li>
              <li>Predicting potential escalations before they occur</li>
              <li>Recommending optimal response strategies</li>
            </ul>

            <h2>Key Benefits of AI-Powered Reports</h2>
            <p>
              When you integrate AI into your customer service reporting workflow, you'll experience several immediate benefits:
            </p>
            <ol>
              <li>
                <strong>Time Savings:</strong> Automated analysis reduces report generation time from hours to minutes.
              </li>
              <li>
                <strong>Deeper Insights:</strong> AI can identify patterns and correlations that might be missed by human analysis.
              </li>
              <li>
                <strong>Proactive Problem Solving:</strong> Predictive analytics help you address issues before they impact customer satisfaction.
              </li>
              <li>
                <strong>Data-Driven Decision Making:</strong> Clear, actionable insights make it easier to justify and implement changes.
              </li>
            </ol>

            <h2>Getting Started with AI-Powered Reports</h2>
            <p>
              Implementing AI in your reporting doesn't have to be complicated. Start with these steps:
            </p>
            <ol>
              <li>Identify your key performance indicators (KPIs)</li>
              <li>Choose the right AI-powered reporting tool</li>
              <li>Train your team on using the new insights</li>
              <li>Regularly review and adjust your reporting strategy</li>
            </ol>

            <p>
              The future of customer service reporting is here, and it's powered by AI. By embracing these tools and technologies, you can transform your customer service operations from reactive to proactive, ultimately delivering better experiences for your customers and better results for your business.
            </p>
          </div>
        </motion.div>

        {/* Author Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8"
        >
          <div className="flex items-center">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[rgb(var(--primary))] to-[rgb(var(--secondary))] mr-6" />
            <div>
              <h3 className="text-lg font-semibold">Sarah Johnson</h3>
              <p className="text-gray-600">Customer Service Technology Expert</p>
              <p className="text-sm text-gray-500 mt-2">
                Sarah has over 10 years of experience in customer service technology and AI implementation.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
