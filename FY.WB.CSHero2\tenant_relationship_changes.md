# Tenant Relationship Changes

## Overview

This document summarizes the changes made to implement proper relationships between `TenantProfile`, `Form`, and `Invoice` entities in the SaaS Template project.

## Changes Made

### 1. Entity Relationships

- Added navigation properties to establish bidirectional relationships:
  - `TenantProfile` now has collections of `Forms` and `Invoices`
  - `Form` and `Invoice` now have a reference to their parent `TenantProfile`

### 2. Entity Configurations

- Updated `FormConfiguration` and `InvoiceConfiguration` to:
  - Make `TenantId` required (NOT NULL)
  - Add foreign key relationships to `TenantProfile`
  - Add indexes on `TenantId` columns for better query performance

### 3. Database Migration

- Created migration `20250520200600_AddTenantIdConstraintsAndIndexes` that:
  - Adds NOT NULL constraints to `TenantId` columns in `Forms` and `Invoices` tables
  - Adds foreign key constraints linking `Forms.TenantId` and `Invoices.TenantId` to `TenantProfiles.Id`
  - Creates indexes on the foreign key columns for better performance

## Benefits

1. **Data Integrity**: Ensures that every `Form` and `Invoice` is explicitly associated with a tenant, preventing orphaned records.
2. **Referential Integrity**: Guarantees that any `TenantId` value in `Forms` or `Invoices` must correspond to an existing `Id` in `TenantProfiles`.
3. **Query Performance**: Indexes on `TenantId` columns improve the speed of queries that filter or join on these columns.
4. **Code Clarity**: Navigation properties make relationships explicit in the code, improving readability and maintainability.

## How to Apply the Migration

The migration will be applied automatically when the application starts, as configured in `Program.cs`. Alternatively, it can be applied manually using the Entity Framework Core CLI:

```powershell
cd FY.WB.CSHero2
dotnet ef database update
```

## Rollback Plan

If issues arise, the migration can be rolled back using:

```powershell
cd FY.WB.CSHero2
dotnet ef database update 20250514024151_AddNewEntities
```

This will revert to the previous migration, removing the constraints and indexes added by this change.
