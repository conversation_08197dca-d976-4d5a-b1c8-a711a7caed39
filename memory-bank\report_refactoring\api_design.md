# API Design: Multi-Storage Report Structure

## Overview

This document outlines the API design for the multi-storage report structure. The API provides endpoints for managing reports, report data, and report rendering. The design follows RESTful principles and uses JSON for request and response formats.

## Base URL

```
https://api.example.com/v1
```

## Authentication

All API endpoints require authentication using JWT (JSON Web Token) in the Authorization header:

```
Authorization: Bearer {token}
```

## Error Handling

All endpoints return standard HTTP status codes:

- 200 OK: Request succeeded
- 201 Created: Resource created successfully
- 400 Bad Request: Invalid request parameters
- 401 Unauthorized: Authentication required
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 409 Conflict: Resource conflict
- 500 Internal Server Error: Server error

Error responses include a JSON body with error details:

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found",
    "details": "Report with ID '12345' does not exist"
  }
}
```

## Versioning

API versioning is handled through the URL path (e.g., `/v1/reports`). This allows for backward compatibility when introducing breaking changes.

## Rate Limiting

API requests are rate-limited to prevent abuse. Rate limit information is included in response headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1625097600
```

## Endpoints

### Report Management

#### Get All Reports

Retrieves a list of reports, with optional filtering by tenant.

**Request:**

```
GET /reports
```

Query Parameters:
- `tenantId` (optional): Filter reports by tenant ID
- `page` (optional): Page number for pagination (default: 1)
- `pageSize` (optional): Number of items per page (default: 20)
- `includeArchived` (optional): Include archived reports (default: false)

**Response:**

```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Annual Report 2025",
      "description": "Annual financial report for 2025",
      "tenantId": "550e8400-e29b-41d4-a716-446655440001",
      "creatorId": "550e8400-e29b-41d4-a716-446655440002",
      "createdAt": "2025-01-15T10:30:00Z",
      "lastModifiedAt": "2025-05-20T14:45:00Z",
      "lastModifiedById": "550e8400-e29b-41d4-a716-446655440003",
      "isPublished": true,
      "isArchived": false,
      "currentVersionId": "550e8400-e29b-41d4-a716-446655440004",
      "currentVersionNumber": 3,
      "style": {
        "theme": "corporate",
        "colorScheme": "blue",
        "typography": "modern",
        "spacing": "compact"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 45,
    "totalPages": 3
  }
}
```

#### Get Report by ID

Retrieves a specific report by ID.

**Request:**

```
GET /reports/{reportId}
```

Path Parameters:
- `reportId`: The ID of the report to retrieve

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Annual Report 2025",
  "description": "Annual financial report for 2025",
  "tenantId": "550e8400-e29b-41d4-a716-446655440001",
  "creatorId": "550e8400-e29b-41d4-a716-446655440002",
  "createdAt": "2025-01-15T10:30:00Z",
  "lastModifiedAt": "2025-05-20T14:45:00Z",
  "lastModifiedById": "550e8400-e29b-41d4-a716-446655440003",
  "isPublished": true,
  "isArchived": false,
  "currentVersionId": "550e8400-e29b-41d4-a716-446655440004",
  "currentVersionNumber": 3,
  "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440005",
  "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440000/550e8400-e29b-41d4-a716-446655440004",
  "style": {
    "id": "550e8400-e29b-41d4-a716-446655440006",
    "theme": "corporate",
    "colorScheme": "blue",
    "typography": "modern",
    "spacing": "compact",
    "layoutOptions": {
      "marginTop": 20,
      "marginBottom": 20,
      "marginLeft": 30,
      "marginRight": 30
    },
    "typographyOptions": {
      "headingFont": "Arial",
      "bodyFont": "Calibri",
      "baseFontSize": 12
    },
    "structureOptions": {
      "sectionSpacing": 20,
      "sectionBorders": false
    },
    "contentOptions": {
      "textAlignment": "left",
      "listStyle": "bullet"
    },
    "visualOptions": {
      "chartStyle": "flat",
      "chartColors": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
    }
  }
}
```

#### Create Report

Creates a new report.

**Request:**

```
POST /reports
```

Request Body:

```json
{
  "name": "Quarterly Report Q2 2025",
  "description": "Quarterly financial report for Q2 2025",
  "tenantId": "550e8400-e29b-41d4-a716-446655440001",
  "style": {
    "theme": "corporate",
    "colorScheme": "blue",
    "typography": "modern",
    "spacing": "compact",
    "layoutOptions": {
      "marginTop": 20,
      "marginBottom": 20,
      "marginLeft": 30,
      "marginRight": 30
    }
  }
}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440007",
  "name": "Quarterly Report Q2 2025",
  "description": "Quarterly financial report for Q2 2025",
  "tenantId": "550e8400-e29b-41d4-a716-446655440001",
  "creatorId": "550e8400-e29b-41d4-a716-446655440002",
  "createdAt": "2025-06-01T09:15:00Z",
  "lastModifiedAt": "2025-06-01T09:15:00Z",
  "lastModifiedById": "550e8400-e29b-41d4-a716-446655440002",
  "isPublished": false,
  "isArchived": false,
  "currentVersionId": "550e8400-e29b-41d4-a716-446655440008",
  "currentVersionNumber": 1,
  "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440009",
  "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440007/550e8400-e29b-41d4-a716-446655440008",
  "style": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "theme": "corporate",
    "colorScheme": "blue",
    "typography": "modern",
    "spacing": "compact",
    "layoutOptions": {
      "marginTop": 20,
      "marginBottom": 20,
      "marginLeft": 30,
      "marginRight": 30
    }
  }
}
```

#### Update Report

Updates an existing report.

**Request:**

```
PUT /reports/{reportId}
```

Path Parameters:
- `reportId`: The ID of the report to update

Request Body:

```json
{
  "name": "Quarterly Report Q2 2025 - Updated",
  "description": "Updated quarterly financial report for Q2 2025",
  "isPublished": true
}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440007",
  "name": "Quarterly Report Q2 2025 - Updated",
  "description": "Updated quarterly financial report for Q2 2025",
  "tenantId": "550e8400-e29b-41d4-a716-446655440001",
  "creatorId": "550e8400-e29b-41d4-a716-446655440002",
  "createdAt": "2025-06-01T09:15:00Z",
  "lastModifiedAt": "2025-06-01T10:30:00Z",
  "lastModifiedById": "550e8400-e29b-41d4-a716-446655440002",
  "isPublished": true,
  "isArchived": false,
  "currentVersionId": "550e8400-e29b-41d4-a716-446655440008",
  "currentVersionNumber": 1,
  "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440009",
  "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440007/550e8400-e29b-41d4-a716-446655440008",
  "style": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "theme": "corporate",
    "colorScheme": "blue",
    "typography": "modern",
    "spacing": "compact"
  }
}
```

#### Delete Report

Deletes a report.

**Request:**

```
DELETE /reports/{reportId}
```

Path Parameters:
- `reportId`: The ID of the report to delete

**Response:**

```
204 No Content
```

#### Archive Report

Archives a report.

**Request:**

```
POST /reports/{reportId}/archive
```

Path Parameters:
- `reportId`: The ID of the report to archive

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440007",
  "name": "Quarterly Report Q2 2025 - Updated",
  "isArchived": true,
  "archivedAt": "2025-06-01T11:45:00Z",
  "archivedById": "550e8400-e29b-41d4-a716-446655440002"
}
```

#### Unarchive Report

Unarchives a report.

**Request:**

```
POST /reports/{reportId}/unarchive
```

Path Parameters:
- `reportId`: The ID of the report to unarchive

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440007",
  "name": "Quarterly Report Q2 2025 - Updated",
  "isArchived": false
}
```

### Report Version Management

#### Get Report Versions

Retrieves all versions of a report.

**Request:**

```
GET /reports/{reportId}/versions
```

Path Parameters:
- `reportId`: The ID of the report

**Response:**

```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440008",
      "reportId": "550e8400-e29b-41d4-a716-446655440007",
      "versionNumber": 1,
      "versionName": "Initial Version",
      "creatorId": "550e8400-e29b-41d4-a716-446655440002",
      "createdAt": "2025-06-01T09:15:00Z",
      "isCurrent": true,
      "isPublished": true,
      "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440009",
      "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440007/550e8400-e29b-41d4-a716-446655440008"
    }
  ]
}
```

#### Get Report Version

Retrieves a specific version of a report.

**Request:**

```
GET /reports/{reportId}/versions/{versionId}
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440008",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "versionNumber": 1,
  "versionName": "Initial Version",
  "creatorId": "550e8400-e29b-41d4-a716-446655440002",
  "createdAt": "2025-06-01T09:15:00Z",
  "isCurrent": true,
  "isPublished": true,
  "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440009",
  "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440007/550e8400-e29b-41d4-a716-446655440008"
}
```

#### Create Report Version

Creates a new version of a report.

**Request:**

```
POST /reports/{reportId}/versions
```

Path Parameters:
- `reportId`: The ID of the report

Request Body:

```json
{
  "versionName": "Version 2",
  "basedOnVersionId": "550e8400-e29b-41d4-a716-446655440008"
}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440011",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "versionNumber": 2,
  "versionName": "Version 2",
  "creatorId": "550e8400-e29b-41d4-a716-446655440002",
  "createdAt": "2025-06-01T14:20:00Z",
  "isCurrent": true,
  "isPublished": false,
  "dataDocumentId": "report-data-550e8400-e29b-41d4-a716-446655440012",
  "componentsBlobId": "reports/550e8400-e29b-41d4-a716-446655440007/550e8400-e29b-41d4-a716-446655440011"
}
```

#### Set Current Version

Sets a version as the current version of a report.

**Request:**

```
POST /reports/{reportId}/versions/{versionId}/set-current
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version to set as current

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440011",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "versionNumber": 2,
  "versionName": "Version 2",
  "isCurrent": true
}
```

### Report Style Management

#### Get Report Style

Retrieves the style of a report.

**Request:**

```
GET /reports/{reportId}/style
```

Path Parameters:
- `reportId`: The ID of the report

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440010",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "theme": "corporate",
  "colorScheme": "blue",
  "typography": "modern",
  "spacing": "compact",
  "layoutOptions": {
    "marginTop": 20,
    "marginBottom": 20,
    "marginLeft": 30,
    "marginRight": 30
  },
  "typographyOptions": {
    "headingFont": "Arial",
    "bodyFont": "Calibri",
    "baseFontSize": 12
  },
  "structureOptions": {
    "sectionSpacing": 20,
    "sectionBorders": false
  },
  "contentOptions": {
    "textAlignment": "left",
    "listStyle": "bullet"
  },
  "visualOptions": {
    "chartStyle": "flat",
    "chartColors": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
  }
}
```

#### Update Report Style

Updates the style of a report.

**Request:**

```
PUT /reports/{reportId}/style
```

Path Parameters:
- `reportId`: The ID of the report

Request Body:

```json
{
  "theme": "modern",
  "colorScheme": "green",
  "typography": "clean",
  "spacing": "comfortable",
  "layoutOptions": {
    "marginTop": 30,
    "marginBottom": 30,
    "marginLeft": 40,
    "marginRight": 40
  },
  "typographyOptions": {
    "headingFont": "Helvetica",
    "bodyFont": "Arial",
    "baseFontSize": 14
  }
}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440010",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "theme": "modern",
  "colorScheme": "green",
  "typography": "clean",
  "spacing": "comfortable",
  "layoutOptions": {
    "marginTop": 30,
    "marginBottom": 30,
    "marginLeft": 40,
    "marginRight": 40
  },
  "typographyOptions": {
    "headingFont": "Helvetica",
    "bodyFont": "Arial",
    "baseFontSize": 14
  },
  "structureOptions": {
    "sectionSpacing": 20,
    "sectionBorders": false
  },
  "contentOptions": {
    "textAlignment": "left",
    "listStyle": "bullet"
  },
  "visualOptions": {
    "chartStyle": "flat",
    "chartColors": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
  }
}
```

### Report Data Management

#### Get Report Data

Retrieves the data of a report version.

**Request:**

```
GET /reports/{reportId}/versions/{versionId}/data
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version

**Response:**

```json
{
  "id": "report-data-550e8400-e29b-41d4-a716-446655440012",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "versionId": "550e8400-e29b-41d4-a716-446655440011",
  "versionNumber": 2,
  "sections": [
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440013",
      "title": "Executive Summary",
      "type": "text",
      "order": 0,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440014",
          "name": "summary",
          "type": "richtext",
          "content": "<p>This is the executive summary of the quarterly report.</p>",
          "order": 0,
          "metadata": {
            "wordCount": 10,
            "lastEdited": "2025-06-01T14:25:00Z"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "backgroundColor": "#f5f5f5"
      }
    },
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440015",
      "title": "Financial Results",
      "type": "chart",
      "order": 1,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440016",
          "name": "quarterlyRevenue",
          "type": "chart-data",
          "content": "{\"labels\":[\"Q1\",\"Q2\"],\"datasets\":[{\"label\":\"Revenue\",\"data\":[10000,12000]}]}",
          "order": 0,
          "metadata": {
            "chartType": "bar",
            "dataSource": "financial-system"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "chartHeight": 400
      }
    }
  ],
  "metadata": {
    "createdAt": "2025-06-01T14:20:00Z",
    "createdBy": "550e8400-e29b-41d4-a716-446655440002",
    "lastModifiedAt": "2025-06-01T14:25:00Z",
    "lastModifiedBy": "550e8400-e29b-41d4-a716-446655440002"
  }
}
```

#### Update Report Data

Updates the data of a report version.

**Request:**

```
PUT /reports/{reportId}/versions/{versionId}/data
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version

Request Body:

```json
{
  "sections": [
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440013",
      "title": "Executive Summary - Updated",
      "type": "text",
      "order": 0,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440014",
          "name": "summary",
          "type": "richtext",
          "content": "<p>This is the updated executive summary of the quarterly report.</p>",
          "order": 0
        }
      ]
    },
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440015",
      "title": "Financial Results",
      "type": "chart",
      "order": 1,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440016",
          "name": "quarterlyRevenue",
          "type": "chart-data",
          "content": "{\"labels\":[\"Q1\",\"Q2\"],\"datasets\":[{\"label\":\"Revenue\",\"data\":[10000,12000]}]}",
          "order": 0
        }
      ]
    }
  ]
}
```

**Response:**

```json
{
  "id": "report-data-550e8400-e29b-41d4-a716-446655440012",
  "reportId": "550e8400-e29b-41d4-a716-446655440007",
  "versionId": "550e8400-e29b-41d4-a716-446655440011",
  "versionNumber": 2,
  "sections": [
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440013",
      "title": "Executive Summary - Updated",
      "type": "text",
      "order": 0,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440014",
          "name": "summary",
          "type": "richtext",
          "content": "<p>This is the updated executive summary of the quarterly report.</p>",
          "order": 0,
          "metadata": {
            "wordCount": 12,
            "lastEdited": "2025-06-01T15:10:00Z"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "backgroundColor": "#f5f5f5"
      }
    },
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440015",
      "title": "Financial Results",
      "type": "chart",
      "order": 1,
      "fields": [
        {
          "id": "field-550e8400-e29b-41d4-a716-446655440016",
          "name": "quarterlyRevenue",
          "type": "chart-data",
          "content": "{\"labels\":[\"Q1\",\"Q2\"],\"datasets\":[{\"label\":\"Revenue\",\"data\":[10000,12000]}]}",
          "order": 0,
          "metadata": {
            "chartType": "bar",
            "dataSource": "financial-system"
          }
        }
      ],
      "metadata": {
        "collapsed": false,
        "chartHeight": 400
      }
    }
  ],
  "metadata": {
    "createdAt": "2025-06-01T14:20:00Z",
    "createdBy": "550e8400-e29b-41d4-a716-446655440002",
    "lastModifiedAt": "2025-06-01T15:10:00Z",
    "lastModifiedBy": "550e8400-e29b-41d4-a716-446655440002"
  }
}
```

#### Get Report Sections

Retrieves the sections of a report version.

**Request:**

```
GET /reports/{reportId}/versions/{versionId}/sections
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version

**Response:**

```json
{
  "data": [
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440013",
      "title": "Executive Summary - Updated",
      "type": "text",
      "order": 0
    },
    {
      "id": "section-550e8400-e29b-41d4-a716-446655440015",
      "title": "Financial Results",
      "type": "chart",
      "order": 1
    }
  ]
}
```

#### Get Report Section

Retrieves a specific section of a report version.

**Request:**

```
GET /reports/{reportId}/versions/{versionId}/sections/{sectionId}
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version
- `sectionId`: The ID of the section

**Response:**

```json
{
  "id": "section-550e8400-e29b-41d4-a716-446655440013",
  "title": "Executive Summary - Updated",
  "type": "text",
  "order": 0,
  "fields": [
    {
      "id": "field-550e8400-e29b-41d4-a716-446655440014",
      "name": "summary",
      "type": "richtext",
      "content": "<p>This is the updated executive summary of the quarterly report.</p>",
      "order": 0,
      "metadata": {
        "wordCount": 12,
        "lastEdited": "2025-06-01T15:10:00Z"
      }
    }
  ],
  "metadata": {
    "collapsed": false,
    "backgroundColor": "#f5f5f5"
  }
}
```

#### Add Report Section

Adds a new section to a report version.

**Request:**

```
POST /reports/{reportId}/versions/{versionId}/sections
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version

Request Body:

```json
{
  "title": "Recommendations",
  "type": "text",
  "order": 2,
  "fields": [
    {
      "name": "recommendations",
      "type": "richtext",
      "content": "<p>Based on the financial results, we recommend the following actions...</p>",
      "order": 0
    }
  ],
  "metadata": {
    "collapsed": false,
    "backgroundColor": "#f0f8ff"
  }
}
```

**Response:**

```json
{
  "id": "section-550e8400-e29b-41d4-a716-446655440017",
  "title": "Recommendations",
  "type": "text",
  "order": 2,
  "fields": [
    {
      "id": "field-550e8400-e29b-41d4-a716-446655440018",
      "name": "recommendations",
      "type": "richtext",
      "content": "<p>Based on the financial results, we recommend the following actions...</p>",
      "order": 0,
      "metadata": {
        "wordCount": 11,
        "lastEdited": "2025-06-01T15:30:00Z"
      }
    }
  ],
  "metadata": {
    "collapsed": false,
    "backgroundColor": "#f0f8ff"
  }
}
```

#### Update Report Section

Updates a section of a report version.

**Request:**

```
PUT /reports/{reportId}/versions/{versionId}/sections/{sectionId}
```

Path Parameters:
- `reportId`: The ID of the report
- `versionId`: The ID of the version
- `sectionId`: The ID of the section

Request Body:

```json
{
  "title": "Recommendations and Next Steps",
  "order": 2,
  "metadata": {
    "collapsed": false,
    "backgroundColor": "#e6f7ff"
  }
}
```

**Response:**

```json
{
  "id": "section-550e8400-e29b-41d4-a716-446655440017",
  "title": "Recommendations and Next Steps",
  "type": "text",
  "order": 2,
  "fields": [
    {
      "id": "field-550e8400-e29b-41d4-a716-446655440018",
      "name": "recommendations",
      "type": "richtext",
      "content": "<p>Based on the financial results, we recommend the following actions...</p>",
      "order": 0,
      "metadata": {
        "wordCount": 11,
        "lastEdited": "2025-06-01T15: