"use client";

import { useState } from "react";
import { Info } from "lucide-react";

interface Field {
  id: string;
  name: string;
  label: string;
  type: string;
  required: boolean;
  options?: any;
}

interface DataType {
  id?: string;
  name: string;
  description?: string;
  fields: Field[];
}

interface DataTypeBuilderProps {
  initialDataType: DataType;
  onSave: (dataType: DataType) => void;
  onCancel: () => void;
}

export function DataTypeBuilder({
  initialDataType,
  onSave,
  onCancel
}: DataTypeBuilderProps) {
  const [dataType, setDataType] = useState<DataType>(initialDataType || {
    name: "",
    description: "",
    fields: []
  });

  return (
    <div className="p-6 border border-dashed rounded-lg flex flex-col items-center justify-center">
      <div className="mb-4">
        <Info className="h-12 w-12 text-blue-500" />
      </div>
      <h3 className="text-lg font-medium mb-2 text-center">DataTypeBuilder Placeholder</h3>
      <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
        This is a placeholder for the DataTypeBuilder component. In a real implementation, 
        this would allow users to define data types and fields.
      </p>
      <div className="flex space-x-4">
        <button
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => onSave({
            ...dataType,
            id: dataType.id || `datatype-${Date.now()}`,
            fields: [
              {
                id: "field-1",
                name: "sampleField",
                label: "Sample Field",
                type: "text",
                required: true
              },
              {
                id: "field-2",
                name: "sampleNumber",
                label: "Sample Number",
                type: "number",
                required: false
              }
            ]
          })}
        >
          Save
        </button>
      </div>
    </div>
  );
}
