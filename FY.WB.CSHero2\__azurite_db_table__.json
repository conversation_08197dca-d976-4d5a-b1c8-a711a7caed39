{"filename": "C:\\Users\\<USER>\\Documents\\GitHub\\ChildrensVillage\\FY.WB.CSHero2\\FY.WB.CSHero2\\__azurite_db_table__.json", "collections": [{"name": "$TABLES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {"account": {"name": "account", "dirty": false, "values": []}, "table": {"name": "table", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$TABLES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$SERVICES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {}, "constraints": null, "uniqueNames": ["accountName"], "transforms": {}, "objType": "$SERVICES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}