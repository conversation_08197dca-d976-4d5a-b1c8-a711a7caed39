<?xml version="1.0" encoding="UTF-8"?>
<DesignBrief>
  <ProjectInformation>
    <ClientName>InnoCloud</ClientName>
    <ProjectTitle>Professional KPI Dashboard Template</ProjectTitle>
    <ProjectDescription>A comprehensive design brief for a clean, professional dashboard template focusing on key performance metrics with a balanced approach to data visualization and executive summaries.</ProjectDescription>
    <DeliveryDate>April 15, 2025</DeliveryDate>
  </ProjectInformation>
  
  <BrandIdentity>
    <ColorPalette>
      <PrimaryColors>
        <Color name="Professional Navy" hex="#1E3A5F" rgb="30,58,95">Bold navy blue as the primary brand color</Color>
        <Color name="Corporate Orange" hex="#FF6B35" rgb="255,107,53">Vibrant orange for accents and highlights</Color>
        <Color name="Clean White" hex="#FFFFFF" rgb="255,255,255">Base background color</Color>
      </PrimaryColors>
      
      <SecondaryColors>
        <Color name="Slate Gray" hex="#607D8B" rgb="96,125,139">Secondary text and dividers</Color>
        <Color name="Accent Blue" hex="#4285F4" rgb="66,133,244">Highlight color for interactive elements</Color>
        <Color name="Success Green" hex="#34A853" rgb="52,168,83">Positive indicators</Color>
        <Color name="Alert Yellow" hex="#FBBC05" rgb="251,188,5">Warning indicators</Color>
        <Color name="Light Gray" hex="#F5F7FA" rgb="245,247,250">Secondary background color</Color>
        <Color name="Dark Gray" hex="#263238" rgb="38,50,56">Primary text color</Color>
      </SecondaryColors>
    </ColorPalette>
    
    <Typography>
      <PrimaryFont>
        <Name>Montserrat</Name>
        <Weights>
          <Weight value="400">Regular</Weight>
          <Weight value="500">Medium</Weight>
          <Weight value="700">Bold</Weight>
          <Weight value="800">Extra Bold</Weight>
        </Weights>
        <Usage>Main headings, subheadings, and key metrics with enhanced font weights for bolder appearance</Usage>
      </PrimaryFont>
      
      <SecondaryFont>
        <Name>Open Sans</Name>
        <Weights>
          <Weight value="400">Regular</Weight>
          <Weight value="600">SemiBold</Weight>
        </Weights>
        <Usage>Body text, captions, and detailed information</Usage>
      </SecondaryFont>
      
      <FontSizes>
        <Size name="Heading 1">32px</Size>
        <Size name="Heading 2">26px</Size>
        <Size name="Heading 3">22px</Size>
        <Size name="Body Text">14px</Size>
        <Size name="Caption">12px</Size>
        <Size name="Small">10px</Size>
      </FontSizes>
      
      <LineHeight>
        <Height name="Tight">1.2</Height>
        <Height name="Normal">1.4</Height>
        <Height name="Relaxed">1.6</Height>
      </LineHeight>
    </Typography>
  </BrandIdentity>
  
  <DesignElements>
    <Spacing>
      <BaseUnit>8px</BaseUnit>
      <Scale>
        <Size name="XSmall">0.5x</Size>
        <Size name="Small">1x</Size>
        <Size name="Medium">2x</Size>
        <Size name="Large">3x</Size>
        <Size name="XLarge">4x</Size>
        <Size name="XXLarge">6x</Size>
      </Scale>
    </Spacing>
    
    <GridSystem>
      <Columns>12</Columns>
      <Gutter>24px</Gutter>
      <Margins>32px</Margins>
      <Breakpoints>
        <Breakpoint name="Mobile">320px - 767px</Breakpoint>
        <Breakpoint name="Tablet">768px - 1023px</Breakpoint>
        <Breakpoint name="Desktop">1024px - 1439px</Breakpoint>
        <Breakpoint name="Large Desktop">1440px+</Breakpoint>
      </Breakpoints>
    </GridSystem>
    
    <Components>
      <Charts>
        <Style>Clean, minimal with light grid lines and clear labeling</Style>
        <Colors>Use primary and secondary colors strategically with bold navy backgrounds and orange highlights</Colors>
        <Labels>Open Sans, Regular, 12px</Labels>
        <Types>Bar charts, line charts, donut charts, progress indicators</Types>
      </Charts>
      
      <Tables>
        <HeaderStyle>Background #1E3A5F, Text #FFFFFF, Bold, with orange accents</HeaderStyle>
        <RowStyle>Background #FFFFFF, Text #263238, Regular</RowStyle>
        <AlternateRowStyle>Background #F9FAFC, Text #263238, Regular</AlternateRowStyle>
        <BorderStyle>#E0E0E0, 1px</BorderStyle>
      </Tables>
      
      <Cards>
        <Style>Background #FFFFFF, Border Radius 8px, Padding 24px, Left orange border accent for emphasis</Style>
        <Shadow>0px 4px 12px rgba(0,0,0,0.08)</Shadow>
      </Cards>
      
      <MetricDisplays>
        <PrimaryMetric>32px, Bold, Includes percentage indicators with enhanced color coding</PrimaryMetric>
        <SecondaryMetric>26px, Includes trend arrows with vibrant color indicators</SecondaryMetric>
        <ComparisonValues>14px, Shows previous period with clear visual differentiation</ComparisonValues>
        <IconBasedKPIs>Row-based KPI displays with icons and colorful status indicators</IconBasedKPIs>
      </MetricDisplays>
      
      <CalloutBoxes>
        <DefaultStyle>Background #F5F7FA, Left Border #4285F4, 4px, Radius 8px, Padding 16px</DefaultStyle>
        <ImportantStyle>Background #FFF5E6, Left Border #FF6B35, 4px, Radius 8px, Padding 16px</ImportantStyle>
        <RecommendationSection>Prominent section with navy background and orange accents</RecommendationSection>
      </CalloutBoxes>
      
      <StatusBadges>
        <Style>Rounded corners with vibrant colors for different status indicators</Style>
        <Types>
          <Badge color="#34A853">Success</Badge>
          <Badge color="#FBBC05">Warning</Badge>
          <Badge color="#EA4335">Alert</Badge>
          <Badge color="#4285F4">Information</Badge>
        </Types>
      </StatusBadges>
      
      <SectionHeaders>
        <Style>Colored header bars (navy background with orange accent line) for each major section</Style>
        <Typography>Montserrat Bold, 26px, white text on navy background</Typography>
      </SectionHeaders>
    </Components>
    
    <Imagery>
      <Photography>
        <Style>Professional, modern office environments, collaborative teams</Style>
        <Tone>Productive, focused, professional</Tone>
        <Treatment>Bold navy blue overlay with orange accent elements</Treatment>
      </Photography>
      
      <Icons>
        <Style>Simple, consistent line weight, rounded corners</Style>
        <Size>24px</Size>
        <Colors>Primary and secondary color palette with enhanced contrast</Colors>
        <Usage>Section identifiers, action buttons, status indicators</Usage>
      </Icons>
      
      <Illustrations>
        <Style>Clean, minimal, abstract business representations with geometric shapes</Style>
        <ColorPalette>Bold primary colors with secondary accents</ColorPalette>
        <LineWeight>2px</LineWeight>
        <Usage>Visual metaphors for complex data concepts</Usage>
      </Illustrations>
      
      <DecorativeElements>
        <OrangeBars>Vertical and horizontal orange accent bars for visual distinction</OrangeBars>
        <GeometricShapes>Subtle background patterns using brand colors</GeometricShapes>
        <LogoPlaceholder>Simple SVG logo placeholder for InnoCloud</LogoPlaceholder>
      </DecorativeElements>
    </Imagery>
  </DesignElements>
  
  <DocumentStructure>
    <Sections>
      <Section name="Cover/Overview" pages="1">
        <Description>Dynamic cover page with gradient background and bold typography</Description>
      </Section>
      <Section name="Executive Summary" pages="1-2" />
      <Section name="KPI Dashboard" pages="2-3" />
      <Section name="Performance Breakdown" pages="2-3" />
      <Section name="Sales Activity" pages="2" />
      <Section name="Product Performance" pages="2" />
      <Section name="Team Activities" pages="1-2" />
      <Section name="Forecasting &amp; Goals" pages="1-2" />
      <Section name="Action Items" pages="1" />
    </Sections>
    
    <Templates>
      <Template name="Dashboard Page">
        <Header>✔</Header>
        <Footer>✔</Footer>
        <PageNumbers>✔</PageNumbers>
      </Template>
      <Template name="Section Divider">
        <Header>✖</Header>
        <Footer>✔</Footer>
        <PageNumbers>✔</PageNumbers>
      </Template>
      <Template name="Cover Page">
        <Header>✖</Header>
        <Footer>✖</Footer>
        <PageNumbers>✖</PageNumbers>
      </Template>
    </Templates>
  </DocumentStructure>
  
  <Guidelines>
    <ContentGuidelines>
      <Tone>Professional, clear, action-oriented</Tone>
      <Language>Precise, data-focused with business terminology</Language>
      <Emphasis>Performance metrics, comparative analysis, actionable insights</Emphasis>
    </ContentGuidelines>
    
    <DataVisualizationGuidelines>
      <Guideline number="1">
        <Name>Clarity</Name>
        <Description>Each visualization should communicate a single key insight</Description>
      </Guideline>
      <Guideline number="2">
        <Name>Consistency</Name>
        <Description>Use the same visualization type for similar metrics across reports</Description>
      </Guideline>
      <Guideline number="3">
        <Name>Context</Name>
        <Description>Always provide comparative data (previous period, goals, etc.)</Description>
      </Guideline>
      <Guideline number="4">
        <Name>Simplicity</Name>
        <Description>Avoid chart junk and unnecessary decorative elements</Description>
      </Guideline>
    </DataVisualizationGuidelines>
    
    <AccessibilityGuidelines>
      <ColorContrast>Minimum ratio 4.5:1</ColorContrast>
      <TextSize>Minimum 12px for all data</TextSize>
      <AlternativeText>Required for all charts and images</AlternativeText>
      <PDFTags>Required</PDFTags>
    </AccessibilityGuidelines>
    
    <BrandAlignment>
      <InnoCloudValues>
        <Value name="Data-Driven">Clean presentation of metrics with clear insights and bold visualization</Value>
        <Value name="Efficiency">Well-organized information architecture with improved visual hierarchy</Value>
        <Value name="Professionalism">Refined aesthetic with attention to detail and enhanced visual elements</Value>
        <Value name="Clarity">Straightforward presentation of complex information with increased white space</Value>
        <Value name="Action-Oriented">Clear next steps and recommended actions in prominent sections</Value>
      </InnoCloudValues>
    </BrandAlignment>
  </Guidelines>
  
  <Deliverables>
    <Deliverable number="1">Print-Ready PDF (High-resolution, CMYK)</Deliverable>
    <Deliverable number="2">Digital Interactive Dashboard (PDF with navigation)</Deliverable>
    <Deliverable number="3">PowerPoint Template (Editable slides based on the design)</Deliverable>
    <Deliverable number="4">Modular Component Library (Reusable design elements)</Deliverable>
    <Deliverable number="5">Style Guide Documentation (Usage instructions and examples)</Deliverable>
  </Deliverables>
</DesignBrief>