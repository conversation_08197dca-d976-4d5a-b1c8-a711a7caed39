'use client';

import { useState } from 'react';
import { ClientTable } from '@/components/features/clients/client-table';
import { ClientForm } from '@/components/features/clients/client-form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { createClient } from '@/lib/api';
import { Client } from '@/types/client';
import { PageHeader } from '@/components/layouts/page-header';

export default function ClientsPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const handleCreateClient = async (data: Partial<Client>) => {
    await createClient(data);
    setDialogOpen(false);
    setRefreshKey(k => k + 1);
  };

  return (
    <div className="container mx-auto py-12 max-w-inner px-4 md:px-16">
      <PageHeader
        title="Clients"
        description="Manage your client relationships and information"
        onButtonClick={() => setDialogOpen(true)}
        btnName="New Client"
        placeHolder="Search clients..."
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />

      <div className="bg-card rounded-lg border">
        <ClientTable key={refreshKey} searchQuery={searchQuery} />

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent className="bg-card max-w-lg bg-white">
            <DialogHeader className="space-y-1">
              <DialogTitle className="text-2xl font-bold text-[rgb(var(--primary))]">Create New Client</DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Add a new client to your client list
              </DialogDescription>
            </DialogHeader>
            <ClientForm
              onSubmit={handleCreateClient}
              onCancel={() => setDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
