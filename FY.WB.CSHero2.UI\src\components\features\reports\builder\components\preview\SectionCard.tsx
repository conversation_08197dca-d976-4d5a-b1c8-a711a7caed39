"use client";

import { useState, useEffect } from "react";
import { Edit, Plus, Trash2, <PERSON>cil, ChevronDown, ChevronUp, Wand2, Check } from "lucide-react";
import { Section, Field, FieldType } from "../../types";
import { FieldEditor } from "../preview/FieldEditor";

// Add style for pulse animation
const pulseStyle = {
  animation: "pulse 1s",
};

// Add CSS for pulse animation
const pulseAnimation = `
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}
.pulse {
  animation: pulse 1s;
}
`;

interface SectionCardProps {
  section: Section;
  isActive: boolean;
  isPulsing?: boolean;
  onSectionClick: (id: string) => void;
  onUpdateSection: (section: Section) => void;
}

export function SectionCard({ section, isActive, isPulsing = false, onSectionClick, onUpdateSection }: SectionCardProps) {
  const [isAddFieldOpen, setIsAddFieldOpen] = useState(false);
  const [newField, setNewField] = useState<Partial<Field>>({
    name: '',
    type: 'String'
  });
  const [tableConfig, setTableConfig] = useState({
    rows: 3,
    columns: 3
  });
  const [showTableConfig, setShowTableConfig] = useState(false);
  const [nameError, setNameError] = useState<string | null>(null);
  
  // States for section title editing
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [titleValue, setTitleValue] = useState(section.title);
  
  // States for section description
  const [showDescription, setShowDescription] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState(section.description || '');

  // Handle section click
  const handleSectionClick = () => {
    onSectionClick(section.id);
  };

  // Handle field type change
  const handleFieldTypeChange = (type: FieldType) => {
    setNewField({
      ...newField,
      type
    });
    setShowTableConfig(type === 'Table');
  };

  // Handle save field
  const handleSaveField = () => {
    // Validate field name
    if (!newField.name) {
      setNameError("Field name is required");
      return;
    }
    
    // Clear any previous errors
    setNameError(null);

    // Create a new field
    const fieldToAdd: Field = {
      id: Date.now().toString(),
      name: newField.name,
      type: newField.type as FieldType
    };

    if (newField.type === 'Table') {
      fieldToAdd.config = {
        rows: tableConfig.rows,
        columns: tableConfig.columns
      };
    }

    // Convert field name to camelCase for the property name
    const propName = newField.name
      .toLowerCase()
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      )
      .replace(/\s+/g, '');
    
    // Create a copy of the section content
    const updatedContent = { ...section.content };
    
    // Initialize with empty content based on field type
    if (newField.type === 'Table') {
      // Create empty table data
      const rows = tableConfig.rows;
      const cols = tableConfig.columns;
      const emptyTable = Array(rows).fill(0).map(() => Array(cols).fill(''));
      updatedContent[propName] = JSON.stringify(emptyTable);
    } else {
      updatedContent[propName] = '';
    }
    
    // Update the section with the new content
    onUpdateSection({
      ...section,
      content: updatedContent
    });

    setIsAddFieldOpen(false);
  };

  // Handle delete field
  const handleDeleteField = (fieldName: string) => {
    // Create a copy of the section content
    const updatedContent = { ...section.content };
    
    // Remove the field from the content
    delete updatedContent[fieldName];
    
    // Update the section with the new content
    onUpdateSection({
      ...section,
      content: updatedContent
    });
  };

  // Handle field update
  const handleFieldUpdate = (fieldName: string, value: any) => {
    // Create a copy of the section content
    const updatedContent = { ...section.content };
    
    // Update the field in the content
    updatedContent[fieldName] = value;
    
    // Update the section with the new content
    onUpdateSection({
      ...section,
      content: updatedContent
    });
  };
  
  // Handle field metadata update (name, description)
  const handleFieldMetadataUpdate = (fieldName: string, updates: { name?: string; description?: string }) => {
    // Get the current metadata for this field from the section
    const fieldMetadata = section.content[`${fieldName}_metadata`] || {};
    
    // Update the metadata with the new values
    const updatedMetadata = {
      ...fieldMetadata,
      ...updates
    };
    
    // Create a copy of the section content
    const updatedContent = { ...section.content };
    
    // Store the updated metadata
    updatedContent[`${fieldName}_metadata`] = updatedMetadata;
    
    // Update the section with the new content
    onUpdateSection({
      ...section,
      content: updatedContent
    });
  };
  
  // Get field display name and description from metadata if available
  const getFieldMetadata = (fieldName: string) => {
    const metadata = section.content[`${fieldName}_metadata`] || {};
    
    return {
      displayName: metadata.name || fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/([A-Z])/g, ' $1'),
      description: metadata.description || ''
    };
  };
  
  // Handle title edit start
  const handleEditTitleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditingTitle(true);
    setTitleValue(section.title);
  };
  
  // Handle title save
  const handleTitleSave = () => {
    if (titleValue.trim()) {
      onUpdateSection({
        ...section,
        title: titleValue
      });
    }
    setIsEditingTitle(false);
  };
  
  // Handle description toggle
  const handleToggleDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDescription(!showDescription);
  };
  
  // Handle description change
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescriptionValue(e.target.value);
  };
  
  // Handle save description
  const handleSaveDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    onUpdateSection({
      ...section,
      description: descriptionValue
    });
    setShowDescription(false);
  };
  
  // Handle enhance description
  const handleEnhanceDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Here we'd typically call an AI enhancement function
    // For now, just add some placeholder text if empty
    if (!descriptionValue) {
      setDescriptionValue("This section should contain key information about the report topic. The AI should emphasize relevant data points and present insights in a clear, concise manner.");
    }
  };

  // Add the pulse animation to the document head
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = pulseAnimation;
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div 
      className={`border rounded-lg overflow-hidden mb-6 transition-all bg-white shadow-lg rounded-lg ${
        isActive ? 'border-primary shadow-md' : 'border-gray-200 hover:border-gray-300'
      } ${isPulsing && isActive ? 'pulse' : ''}`}
    >
      <div 
        className={`p-4 border-b ${
          isActive ? 'bg-blue-50' : 'bg-gray-50'
        }`}
      >
        {/* Title section with edit functionality */}
        <div className="cursor-pointer" onClick={handleSectionClick}>
          <div className="flex items-center mb-1">
            {isEditingTitle && isActive ? (
              <div className="flex items-center flex-1">
                <input
                  type="text"
                  value={titleValue}
                  onChange={(e) => setTitleValue(e.target.value)}
                  onBlur={handleTitleSave}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleTitleSave();
                    if (e.key === 'Escape') {
                      setIsEditingTitle(false);
                      setTitleValue(section.title);
                    }
                  }}
                  className="flex-1 p-1 border rounded mr-2 text-lg"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
                <button
                  className="p-1 rounded hover:bg-gray-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTitleSave();
                  }}
                >
                  <Check className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <div className="flex items-center flex-1">
                <h3 className="text-lg font-medium">{section.title}</h3>
                
                {isActive && (
                  <button 
                    className="ml-2 p-1 rounded hover:bg-gray-200 text-gray-500"
                    onClick={handleEditTitleClick}
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                )}
                
                {isActive && (
                  <span className="ml-3 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    Active
                  </span>
                )}
              </div>
            )}
          </div>
          
          {/* Description section - always visible */}
          {section.description && (
            <div 
              className="ml-1 mb-1 text-sm text-gray-400 italic cursor-pointer hover:text-gray-600 flex items-center"
              onClick={(e) => {
                e.stopPropagation();
                if (isActive) setShowDescription(true);
              }}
            >
              <span>{section.description}</span>
              {isActive && (
                <Pencil className="h-3 w-3 ml-1 text-gray-400" />
              )}
            </div>
          )}
          {!section.description && (
            <div 
              className="ml-1 text-sm text-gray-400 italic cursor-pointer hover:text-gray-600 flex items-center"
              onClick={(e) => {
                e.stopPropagation();
                setShowDescription(true);
              }}
            >
              <span>Click to add a description for AI...</span>
              {isActive && (
                <Pencil className="h-3 w-3 ml-1 text-gray-400" />
              )}
            </div>
          )}
        </div>
        
        {/* Description section (collapsible) */}
        {isActive && showDescription && (
          <div className="p-3" onClick={(e) => e.stopPropagation()}>
            <textarea 
              className="w-full p-2 border rounded text-sm mb-2"
              rows={3}
              value={descriptionValue}
              onChange={handleDescriptionChange}
              placeholder="Describe this section for the AI that will generate the report..."
            />
            <div className="flex justify-end space-x-2">
              <button
                className="px-2 py-1 text-xs border rounded hover:bg-gray-50"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDescription(false);
                }}
              >
                Cancel
              </button>
              <button
                className="px-2 py-1 text-xs border rounded hover:bg-gray-50 flex items-center"
                onClick={handleEnhanceDescription}
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Enhance
              </button>
              <button
                className="px-2 py-1 text-xs bg-primary text-white rounded"
                onClick={handleSaveDescription}
              >
                Save
              </button>
            </div>
          </div>
        )}
      </div>
      
      <div className="p-6 bg-gray-50">
        {Object.keys(section.content).filter(key => !key.endsWith('_metadata')).length > 0 ? (
          <div className="space-y-8">
            {Object.entries(section.content)
              .filter(([key]) => !key.endsWith('_metadata'))
              .map(([key, value]) => {
                const { displayName, description } = getFieldMetadata(key);
                
                return (
                  <div key={key} className="border-2 border-gray-300 shadow-md rounded-lg p-5 bg-white">
                    <FieldEditor 
                      fieldName={key}
                      fieldValue={value}
                      isEditable={isActive}
                      onFieldUpdate={handleFieldUpdate}
                      onMetadataUpdate={isActive ? handleFieldMetadataUpdate : undefined}
                      fieldDescription={description}
                      displayName={displayName}
                    />
                    
                    {isActive && (
                      <div className="mt-3 flex justify-end">
                        <button 
                          className="p-1.5 rounded-full hover:bg-red-100 text-red-500"
                          onClick={() => handleDeleteField(key)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <p>No fields in this section.</p>
            {isActive && (
              <button
                className="mt-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70"
                onClick={() => setIsAddFieldOpen(true)}
              >
                <Plus className="h-4 w-4 inline-block mr-1" />
                Add Field
              </button>
            )}
          </div>
        )}
        
        {isActive && Object.keys(section.content).length > 0 && (
          <div className="mt-4 text-center">
            <button
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70"
              onClick={() => setIsAddFieldOpen(true)}
            >
              <Plus className="h-4 w-4 inline-block mr-1" />
              Add Field
            </button>
          </div>
        )}
      </div>

      {/* Add Field Modal */}
      {isAddFieldOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Add New Field</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Field Name</label>
                <input
                  type="text"
                  value={newField.name}
                  onChange={(e) => {
                    setNewField({ ...newField, name: e.target.value });
                    if (e.target.value) setNameError(null);
                  }}
                  className={`w-full px-3 py-2 border rounded-md ${nameError ? 'border-red-500' : ''}`}
                  placeholder="Enter field name"
                />
                {nameError && (
                  <p className="mt-1 text-sm text-red-600">{nameError}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Field Type</label>
                <select
                  className="w-full px-3 py-2 border rounded-md"
                  value={newField.type}
                  onChange={(e) => handleFieldTypeChange(e.target.value as FieldType)}
                >
                  <option value="String">String</option>
                  <option value="Number">Number</option>
                  <option value="Date">Date</option>
                  <option value="TextArea">TextArea</option>
                  <option value="Table">Table</option>
                  <option value="Image">Image</option>
                </select>
              </div>

              {showTableConfig && (
                <div className="border p-4 rounded-md">
                  <h4 className="font-medium mb-3">Table Configuration</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Rows</label>
                      <input
                        type="number"
                        min="1"
                        value={tableConfig.rows}
                        onChange={(e) => setTableConfig({ ...tableConfig, rows: parseInt(e.target.value) || 1 })}
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Columns</label>
                      <input
                        type="number"
                        min="1"
                        value={tableConfig.columns}
                        onChange={(e) => setTableConfig({ ...tableConfig, columns: parseInt(e.target.value) || 1 })}
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <button
                className="px-4 py-2 border rounded-md hover:bg-gray-50"
                onClick={() => setIsAddFieldOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70"
                onClick={handleSaveField}
              >
                Add Field
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
