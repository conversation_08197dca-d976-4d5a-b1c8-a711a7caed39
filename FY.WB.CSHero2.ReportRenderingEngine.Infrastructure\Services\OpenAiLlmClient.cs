using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Implementation of ILlmClient for OpenAI
    /// </summary>
    public class OpenAiLlmClient : ILlmClient
    {
        private readonly LlmConfig _config;
        private readonly ILogger _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private int _totalRequests;
        private int _successfulRequests;
        private int _failedRequests;
        private long _totalTokensUsed;
        private double _totalLatencyMs;

        /// <summary>
        /// Creates a new OpenAI LLM client
        /// </summary>
        /// <param name="config">The LLM configuration</param>
        /// <param name="logger">The logger</param>
        /// <param name="httpClientFactory">The HTTP client factory</param>
        public OpenAiLlmClient(LlmConfig config, ILogger logger, IHttpClientFactory httpClientFactory)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        /// <summary>
        /// Generates an HTML template using OpenAI's API
        /// </summary>
        /// <param name="prompt">The XML prompt</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The generated HTML</returns>
        public async Task<string> GenerateHtmlTemplateAsync(string prompt, CancellationToken cancellationToken = default)
        {
            _totalRequests++;

            try
            {
                _logger.LogInformation("Sending request to OpenAI API with model {Model}", _config.Model);

                var stopwatch = Stopwatch.StartNew();

                // Implement actual API call to OpenAI
                var requestBody = new
                {
                    model = _config.Model,
                    messages = new[]
                    {
                        new
                        {
                            role = "system",
                            content = "You are an expert HTML template generator. Generate clean, professional HTML based on the provided XML data and instructions."
                        },
                        new
                        {
                            role = "user",
                            content = prompt
                        }
                    },
                    max_tokens = _config.MaxTokens,
                    temperature = 0.1 // Low temperature for consistent output
                };

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(requestBody);
                var httpContent = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.ApiKey);

                var response = await httpClient.PostAsync("https://api.openai.com/v1/chat/completions", httpContent, cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("OpenAI API call failed with status {StatusCode}: {ErrorContent}",
                        response.StatusCode, errorContent);
                    throw new HttpRequestException($"OpenAI API call failed: {response.StatusCode}");
                }

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                using var jsonDoc = System.Text.Json.JsonDocument.Parse(responseContent);

                var result = jsonDoc.RootElement
                    .GetProperty("choices")[0]
                    .GetProperty("message")
                    .GetProperty("content")
                    .GetString() ?? "<div>No content generated</div>";

                stopwatch.Stop();
                _successfulRequests++;
                _totalLatencyMs += stopwatch.ElapsedMilliseconds;
                _totalTokensUsed += EstimateTokenCount(prompt, result);

                _logger.LogInformation("OpenAI request completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _failedRequests++;
                _logger.LogError(ex, "Error calling OpenAI API");
                throw;
            }
        }

        /// <summary>
        /// Generates React component code based on the provided prompt and context
        /// </summary>
        public async Task<string> GenerateReactComponentAsync(string prompt, ComponentGenerationContext context, CancellationToken cancellationToken = default)
        {
            _totalRequests++;

            try
            {
                _logger.LogInformation("Generating React component with OpenAI API using model {Model}", _config.Model);

                var stopwatch = Stopwatch.StartNew();

                var systemPrompt = BuildReactComponentSystemPrompt(context);
                var userPrompt = BuildReactComponentUserPrompt(prompt, context);

                var requestBody = new
                {
                    model = _config.Model,
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt },
                        new { role = "user", content = userPrompt }
                    },
                    max_tokens = _config.MaxTokens,
                    temperature = 0.3 // Slightly higher for creative component generation
                };

                var result = await SendOpenAIRequestAsync(requestBody, cancellationToken);

                stopwatch.Stop();
                _successfulRequests++;
                _totalLatencyMs += stopwatch.ElapsedMilliseconds;
                _totalTokensUsed += EstimateTokenCount(prompt, result);

                _logger.LogInformation("React component generation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _failedRequests++;
                _logger.LogError(ex, "Error generating React component with OpenAI API");
                throw;
            }
        }

        /// <summary>
        /// Validates React component code and provides feedback
        /// </summary>
        public async Task<ComponentValidationResult> ValidateComponentAsync(string componentCode, string framework = "NextJS", CancellationToken cancellationToken = default)
        {
            _totalRequests++;

            try
            {
                _logger.LogInformation("Validating React component with OpenAI API");

                var stopwatch = Stopwatch.StartNew();

                var systemPrompt = BuildValidationSystemPrompt(framework);
                var userPrompt = BuildValidationUserPrompt(componentCode, framework);

                var requestBody = new
                {
                    model = _config.Model,
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt },
                        new { role = "user", content = userPrompt }
                    },
                    max_tokens = _config.MaxTokens,
                    temperature = 0.1 // Low temperature for consistent validation
                };

                var result = await SendOpenAIRequestAsync(requestBody, cancellationToken);

                stopwatch.Stop();
                _successfulRequests++;
                _totalLatencyMs += stopwatch.ElapsedMilliseconds;
                _totalTokensUsed += EstimateTokenCount(userPrompt, result);

                _logger.LogInformation("Component validation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                return ParseValidationResult(result);
            }
            catch (Exception ex)
            {
                _failedRequests++;
                _logger.LogError(ex, "Error validating component with OpenAI API");
                return new ComponentValidationResult
                {
                    IsValid = false,
                    QualityScore = 0
                };
            }
        }

        /// <summary>
        /// Optimizes React component code for performance, accessibility, or other criteria
        /// </summary>
        public async Task<string> OptimizeComponentAsync(string componentCode, string optimizationType = "performance", CancellationToken cancellationToken = default)
        {
            _totalRequests++;

            try
            {
                _logger.LogInformation("Optimizing React component for {OptimizationType} with OpenAI API", optimizationType);

                var stopwatch = Stopwatch.StartNew();

                var systemPrompt = BuildOptimizationSystemPrompt(optimizationType);
                var userPrompt = BuildOptimizationUserPrompt(componentCode, optimizationType);

                var requestBody = new
                {
                    model = _config.Model,
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt },
                        new { role = "user", content = userPrompt }
                    },
                    max_tokens = _config.MaxTokens,
                    temperature = 0.2 // Low-medium temperature for optimization
                };

                var result = await SendOpenAIRequestAsync(requestBody, cancellationToken);

                stopwatch.Stop();
                _successfulRequests++;
                _totalLatencyMs += stopwatch.ElapsedMilliseconds;
                _totalTokensUsed += EstimateTokenCount(userPrompt, result);

                _logger.LogInformation("Component optimization completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _failedRequests++;
                _logger.LogError(ex, "Error optimizing component with OpenAI API");
                throw;
            }
        }

        /// <summary>
        /// Gets usage metrics for this client
        /// </summary>
        /// <returns>LLM usage metrics</returns>
        public Task<LlmMetrics> GetMetricsAsync()
        {
            return Task.FromResult(new LlmMetrics
            {
                TotalRequests = _totalRequests,
                SuccessfulRequests = _successfulRequests,
                FailedRequests = _failedRequests,
                AverageLatencyMs = _successfulRequests > 0 ? _totalLatencyMs / _successfulRequests : 0,
                TotalTokensUsed = _totalTokensUsed
            });
        }

        #region Private Helper Methods

        /// <summary>
        /// Sends a request to OpenAI API and returns the response content
        /// </summary>
        private async Task<string> SendOpenAIRequestAsync(object requestBody, CancellationToken cancellationToken)
        {
            var jsonContent = System.Text.Json.JsonSerializer.Serialize(requestBody);
            var httpContent = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.ApiKey);

            var response = await httpClient.PostAsync("https://api.openai.com/v1/chat/completions", httpContent, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("OpenAI API call failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"OpenAI API call failed: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            using var jsonDoc = System.Text.Json.JsonDocument.Parse(responseContent);

            return jsonDoc.RootElement
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString() ?? string.Empty;
        }

        /// <summary>
        /// Builds system prompt for React component generation
        /// </summary>
        private string BuildReactComponentSystemPrompt(ComponentGenerationContext context)
        {
            var framework = context.Framework;
            var styleFramework = context.StyleFramework;
            var useTypeScript = context.UseTypeScript;

            return $@"You are an expert {framework} developer specializing in generating high-quality, production-ready React components.

Guidelines:
- Generate {(useTypeScript ? "TypeScript" : "JavaScript")} code with proper type definitions
- Use {styleFramework} for styling
- Follow {framework} best practices and conventions
- Include proper error handling and loading states
- Optimize for performance with React.memo, useCallback, useMemo when appropriate
- Include accessibility features (ARIA attributes, semantic HTML)
- Write clean, maintainable, and well-documented code
- Use modern React patterns (hooks, functional components)
- Include proper prop validation
- Follow responsive design principles

Return only the component code without explanations or markdown formatting.";
        }

        /// <summary>
        /// Builds user prompt for React component generation
        /// </summary>
        private string BuildReactComponentUserPrompt(string prompt, ComponentGenerationContext context)
        {
            var componentName = ToPascalCase(context.SectionName);
            var dataJson = System.Text.Json.JsonSerializer.Serialize(context.Data, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });

            return $@"Generate a {context.Framework} React component with the following requirements:

Component Name: {componentName}
Section ID: {context.SectionId}
Component Type: {context.ComponentType}
Theme: {context.Theme}

Data Structure:
{dataJson}

Requirements:
{prompt}

Additional Context:
- Framework: {context.Framework}
- Style Framework: {context.StyleFramework}
- TypeScript: {context.UseTypeScript}
- Include Accessibility: {context.IncludeAccessibility}
- Include Responsive Design: {context.IncludeResponsiveDesign}
- Optimize for Performance: {context.OptimizeForPerformance}

Generate a complete, functional React component that meets these requirements.";
        }

        /// <summary>
        /// Builds system prompt for component validation
        /// </summary>
        private string BuildValidationSystemPrompt(string framework)
        {
            return $@"You are an expert code reviewer specializing in {framework} React components.

Your task is to validate React component code and provide detailed feedback in JSON format.

Validation Criteria:
1. Syntax errors and TypeScript type safety
2. React best practices and patterns
3. Performance optimizations
4. Accessibility compliance
5. Security vulnerabilities
6. Code maintainability
7. Framework-specific conventions

Return a JSON object with this structure:
{{
  ""isValid"": boolean,
  ""qualityScore"": number (0-100),
  ""performanceScore"": number (0-100),
  ""accessibilityScore"": number (0-100),
  ""maintainabilityScore"": number (0-100),
  ""bestPracticesScore"": number (0-100),
  ""securityScore"": number (0-100),
  ""errors"": [{{""code"": ""string"", ""message"": ""string"", ""line"": number, ""severity"": ""string""}}],
  ""warnings"": [{{""code"": ""string"", ""message"": ""string"", ""suggestion"": ""string""}}],
  ""suggestions"": [{{""type"": ""string"", ""message"": ""string"", ""priority"": number}}],
  ""detectedDependencies"": [""string""],
  ""estimatedBundleSize"": ""string""
}}";
        }

        /// <summary>
        /// Builds user prompt for component validation
        /// </summary>
        private string BuildValidationUserPrompt(string componentCode, string framework)
        {
            return $@"Please validate the following {framework} React component code:

```typescript
{componentCode}
```

Provide detailed validation results in the specified JSON format.";
        }

        /// <summary>
        /// Builds system prompt for component optimization
        /// </summary>
        private string BuildOptimizationSystemPrompt(string optimizationType)
        {
            var focus = optimizationType.ToLower() switch
            {
                "performance" => "Reducing re-renders, optimizing state management, lazy loading, memoization",
                "accessibility" => "ARIA attributes, keyboard navigation, screen reader support, semantic HTML",
                "bundle-size" => "Tree shaking, dynamic imports, removing unused code, optimizing dependencies",
                "maintainability" => "Code organization, reusability, documentation, type safety",
                "security" => "Input validation, XSS prevention, secure coding practices",
                _ => "General code quality and best practices"
            };

            return $@"You are an expert React developer specializing in code optimization.

Your task is to optimize React component code for {optimizationType}.

Focus areas for {optimizationType}:
{focus}

Guidelines:
- Maintain the original functionality
- Preserve the component's API (props interface)
- Add comments explaining optimizations
- Follow React and modern JavaScript best practices
- Ensure the optimized code is production-ready

Return only the optimized component code without explanations.";
        }

        /// <summary>
        /// Builds user prompt for component optimization
        /// </summary>
        private string BuildOptimizationUserPrompt(string componentCode, string optimizationType)
        {
            return $@"Please optimize the following React component for {optimizationType}:

```typescript
{componentCode}
```

Return the optimized component code.";
        }

        /// <summary>
        /// Parses validation result from LLM response
        /// </summary>
        private ComponentValidationResult ParseValidationResult(string response)
        {
            try
            {
                // Try to extract JSON from the response
                var jsonStart = response.IndexOf('{');
                var jsonEnd = response.LastIndexOf('}');

                if (jsonStart >= 0 && jsonEnd > jsonStart)
                {
                    var jsonContent = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    return System.Text.Json.JsonSerializer.Deserialize<ComponentValidationResult>(jsonContent, options)
                           ?? new ComponentValidationResult { IsValid = false, QualityScore = 0 };
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse validation result JSON");
            }

            // Fallback: create a basic validation result
            return new ComponentValidationResult
            {
                IsValid = !response.ToLower().Contains("error"),
                QualityScore = response.ToLower().Contains("error") ? 30 : 70
            };
        }

        /// <summary>
        /// Converts string to PascalCase
        /// </summary>
        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return "Component";

            var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new System.Text.StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    result.Append(char.ToUpper(word[0]));
                    if (word.Length > 1)
                    {
                        result.Append(word.Substring(1).ToLower());
                    }
                }
            }

            return result.Length > 0 ? result.ToString() : "Component";
        }

        /// <summary>
        /// Estimates token count for prompt and response
        /// </summary>
        /// <param name="prompt">The input prompt</param>
        /// <param name="response">The LLM response</param>
        /// <returns>Estimated token count</returns>
        private static long EstimateTokenCount(string prompt, string response)
        {
            // Simple estimation - in a real implementation, use a proper tokenizer
            // For many models, 1 token is roughly 4 characters
            return (prompt.Length + response.Length) / 4;
        }

        #endregion
    }
}
