using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Main orchestrator for the report rendering process.
    /// Coordinates between data retrieval, prompt building, and LLM interaction.
    /// </summary>
    public class ReportRenderer
    {
        private readonly ILogger<ReportRenderer> _logger;
        private readonly IDatabaseService _database;
        private readonly ILlmClient _llm;
        private readonly IMemoryCache _cache;
        private readonly IHtmlValidator _validator;

        /// <summary>
        /// Creates a new instance of the ReportRenderer
        /// </summary>
        /// <param name="logger">Logger for diagnostic information</param>
        /// <param name="database">Service for retrieving template data</param>
        /// <param name="llm">Client for interacting with the LLM</param>
        /// <param name="cache">Optional cache for storing prompts</param>
        /// <param name="validator">Optional validator for HTML output</param>
        public ReportRenderer(
            ILogger<ReportRenderer> logger,
            IDatabaseService database,
            ILlmClient llm,
            IMemoryCache cache = null,
            IHtmlValidator validator = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _database = database ?? throw new ArgumentNullException(nameof(database));
            _llm = llm ?? throw new ArgumentNullException(nameof(llm));
            _cache = cache; // Optional dependency
            _validator = validator; // Optional dependency
        }

        /// <summary>
        /// Renders a document by coordinating the entire process from data retrieval to LLM processing.
        /// </summary>
        /// <param name="documentId">The unique identifier of the document to render</param>
        /// <param name="promptKey">The key for retrieving prompt instructions (defaults to standard render)</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The rendered HTML as a string</returns>
        /// <exception cref="DocumentRenderingException">Thrown when the rendering process fails</exception>
        public async Task<string> RenderAsync(
            Guid documentId, 
            string promptKey = "prompt.render.instructions",
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogInformation("Starting rendering process for DocumentId: {DocumentId}", documentId);

                // Fetch all required data in parallel for efficiency
                var metadataTask = _database.GetTemplateMetadataAsync(documentId, cancellationToken);
                var dataTask = _database.GetTemplateDataAsync(documentId, cancellationToken);
                var htmlTemplateTask = _database.GetExistingHtmlAsync(documentId, cancellationToken);
                
                // Get prompt from cache if available, otherwise from database
                var instructionPrompt = await GetCachedPromptAsync(promptKey, cancellationToken);
                
                // Wait for all parallel tasks to complete
                await Task.WhenAll(metadataTask, dataTask, htmlTemplateTask);
                
                var metadata = await metadataTask;
                var data = await dataTask;
                var htmlTemplate = await htmlTemplateTask;

                _logger.LogDebug("Data retrieval completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                
                // Build the prompt object and serialize to XML
                var renderRequest = PromptBuilder.BuildPromptObject(metadata, data, htmlTemplate, instructionPrompt);
                var xmlPrompt = XmlPromptSerializer.SerializeToXml(renderRequest);
                
                _logger.LogDebug("Prompt preparation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                // Send to LLM for HTML generation with timeout protection
                using var llmCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                llmCts.CancelAfter(TimeSpan.FromSeconds(30)); // Configurable timeout
                
                var htmlResult = await _llm.GenerateHtmlTemplateAsync(xmlPrompt, llmCts.Token);
                
                _logger.LogDebug("LLM rendering completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                // Validate HTML if validator is available
                if (_validator != null)
                {
                    var validationResult = await _validator.ValidateAsync(htmlResult, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        _logger.LogWarning("HTML validation failed: {ValidationErrors}", 
                            string.Join(", ", validationResult.Errors));
                    }
                }

                stopwatch.Stop();
                _logger.LogInformation("Rendering completed for DocumentId: {DocumentId} in {TotalElapsedMs}ms", 
                    documentId, stopwatch.ElapsedMilliseconds);
                
                return htmlResult;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Rendering operation was cancelled for DocumentId: {DocumentId}", documentId);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering document: {DocumentId}", documentId);
                throw new DocumentRenderingException($"Failed to render document {documentId}", ex);
            }
        }

        /// <summary>
        /// Retrieves a prompt from cache if available, otherwise from the database
        /// </summary>
        /// <param name="promptKey">The key identifying the prompt</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The prompt text</returns>
        private async Task<string> GetCachedPromptAsync(string promptKey, CancellationToken cancellationToken)
        {
            if (_cache == null)
            {
                return await _database.GetPromptByKeyAsync(promptKey, cancellationToken);
            }

            return await _cache.GetOrCreateAsync(
                $"prompt:{promptKey}", 
                async entry => 
                {
                    entry.SetAbsoluteExpiration(TimeSpan.FromHours(1)); // Configurable expiration
                    return await _database.GetPromptByKeyAsync(promptKey, cancellationToken);
                });
        }
    }

    /// <summary>
    /// Custom exception for document rendering failures
    /// </summary>
    public class DocumentRenderingException : Exception
    {
        /// <summary>
        /// Creates a new DocumentRenderingException with the specified message
        /// </summary>
        /// <param name="message">The exception message</param>
        public DocumentRenderingException(string message) : base(message) { }
        
        /// <summary>
        /// Creates a new DocumentRenderingException with the specified message and inner exception
        /// </summary>
        /// <param name="message">The exception message</param>
        /// <param name="innerException">The inner exception</param>
        public DocumentRenderingException(string message, Exception innerException) 
            : base(message, innerException) { }
    }
}
