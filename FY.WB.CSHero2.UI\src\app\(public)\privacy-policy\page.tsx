"use client";
import { motion } from 'framer-motion';

export default function PrivacyPolicyPage() {
  return (
    <div className="gradient-primary-secondary">
      <div className="container mx-auto px-4 py-24 md:px-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4 text-white">Privacy Policy</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Learn about us
          </p>
        </motion.div>
      </div>
    </div>
  );
}
