export interface Invoice {
  id: string;
  tenantId: string;
  date: string;
  type: 'Invoice' | 'Credit' | 'Refund' | 'Payment';
  orderNumber: string;
  plans: string;
  amount: string;
  status: 'paid' | 'pending' | 'overdue' | 'canceled';
  createdAt: string;
  updatedAt?: string;
}

export interface InvoiceCreateInput {
  tenantId: string;
  type: 'Invoice' | 'Credit' | 'Refund' | 'Payment';
  orderNumber: string;
  plans: string;
  amount: string;
  status: 'paid' | 'pending' | 'overdue' | 'canceled';
}

export interface InvoiceUpdateInput {
  type?: 'Invoice' | 'Credit' | 'Refund' | 'Payment';
  orderNumber?: string;
  plans?: string;
  amount?: string;
  status?: 'paid' | 'pending' | 'overdue' | 'canceled';
}
