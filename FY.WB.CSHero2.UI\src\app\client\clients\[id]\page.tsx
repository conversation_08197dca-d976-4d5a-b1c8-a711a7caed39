'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { ArrowLeft, Calendar, Building, Phone, MapPin, Users, Briefcase, Clock, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Client } from '@/types/client';
import { Report } from '@/types/report';
import Link from 'next/link';
import { getClientById, getReports } from '@/lib/api';
import { useToast } from '@/components/providers/toast-provider';

export default function ClientProfilePage() {
  const params = useParams();
  const clientId = params.id as string;
  const [client, setClient] = useState<Client | null>(null);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchClientData = async () => {
      try {
        setLoading(true);
        const clientData = await getClientById(clientId);
        setClient(clientData);

        // Fetch reports for this client
        const reportsResponse = await getReports({ clientId });
        setReports(reportsResponse.items || []);
        setError(null);
      } catch (err) {
        console.error('Error fetching client data:', err);
        setError('Failed to fetch client data');
        setReports([]); // Ensure reports is always an array even on error
        toast({
          title: 'Error',
          description: 'Failed to fetch client data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchClientData();
  }, [clientId, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex items-center justify-center h-64">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !client) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex flex-col items-center justify-center h-64 gap-4">
          <p className="text-xl text-red-600">{error || 'Client not found'}</p>
          <Button asChild variant="outline">
            <Link href="/client/clients">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Clients
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'active':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20';
      case 'inactive':
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
      default:
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
    }
  };

  const getReportStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'completed':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20';
      case 'in progress':
        return 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20';
      case 'under review':
        return 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20';
      case 'draft':
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
      default:
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
    }
  };

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Button asChild variant="outline" size="sm" className="h-9">
              <Link href="/client/clients">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Clients
              </Link>
            </Button>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
              {client.status}
            </span>
          </div>

          <h1 className="text-4xl font-bold mb-2 text-[rgb(var(--primary))]">{client.companyName}</h1>
          <p className="text-xl text-gray-600 mb-6">
            Client profile and related reports
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Contact Information</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Contact Person</p>
                  <p className="font-medium">{client.name}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Building className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Company</p>
                  <p className="font-medium">{client.companyName}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{client.email}</p>
                </div>
              </div>

              {client.phone && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">{client.phone}</p>
                  </div>
                </div>
              )}

              {client.address && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <MapPin className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Address</p>
                    <p className="font-medium">{client.address}</p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Company Details</h2>
            <div className="space-y-4">
              {client.industry && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Briefcase className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Industry</p>
                    <p className="font-medium">{client.industry}</p>
                  </div>
                </div>
              )}

              {client.companySize && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Company Size</p>
                    <p className="font-medium">{client.companySize}</p>
                  </div>
                </div>
              )}

              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Client Since</p>
                  <p className="font-medium">{formatDate(client.createdAt)}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Clock className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Last Updated</p>
                  <p className="font-medium">{formatDate(client.updatedAt)}</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Reports Summary</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Total Reports</p>
                <p className="font-medium text-lg">{reports.length}</p>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="font-medium text-lg">{reports.filter(r => r.status === 'Completed').length}</p>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">In Progress</p>
                <p className="font-medium text-lg">{reports.filter(r => r.status === 'In Progress').length}</p>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Under Review</p>
                <p className="font-medium text-lg">{reports.filter(r => r.status === 'Under Review').length}</p>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Draft</p>
                <p className="font-medium text-lg">{reports.filter(r => r.status === 'Draft').length}</p>
              </div>

              <div className="pt-4">
                <Button asChild className="w-full gradient-primary-secondary text-white">
                  <Link href={`/client/reports/create?clientId=${client.id}`}>
                    Create New Report
                  </Link>
                </Button>
              </div>
            </div>
          </Card>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6 text-[rgb(var(--primary))]">Client Reports</h2>

          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="all">All Reports</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="in-progress">In Progress</TabsTrigger>
              <TabsTrigger value="draft">Draft</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reports.length === 0 ? (
                  <div className="col-span-3 py-12 text-center">
                    <p className="text-muted-foreground">No reports found for this client</p>
                    <Button asChild className="mt-4 gradient-primary-secondary text-white">
                      <Link href={`/client/reports/create?clientId=${client.id}`}>
                        Create First Report
                      </Link>
                    </Button>
                  </div>
                ) : (
                  reports.map((report) => (
                    <ReportCard key={report.id} report={report} />
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="completed">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reports.filter(r => r.status === 'Completed').length === 0 ? (
                  <div className="col-span-3 py-12 text-center">
                    <p className="text-muted-foreground">No completed reports found</p>
                  </div>
                ) : (
                  reports
                    .filter(r => r.status === 'Completed')
                    .map((report) => (
                      <ReportCard key={report.id} report={report} />
                    ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="in-progress">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reports.filter(r => r.status === 'In Progress' || r.status === 'Under Review').length === 0 ? (
                  <div className="col-span-3 py-12 text-center">
                    <p className="text-muted-foreground">No in-progress reports found</p>
                  </div>
                ) : (
                  reports
                    .filter(r => r.status === 'In Progress' || r.status === 'Under Review')
                    .map((report) => (
                      <ReportCard key={report.id} report={report} />
                    ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="draft">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reports.filter(r => r.status === 'Draft').length === 0 ? (
                  <div className="col-span-3 py-12 text-center">
                    <p className="text-muted-foreground">No draft reports found</p>
                  </div>
                ) : (
                  reports
                    .filter(r => r.status === 'Draft')
                    .map((report) => (
                      <ReportCard key={report.id} report={report} />
                    ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </motion.div>
    </div>
  );
}

interface ReportCardProps {
  report: Report;
}

function ReportCard({ report }: ReportCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'completed':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20';
      case 'in progress':
        return 'bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-700 ring-1 ring-blue-600/20';
      case 'under review':
        return 'bg-gradient-to-r from-amber-50 to-amber-100/50 text-amber-700 ring-1 ring-amber-600/20';
      case 'draft':
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
      default:
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
    }
  };

  return (
    <Card className="overflow-hidden bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="inline-flex items-center rounded-md bg-primary/10 text-primary px-2 py-1 text-sm font-medium">
            {report.category}
          </div>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
            {report.status}
          </span>
        </div>

        <Link href={`/reports/${report.id}`} className="block group">
          <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
            {report.name}
          </h3>
        </Link>

        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <Calendar className="h-4 w-4 mr-1" />
          <span>Created: {formatDate(report.creationTime)}</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="inline-flex items-center justify-center rounded-md bg-muted/50 w-8 h-8 text-sm font-medium">
            {report.slideCount}
          </div>
          <div className="text-sm text-muted-foreground">
            By {report.author}
          </div>
        </div>
      </div>

      <div className="border-t p-4 bg-muted/5">
        <div className="flex justify-between">
          <Button asChild variant="outline" size="sm" className="h-8 px-3 hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors">
            <Link href={`/reports/${report.id}/view`}>
              View Report
            </Link>
          </Button>
          <Button asChild variant="ghost" size="sm" className="h-8 px-3 hover:bg-primary/5 hover:text-primary transition-colors">
            <Link href={`/reports/${report.id}/edit`}>
              Edit
            </Link>
          </Button>
        </div>
      </div>
    </Card>
  );
}
