# DbContext Threading Fix - Architecture Diagrams

## Current Problem Architecture

```mermaid
sequenceDiagram
    participant Client as Frontend Client
    participant Controller as ReportRenderingController
    participant Renderer as ReportRenderer
    participant DB as DatabaseService
    participant Context as ApplicationDbContext (Scoped)
    
    Client->>Controller: GET /api/reports/{id}/render
    Controller->>Renderer: RenderAsync(documentId)
    
    Note over Renderer: Creates 3 parallel tasks
    
    par Parallel Task 1
        Renderer->>DB: GetTemplateMetadataAsync()
        DB->>Context: Templates.FirstOrDefaultAsync()
    and Parallel Task 2
        Renderer->>DB: GetTemplateDataAsync()
        DB->>Context: Reports.FirstOrDefaultAsync()
    and Parallel Task 3
        Renderer->>DB: GetExistingHtmlAsync()
        DB->>Context: ReportVersions.FirstOrDefaultAsync()
    end
    
    Note over Context: ❌ THREADING ERROR!<br/>Multiple operations on same DbContext
    Context-->>DB: InvalidOperationException
    DB-->>Renderer: Exception
    Renderer-->>Controller: Exception
    Controller-->>Client: 500 Error
```

## Proposed Solution Architecture

```mermaid
sequenceDiagram
    participant Client as Frontend Client
    participant Controller as ReportRenderingController
    participant Renderer as ReportRenderer
    participant DB as DatabaseService
    participant Factory as TenantDbContextFactory
    participant Context1 as DbContext Instance 1
    participant Context2 as DbContext Instance 2
    participant Context3 as DbContext Instance 3
    
    Client->>Controller: GET /api/reports/{id}/render
    Controller->>Renderer: RenderAsync(documentId)
    
    Note over Renderer: Creates 3 parallel tasks
    
    par Parallel Task 1
        Renderer->>DB: GetTemplateMetadataAsync()
        DB->>Factory: CreateDbContext()
        Factory->>Context1: New instance with tenant context
        DB->>Context1: Templates.FirstOrDefaultAsync()
        Context1-->>DB: Results
        Note over Context1: Disposed after use
    and Parallel Task 2
        Renderer->>DB: GetTemplateDataAsync()
        DB->>Factory: CreateDbContext()
        Factory->>Context2: New instance with tenant context
        DB->>Context2: Reports.FirstOrDefaultAsync()
        Context2-->>DB: Results
        Note over Context2: Disposed after use
    and Parallel Task 3
        Renderer->>DB: GetExistingHtmlAsync()
        DB->>Factory: CreateDbContext()
        Factory->>Context3: New instance with tenant context
        DB->>Context3: ReportVersions.FirstOrDefaultAsync()
        Context3-->>DB: Results
        Note over Context3: Disposed after use
    end
    
    Note over DB: ✅ THREAD SAFE!<br/>Each task has own DbContext
    DB-->>Renderer: All results
    Renderer-->>Controller: Rendered HTML
    Controller-->>Client: 200 Success
```

## Dependency Injection Architecture

```mermaid
graph TB
    subgraph "Current DI Container (Problematic)"
        A[Scoped ApplicationDbContext] --> B[Scoped DatabaseService]
        B --> C[ReportRenderer uses same context]
        C --> D[Threading Issues]
    end
    
    subgraph "Proposed DI Container (Solution)"
        E[Singleton IDbContextFactory] --> F[Singleton TenantDbContextFactory]
        F --> G[Scoped DatabaseService]
        G --> H[Creates new context per operation]
        H --> I[Thread Safe Operations]
        
        J[Scoped ApplicationDbContext] --> K[Still available for other services]
    end
    
    style D fill:#ffcccc
    style I fill:#ccffcc
```

## Multi-Tenant Context Flow

```mermaid
graph LR
    subgraph "Request Context"
        A[HTTP Request] --> B[Tenant Resolution]
        B --> C[IMultiTenantContextAccessor]
    end
    
    subgraph "Factory Pattern"
        D[TenantDbContextFactory] --> E[CreateDbContext()]
        E --> F[Get Current Tenant Info]
        F --> G[Create New DbContext]
        G --> H[Inject Tenant Context]
        H --> I[Return Tenant-Aware Context]
    end
    
    C --> F
    
    subgraph "Database Operations"
        I --> J[Query with Tenant Filter]
        J --> K[Tenant-Isolated Results]
    end
    
    style K fill:#ccffcc
```

## Component Interaction Diagram

```mermaid
classDiagram
    class ITenantDbContextFactory {
        <<interface>>
        +CreateDbContext() ApplicationDbContext
        +CreateDbContext(tenantId) ApplicationDbContext
    }
    
    class TenantDbContextFactory {
        -IDbContextFactory~ApplicationDbContext~ _factory
        -IServiceProvider _serviceProvider
        -ILogger _logger
        +CreateDbContext() ApplicationDbContext
        +CreateDbContext(tenantId) ApplicationDbContext
    }
    
    class DatabaseService {
        -ITenantDbContextFactory _contextFactory
        -ILogger _logger
        +GetTemplateMetadataAsync() Task~DocumentTemplateMetadata~
        +GetTemplateDataAsync() Task~Dictionary~
        +GetExistingHtmlAsync() Task~string~
    }
    
    class ReportRenderer {
        -IDatabaseService _database
        +RenderAsync() Task~string~
    }
    
    class ApplicationDbContext {
        +Templates DbSet~Template~
        +Reports DbSet~Report~
        +ReportVersions DbSet~ReportVersion~
    }
    
    ITenantDbContextFactory <|-- TenantDbContextFactory
    TenantDbContextFactory --> ApplicationDbContext : creates
    DatabaseService --> ITenantDbContextFactory : uses
    ReportRenderer --> DatabaseService : uses
    
    note for TenantDbContextFactory "Creates new DbContext\ninstance per operation\nwith proper tenant context"
    note for DatabaseService "Each method gets\nits own DbContext\ninstance"
```

## Threading Safety Comparison

```mermaid
graph TB
    subgraph "Before: Threading Issues"
        A1[Request 1] --> B1[ReportRenderer]
        A2[Request 2] --> B2[ReportRenderer]
        A3[Request 3] --> B3[ReportRenderer]
        
        B1 --> C[Shared DatabaseService]
        B2 --> C
        B3 --> C
        
        C --> D[Single ApplicationDbContext]
        
        D --> E[❌ Concurrent Access]
        E --> F[InvalidOperationException]
    end
    
    subgraph "After: Thread Safe"
        G1[Request 1] --> H1[ReportRenderer]
        G2[Request 2] --> H2[ReportRenderer]
        G3[Request 3] --> H3[ReportRenderer]
        
        H1 --> I1[DatabaseService Instance]
        H2 --> I2[DatabaseService Instance]
        H3 --> I3[DatabaseService Instance]
        
        I1 --> J[TenantDbContextFactory]
        I2 --> J
        I3 --> J
        
        J --> K1[DbContext 1]
        J --> K2[DbContext 2]
        J --> K3[DbContext 3]
        
        K1 --> L[✅ Isolated Operations]
        K2 --> L
        K3 --> L
    end
    
    style F fill:#ffcccc
    style L fill:#ccffcc
```

## Performance Impact Analysis

```mermaid
graph LR
    subgraph "Memory Usage"
        A[Before: 1 DbContext] --> B[After: N DbContexts]
        B --> C[Minimal Impact<br/>Short-lived instances]
    end
    
    subgraph "Database Connections"
        D[Before: 1 Connection] --> E[After: N Connections]
        E --> F[Connection Pool<br/>Handles efficiently]
    end
    
    subgraph "Performance"
        G[Before: Sequential Risk] --> H[After: Parallel Safe]
        H --> I[Better Throughput<br/>No blocking]
    end
    
    style C fill:#ffffcc
    style F fill:#ffffcc
    style I fill:#ccffcc
```

## Implementation Timeline

```mermaid
gantt
    title DbContext Threading Fix Implementation
    dateFormat  YYYY-MM-DD
    section Phase 1: Foundation
    Create ITenantDbContextFactory    :done, p1, 2025-01-01, 1d
    Implement TenantDbContextFactory  :done, p2, after p1, 2d
    section Phase 2: Integration
    Update DatabaseService           :active, p3, after p2, 2d
    Update DI Configuration          :p4, after p3, 1d
    section Phase 3: Testing
    Unit Tests                       :p5, after p4, 2d
    Integration Tests                :p6, after p5, 2d
    Performance Testing              :p7, after p6, 1d
    section Phase 4: Deployment
    Code Review                      :p8, after p7, 1d
    Production Deployment            :p9, after p8, 1d
```

This architecture ensures thread safety while maintaining the multi-tenant capabilities and performance characteristics required for a SaaS application.