import { NextResponse } from 'next/server';
import { API_BASE_URL } from '@/lib/constants';

export async function GET() {
  try {

    console.log(`Testing connection to backend at: ${API_BASE_URL}/api/ping`);

    const response = await fetch(`${API_BASE_URL}/api/ping`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      console.error('Backend health check failed with status:', response.status);
      return NextResponse.json(
        { status: 'error', message: 'Backend connection failed', backendStatus: response.status },
        { status: 500 }
      );
    }

    const data = await response.json();
    console.log('Backend health check successful:', data);

    return NextResponse.json({
      status: 'ok',
      frontendTime: new Date().toISOString(),
      backendResponse: data
    });
  } catch (error) {
    console.error('Error connecting to backend:', error);
    return NextResponse.json(
      { status: 'error', message: error instanceof Error ? error.message : 'Failed to connect to backend' },
      { status: 500 }
    );
  }
}
