using System;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using Azure.Identity;

namespace CosmosDbSetup
{
    class Program
    {
        // Configuration - Update these values
        private static readonly string CosmosEndpoint = "https://your-cosmos-account.documents.azure.com:443/";
        private static readonly string CosmosKey = "your-cosmos-primary-key";
        private static readonly string DatabaseName = "ReportRenderingEngine";
        private static readonly string BlobConnectionString = "your-blob-storage-connection-string";
        
        // Container configurations
        private static readonly (string Name, string PartitionKey, int? Throughput)[] Containers = new[]
        {
            ("ReportStyles", "/tenantId", 400),
            ("ReportTemplates", "/tenantId", 400),
            ("ReportData", "/tenantId", 400),
            ("ReportMetadata", "/tenantId", 400)
        };
        
        private static readonly string[] BlobContainers = new[]
        {
            "report-templates",
            "report-data",
            "report-outputs",
            "report-assets"
        };

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Cosmos DB and Blob Storage Setup ===");
            Console.WriteLine();
            
            try
            {
                await SetupCosmosDb();
                await SetupBlobStorage();
                
                Console.WriteLine();
                Console.WriteLine("✅ Setup completed successfully!");
                Console.WriteLine();
                Console.WriteLine("Next steps:");
                Console.WriteLine("1. Update your appsettings.json with the connection details");
                Console.WriteLine("2. Run your application to test the connections");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during setup: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static async Task SetupCosmosDb()
        {
            Console.WriteLine("🚀 Setting up Cosmos DB...");
            
            using var cosmosClient = new CosmosClient(CosmosEndpoint, CosmosKey);
            
            // Create database
            Console.WriteLine($"Creating database: {DatabaseName}");
            var databaseResponse = await cosmosClient.CreateDatabaseIfNotExistsAsync(DatabaseName);
            var database = databaseResponse.Database;
            
            Console.WriteLine($"✅ Database created/exists: {DatabaseName}");
            Console.WriteLine($"   Request charge: {databaseResponse.RequestCharge} RUs");
            
            // Create containers
            foreach (var (name, partitionKey, throughput) in Containers)
            {
                Console.WriteLine($"Creating container: {name}");
                
                var containerProperties = new ContainerProperties(name, partitionKey);
                
                // Add indexing policy for better performance
                containerProperties.IndexingPolicy.IndexingMode = IndexingMode.Consistent;
                containerProperties.IndexingPolicy.Automatic = true;
                
                // Add common paths for indexing
                containerProperties.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/tenantId/?" });
                containerProperties.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/id/?" });
                containerProperties.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/createdAt/?" });
                containerProperties.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/updatedAt/?" });
                
                var containerResponse = await database.CreateContainerIfNotExistsAsync(
                    containerProperties, 
                    throughput);
                
                Console.WriteLine($"✅ Container created/exists: {name}");
                Console.WriteLine($"   Partition key: {partitionKey}");
                Console.WriteLine($"   Throughput: {throughput} RUs");
                Console.WriteLine($"   Request charge: {containerResponse.RequestCharge} RUs");
            }
        }

        private static async Task SetupBlobStorage()
        {
            Console.WriteLine();
            Console.WriteLine("🚀 Setting up Blob Storage...");
            
            var blobServiceClient = new BlobServiceClient(BlobConnectionString);
            
            foreach (var containerName in BlobContainers)
            {
                Console.WriteLine($"Creating blob container: {containerName}");
                
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                await containerClient.CreateIfNotExistsAsync(Azure.Storage.Blobs.Models.PublicAccessType.None);
                
                Console.WriteLine($"✅ Blob container created/exists: {containerName}");
            }
        }
    }
}
