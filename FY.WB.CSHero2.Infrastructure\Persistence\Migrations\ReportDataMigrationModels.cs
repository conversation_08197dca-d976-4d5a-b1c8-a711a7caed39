using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Migrations
{
    /// <summary>
    /// Model for deserializing existing report content JSON
    /// </summary>
    public class ReportContentModel
    {
        [JsonPropertyName("template")]
        public string? Template { get; set; }

        [JsonPropertyName("sections")]
        public List<ReportSectionModel>? Sections { get; set; }
    }

    /// <summary>
    /// Model for deserializing report section JSON
    /// </summary>
    public class ReportSectionModel
    {
        [JsonPropertyName("section-title")]
        public string? SectionTitle { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("content")]
        public object? Content { get; set; }
    }
}
