export interface Subscription {
  id: string;
  name: string;
  description: string;
  type: 'basic' | 'professional' | 'enterprise';
  billingCycle: 'monthly' | 'quarterly' | 'annual';
  price: number;
  features: string[];
  isPopular?: boolean;
  trialDays?: number;
  discountPercentage?: number;
}

export interface PaginatedSubscriptionResponse {
  items: Subscription[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
