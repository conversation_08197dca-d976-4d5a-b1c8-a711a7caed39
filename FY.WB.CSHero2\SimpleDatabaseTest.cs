using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace FY.WB.CSHero2
{
    public class SimpleDatabaseTest
    {
        public static async Task Main(string[] args)
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            // Get connection string
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            Console.WriteLine("Using connection string from appsettings.json:");
            Console.WriteLine(connectionString);
            Console.WriteLine();

            // Add timeout parameter if not present
            if (!connectionString.Contains("Connection Timeout=") && !connectionString.Contains("Connect Timeout="))
            {
                connectionString += ";Connection Timeout=30";
            }

            // Add TrustServerCertificate if not present
            if (!connectionString.Contains("TrustServerCertificate="))
            {
                connectionString += ";TrustServerCertificate=True";
            }

            Console.WriteLine("Modified connection string:");
            Console.WriteLine(connectionString);
            Console.WriteLine();

            // Test the connection
            Console.WriteLine("Testing database connection...");
            DbTest.TestConnection(connectionString);

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
