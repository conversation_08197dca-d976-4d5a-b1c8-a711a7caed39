import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';
import type { Invoice, InvoiceCreateInput, InvoiceUpdateInput } from '@/types/invoice';

// Define the database structure
interface Database {
  invoices: Invoice[];
  [key: string]: any;
}

// Sample invoice data
const invoiceData: Invoice[] = [
  {
    id: "invoice-1",
    tenantId: "1",
    date: "2025-03-15",
    type: "Invoice",
    orderNumber: "INV-20250315-001",
    plans: "Professional Plan - Monthly",
    amount: "US$49.99",
    status: "paid",
    createdAt: "2025-03-15T00:00:00Z"
  },
  {
    id: "invoice-2",
    tenantId: "1",
    date: "2025-02-15",
    type: "Invoice",
    orderNumber: "INV-20250215-001",
    plans: "Professional Plan - Monthly",
    amount: "US$49.99",
    status: "paid",
    createdAt: "2025-02-15T00:00:00Z"
  },
  {
    id: "invoice-3",
    tenantId: "1",
    date: "2025-01-15",
    type: "Invoice",
    orderNumber: "INV-20250115-001",
    plans: "Professional Plan - Monthly",
    amount: "US$49.99",
    status: "paid",
    createdAt: "2025-01-15T00:00:00Z"
  },
  {
    id: "invoice-4",
    tenantId: "2",
    date: "2025-02-20",
    type: "Invoice",
    orderNumber: "INV-20250220-001",
    plans: "Enterprise Plan - Annual",
    amount: "US$5999.88",
    status: "overdue",
    createdAt: "2025-02-20T00:00:00Z"
  },
  {
    id: "invoice-5",
    tenantId: "3",
    date: "2025-03-05",
    type: "Invoice",
    orderNumber: "INV-20250305-001",
    plans: "Basic Plan - Quarterly",
    amount: "US$89.97",
    status: "paid",
    createdAt: "2025-03-05T00:00:00Z"
  },
  {
    id: "invoice-6",
    tenantId: "3",
    date: "2024-12-05",
    type: "Invoice",
    orderNumber: "INV-20241205-001",
    plans: "Basic Plan - Quarterly",
    amount: "US$89.97",
    status: "paid",
    createdAt: "2024-12-05T00:00:00Z"
  },
  {
    id: "invoice-7",
    tenantId: "2",
    date: "2025-03-10",
    type: "Payment",
    orderNumber: "PMT-20250310-001",
    plans: "Manual payment for past due invoice",
    amount: "US$2999.94",
    status: "paid",
    createdAt: "2025-03-10T00:00:00Z"
  },
  {
    id: "invoice-8",
    tenantId: "1",
    date: "2025-03-05",
    type: "Refund",
    orderNumber: "REF-20250305-001",
    plans: "Partial refund for service outage",
    amount: "US$16.66",
    status: "paid",
    createdAt: "2025-03-05T00:00:00Z"
  }
];

/**
 * Initialize the invoices data in the database
 */
async function initializeInvoices(): Promise<void> {
  const data = await readJsonFile<any>();
  
  // Initialize invoices array if it doesn't exist
  if (!data.invoices) {
    data.invoices = invoiceData;
    await writeJsonFile('db.json', data);
  }
}

// Get all invoices with optional filtering, sorting, and pagination
export async function getInvoices({
  query = {},
  sortBy,
  order,
  page,
  limit
}: {
  query?: Record<string, string | string[]>;
  sortBy?: string;
  order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
} = {}) {
  const db = await readJsonFile<Database>();
  
  // Filter logic
  let invoices = db.invoices || [];
  
  // Apply filters from query
  Object.entries(query).forEach(([key, value]) => {
    if (value) {
      invoices = invoices.filter((invoice: any) => {
        if (Array.isArray(value)) {
          return value.includes(String(invoice[key]));
        }
        return String(invoice[key]) === String(value);
      });
    }
  });
  
  // Apply sorting
  if (sortBy) {
    invoices = invoices.sort((a: any, b: any) => {
      if (order === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
  } else {
    // Default sort by date descending
    invoices = invoices.sort((a: any, b: any) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
  }
  
  // Apply pagination
  if (page !== undefined && limit !== undefined) {
    const start = (page - 1) * limit;
    invoices = invoices.slice(start, start + limit);
  }
  
  return invoices;
}

// Get invoices for a specific tenant
export async function getInvoicesByTenantId(tenantId: string): Promise<Invoice[]> {
  const db = await readJsonFile<Database>();
  const invoices = db.invoices || [];
  return invoices.filter((invoice: Invoice) => invoice.tenantId === tenantId)
    .sort((a: Invoice, b: Invoice) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

// Get a single invoice by ID
export async function getInvoiceById(id: string): Promise<Invoice | null> {
  const db = await readJsonFile<Database>();
  const invoices = db.invoices || [];
  return invoices.find((invoice: Invoice) => invoice.id === id) || null;
}

// Create a new invoice
export async function createInvoice(data: InvoiceCreateInput): Promise<Invoice> {
  const db = await readJsonFile<Database>();
  const invoices = db.invoices || [];
  
  // Create a new invoice with generated ID
  const newInvoice: Invoice = {
    id: `invoice-${Date.now()}`,
    date: new Date().toISOString().split('T')[0],
    createdAt: new Date().toISOString(),
    ...data
  };
  
  // Add to collection 
  invoices.push(newInvoice);
  db.invoices = invoices;
  
  // In a real application, you would save the DB here
  // For our in-memory approach, this is sufficient
  
  return newInvoice;
}

// Update an existing invoice
export async function updateInvoice(id: string, data: InvoiceUpdateInput): Promise<Invoice | null> {
  const db = await readJsonFile<Database>();
  const invoices = db.invoices || [];
  const index = invoices.findIndex((invoice: Invoice) => invoice.id === id);
  
  if (index === -1) return null;
  
  // Update the invoice
  const updatedInvoice = {
    ...invoices[index],
    ...data,
    updatedAt: new Date().toISOString()
  };
  
  invoices[index] = updatedInvoice;
  db.invoices = invoices;
  
  return updatedInvoice;
}

// Delete an invoice
export async function deleteInvoice(id: string): Promise<boolean> {
  const db = await readJsonFile<Database>();
  const invoices = db.invoices || [];
  const index = invoices.findIndex((invoice: Invoice) => invoice.id === id);
  
  if (index === -1) return false;
  
  invoices.splice(index, 1);
  db.invoices = invoices;
  
  return true;
}
