using System.Collections.Generic;
using System.Xml.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models
{
    /// <summary>
    /// Represents a section in the render request
    /// </summary>
    public class RenderSection
    {
        /// <summary>
        /// Unique identifier for the section
        /// </summary>
        [XmlAttribute("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the section
        /// </summary>
        [XmlAttribute("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the section
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Fields contained within this section
        /// </summary>
        [XmlArray("Fields")]
        [XmlArrayItem("Field")]
        public List<RenderField> Fields { get; set; } = new List<RenderField>();
    }
}
