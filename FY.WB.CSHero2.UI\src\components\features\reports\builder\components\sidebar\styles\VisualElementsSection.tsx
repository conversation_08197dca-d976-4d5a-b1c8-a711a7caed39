"use client";

import { VisualElementOptions } from "../../../types";
import { visualElementsConfig } from "../../../config/visualFields";
import { FormSection } from "../../ui/FormSection";
import { FormField } from "../../ui/FormField";
import { getSectionValues } from "../../../utils/formUtils";

interface VisualElementsSectionProps {
  visual?: VisualElementOptions;
  onUpdate: (field: string, value: any) => void;
}

export function VisualElementsSection({ visual = {}, onUpdate }: VisualElementsSectionProps) {
  const handleNestedUpdate = (category: string, field: string, value: any): void => {
    const currentCategory = visual[category as keyof VisualElementOptions] as Record<string, any> || {};
    const updatedCategory = {
      ...currentCategory,
      [field]: value
    };
    
    onUpdate(category, updatedCategory);
  };

  return (
    <div className="space-y-4">
      {visualElementsConfig.sections.map(section => {
        const sectionValues = getSectionValues(visual, section.id);
        
        return (
          <FormSection 
            key={section.id}
            title={section.title}
            defaultExpanded={section.defaultExpanded}
          >
            {section.fields.map(field => (
              <FormField
                key={field.name}
                field={field}
                value={sectionValues[field.name]}
                onChange={(value) => handleNestedUpdate(section.id, field.name, value)}
                parentValues={sectionValues}
              />
            ))}
          </FormSection>
        );
      })}
    </div>
  );
}
