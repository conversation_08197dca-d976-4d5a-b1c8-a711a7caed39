using MediatR;
using FY.WB.CSHero2.Application.TenantProfiles.Dtos;
using System;

namespace FY.WB.CSHero2.Application.TenantProfiles.Queries
{
    public class GetTenantProfileByIdQuery : IRequest<TenantProfileDto?>
    {
        public Guid Id { get; } // This is the ID of the TenantProfile record itself

        public GetTenantProfileByIdQuery(Guid id)
        {
            Id = id;
        }
    }
}
