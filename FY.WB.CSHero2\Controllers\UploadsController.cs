using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using MediatR; // Added for IMediator
using Microsoft.Extensions.Logging; // Added for ILogger
using FY.WB.CSHero2.Application.Uploads.Dtos; // Added for Upload DTOs
using FY.WB.CSHero2.Application.Uploads.Queries; // Added for Upload Queries
using FY.WB.CSHero2.Application.Uploads.Commands; // Added for Upload Commands
using FY.WB.CSHero2.Application.Common.Dtos; // Added for PagedResult
using FY.WB.CSHero2.Application.Common.Exceptions; // Added for custom exceptions
using FluentValidation; // Added for ValidationException

namespace FY.WB.CSHero2.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class UploadsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<UploadsController> _logger;

        public UploadsController(IMediator mediator, ILogger<UploadsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all uploads with optional filtering, sorting, and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResult<UploadDto>), 200)]
        public async Task<ActionResult<PagedResult<UploadDto>>> GetUploads(
            [FromQuery] string? search,
            [FromQuery] string? contentType,
            [FromQuery] string? sortBy,
            [FromQuery] string? sortOrder,
            [FromQuery] int? page,
            [FromQuery] int? limit)
        {
            _logger.LogInformation("Attempting to get uploads with parameters: Search={Search}, ContentType={ContentType}, SortBy={SortBy}, SortOrder={SortOrder}, Page={Page}, Limit={Limit}",
                search, contentType, sortBy, sortOrder, page, limit);

            var parameters = new UploadQueryParametersDto
            {
                Search = search,
                ContentType = contentType,
                SortBy = sortBy,
                SortOrder = sortOrder,
                Page = page,
                Limit = limit
            };

            var query = new GetUploadsQuery(parameters);
            var result = await _mediator.Send(query);
            
            _logger.LogInformation("Successfully retrieved {Count} uploads for page {Page}", result.Items.Count, result.Page);
            return Ok(result);
        }

        /// <summary>
        /// Get an upload by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(UploadDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<UploadDto?>> GetUpload(Guid id) // Changed id to Guid
        {
            _logger.LogInformation("Attempting to get upload with ID: {UploadId}", id);
            try
            {
                var query = new GetUploadByIdQuery(id);
                var result = await _mediator.Send(query);

                if (result == null)
                {
                    _logger.LogWarning("Upload with ID {UploadId} not found", id);
                    return NotFound(new { message = $"Upload with ID {id} not found." });
                }
                
                _logger.LogInformation("Successfully retrieved upload with ID: {UploadId}", id);
                return Ok(result);
            }
            catch (FormatException)
            {
                _logger.LogWarning("Invalid ID format for Upload: {UploadId}", id);
                return BadRequest(new { message = "Invalid ID format. Please provide a valid GUID."});
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving upload with ID {UploadId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while retrieving the upload.", details = ex.Message });
            }
        }

        /// <summary>
        /// Creates a new upload record.
        /// Note: This endpoint is for creating the metadata record. Actual file upload should be handled separately (e.g., directly to a storage service or via a streaming endpoint).
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), 201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateUpload([FromBody] CreateUploadRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new upload record for filename: {Filename}", dto.Filename);
            try
            {
                var command = new CreateUploadCommand(dto);
                var uploadId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created upload record with ID: {UploadId}", uploadId);
                return CreatedAtAction(nameof(GetUpload), new { id = uploadId }, new { id = uploadId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating upload record.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating upload record: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating upload record: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the upload record.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing upload's metadata.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateUpload(Guid id, [FromBody] UpdateUploadRequestDto dto)
        {
            _logger.LogInformation("Attempting to update upload record with ID: {UploadId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for upload update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateUploadCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated upload record with ID: {UploadId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating upload record with ID: {UploadId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Upload record with ID {UploadId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update upload record with ID: {UploadId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating upload record with ID {UploadId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the upload record.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes an upload record by its ID.
        /// Note: This typically only deletes the metadata. Actual file deletion from storage should be handled by the command handler or a separate process.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteUpload(Guid id)
        {
            _logger.LogInformation("Attempting to delete upload record with ID: {UploadId}", id);
            try
            {
                var command = new DeleteUploadCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted upload record with ID: {UploadId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Upload record with ID {UploadId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete upload record with ID: {UploadId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting upload record with ID {UploadId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the upload record.", details = ex.Message });
            }
        }
    }
}
