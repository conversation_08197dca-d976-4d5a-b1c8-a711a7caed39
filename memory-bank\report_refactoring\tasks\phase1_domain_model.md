# Phase 1: Domain Model Changes

## Overview

This phase focuses on creating and updating the domain entities to support the new hierarchical report structure. The goal is to replace the current JSON-based content storage with proper entity relationships.

## Tasks

### Task 1.1: Create ReportSection Entity

**Description:** Create a new entity to represent report sections.

**Steps:**
1. Create `ReportSection.cs` in `FY.WB.CSHero2.Domain/Entities/`
2. Implement the following properties:
   - `Id` (Guid, primary key)
   - `ReportId` (Guid, foreign key to Report)
   - `Title` (string)
   - `Type` (string)
   - `Order` (int, for section ordering)
3. Add navigation properties:
   - `Report` (Report)
   - `Fields` (ICollection<ReportSectionField>)
4. Inherit from `AuditedEntity<Guid>` to include audit fields

**Code Example:**
```csharp
public class ReportSection : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; } = null!;
    public virtual ICollection<ReportSectionField> Fields { get; set; } = new List<ReportSectionField>();
}
```

**Acceptance Criteria:**
- Entity class created with all required properties
- Navigation properties properly defined
- Inherits from appropriate base class

### Task 1.2: Create ReportSectionField Entity

**Description:** Create a new entity to represent fields within report sections.

**Steps:**
1. Create `ReportSectionField.cs` in `FY.WB.CSHero2.Domain/Entities/`
2. Implement the following properties:
   - `Id` (Guid, primary key)
   - `SectionId` (Guid, foreign key to ReportSection)
   - `Name` (string)
   - `Type` (string)
   - `Content` (string)
   - `Order` (int, for field ordering)
3. Add navigation property:
   - `Section` (ReportSection)
4. Inherit from `AuditedEntity<Guid>` to include audit fields

**Code Example:**
```csharp
public class ReportSectionField : AuditedEntity<Guid>
{
    public Guid SectionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Navigation property
    public virtual ReportSection Section { get; set; } = null!;
}
```

**Acceptance Criteria:**
- Entity class created with all required properties
- Navigation property properly defined
- Inherits from appropriate base class

### Task 1.3: Update Report Entity

**Description:** Update the existing Report entity to include navigation properties to sections.

**Steps:**
1. Open `Report.cs` in `FY.WB.CSHero2.Domain/Entities/`
2. Add navigation property for sections:
   - `Sections` (ICollection<ReportSection>)
3. Initialize the collection in the constructor

**Code Example:**
```csharp
// Add to Report.cs
public virtual ICollection<ReportSection> Sections { get; set; } = new List<ReportSection>();
```

**Acceptance Criteria:**
- Navigation property added to Report entity
- Collection properly initialized

### Task 1.4: Create ReportSectionConfiguration

**Description:** Create entity configuration for the ReportSection entity.

**Steps:**
1. Create `ReportSectionConfiguration.cs` in `FY.WB.CSHero2.Infrastructure/Persistence/Configurations/`
2. Implement `IEntityTypeConfiguration<ReportSection>`
3. Configure:
   - Table name
   - Primary key
   - Required properties
   - Property lengths
   - Relationships
   - Indexes

**Code Example:**
```csharp
public class ReportSectionConfiguration : IEntityTypeConfiguration<ReportSection>
{
    public void Configure(EntityTypeBuilder<ReportSection> builder)
    {
        builder.ToTable("ReportSections");
        builder.HasKey(s => s.Id);
        
        builder.Property(s => s.Title).IsRequired().HasMaxLength(200);
        builder.Property(s => s.Type).IsRequired().HasMaxLength(50);
        builder.Property(s => s.Order).IsRequired();
        
        builder.HasOne(s => s.Report)
            .WithMany(r => r.Sections)
            .HasForeignKey(s => s.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(s => new { s.ReportId, s.Order });
    }
}
```

**Acceptance Criteria:**
- Configuration class created
- Table name and primary key defined
- Properties configured with appropriate constraints
- Relationship with Report entity configured
- Appropriate indexes created

### Task 1.5: Create ReportSectionFieldConfiguration

**Description:** Create entity configuration for the ReportSectionField entity.

**Steps:**
1. Create `ReportSectionFieldConfiguration.cs` in `FY.WB.CSHero2.Infrastructure/Persistence/Configurations/`
2. Implement `IEntityTypeConfiguration<ReportSectionField>`
3. Configure:
   - Table name
   - Primary key
   - Required properties
   - Property lengths
   - Relationships
   - Indexes

**Code Example:**
```csharp
public class ReportSectionFieldConfiguration : IEntityTypeConfiguration<ReportSectionField>
{
    public void Configure(EntityTypeBuilder<ReportSectionField> builder)
    {
        builder.ToTable("ReportSectionFields");
        builder.HasKey(f => f.Id);
        
        builder.Property(f => f.Name).IsRequired().HasMaxLength(100);
        builder.Property(f => f.Type).IsRequired().HasMaxLength(50);
        builder.Property(f => f.Content).HasColumnType("nvarchar(max)");
        builder.Property(f => f.Order).IsRequired();
        
        builder.HasOne(f => f.Section)
            .WithMany(s => s.Fields)
            .HasForeignKey(f => f.SectionId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(f => new { f.SectionId, f.Order });
    }
}
```

**Acceptance Criteria:**
- Configuration class created
- Table name and primary key defined
- Properties configured with appropriate constraints
- Relationship with ReportSection entity configured
- Appropriate indexes created

### Task 1.6: Update ReportConfiguration

**Description:** Update the existing Report entity configuration to include the relationship with sections.

**Steps:**
1. Open `ReportConfiguration.cs` in `FY.WB.CSHero2.Infrastructure/Persistence/Configurations/`
2. Add configuration for the relationship with sections

**Code Example:**
```csharp
// Add to Configure method in ReportConfiguration.cs
builder.HasMany(r => r.Sections)
    .WithOne(s => s.Report)
    .HasForeignKey(s => s.ReportId)
    .OnDelete(DeleteBehavior.Cascade);
```

**Acceptance Criteria:**
- Relationship with sections configured
- Cascade delete behavior specified

### Task 1.7: Update ApplicationDbContext

**Description:** Update the ApplicationDbContext to include DbSets for the new entities.

**Steps:**
1. Open `ApplicationDbContext.cs` in `FY.WB.CSHero2.Infrastructure/Persistence/`
2. Add DbSet properties for the new entities:
   - `ReportSections`
   - `ReportSectionFields`

**Code Example:**
```csharp
// Add to ApplicationDbContext.cs
public DbSet<ReportSection> ReportSections { get; set; } = null!;
public DbSet<ReportSectionField> ReportSectionFields { get; set; } = null!;
```

**Acceptance Criteria:**
- DbSet properties added for both new entities
- Properties properly initialized

## Dependencies

- None (this is the first phase)

## Deliverables

- ReportSection entity
- ReportSectionField entity
- Updated Report entity
- Entity configurations for new entities
- Updated ApplicationDbContext

## Estimated Effort

- Task 1.1: 1 hour
- Task 1.2: 1 hour
- Task 1.3: 0.5 hours
- Task 1.4: 1 hour
- Task 1.5: 1 hour
- Task 1.6: 0.5 hours
- Task 1.7: 0.5 hours

**Total: 5.5 hours**