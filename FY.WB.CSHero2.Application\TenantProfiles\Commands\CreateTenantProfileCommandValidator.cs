using FluentValidation;
using FY.WB.CSHero2.Application.TenantProfiles.Dtos;

namespace FY.WB.CSHero2.Application.TenantProfiles.Commands
{
    public class CreateTenantProfileCommandValidator : AbstractValidator<CreateTenantProfileCommand>
    {
        public CreateTenantProfileCommandValidator()
        {
            RuleFor(v => v.Dto.Name)
                .NotEmpty().WithMessage("Name is required.")
                .MaximumLength(200).WithMessage("Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Email)
                .NotEmpty().WithMessage("Email is required.")
                .EmailAddress().WithMessage("Email is not a valid email address.")
                .MaximumLength(256).WithMessage("Email must not exceed 256 characters.");

            RuleFor(v => v.Dto.Status)
                .NotEmpty().WithMessage("Status is required.")
                .MaximumLength(50).WithMessage("Status must not exceed 50 characters.");
            
            RuleFor(v => v.Dto.Phone)
                .MaximumLength(50).WithMessage("Phone must not exceed 50 characters.");

            RuleFor(v => v.Dto.Company)
                .MaximumLength(200).WithMessage("Company must not exceed 200 characters.");

            RuleFor(v => v.Dto.Subscription)
                .MaximumLength(100).WithMessage("Subscription must not exceed 100 characters.");

            RuleFor(v => v.Dto.BillingCycle)
                .MaximumLength(50).WithMessage("Billing Cycle must not exceed 50 characters.");
            
            RuleFor(v => v.Dto.SubscriptionStatus)
                .MaximumLength(50).WithMessage("Subscription Status must not exceed 50 characters.");

            // Validation for PaymentMethodInfo
            When(v => v.Dto.PaymentMethod != null, () => {
                RuleFor(v => v.Dto.PaymentMethod!.CardType)
                    .NotEmpty().WithMessage("Card Type is required when Payment Method is provided.")
                    .MaximumLength(50).WithMessage("Card Type must not exceed 50 characters.");
                RuleFor(v => v.Dto.PaymentMethod!.LastFourDigits)
                    .NotEmpty().WithMessage("Last Four Digits are required when Payment Method is provided.")
                    .Length(4).WithMessage("Last Four Digits must be 4 characters.");
                RuleFor(v => v.Dto.PaymentMethod!.ExpirationDate)
                    .NotEmpty().WithMessage("Expiration Date is required when Payment Method is provided.")
                    .Matches(@"^(0[1-9]|1[0-2])\/\d{2}$").WithMessage("Expiration Date must be in MM/YY format.");
                 RuleFor(v => v.Dto.PaymentMethod!.SecurityMethod)
                    .MaximumLength(50).WithMessage("Security Method must not exceed 50 characters.");
            });

            // Validation for BillingAddressInfo
            When(v => v.Dto.BillingAddress != null, () => {
                RuleFor(v => v.Dto.BillingAddress!.Street)
                    .NotEmpty().WithMessage("Street is required when Billing Address is provided.")
                    .MaximumLength(200).WithMessage("Street must not exceed 200 characters.");
                RuleFor(v => v.Dto.BillingAddress!.City)
                    .NotEmpty().WithMessage("City is required when Billing Address is provided.")
                    .MaximumLength(100).WithMessage("City must not exceed 100 characters.");
                RuleFor(v => v.Dto.BillingAddress!.State)
                    .NotEmpty().WithMessage("State is required when Billing Address is provided.")
                    .MaximumLength(100).WithMessage("State must not exceed 100 characters.");
                RuleFor(v => v.Dto.BillingAddress!.ZipCode)
                    .NotEmpty().WithMessage("Zip Code is required when Billing Address is provided.")
                    .MaximumLength(20).WithMessage("Zip Code must not exceed 20 characters.");
                RuleFor(v => v.Dto.BillingAddress!.Country)
                    .NotEmpty().WithMessage("Country is required when Billing Address is provided.")
                    .MaximumLength(100).WithMessage("Country must not exceed 100 characters.");
            });
        }
    }
}
