# Project Plan: Frontend to Backend API Integration & Seeding

**Version:** 1.0
**Date:** 2025-05-11
**Related Documents:** `projectbrief.md`, `activeContext.md`, `progress.md`, `.clinerules/*`

## 1. Goal

To transition the Next.js frontend from using a mock JSON server to consuming live data from the ASP.NET Core backend API. This includes:
-   Defining and implementing necessary API endpoints following CQRS patterns.
-   Creating seed data for the backend database based on the current mock data.
-   Updating the frontend to make live API calls.
-   Ensuring all work adheres to the project's `.clinerules`.

**Prerequisite Note:** The successful execution and detailed specification within Phases 1-5 of this plan are dependent on the completion of **Phase 0: Discovery & Analysis**. Additionally, the 404 error on `/client/dashboard` (see `activeContext.md`) may need resolution if it blocks access to pages required for this integration.

## 2. Phases and Tasks

### Phase 0: Discovery & Analysis (Completed: 2025-05-11)

*   **Task 0.1: Identify Data-Driven Frontend Pages**
    *   **Objective:** Compile a list of all Next.js pages currently displaying data from the mock JSON server.
    *   **Actions:**
        - [✅] <PERSON><PERSON> listed files in `FY.WB.CSHero2.UI/src/app/`.
        - [✅] **User implicitly confirmed focus by guiding analysis towards `client/dashboard/page.tsx` and related data files.** Initial focus will be on `Client` and `Report` entities based on dashboard usage.
    *   **Deliverable:** Key pages identified: `client/dashboard/page.tsx` (uses `Client` and `Report` data). Other pages like `client/clients/page.tsx`, `admin/tenants/page.tsx`, `client/reports/page.tsx` are also strong candidates for future integration.

*   **Task 0.2: Locate and Analyze Mock JSON Data**
    *   **Objective:** Understand the structure, entities, and relationships represented by the current mock data.
    *   **Actions:**
        - [✅] User confirmed mock data path: `FY.WB.CSHero2.UI/src/data/db/db.json`.
        - [✅] Cline analyzed `db.json`, `FY.WB.CSHero2.UI/src/lib/api.ts`, `FY.WB.CSHero2.UI/src/app/api/v1/clients/route.ts`, and `FY.WB.CSHero2.UI/src/lib/data/clients.ts`.
    *   **Deliverable:**
        *   Primary mock data source: `FY.WB.CSHero2.UI/src/data/db/db.json`.
        *   Key entities identified for initial focus: `Client`, `Report`. Other entities in `db.json` include `User`, `Form`, `Upload`, `Template`, `Tenant`, `Invoice`, and various metrics.
        *   `Client` entity structure (from `db.json` and `FY.WB.CSHero2.UI/src/lib/data/clients.ts`): `id`, `name`, `email`, `status`, `companyName`, `createdAt`, `updatedAt`, `phone`, `address`, `companySize`, `industry`.
        *   `Report` entity structure (from `db.json`): `reportId`, `clientId`, `clientName`, `reportName`, `category`, `slideCount`, `createdAt`, `lastModified`, `status`, `author`.

### Phase 1: API Endpoint Design (CQRS) - Focus: `Client` Entity

*   **Task 1.1: Define CQRS Queries & Commands for `Client`**
    *   **Objective:** Define MediatR requests for `Client` entity operations.
    *   **Actions:**
        - [ ] **Queries:**
            - `GetClientsQuery`: Fetch list of clients (supports filtering, sorting, pagination).
            - `GetClientByIdQuery`: Fetch a single client by ID.
        - [ ] **Commands:**
            - `CreateClientCommand`: Create a new client.
            - `UpdateClientCommand`: Update an existing client.
            - `DeleteClientCommand`: Delete a client.
            - `ArchiveClientCommand`: Mark a client as inactive.
    *   **Deliverable:** Conceptual list of `Query` and `Command` definitions for the `Client` entity.

*   **Task 1.2: Design Data Transfer Objects (DTOs) for `Client`**
    *   **Objective:** Design DTOs for `Client` queries and commands.
    *   **Actions:**
        - [ ] `ClientDto`: For representing client data in responses. Likely similar to the `Client` interface in `FY.WB.CSHero2.UI/src/lib/data/clients.ts`.
        - [ ] `CreateClientRequestDto`: For `CreateClientCommand` payload.
        - [ ] `UpdateClientRequestDto`: For `UpdateClientCommand` payload.
        - [ ] `ClientQueryParametersDto`: For `GetClientsQuery` to encapsulate pagination, sorting, and filtering options.
    *   **Deliverable:** Conceptual DTO definitions for the `Client` entity.

*   **Task 1.1: Define CQRS Queries & Commands**
    *   **Objective:** For each identified frontend page/feature, define the necessary CQRS `Queries` (to fetch data) and `Commands` (for data modification, if any).
    *   **Actions:**
        - [ ] Analyze data requirements for each page and define corresponding MediatR requests.
    *   **Deliverable:** List of `Query` and `Command` definitions (e.g., `GetInvoicesQuery`, `CreateInvoiceCommand`).

*   **Task 1.2: Design Data Transfer Objects (DTOs)**
    *   **Objective:** Design DTOs for all Queries and Commands.
    *   **Actions:**
        - [ ] Create record types or classes for request and response payloads, ensuring they are optimized for the specific use case (e.g., read models for queries).
    *   **Deliverable:** DTO definitions (e.g., `InvoiceDto`, `InvoiceDetailsDto`, `CreateInvoiceRequestDto`).

*   **Task 1.3: Document API Contracts**
    *   **Objective:** Formalize the API endpoint contracts.
    *   **Actions:**
        - [ ] Document expected HTTP methods, route paths, request DTOs, and response DTOs for each endpoint.
    *   **Deliverable:** API contract documentation (can be part of Swagger/OpenAPI XML comments).

### Phase 2: Backend Implementation (ASP.NET Core)

*Adherence to `.clinerules/saas_scaffold_prompt_template.md` is mandatory for all backend development.*

*   **Task 2.1: Define/Update Domain Entities**
    *   **Objective:** Create or update entities in the `FY.WB.CSHero2.Domain` project based on mock data analysis (Task 0.2).
    *   **Actions:**
        - [ ] Define entity classes with properties and relationships.
    *   **Deliverable:** C# entity files (e.g., `Invoice.cs`, `Client.cs`).

*   **Task 2.2: Implement EF Core Configurations**
    *   **Objective:** Configure EF Core mapping for new/updated entities.
    *   **Actions:**
        - [ ] Create `IEntityTypeConfiguration<T>` classes in `FY.WB.CSHero2.Infrastructure/Persistence/Configurations/`.
    *   **Deliverable:** EF Core configuration files.

*   **Task 2.3: Create/Update Database Migrations**
    *   **Objective:** Update the database schema to reflect new/updated entities.
    *   **Actions:**
        - [ ] Generate and apply EF Core migrations.
    *   **Deliverable:** New migration files; updated database schema.

*   **Task 2.4: Implement CQRS Query Handlers**
    *   **Objective:** Implement handlers in `FY.WB.CSHero2.Application` for all defined Queries.
    *   **Actions:**
        - [ ] Create handler classes, inject repository interfaces, fetch data, map to DTOs. Use `AsNoTracking()` for read-only queries.
    *   **Deliverable:** Query handler files (e.g., `GetInvoicesQueryHandler.cs`).

*   **Task 2.5: Implement CQRS Command Handlers (if applicable)**
    *   **Objective:** Implement handlers in `FY.WB.CSHero2.Application` for all defined Commands.
    *   **Actions:**
        - [ ] Create handler classes, inject repositories/UnitOfWork, perform business logic, persist changes.
    *   **Deliverable:** Command handler files (e.g., `CreateInvoiceCommandHandler.cs`).

*   **Task 2.6: Implement Validators for Commands**
    *   **Objective:** Create FluentValidation validators for all Command DTOs.
    *   **Actions:**
        - [ ] Define validation rules in validator classes located alongside commands.
    *   **Deliverable:** Validator files (e.g., `CreateInvoiceCommandValidator.cs`).

*   **Task 2.7: Create API Controllers**
    *   **Objective:** Expose CQRS operations via API endpoints in `FY.WB.CSHero2.Api` (or the main API project).
    *   **Actions:**
        - [ ] Create controller methods that accept DTOs, send MediatR requests, and return `ActionResult<T>`.
    *   **Deliverable:** Controller files or updated existing controllers.

*   **Task 2.8: Ensure `.clinerules` Adherence**
    *   **Objective:** Verify all backend code meets project standards.
    *   **Actions:**
        - [ ] Review XML documentation, naming conventions, project structure, DI, etc.
    *   **Deliverable:** Compliant backend code.

### Phase 3: Seed Data Creation & Implementation

*Adherence to `.clinerules/seeding.md` and section XIII of `saas_scaffold_prompt_template.md` is mandatory.*

*   **Task 3.1: Design Seed Data Structure**
    *   **Objective:** Define the structure for JSON seed files based on the new Domain Entities.
    *   **Actions:**
        - [ ] Plan the JSON format for each entity to be seeded.
    *   **Deliverable:** Schema/example for seed JSON files.

*   **Task 3.2: Create JSON Seed Files**
    *   **Objective:** Transform existing mock JSON data into the new seed data format.
    *   **Actions:**
        - [ ] Create `.json` files (e.g., `invoices.json`, `clients.json`) in `FY.WB.CSHero2/Tests/TestData/` (or the designated seed data location as per final interpretation of `.clinerules`).
    *   **Deliverable:** JSON seed data files.

*   **Task 3.3: Implement `SeedData.cs`**
    *   **Objective:** Create an idempotent seeder service.
    *   **Actions:**
        - [ ] Implement `SeedData.cs` (e.g., in `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/`) with a method like `SeedAsync(IServiceProvider serviceProvider)` that reads JSON files and populates the database, checking for existing data to ensure idempotency.
    *   **Deliverable:** `SeedData.cs` implementation.

*   **Task 3.4: Configure Seeder Execution**
    *   **Objective:** Ensure seed data is applied during development startup.
    *   **Actions:**
        - [ ] Call the seed method from `Program.cs` in the API project, typically within an `if (app.Environment.IsDevelopment())` block.
    *   **Deliverable:** Updated `Program.cs`.

### Phase 4: Frontend Integration (Next.js)

*   **Task 4.1: Create/Update Next.js BFF API Routes**
    *   **Objective:** Create or modify BFF API routes in `FY.WB.CSHero2.UI/src/app/api/` to proxy requests to the new ASP.NET Core endpoints.
    *   **Actions:**
        - [ ] Implement `POST`, `GET`, etc., handlers in `route.ts` files. These routes will handle forwarding the request (including the `authToken` cookie) to the backend and returning the response.
    *   **Deliverable:** BFF `route.ts` files.

*   **Task 4.2: Update Frontend Page Components**
    *   **Objective:** Modify Next.js page components to call the new BFF API routes instead of the mock server.
    *   **Actions:**
        - [ ] Replace mock data fetching logic with `fetch` calls to the BFF routes.
    *   **Deliverable:** Updated `page.tsx` (and related component) files.

*   **Task 4.3: Adapt to Backend DTOs**
    *   **Objective:** Ensure frontend components correctly use the data structures (DTOs) returned by the live API.
    *   **Actions:**
        - [ ] Update TypeScript interfaces/types in the frontend to match backend DTOs; adjust data mapping and rendering logic as needed.
    *   **Deliverable:** Updated frontend components and type definitions.

*   **Task 4.4: Verify Authentication Flow**
    *   **Objective:** Ensure authenticated requests from the frontend to the backend work correctly.
    *   **Actions:**
        - [ ] Test that `authToken` HttpOnly cookies are sent with requests from BFF to backend, and that backend authorization policies are enforced.
    *   **Deliverable:** Confirmed secure and functional authentication for API calls.

### Phase 5: Verification & Documentation Update

*   **Task 5.1: End-to-End Testing**
    *   **Objective:** Verify that each integrated page fetches and displays data correctly from the live backend and that data modifications (if any) work as expected.
    *   **Actions:**
        - [ ] Manually test UI flows; consider creating Playwright E2E test stubs as per `.clinerules/seeding.md`.
    *   **Deliverable:** Verified frontend functionality with live data.

*   **Task 5.2: Update `progress.md`**
    *   **Objective:** Reflect the completion of this integration effort.
    *   **Actions:**
        - [ ] Mark relevant tasks in `progress.md` as complete or in progress. Add new items if this effort constitutes a new phase/major step.
    *   **Deliverable:** Updated `progress.md`.

*   **Task 5.3: Update `activeContext.md`**
    *   **Objective:** Document the current state after this integration.
    *   **Actions:**
        - [ ] Update current work focus, recent changes, and next immediate steps.
    *   **Deliverable:** Updated `activeContext.md`.

*   **Task 5.4: Update Other Memory Bank Documents**
    *   **Objective:** Ensure all memory bank documents are consistent with the new system state.
    *   **Actions:**
        - [ ] Review and update `systemPatterns.md`, `techContext.md`, etc., if new patterns were introduced or existing ones significantly changed.
    *   **Deliverable:** Updated memory bank documentation.

## 3. Next Steps for Plan Execution

- [ ] 1.  Complete **Phase 0: Discovery & Analysis**. This requires user input or further investigation by Cline.
- [ ] 2.  Address the `/client/dashboard` 404 error if it blocks access to pages needed for Phase 0.
- [ ] 3.  Proceed sequentially through Phases 1-5 for each identified feature/page, iterating as necessary.
