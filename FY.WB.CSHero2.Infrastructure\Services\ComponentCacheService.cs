using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Interfaces;

namespace FY.WB.CSHero2.Infrastructure.Services
{
    /// <summary>
    /// Configuration options for Report Rendering Engine
    /// </summary>
    public class ReportRenderingOptions
    {
        public bool EnableComponentCaching { get; set; } = true;
        public int ComponentCacheDurationMinutes { get; set; } = 60;
        public LLMServiceOptions LLMService { get; set; } = new LLMServiceOptions();
    }



    /// <summary>
    /// Caching service for component generation to improve performance
    /// </summary>
    public class ComponentCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<ComponentCacheService> _logger;
        private readonly ReportRenderingOptions _options;

        // Cache key prefixes
        private const string COMPONENT_CACHE_PREFIX = "component:";
        private const string TEMPLATE_CACHE_PREFIX = "template:";
        private const string VALIDATION_CACHE_PREFIX = "validation:";
        private const string LLM_CACHE_PREFIX = "llm:";

        public ComponentCacheService(
            IMemoryCache memoryCache,
            ILogger<ComponentCacheService> logger,
            IOptions<ReportRenderingOptions> options)
        {
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        #region Component Caching

        /// <summary>
        /// Gets cached component result
        /// </summary>
        public async Task<ComponentResult?> GetCachedComponentAsync(
            string cacheKey,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var key = COMPONENT_CACHE_PREFIX + cacheKey;

                if (_memoryCache.TryGetValue(key, out ComponentResult? cachedResult))
                {
                    _logger.LogDebug("Component cache hit for key: {CacheKey}", cacheKey);
                    return await Task.FromResult(cachedResult);
                }

                _logger.LogDebug("Component cache miss for key: {CacheKey}", cacheKey);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached component for key: {CacheKey}", cacheKey);
                return null;
            }
        }

        /// <summary>
        /// Caches component result
        /// </summary>
        public async Task SetCachedComponentAsync(
            string cacheKey,
            ComponentResult result,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_options.EnableComponentCaching)
                {
                    return;
                }

                var key = COMPONENT_CACHE_PREFIX + cacheKey;
                var expiration = TimeSpan.FromMinutes(_options.ComponentCacheDurationMinutes);

                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration,
                    SlidingExpiration = TimeSpan.FromMinutes(_options.ComponentCacheDurationMinutes / 2),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, result, cacheOptions);

                _logger.LogDebug("Cached component result for key: {CacheKey}, expires in: {Expiration}",
                    cacheKey, expiration);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error caching component for key: {CacheKey}", cacheKey);
            }
        }

        /// <summary>
        /// Generates cache key for component request
        /// </summary>
        public string GenerateComponentCacheKey(ComponentRequest request)
        {
            try
            {
                var keyData = new
                {
                    ReportId = request.ReportId,
                    SectionId = request.SectionId,
                    DataHash = GenerateDataHash(request.Data),
                    Options = new
                    {
                        request.Options.Framework,
                        request.Options.StyleFramework,
                        request.Options.UseTypeScript,
                        request.Options.IncludeAccessibility,
                        request.Options.IncludeResponsiveDesign,
                        request.Options.OptimizeForPerformance
                    }
                };

                var keyJson = JsonSerializer.Serialize(keyData);
                return GenerateHash(keyJson);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error generating component cache key");
                return $"fallback_{request.ReportId}_{request.SectionId}_{DateTime.UtcNow.Ticks}";
            }
        }

        #endregion

        #region Template Caching

        /// <summary>
        /// Gets cached template
        /// </summary>
        public async Task<T?> GetCachedTemplateAsync<T>(
            string templateId,
            CancellationToken cancellationToken = default) where T : class
        {
            try
            {
                var key = TEMPLATE_CACHE_PREFIX + templateId;

                if (_memoryCache.TryGetValue(key, out T? cachedTemplate))
                {
                    _logger.LogDebug("Template cache hit for ID: {TemplateId}", templateId);
                    return await Task.FromResult(cachedTemplate);
                }

                _logger.LogDebug("Template cache miss for ID: {TemplateId}", templateId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached template for ID: {TemplateId}", templateId);
                return null;
            }
        }

        /// <summary>
        /// Caches template
        /// </summary>
        public async Task SetCachedTemplateAsync<T>(
            string templateId,
            T template,
            TimeSpan? customExpiration = null,
            CancellationToken cancellationToken = default) where T : class
        {
            try
            {
                var key = TEMPLATE_CACHE_PREFIX + templateId;
                var expiration = customExpiration ?? TimeSpan.FromHours(1); // Templates change less frequently

                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration,
                    Priority = CacheItemPriority.High // Templates are important
                };

                _memoryCache.Set(key, template, cacheOptions);

                _logger.LogDebug("Cached template for ID: {TemplateId}, expires in: {Expiration}",
                    templateId, expiration);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error caching template for ID: {TemplateId}", templateId);
            }
        }

        #endregion

        #region Validation Caching

        /// <summary>
        /// Gets cached validation result
        /// </summary>
        public async Task<ValidationResult?> GetCachedValidationAsync(
            string componentCode,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = GenerateHash(componentCode);
                var key = VALIDATION_CACHE_PREFIX + cacheKey;

                if (_memoryCache.TryGetValue(key, out ValidationResult? cachedResult))
                {
                    _logger.LogDebug("Validation cache hit for component hash: {Hash}", cacheKey);
                    return await Task.FromResult(cachedResult);
                }

                _logger.LogDebug("Validation cache miss for component hash: {Hash}", cacheKey);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached validation result");
                return null;
            }
        }

        /// <summary>
        /// Caches validation result
        /// </summary>
        public async Task SetCachedValidationAsync(
            string componentCode,
            ValidationResult result,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = GenerateHash(componentCode);
                var key = VALIDATION_CACHE_PREFIX + cacheKey;
                var expiration = TimeSpan.FromMinutes(30); // Validation results are relatively stable

                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration,
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, result, cacheOptions);

                _logger.LogDebug("Cached validation result for component hash: {Hash}, expires in: {Expiration}",
                    cacheKey, expiration);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error caching validation result");
            }
        }

        #endregion

        #region LLM Response Caching

        /// <summary>
        /// Gets cached LLM response
        /// </summary>
        public async Task<string?> GetCachedLLMResponseAsync(
            string prompt,
            Dictionary<string, object> context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_options.LLMService.EnableCaching)
                {
                    return null;
                }

                var cacheKey = GenerateLLMCacheKey(prompt, context);
                var key = LLM_CACHE_PREFIX + cacheKey;

                if (_memoryCache.TryGetValue(key, out string? cachedResponse))
                {
                    _logger.LogDebug("LLM cache hit for prompt hash: {Hash}", cacheKey);
                    return await Task.FromResult(cachedResponse);
                }

                _logger.LogDebug("LLM cache miss for prompt hash: {Hash}", cacheKey);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached LLM response");
                return null;
            }
        }

        /// <summary>
        /// Caches LLM response
        /// </summary>
        public async Task SetCachedLLMResponseAsync(
            string prompt,
            Dictionary<string, object> context,
            string response,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_options.LLMService.EnableCaching)
                {
                    return;
                }

                var cacheKey = GenerateLLMCacheKey(prompt, context);
                var key = LLM_CACHE_PREFIX + cacheKey;
                var expiration = TimeSpan.FromMinutes(_options.LLMService.CacheDurationMinutes);

                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration,
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, response, cacheOptions);

                _logger.LogDebug("Cached LLM response for prompt hash: {Hash}, expires in: {Expiration}",
                    cacheKey, expiration);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error caching LLM response");
            }
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Clears all component-related caches
        /// </summary>
        public async Task ClearAllCachesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // Note: IMemoryCache doesn't provide a direct way to clear by prefix
                // In a production environment, consider using IDistributedCache with Redis
                // which supports pattern-based deletion

                _logger.LogInformation("Cache clear requested - consider implementing distributed cache for better cache management");

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing caches");
            }
        }

        /// <summary>
        /// Gets cache statistics
        /// </summary>
        public async Task<Dictionary<string, object>> GetCacheStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // Note: IMemoryCache doesn't provide built-in statistics
                // In a production environment, consider implementing custom cache statistics

                var stats = new Dictionary<string, object>
                {
                    ["cacheType"] = "MemoryCache",
                    ["componentCachingEnabled"] = _options.EnableComponentCaching,
                    ["llmCachingEnabled"] = _options.LLMService.EnableCaching,
                    ["componentCacheDurationMinutes"] = _options.ComponentCacheDurationMinutes,
                    ["llmCacheDurationMinutes"] = _options.LLMService.CacheDurationMinutes
                };

                return await Task.FromResult(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return new Dictionary<string, object>();
            }
        }

        #endregion

        #region Private Methods

        private string GenerateDataHash(Dictionary<string, object> data)
        {
            try
            {
                var dataJson = JsonSerializer.Serialize(data, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false
                });
                return GenerateHash(dataJson);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error generating data hash");
                return DateTime.UtcNow.Ticks.ToString();
            }
        }

        private string GenerateLLMCacheKey(string prompt, Dictionary<string, object> context)
        {
            try
            {
                var combined = prompt + JsonSerializer.Serialize(context);
                return GenerateHash(combined);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error generating LLM cache key");
                return DateTime.UtcNow.Ticks.ToString();
            }
        }

        private string GenerateHash(string input)
        {
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(hashBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }

        #endregion
    }
}
