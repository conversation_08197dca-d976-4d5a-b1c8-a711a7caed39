using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FY.WB.CSHero2.Controllers
{
    [Authorize(Roles = "Admin")]
    [ApiController]
    [Route("api/v1/system")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class SystemController : ControllerBase
    {
        private readonly ICompanyProfileService _companyProfileService;

        public SystemController(ICompanyProfileService companyProfileService)
        {
            _companyProfileService = companyProfileService;
        }

        [HttpGet("company-profile")]
        public ActionResult<CompanyProfileDto> GetCompanyProfile()
        {
            var companyProfile = _companyProfileService.GetCompanyProfile();
            return Ok(companyProfile);
        }
    }
}
