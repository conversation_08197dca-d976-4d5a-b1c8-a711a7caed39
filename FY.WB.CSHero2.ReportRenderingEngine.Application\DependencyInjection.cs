using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application
{
    /// <summary>
    /// Dependency injection configuration for Report Rendering Engine Application layer
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// Adds Report Rendering Engine Application services to the dependency injection container
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddReportRenderingEngineApplication(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Core Services
            services.AddScoped<ITemplateService, TemplateServiceImpl>();
            services.AddScoped<IReportService, ReportServiceImpl>();
            services.AddScoped<IVersioningService, VersioningServiceImpl>();
            services.AddScoped<IReportRenderer, ReportRendererV2>();

            // Component Generation Services
            services.AddScoped<IComponentGenerator, ComponentGeneratorImpl>();
            services.AddScoped<ComponentBuilder>();
            services.AddScoped<ComponentGenerationWorkflow>();
            services.AddScoped<EnhancedPromptBuilder>();

            // Enhanced Versioning Services (Phase 5)
            services.AddScoped<EnhancedVersioningService>();
            services.AddScoped<VersionComparisonService>();

            // Export Services (Phase 5)
            services.AddScoped<IExportService, ExportServiceImpl>();

            // Supporting Services
            services.AddScoped<ReportRenderer>(); // Legacy renderer for backward compatibility

            // Configuration
            services.Configure<ReportRenderingOptions>(
                configuration.GetSection("ReportRendering"));

            return services;
        }
    }

    /// <summary>
    /// Configuration options for Report Rendering Engine
    /// </summary>
    public class ReportRenderingOptions
    {
        /// <summary>
        /// Default framework for component generation
        /// </summary>
        public string DefaultFramework { get; set; } = "NextJS";

        /// <summary>
        /// Default style framework
        /// </summary>
        public string DefaultStyleFramework { get; set; } = "TailwindCSS";

        /// <summary>
        /// Whether to use TypeScript by default
        /// </summary>
        public bool UseTypeScript { get; set; } = true;

        /// <summary>
        /// Whether to include accessibility features by default
        /// </summary>
        public bool IncludeAccessibility { get; set; } = true;

        /// <summary>
        /// Whether to include responsive design by default
        /// </summary>
        public bool IncludeResponsiveDesign { get; set; } = true;

        /// <summary>
        /// Whether to optimize for performance by default
        /// </summary>
        public bool OptimizeForPerformance { get; set; } = true;

        /// <summary>
        /// Default theme for components
        /// </summary>
        public string DefaultTheme { get; set; } = "Default";

        /// <summary>
        /// Maximum number of versions to keep per report
        /// </summary>
        public int MaxVersionsPerReport { get; set; } = 10;

        /// <summary>
        /// Whether to enable component caching
        /// </summary>
        public bool EnableComponentCaching { get; set; } = true;

        /// <summary>
        /// Component cache duration in minutes
        /// </summary>
        public int ComponentCacheDurationMinutes { get; set; } = 60;

        /// <summary>
        /// Whether to enable parallel component generation
        /// </summary>
        public bool EnableParallelGeneration { get; set; } = true;

        /// <summary>
        /// Maximum degree of parallelism for component generation
        /// </summary>
        public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// LLM service configuration
        /// </summary>
        public LLMServiceOptions LLMService { get; set; } = new LLMServiceOptions();
    }

    /// <summary>
    /// LLM service configuration options
    /// </summary>
    public class LLMServiceOptions
    {
        /// <summary>
        /// LLM service endpoint
        /// </summary>
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// API key for LLM service
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// Model to use for component generation
        /// </summary>
        public string Model { get; set; } = "gpt-4";

        /// <summary>
        /// Maximum tokens for LLM requests
        /// </summary>
        public int MaxTokens { get; set; } = 4000;

        /// <summary>
        /// Temperature for LLM requests
        /// </summary>
        public double Temperature { get; set; } = 0.7;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Whether to enable LLM request caching
        /// </summary>
        public bool EnableCaching { get; set; } = true;

        /// <summary>
        /// Cache duration for LLM responses in minutes
        /// </summary>
        public int CacheDurationMinutes { get; set; } = 120;
    }
}
