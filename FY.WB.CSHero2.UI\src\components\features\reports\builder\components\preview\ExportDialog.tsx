"use client";

import { useState } from "react";
import { X, FileDown, FileText, Image, File } from "lucide-react";

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (format: string) => void;
}

export function ExportDialog({ isOpen, onClose, onExport }: ExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState<string>("pdf");
  const [isExporting, setIsExporting] = useState<boolean>(false);

  if (!isOpen) return null;

  const handleExport = () => {
    setIsExporting(true);
    
    // Simulate export process
    setTimeout(() => {
      onExport(selectedFormat);
      setIsExporting(false);
      onClose();
    }, 1500);
  };

  const exportOptions = [
    { id: "pdf", label: "PDF Document", icon: File, description: "Standard PDF format for printing and sharing" },
    { id: "docx", label: "Word Document", icon: FileText, description: "Editable document format" },
    { id: "html", label: "HTML File", icon: FileDown, description: "Web-ready HTML format" },
    { id: "png", label: "PNG Image", icon: Image, description: "High-resolution image format" }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">Export Report</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4">
          <p className="text-gray-600 mb-4">
            Choose a format to export your report:
          </p>
          
          <div className="space-y-2">
            {exportOptions.map((option) => (
              <label 
                key={option.id}
                className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedFormat === option.id 
                    ? 'border-primary bg-blue-50' 
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <input
                  type="radio"
                  name="exportFormat"
                  value={option.id}
                  checked={selectedFormat === option.id}
                  onChange={() => setSelectedFormat(option.id)}
                  className="mt-1 mr-3"
                />
                <div className="flex-1">
                  <div className="flex items-center">
                    <option.icon className="h-5 w-5 mr-2 text-gray-600" />
                    <span className="font-medium">{option.label}</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">{option.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>
        
        <div className="p-4 border-t flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className={`px-4 py-2 rounded-md text-white ${
              isExporting 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-primary hover:bg-primary/70'
            }`}
          >
            {isExporting ? 'Exporting...' : 'Export'}
          </button>
        </div>
      </div>
    </div>
  );
}
