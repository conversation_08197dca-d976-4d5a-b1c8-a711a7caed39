"use client";

import { useState } from "react";
import { LayoutTemplate } from "lucide-react";

interface TemplatePage {
  id: string;
  name: string;
  description?: string;
  dataElements: any[];
}

interface ReportTemplate {
  id?: string;
  name: string;
  description?: string;
  pages: TemplatePage[];
  dataTypeId?: string;
}

interface TemplateBuilderProps {
  initialTemplate: ReportTemplate;
  dataTypeId?: string;
  onSave: (template: ReportTemplate) => void;
  onCancel: () => void;
}

export function TemplateBuilder({
  initialTemplate,
  dataTypeId,
  onSave,
  onCancel
}: TemplateBuilderProps) {
  const [template, setTemplate] = useState<ReportTemplate>(initialTemplate || {
    name: "Sample Template",
    description: "Sample template description",
    pages: [
      {
        id: `page-${Date.now()}`,
        name: "Page 1",
        description: "Sample page description",
        dataElements: []
      }
    ],
    dataTypeId: dataTypeId
  });

  return (
    <div className="p-6 border border-dashed rounded-lg flex flex-col items-center justify-center">
      <div className="mb-4">
        <LayoutTemplate className="h-12 w-12 text-purple-500" />
      </div>
      <h3 className="text-lg font-medium mb-2 text-center">TemplateBuilder Placeholder</h3>
      <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
        This is a placeholder for the TemplateBuilder component. In a real implementation, 
        this would allow users to create and edit report templates.
      </p>
      <div className="flex space-x-4">
        <button
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          onClick={() => onSave({
            ...template,
            id: template.id || `template-${Date.now()}`
          })}
        >
          Save Template
        </button>
      </div>
    </div>
  );
}
