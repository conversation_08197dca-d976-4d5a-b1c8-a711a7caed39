'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// import { Textarea } from '@/components/ui/textarea'; // Assuming you have a Textarea component
import { ArrowLeft } from 'lucide-react';
import { getReportById, updateReport } from '@/lib/api';
import { Report } from '@/types/report';
import { useToast } from '@/components/providers/toast-provider';

export default function ReportEditPage() {
  const params = useParams();
  const router = useRouter();
  const reportId = params.reportId as string;
  const { toast } = useToast();

  const [report, setReport] = useState<Partial<Report>>({
    name: '',
    category: '',
    status: undefined, // Changed initial status to undefined
    // Add other fields as necessary
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (reportId) {
      const fetchReport = async () => {
        setLoading(true);
        setError(null);
        try {
          const data = await getReportById(reportId);
          setReport({
            name: data.name || '',
            category: data.category || '',
            status: data.status || '',
            // Ensure all editable fields are populated
          });
        } catch (err) {
          setError(err instanceof Error ? err.message : 'An unknown error occurred');
          toast({
            title: 'Error',
            description: 'Failed to fetch report details.',
            variant: 'destructive',
          });
        } finally {
          setLoading(false);
        }
      };
      fetchReport();
    }
  }, [reportId, toast]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReport(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!reportId) return;

    setSaving(true);
    setError(null);
    try {
      // Construct the payload carefully, ensuring all required fields for update are present
      // and that you are not sending fields that shouldn't be updated (like id, creationTime etc.)
      const payload: Partial<Report> = {
        name: report.name,
        category: report.category,
        status: report.status,
        // Add other fields from the form
      };
      await updateReport(reportId, payload);
      toast({
        title: 'Success',
        description: 'Report updated successfully!',
      });
      router.push(`/client/reports/${reportId}`); // Navigate to view page after successful update
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast({
        title: 'Error',
        description: 'Failed to update report.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16 flex justify-center items-center h-64">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
      </div>
    );
  }

  if (error && !loading) { // Show error only if not loading and error exists
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16 text-center">
        <p className="text-red-500 text-xl">Error: {error}</p>
        <Button asChild variant="outline" className="mt-4">
          <Link href={`/client/reports/${reportId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to View Report
          </Link>
        </Button>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <div className="mb-8">
        <Button asChild variant="outline" size="sm" className="h-9 mb-6">
          {/* Corrected Link path */}
          <Link href={`/client/reports/${reportId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to View Report
          </Link>
        </Button>
        <h1 className="text-4xl font-bold text-[rgb(var(--primary))]">Edit Report</h1>
        <p className="text-xl text-gray-600 mt-2">
          Editing report with ID: {reportId}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="bg-card p-6 sm:p-8 rounded-lg border space-y-6">
        <div>
          <Label htmlFor="name" className="text-sm font-medium text-foreground/90">Report Name</Label>
          <Input
            id="name"
            name="name"
            type="text"
            value={report.name}
            onChange={handleChange}
            className="mt-1 w-full"
            required
          />
        </div>

        <div>
          <Label htmlFor="category" className="text-sm font-medium text-foreground/90">Category</Label>
          <Input
            id="category"
            name="category"
            type="text"
            value={report.category}
            onChange={handleChange}
            className="mt-1 w-full"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="status" className="text-sm font-medium text-foreground/90">Status</Label>
          <Input // Consider using a Select component if statuses are predefined
            id="status"
            name="status"
            type="text"
            value={report.status}
            onChange={handleChange}
            className="mt-1 w-full"
            required
          />
        </div>

        {/* Add more form fields for other editable report properties */}
        {/* Example:
        <div>
          <Label htmlFor="description">Description</Label>
          <textarea // Using a standard textarea for now
            id="description"
            name="description"
            value={report.description || ''}
            onChange={handleChange}
            className="mt-1 w-full p-2 border rounded-md" // Basic styling
          />
        </div>
        */}
        {/* Removed Textarea component usage, will use standard textarea if needed or add component later */}

        {error && <p className="text-sm text-red-600">{error}</p>}

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" className="gradient-primary-secondary text-white" disabled={saving}>
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}
