# Azure CLI Setup Scripts for Report Rendering Engine

This directory contains Azure CLI PowerShell scripts to complete the Cosmos DB and Blob Storage setup for the Report Rendering Engine project.

## Prerequisites

1. **Azure CLI installed** - [Download here](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
2. **PowerShell 5.1 or later**
3. **Azure account with permissions** to create resources in the `rg_CSHero` resource group
4. **Login to Azure CLI**: Run `az login` before executing any scripts

## Scripts Overview

### Main Setup Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `setup-blob-storage.ps1` | Creates Azure Storage Account and blob containers | Run first |
| `setup-cosmos-containers.ps1` | Creates Cosmos DB database and containers | Run second |

### Utility Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `get-connection-strings.ps1` | Retrieves connection strings for app configuration | After setup |
| `check-azure-resources.ps1` | Verifies all resources exist and are configured | Anytime |
| `cleanup-azure-resources.ps1` | Safely removes created resources | When needed |

## Quick Start

### Step 1: Login to Azure
```powershell
az login
```

### Step 2: Run Setup Scripts
```powershell
# Create blob storage
.\setup-blob-storage.ps1

# Create Cosmos DB containers
.\setup-cosmos-containers.ps1
```

### Step 3: Verify Setup
```powershell
# Check all resources
.\check-azure-resources.ps1

# Get connection strings
.\get-connection-strings.ps1 -SaveToFile
```

## Detailed Usage

### setup-blob-storage.ps1

Creates the Azure Storage Account and blob containers needed for the Report Rendering Engine.

**Default Parameters:**
- ResourceGroup: `rg_CSHero`
- StorageAccount: `csherostorage`
- Location: `centralus`

**Usage:**
```powershell
# Use defaults
.\setup-blob-storage.ps1

# Custom parameters
.\setup-blob-storage.ps1 -ResourceGroup "my-rg" -StorageAccount "mystorageaccount" -Location "eastus"
```

**Creates:**
- Storage Account: `csherostorage` (Standard LRS, StorageV2)
- Blob Container: `report-data` (private access)

### setup-cosmos-containers.ps1

Creates the Cosmos DB database and containers in the existing `cshero-cosmosdb` account.

**Default Parameters:**
- ResourceGroup: `rg_CSHero`
- CosmosAccount: `cshero-cosmosdb`
- DatabaseName: `ReportRenderingEngine`

**Usage:**
```powershell
# Use defaults
.\setup-cosmos-containers.ps1

# Custom parameters
.\setup-cosmos-containers.ps1 -CosmosAccount "my-cosmos-account" -DatabaseName "MyDatabase"
```

**Creates:**
- Database: `ReportRenderingEngine` (serverless)
- Container: `report-styles` (partitioned by `/tenantId`)

### get-connection-strings.ps1

Retrieves connection strings and configuration details for your application.

**Usage:**
```powershell
# Display connection strings
.\get-connection-strings.ps1

# Save to file
.\get-connection-strings.ps1 -SaveToFile
```

**Output:**
- Cosmos DB connection string
- Blob Storage connection string
- Individual endpoint and key details
- Ready-to-use appsettings.json format (when using -SaveToFile)

### check-azure-resources.ps1

Verifies that all required Azure resources exist and are properly configured.

**Usage:**
```powershell
.\check-azure-resources.ps1
```

**Checks:**
- Resource Group existence
- Cosmos DB account status
- Database and container creation
- Storage account status
- Blob container creation

### cleanup-azure-resources.ps1

Safely removes created resources. **Use with caution!**

**Usage:**
```powershell
# Interactive cleanup (storage only)
.\cleanup-azure-resources.ps1 -StorageOnly

# Force cleanup without confirmation
.\cleanup-azure-resources.ps1 -StorageOnly -Force
```

**Safety Features:**
- Requires confirmation unless `-Force` is used
- Only deletes storage account by default (protects Cosmos DB)
- Full Cosmos DB cleanup requires manual intervention

## Configuration Integration

After running the setup scripts, update your application configuration:

### appsettings.json
```json
{
  "CosmosDb": {
    "ConnectionString": "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=...",
    "DatabaseName": "ReportRenderingEngine"
  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=csherostorage;AccountKey=...",
    "ContainerName": "report-data"
  }
}
```

### Environment Variables (Alternative)
```bash
COSMOS_CONNECTION_STRING="AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=..."
BLOB_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=csherostorage;AccountKey=..."
```

## Troubleshooting

### Common Issues

1. **"Please login to Azure CLI first"**
   ```powershell
   az login
   ```

2. **Storage account name conflicts**
   - Storage account names must be globally unique
   - Try: `.\setup-blob-storage.ps1 -StorageAccount "csherostorage$(Get-Date -Format 'yyyyMMdd')"`

3. **Permission denied errors**
   - Ensure your Azure account has Contributor role on the resource group
   - Check: `az role assignment list --assignee <EMAIL>`

4. **Resource already exists**
   - Scripts handle existing resources gracefully
   - Use `check-azure-resources.ps1` to verify current state

### Verification Commands

```powershell
# Check Azure CLI version
az --version

# Check current subscription
az account show

# List resource groups
az group list --output table

# Check specific resource group
az group show --name "rg_CSHero"
```

## Cost Considerations

**Cosmos DB (Serverless):**
- No minimum charges
- Pay per Request Unit (RU) consumed
- Ideal for development and variable workloads

**Blob Storage (Standard LRS):**
- ~$0.018 per GB per month
- Transaction costs apply
- Most cost-effective option

## Security Notes

1. **Connection Strings**: Never commit to source control
2. **Access Keys**: Rotate regularly in production
3. **Network Access**: Consider IP restrictions for production
4. **Managed Identity**: Recommended for production deployments

## Next Steps

1. Run the setup scripts
2. Update your application configuration
3. Test connections using existing test projects
4. Consider setting up monitoring and alerts
5. Plan for production deployment with enhanced security

## Support

If you encounter issues:
1. Check the script output for specific error messages
2. Verify Azure CLI login status: `az account show`
3. Check resource group permissions
4. Review the generated `azure-connection-details.txt` file
5. Use `check-azure-resources.ps1` to verify current state
