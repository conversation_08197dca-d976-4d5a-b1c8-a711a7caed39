# Get connection strings for Azure resources
# Outputs connection details for application configuration

param(
    [string]$ResourceGroup = "rg_CSHero",
    [string]$CosmosAccount = "cshero-cosmosdb",
    [string]$StorageAccount = "csherostorage",
    [switch]$SaveToFile
)

Write-Host "=== Azure Connection Strings ===" -ForegroundColor Green
Write-Host ""

try {
    # Get Cosmos DB connection string
    Write-Host "🔗 Cosmos DB Connection String:" -ForegroundColor Cyan
    $cosmosConnectionString = az cosmosdb keys list `
        --name $CosmosAccount `
        --resource-group $ResourceGroup `
        --type connection-strings `
        --query 'connectionStrings[0].connectionString' -o tsv

    Write-Host $cosmosConnectionString -ForegroundColor White
    Write-Host ""

    # Get Blob Storage connection string
    Write-Host "🔗 Blob Storage Connection String:" -ForegroundColor Cyan
    $blobConnectionString = az storage account show-connection-string `
        --name $StorageAccount `
        --resource-group $ResourceGroup `
        --query 'connectionString' -o tsv

    Write-Host $blobConnectionString -ForegroundColor White
    Write-Host ""

    # Get individual keys for reference
    Write-Host "📋 Individual Connection Details:" -ForegroundColor Cyan
    $cosmosEndpoint = az cosmosdb show `
        --name $CosmosAccount `
        --resource-group $ResourceGroup `
        --query 'documentEndpoint' -o tsv

    $cosmosKey = az cosmosdb keys list `
        --name $CosmosAccount `
        --resource-group $ResourceGroup `
        --query 'primaryMasterKey' -o tsv

    Write-Host "Cosmos Endpoint: $cosmosEndpoint" -ForegroundColor White
    Write-Host "Cosmos Primary Key: $cosmosKey" -ForegroundColor White

    if ($SaveToFile) {
        $connectionDetails = @"
# Azure Connection Details for Report Rendering Engine
# Generated on $(Get-Date)

## Cosmos DB
CosmosConnectionString=$cosmosConnectionString
CosmosEndpoint=$cosmosEndpoint
CosmosPrimaryKey=$cosmosKey

## Blob Storage
BlobConnectionString=$blobConnectionString

## appsettings.json format
{
  "CosmosDb": {
    "ConnectionString": "$cosmosConnectionString",
    "DatabaseName": "ReportRenderingEngine"
  },
  "BlobStorage": {
    "ConnectionString": "$blobConnectionString",
    "ContainerName": "report-data"
  }
}
"@
        
        $connectionDetails | Out-File -FilePath "azure-connection-details.txt" -Encoding UTF8
        Write-Host ""
        Write-Host "💾 Connection details saved to: azure-connection-details.txt" -ForegroundColor Green
    }

} catch {
    Write-Host "❌ Error retrieving connection strings: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
