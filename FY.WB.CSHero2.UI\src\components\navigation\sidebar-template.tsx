"use client";

import { cn } from "@/lib/utils";
import { SidebarProps } from "./types";
import { UserAvatar } from "./user-avatar";
import { useNavigation } from "@/hooks/use-navigation";

export function SidebarTemplate({ children }: SidebarProps) {
  // Still using the hook for consistency with the rest of the app
  const { isExpanded } = useNavigation();
  
  return (
    <nav
      className="nav-transition fixed left-0 top-0 z-30 h-screen bg-nav-bg shadow-lg overflow-hidden pt-16 w-20"
    >
      <div className="flex h-full flex-col">
        {/* Header with user avatar */}
        <div className="flex h-16 items-center justify-center px-4">
          <UserAvatar />
        </div>

        <div className="flex flex-1 flex-col gap-2 overflow-y-auto px-2 py-4">
          {children}
        </div>
      </div>
    </nav>
  );
}
