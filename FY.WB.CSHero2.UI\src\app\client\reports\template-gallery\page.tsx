"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, Plus, Check } from "lucide-react";
import { TemplateGallery, ReportTemplate, StyleTemplate } from "@/components/features/reports/templates/template-gallery";
import { ChartComponent, ChartConfig } from "@/components/features/reports/visualization/chart-component";

export default function TemplateGalleryPage() {
  const router = useRouter();
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [selectedStyleTemplate, setSelectedStyleTemplate] = useState<StyleTemplate | null>(null);
  const [previewCharts, setPreviewCharts] = useState<ChartConfig[]>([]);

  // Handle template selection
  const handleSelectTemplate = (template: ReportTemplate) => {
    setSelectedTemplate(template);
    
    // Set preview charts based on the template's chartData
    if (template.chartData && Array.isArray(template.chartData)) {
      setPreviewCharts(template.chartData);
    } else {
      // Fallback to empty array if no chart data is available
      setPreviewCharts([]);
    }
  };

  // Handle style template selection
  const handleSelectStyleTemplate = (template: StyleTemplate) => {
    setSelectedStyleTemplate(template);
  };

  // Handle use template button click
  const handleUseTemplate = () => {
    if (selectedTemplate) {
      // Redirect to the editor page with the selected template and style template
      const styleParam = selectedStyleTemplate ? `&style=${selectedStyleTemplate.id}` : '';
      router.push(`/client/reports/editor?template=${selectedTemplate.id}${styleParam}`);
    }
  };

  // Handle create from scratch button click
  const handleCreateFromScratch = () => {
    // Redirect to the editor page with the default template
    router.push("/client/reports/editor?template=default");
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Template Gallery</h1>
          <p className="text-gray-500 mt-2">
            Choose from our pre-constructed templates or build your own custom report
          </p>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={() => router.back()}
            className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </button>
          <div className="flex space-x-2">
            <button
              onClick={handleCreateFromScratch}
              className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/70 flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create from Scratch
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-2">
            <TemplateGallery 
              onSelectTemplate={handleSelectTemplate} 
              onSelectStyleTemplate={handleSelectStyleTemplate} 
            />
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-8">
            <h2 className="text-xl font-semibold mb-4">Template Preview</h2>
            
            {selectedTemplate ? (
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium text-lg">{selectedTemplate.name}</h3>
                  <p className="text-gray-500 mt-1">{selectedTemplate.description}</p>
                  
                  {selectedStyleTemplate && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
                      <h4 className="text-sm font-medium text-blue-700 mb-2">
                        Selected Style: {selectedStyleTemplate.name}
                      </h4>
                      <div className="flex justify-center">
                        <div className="relative w-32 h-48 border rounded overflow-hidden">
                          <div className="relative w-full h-full">
                            <Image
                              src={selectedStyleTemplate.thumbnailUrl}
                              alt={`${selectedStyleTemplate.name} style template`}
                              className="object-cover"
                              fill
                              sizes="(max-width: 768px) 100vw, 128px"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {!selectedTemplate.templateData?.pages && !selectedTemplate.pages && selectedTemplate.sections && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Included Sections
                      </h4>
                      <div className="border rounded-md p-3 bg-gray-50">
                        <ul className="space-y-2">
                          {selectedTemplate.sections.map((section: any) => (
                            <li key={section.id} className="flex items-center text-sm">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              {section.title || section['section-title'] || "Untitled Section"}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                  
                  {selectedTemplate.fields && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Included Fields
                      </h4>
                      <div className="border rounded-md p-3 bg-gray-50">
                        <ul className="space-y-2">
                          {selectedTemplate.fields.map((field: any) => (
                            <li key={field.id} className="flex items-center text-sm">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span className="font-medium">{field.name}</span>
                              <span className="text-xs text-gray-500 ml-2">({field.type})</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                  
                  {previewCharts.length > 0 && (
                    <div className="mt-6 space-y-6">
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                        Sample Visualizations
                      </h4>
                      {previewCharts.map((chartConfig, index) => (
                        <div key={index} className="border rounded-md p-4">
                          <ChartComponent
                            config={chartConfig}
                            height={200}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                  
                  <div className="mt-8">
                    <button
                      onClick={handleUseTemplate}
                      className="w-full px-4 py-2 bg-primary text-white rounded-md primary/70"
                    >
                      Use This Template
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 border border-dashed border-gray-300 rounded-lg">
                <p className="text-gray-500">Select a template to preview</p>
                <p className="text-sm text-gray-400 mt-1">
                  Preview will show sample visualizations and included pages
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
