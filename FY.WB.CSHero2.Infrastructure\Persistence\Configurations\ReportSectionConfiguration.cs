using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ReportSectionConfiguration : IEntityTypeConfiguration<ReportSection>
    {
        public void Configure(EntityTypeBuilder<ReportSection> builder)
        {
            // Table configuration
            builder.ToTable("ReportSections");
            builder.HasKey(s => s.Id);
            
            // Property configurations
            builder.Property(s => s.ReportId)
                .IsRequired();
                
            builder.Property(s => s.Title)
                .IsRequired()
                .HasMaxLength(200);
                
            builder.Property(s => s.Type)
                .IsRequired()
                .HasMaxLength(50);
                
            builder.Property(s => s.Order)
                .IsRequired();
            
            // Relationship configurations
            builder.HasOne(s => s.Report)
                .WithMany(r => r.Sections)
                .HasForeignKey(s => s.ReportId)
                .OnDelete(DeleteBehavior.Cascade);
            
            // Index configurations
            builder.HasIndex(s => new { s.ReportId, s.Order })
                .HasDatabaseName("IX_ReportSections_ReportId_Order");
                
            builder.HasIndex(s => s.ReportId)
                .HasDatabaseName("IX_ReportSections_ReportId");
        }
    }
}
