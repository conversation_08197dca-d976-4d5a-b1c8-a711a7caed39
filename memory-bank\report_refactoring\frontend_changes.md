# Frontend Changes for Multi-Storage Report Structure

## Overview

This document outlines the frontend changes required to support the multi-storage report structure. The changes involve updating TypeScript interfaces, API service functions, and UI components to work with the new data model and API endpoints.

## TypeScript Interface Changes

### Current Interfaces

```typescript
// Current Report interface
interface Report {
  id: string;
  name: string;
  description: string;
  tenantId: string;
  creatorId: string;
  createdAt: string;
  lastModifiedAt: string;
  lastModifiedById: string;
  isPublished: boolean;
  isArchived: boolean;
  currentVersionId: string;
  currentVersionNumber: number;
}

// Current ReportVersion interface
interface ReportVersion {
  id: string;
  reportId: string;
  versionNumber: number;
  versionName: string;
  creatorId: string;
  createdAt: string;
  isCurrent: boolean;
  isPublished: boolean;
  jsonData: string;
}

// Current ComponentDefinition interface
interface ComponentDefinition {
  id: string;
  reportVersionId: string;
  sectionId: string;
  sectionName: string;
  componentCode: string;
  importsJson: string;
}
```

### Updated Interfaces

```typescript
// Updated Report interface
interface Report {
  id: string;
  name: string;
  description: string;
  tenantId: string;
  creatorId: string;
  createdAt: string;
  lastModifiedAt: string;
  lastModifiedById: string;
  isPublished: boolean;
  isArchived: boolean;
  currentVersionId: string;
  currentVersionNumber: number;
  dataDocumentId: string;
  componentsBlobId: string;
  style: ReportStyle;
}

// Updated ReportVersion interface
interface ReportVersion {
  id: string;
  reportId: string;
  versionNumber: number;
  versionName: string;
  creatorId: string;
  createdAt: string;
  isCurrent: boolean;
  isPublished: boolean;
  dataDocumentId: string;
  componentsBlobId: string;
}

// New ReportStyle interface
interface ReportStyle {
  id: string;
  reportId: string;
  theme: string;
  colorScheme: string;
  typography: string;
  spacing: string;
  layoutOptions?: LayoutOptions;
  typographyOptions?: TypographyOptions;
  structureOptions?: StructureOptions;
  contentOptions?: ContentOptions;
  visualOptions?: VisualOptions;
}

// Style option interfaces
interface LayoutOptions {
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

interface TypographyOptions {
  headingFont: string;
  bodyFont: string;
  baseFontSize: number;
}

interface StructureOptions {
  sectionSpacing: number;
  sectionBorders: boolean;
}

interface ContentOptions {
  textAlignment: string;
  listStyle: string;
}

interface VisualOptions {
  chartStyle: string;
  chartColors: string[];
}

// New ReportData interface
interface ReportData {
  id: string;
  reportId: string;
  versionId: string;
  versionNumber: number;
  sections: ReportSection[];
  metadata: ReportDataMetadata;
}

// New ReportSection interface
interface ReportSection {
  id: string;
  title: string;
  type: string;
  order: number;
  fields: ReportSectionField[];
  metadata?: Record<string, any>;
}

// New ReportSectionField interface
interface ReportSectionField {
  id: string;
  name: string;
  type: string;
  content: string;
  order: number;
  metadata?: Record<string, any>;
}

// New ReportDataMetadata interface
interface ReportDataMetadata {
  createdAt: string;
  createdBy: string;
  lastModifiedAt: string;
  lastModifiedBy: string;
}

// New ComponentDefinition interface
interface ComponentDefinition {
  id: string;
  name: string;
  sectionId: string;
  code: string;
  imports: string[];
  props: string[];
}

// New ComponentsMetadata interface
interface ComponentsMetadata {
  reportId: string;
  versionId: string;
  components: ComponentMetadata[];
  createdAt: string;
  createdBy: string;
}

// New ComponentMetadata interface
interface ComponentMetadata {
  id: string;
  name: string;
  sectionId: string;
  fileName: string;
  imports: string[];
  props: string[];
}
```

## API Service Function Changes

### Current API Service Functions

```typescript
// Current report service functions
const reportService = {
  getReports: async (tenantId?: string): Promise<Report[]> => {
    const url = tenantId ? `/api/reports?tenantId=${tenantId}` : '/api/reports';
    const response = await fetch(url);
    const data = await response.json();
    return data.data;
  },
  
  getReport: async (reportId: string): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}`);
    return await response.json();
  },
  
  createReport: async (report: Partial<Report>): Promise<Report> => {
    const response = await fetch('/api/reports', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    });
    return await response.json();
  },
  
  updateReport: async (reportId: string, report: Partial<Report>): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    });
    return await response.json();
  },
  
  deleteReport: async (reportId: string): Promise<void> => {
    await fetch(`/api/reports/${reportId}`, { method: 'DELETE' });
  },
  
  getReportVersions: async (reportId: string): Promise<ReportVersion[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions`);
    const data = await response.json();
    return data.data;
  },
  
  getReportVersion: async (reportId: string, versionId: string): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}`);
    return await response.json();
  },
  
  createReportVersion: async (reportId: string, version: Partial<ReportVersion>): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(version)
    });
    return await response.json();
  },
  
  updateReportVersion: async (reportId: string, versionId: string, version: Partial<ReportVersion>): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(version)
    });
    return await response.json();
  },
  
  getComponentDefinitions: async (reportId: string, versionId: string): Promise<ComponentDefinition[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components`);
    const data = await response.json();
    return data.data;
  },
  
  getComponentDefinition: async (reportId: string, versionId: string, componentId: string): Promise<ComponentDefinition> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components/${componentId}`);
    return await response.json();
  },
  
  createComponentDefinition: async (reportId: string, versionId: string, component: Partial<ComponentDefinition>): Promise<ComponentDefinition> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(component)
    });
    return await response.json();
  },
  
  updateComponentDefinition: async (reportId: string, versionId: string, componentId: string, component: Partial<ComponentDefinition>): Promise<ComponentDefinition> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components/${componentId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(component)
    });
    return await response.json();
  }
};
```

### Updated API Service Functions

```typescript
// Updated report service functions
const reportService = {
  getReports: async (tenantId?: string): Promise<Report[]> => {
    const url = tenantId ? `/api/reports?tenantId=${tenantId}` : '/api/reports';
    const response = await fetch(url);
    const data = await response.json();
    return data.data;
  },
  
  getReport: async (reportId: string): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}`);
    return await response.json();
  },
  
  createReport: async (report: Partial<Report>): Promise<Report> => {
    const response = await fetch('/api/reports', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    });
    return await response.json();
  },
  
  updateReport: async (reportId: string, report: Partial<Report>): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    });
    return await response.json();
  },
  
  deleteReport: async (reportId: string): Promise<void> => {
    await fetch(`/api/reports/${reportId}`, { method: 'DELETE' });
  },
  
  archiveReport: async (reportId: string): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}/archive`, {
      method: 'POST'
    });
    return await response.json();
  },
  
  unarchiveReport: async (reportId: string): Promise<Report> => {
    const response = await fetch(`/api/reports/${reportId}/unarchive`, {
      method: 'POST'
    });
    return await response.json();
  },
  
  getReportVersions: async (reportId: string): Promise<ReportVersion[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions`);
    const data = await response.json();
    return data.data;
  },
  
  getReportVersion: async (reportId: string, versionId: string): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}`);
    return await response.json();
  },
  
  createReportVersion: async (reportId: string, version: Partial<ReportVersion>): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(version)
    });
    return await response.json();
  },
  
  setCurrentVersion: async (reportId: string, versionId: string): Promise<ReportVersion> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/set-current`, {
      method: 'POST'
    });
    return await response.json();
  },
  
  getReportStyle: async (reportId: string): Promise<ReportStyle> => {
    const response = await fetch(`/api/reports/${reportId}/style`);
    return await response.json();
  },
  
  updateReportStyle: async (reportId: string, style: Partial<ReportStyle>): Promise<ReportStyle> => {
    const response = await fetch(`/api/reports/${reportId}/style`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(style)
    });
    return await response.json();
  },
  
  getReportData: async (reportId: string, versionId: string): Promise<ReportData> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/data`);
    return await response.json();
  },
  
  updateReportData: async (reportId: string, versionId: string, data: Partial<ReportData>): Promise<ReportData> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/data`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    return await response.json();
  },
  
  getReportSections: async (reportId: string, versionId: string): Promise<ReportSection[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections`);
    const data = await response.json();
    return data.data;
  },
  
  getReportSection: async (reportId: string, versionId: string, sectionId: string): Promise<ReportSection> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}`);
    return await response.json();
  },
  
  addReportSection: async (reportId: string, versionId: string, section: Partial<ReportSection>): Promise<ReportSection> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(section)
    });
    return await response.json();
  },
  
  updateReportSection: async (reportId: string, versionId: string, sectionId: string, section: Partial<ReportSection>): Promise<ReportSection> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(section)
    });
    return await response.json();
  },
  
  deleteReportSection: async (reportId: string, versionId: string, sectionId: string): Promise<void> => {
    await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}`, {
      method: 'DELETE'
    });
  },
  
  getReportSectionFields: async (reportId: string, versionId: string, sectionId: string): Promise<ReportSectionField[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}/fields`);
    const data = await response.json();
    return data.data;
  },
  
  getReportSectionField: async (reportId: string, versionId: string, sectionId: string, fieldId: string): Promise<ReportSectionField> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}/fields/${fieldId}`);
    return await response.json();
  },
  
  addReportSectionField: async (reportId: string, versionId: string, sectionId: string, field: Partial<ReportSectionField>): Promise<ReportSectionField> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}/fields`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(field)
    });
    return await response.json();
  },
  
  updateReportSectionField: async (reportId: string, versionId: string, sectionId: string, fieldId: string, field: Partial<ReportSectionField>): Promise<ReportSectionField> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}/fields/${fieldId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(field)
    });
    return await response.json();
  },
  
  deleteReportSectionField: async (reportId: string, versionId: string, sectionId: string, fieldId: string): Promise<void> => {
    await fetch(`/api/reports/${reportId}/versions/${versionId}/sections/${sectionId}/fields/${fieldId}`, {
      method: 'DELETE'
    });
  },
  
  getReportComponents: async (reportId: string, versionId: string): Promise<ComponentDefinition[]> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components`);
    const data = await response.json();
    return data.data;
  },
  
  getReportComponent: async (reportId: string, versionId: string, componentName: string): Promise<ComponentDefinition> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/components/${componentName}`);
    return await response.json();
  },
  
  renderReport: async (reportId: string, versionId: string): Promise<string> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/render`);
    return await response.text();
  },
  
  exportReportAsPdf: async (reportId: string, versionId: string): Promise<Blob> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/export/pdf`);
    return await response.blob();
  },
  
  exportReportAsHtml: async (reportId: string, versionId: string): Promise<Blob> => {
    const response = await fetch(`/api/reports/${reportId}/versions/${versionId}/export/html`);
    return await response.blob();
  }
};
```

## UI Component Changes

### Report List Component

#### Current Implementation

```tsx
const ReportList: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchReports = async () => {
      try {
        const data = await reportService.getReports();
        setReports(data);
      } catch (error) {
        console.error('Error fetching reports:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReports();
  }, []);
  
  if (loading) {
    return <div>Loading reports...</div>;
  }
  
  return (
    <div className="report-list">
      <h1>Reports</h1>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Created</th>
            <th>Last Modified</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {reports.map(report => (
            <tr key={report.id}>
              <td>{report.name}</td>
              <td>{new Date(report.createdAt).toLocaleDateString()}</td>
              <td>{report.lastModifiedAt ? new Date(report.lastModifiedAt).toLocaleDateString() : 'N/A'}</td>
              <td>{report.isPublished ? 'Published' : 'Draft'}</td>
              <td>
                <Link to={`/reports/${report.id}`}>View</Link>
                <Link to={`/reports/${report.id}/edit`}>Edit</Link>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

#### Updated Implementation

```tsx
const ReportList: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchReports = async () => {
      try {
        const data = await reportService.getReports();
        setReports(data);
      } catch (error) {
        console.error('Error fetching reports:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReports();
  }, []);
  
  const handleArchive = async (reportId: string) => {
    try {
      await reportService.archiveReport(reportId);
      setReports(reports.map(report => 
        report.id === reportId ? { ...report, isArchived: true } : report
      ));
    } catch (error) {
      console.error('Error archiving report:', error);
    }
  };
  
  const handleUnarchive = async (reportId: string) => {
    try {
      await reportService.unarchiveReport(reportId);
      setReports(reports.map(report => 
        report.id === reportId ? { ...report, isArchived: false } : report
      ));
    } catch (error) {
      console.error('Error unarchiving report:', error);
    }
  };
  
  if (loading) {
    return <div>Loading reports...</div>;
  }
  
  return (
    <div className="report-list">
      <h1>Reports</h1>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Created</th>
            <th>Last Modified</th>
            <th>Status</th>
            <th>Theme</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {reports.map(report => (
            <tr key={report.id} className={report.isArchived ? 'archived' : ''}>
              <td>{report.name}</td>
              <td>{new Date(report.createdAt).toLocaleDateString()}</td>
              <td>{report.lastModifiedAt ? new Date(report.lastModifiedAt).toLocaleDateString() : 'N/A'}</td>
              <td>{report.isPublished ? 'Published' : 'Draft'}</td>
              <td>{report.style?.theme || 'Default'}</td>
              <td>
                <Link to={`/reports/${report.id}`}>View</Link>
                <Link to={`/reports/${report.id}/edit`}>Edit</Link>
                {report.isArchived ? (
                  <button onClick={() => handleUnarchive(report.id)}>Unarchive</button>
                ) : (
                  <button onClick={() => handleArchive(report.id)}>Archive</button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

### Report Editor Component

#### Current Implementation

```tsx
const ReportEditor: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const [report, setReport] = useState<Report | null>(null);
  const [version, setVersion] = useState<ReportVersion | null>(null);
  const [loading, setLoading] = useState(true);
  const [jsonData, setJsonData] = useState<any>(null);
  
  useEffect(() => {
    const fetchReportData = async () => {
      try {
        const reportData = await reportService.getReport(reportId);
        setReport(reportData);
        
        const versionData = await reportService.getReportVersion(reportId, reportData.currentVersionId);
        setVersion(versionData);
        
        if (versionData.jsonData) {
          setJsonData(JSON.parse(versionData.jsonData));
        }
      } catch (error) {
        console.error('Error fetching report data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReportData();
  }, [reportId]);
  
  const handleSave = async () => {
    if (!report || !version) return;
    
    try {
      const updatedVersion = await reportService.updateReportVersion(reportId, version.id, {
        ...version,
        jsonData: JSON.stringify(jsonData)
      });
      setVersion(updatedVersion);
      alert('Report saved successfully');
    } catch (error) {
      console.error('Error saving report:', error);
      alert('Error saving report');
    }
  };
  
  if (loading) {
    return <div>Loading report...</div>;
  }
  
  if (!report || !version) {
    return <div>Report not found</div>;
  }
  
  return (
    <div className="report-editor">
      <h1>Editing: {report.name}</h1>
      <div className="report-metadata">
        <p>Version: {version.versionNumber} ({version.versionName})</p>
        <p>Status: {report.isPublished ? 'Published' : 'Draft'}</p>
      </div>
      
      <div className="report-content">
        {/* Simple JSON editor for demonstration */}
        <textarea
          value={JSON.stringify(jsonData, null, 2)}
          onChange={e => {
            try {
              setJsonData(JSON.parse(e.target.value));
            } catch (error) {
              // Ignore parse errors while typing
            }
          }}
          rows={20}
          cols={80}
        />
      </div>
      
      <div className="report-actions">
        <button onClick={handleSave}>Save</button>
      </div>
    </div>
  );
};
```

#### Updated Implementation

```tsx
const ReportEditor: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const [report, setReport] = useState<Report | null>(null);
  const [version, setVersion] = useState<ReportVersion | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [style, setStyle] = useState<ReportStyle | null>(null);
  const [components, setComponents] = useState<ComponentDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('content'); // 'content', 'style', 'preview'
  
  useEffect(() => {
    const fetchReportData = async () => {
      try {
        const reportData = await reportService.getReport(reportId);
        setReport(reportData);
        
        const versionData = await reportService.getReportVersion(reportId, reportData.currentVersionId);
        setVersion(versionData);
        
        const styleData = await reportService.getReportStyle(reportId);
        setStyle(styleData);
        
        const reportDataResponse = await reportService.getReportData(reportId, reportData.currentVersionId);
        setReportData(reportDataResponse);
        
        const componentsData = await reportService.getReportComponents(reportId, reportData.currentVersionId);
        setComponents(componentsData);
      } catch (error) {
        console.error('Error fetching report data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReportData();
  }, [reportId]);
  
  const handleSaveContent = async () => {
    if (!report || !version || !reportData) return;
    
    try {
      const updatedReportData = await reportService.updateReportData(reportId, version.id, reportData);
      setReportData(updatedReportData);
      alert('Report content saved successfully');
    } catch (error) {
      console.error('Error saving report content:', error);
      alert('Error saving report content');
    }
  };
  
  const handleSaveStyle = async () => {
    if (!report || !style) return;
    
    try {
      const updatedStyle = await reportService.updateReportStyle(reportId, style);
      setStyle(updatedStyle);
      alert('Report style saved successfully');
    } catch (error) {
      console.error('Error saving report style:', error);
      alert('Error saving report style');
    }
  };
  
  const handleAddSection = async () => {
    if (!report || !version || !reportData) return;
    
    const newSection: Partial<ReportSection> = {
      title: 'New Section',
      type: 'text',
      order: reportData.sections.length,
      fields: []
    };
    
    try {
      const addedSection = await reportService.addReportSection(reportId, version.id, newSection);
      setReportData({
        ...reportData,
        sections: [...reportData.sections, addedSection]
      });
    } catch (error) {
      console.error('Error adding section:', error);
      alert('Error adding section');
    }
  };
  
  const handleUpdateSection = async (sectionId: string, updates: Partial<ReportSection>) => {
    if (!report || !version || !reportData) return;
    
    try {
      const updatedSection = await reportService.updateReportSection(reportId, version.id, sectionId, updates);
      setReportData({
        ...reportData,
        sections: reportData.sections.map(section => 
          section.id === sectionId ? updatedSection : section
        )
      });
    } catch (error) {
      console.error('Error updating section:', error);
      alert('Error updating section');
    }
  };
  
  const handleDeleteSection = async (sectionId: string) => {
    if (!report || !version || !reportData) return;
    
    try {
      await reportService.deleteReportSection(reportId, version.id, sectionId);
      setReportData({
        ...reportData,
        sections: reportData.sections.filter(section => section.id !== sectionId)
      });
    } catch (error) {
      console.error('Error deleting section:', error);
      alert('Error deleting section');
    }
  };
  
  const handleAddField = async (sectionId: string) => {
    if (!report || !version || !reportData) return;
    
    const section = reportData.sections.find(s => s.id === sectionId);
    if (!section) return;
    
    const newField: Partial<ReportSectionField> = {
      name: 'newField',
      type: 'text',
      content: '',
      order: section.fields.length
    };
    
    try {
      const addedField = await reportService.addReportSectionField(reportId, version.id, sectionId, newField);
      setReportData({
        ...reportData,
        sections: reportData.sections.map(section =>