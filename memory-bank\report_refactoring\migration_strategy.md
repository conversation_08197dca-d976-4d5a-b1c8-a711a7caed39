# Migration Strategy: SQL to Multi-Storage Report Structure

## Overview

This document outlines the strategy for migrating the existing report data from a SQL-only approach to the new multi-storage architecture. The migration will involve moving:

1. Report data (sections and fields) from SQL JSON columns to Azure Cosmos DB
2. Component definitions from SQL to Azure Blob Storage
3. Updating SQL entities with references to the new storage locations

The migration strategy is designed to minimize risk, ensure data integrity, and minimize disruption to users.

## Migration Principles

1. **Data Integrity**: Ensure no data is lost during migration
2. **Minimal Disruption**: Minimize downtime and impact on users
3. **Reversibility**: Maintain the ability to roll back if issues occur
4. **Verifiability**: Validate migration success at each step
5. **Incremental Approach**: Migrate in phases to reduce risk
6. **Performance Optimization**: Optimize migration process for large datasets

## Pre-Migration Activities

### 1. Infrastructure Setup

1. **Create Azure Resources**
   - Set up Azure Cosmos DB account, database, and container
   - Set up Azure Blob Storage account and container
   - Configure access policies and networking
   - Set up monitoring and alerts

2. **Application Updates**
   - Implement new domain models
   - Implement repository interfaces
   - Implement service interfaces
   - Update API controllers
   - Implement migration service

3. **Environment Preparation**
   - Create staging environment with production-like data
   - Configure connection strings
   - Set up logging and monitoring

### 2. Migration Testing

1. **Test Migration Process**
   - Test with small dataset in development
   - Test with representative dataset in staging
   - Test with production-size dataset in staging
   - Measure performance and optimize

2. **Test Rollback Process**
   - Test rollback procedures
   - Verify data integrity after rollback
   - Measure rollback time

3. **Test Application with Migrated Data**
   - Verify application functionality with migrated data
   - Test performance with migrated data
   - Test edge cases and error scenarios

### 3. Migration Planning

1. **Create Detailed Migration Plan**
   - Define migration schedule
   - Define roles and responsibilities
   - Define communication plan
   - Define success criteria
   - Define rollback criteria

2. **Create Contingency Plans**
   - Define fallback procedures
   - Define escalation procedures
   - Define data recovery procedures

3. **Create Communication Plan**
   - Notify users of planned migration
   - Prepare status updates
   - Prepare post-migration communication

## Migration Phases

### Phase 1: Preparation (Week 1)

1. **Backup Existing Data**
   - Create full backup of SQL database
   - Verify backup integrity
   - Store backup in secure location

2. **Deploy New Application Version**
   - Deploy application with new models and interfaces
   - Configure to use SQL-only mode initially
   - Verify application functionality

3. **Enable Feature Flags**
   - Configure feature flags for multi-storage features
   - Keep flags disabled initially
   - Test flag toggling

### Phase 2: Pilot Migration (Week 2)

1. **Select Pilot Reports**
   - Identify 5-10 representative reports
   - Include various sizes and complexities
   - Include reports from different tenants

2. **Migrate Pilot Reports**
   - Execute migration for pilot reports
   - Verify data integrity
   - Measure performance
   - Document any issues

3. **Validate Pilot Migration**
   - Test application with migrated reports
   - Verify rendering and functionality
   - Compare performance before and after
   - Gather feedback from selected users

### Phase 3: Incremental Migration (Weeks 2-3)

1. **Define Migration Batches**
   - Group reports by tenant
   - Group reports by size and complexity
   - Schedule batches for migration
   - Prioritize less critical reports first

2. **Execute Batch Migration**
   - Migrate one batch at a time
   - Verify each batch after migration
   - Monitor system performance during migration
   - Adjust batch size based on performance

3. **Validate Incremental Migration**
   - Test application with each migrated batch
   - Verify cross-batch functionality
   - Monitor system performance
   - Address any issues before proceeding

### Phase 4: Full Migration (Week 3)

1. **Schedule Maintenance Window**
   - Notify users of planned maintenance
   - Schedule during low-usage period
   - Prepare status updates

2. **Execute Full Migration**
   - Migrate remaining reports
   - Monitor progress and performance
   - Address any issues immediately
   - Document migration metrics

3. **Enable Multi-Storage Mode**
   - Enable feature flags for multi-storage
   - Verify application functionality
   - Monitor system performance
   - Be prepared to roll back if necessary

### Phase 5: Post-Migration Activities (Week 4)

1. **Verify Migration Success**
   - Verify all reports are migrated
   - Verify data integrity
   - Verify application functionality
   - Verify system performance

2. **Optimize Performance**
   - Analyze performance metrics
   - Identify bottlenecks
   - Implement optimizations
   - Verify improvement

3. **Clean Up Legacy Data**
   - Archive legacy JSON data
   - Update database schema
   - Reclaim database space
   - Update documentation

## Migration Process Details

### Data Extraction and Transformation

The migration service will extract data from the existing SQL database and transform it into the new data models for Cosmos DB and Blob Storage.

```csharp
public async Task<int> MigrateReportDataAsync(CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Starting report data migration");
    
    // Get all reports with their current versions
    var reports = await _sqlContext.Reports
        .Include(r => r.Versions.Where(v => v.IsCurrent))
        .ThenInclude(v => v.ComponentDefinitions)
        .ToListAsync(cancellationToken);
        
    _logger.LogInformation("Found {ReportCount} reports to migrate", reports.Count);
    
    int migratedCount = 0;
    
    foreach (var report in reports)
    {
        try
        {
            var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
            if (currentVersion == null)
            {
                _logger.LogWarning("Report {ReportId} has no current version, skipping", report.Id);
                continue;
            }
            
            // Migrate report data to Cosmos DB
            var reportData = await MigrateReportDataToCosmosDbAsync(report, currentVersion, cancellationToken);
            
            // Migrate components to Blob Storage
            var blobId = await MigrateComponentsToBlobStorageAsync(report, currentVersion, cancellationToken);
            
            // Update SQL references
            report.DataDocumentId = reportData.Id;
            report.ComponentsBlobId = blobId;
            currentVersion.DataDocumentId = reportData.Id;
            currentVersion.ComponentsBlobId = blobId;
            
            await _sqlContext.SaveChangesAsync(cancellationToken);
            
            migratedCount++;
            _logger.LogInformation("Successfully migrated report {ReportId}", report.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error migrating report {ReportId}", report.Id);
        }
    }
    
    _logger.LogInformation("Completed report data migration. Migrated {MigratedCount} of {TotalCount} reports", 
        migratedCount, reports.Count);
        
    return migratedCount;
}
```

### Cosmos DB Migration

The migration service will extract sections and fields from the JSON data in SQL and create documents in Cosmos DB.

```csharp
private async Task<ReportData> MigrateReportDataToCosmosDbAsync(
    Report report, 
    ReportVersion version, 
    CancellationToken cancellationToken)
{
    // Extract sections and fields from JSON data
    var jsonData = version.JsonData;
    if (string.IsNullOrEmpty(jsonData))
    {
        _logger.LogWarning("Report {ReportId} has empty JSON data", report.Id);
        jsonData = "{}";
    }
    
    var reportContent = JsonSerializer.Deserialize<ReportContent>(jsonData, 
        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        
    var sections = new List<ReportSection>();
    
    if (reportContent?.Sections != null)
    {
        int sectionOrder = 0;
        foreach (var sectionData in reportContent.Sections)
        {
            var section = new ReportSection
            {
                Id = Guid.NewGuid().ToString(),
                Title = sectionData.Title ?? "Untitled Section",
                Type = sectionData.Type ?? "unknown",
                Order = sectionOrder++,
                Fields = new List<ReportSectionField>(),
                Metadata = new Dictionary<string, object>()
            };
            
            if (sectionData.Metadata != null)
            {
                foreach (var meta in sectionData.Metadata)
                {
                    section.Metadata[meta.Key] = meta.Value;
                }
            }
            
            if (sectionData.Fields != null)
            {
                int fieldOrder = 0;
                foreach (var fieldData in sectionData.Fields)
                {
                    var field = new ReportSectionField
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = fieldData.Name,
                        Type = fieldData.Type ?? "text",
                        Content = fieldData.Content ?? string.Empty,
                        Order = fieldOrder++,
                        Metadata = new Dictionary<string, object>()
                    };
                    
                    if (fieldData.Metadata != null)
                    {
                        foreach (var meta in fieldData.Metadata)
                        {
                            field.Metadata[meta.Key] = meta.Value;
                        }
                    }
                    
                    section.Fields.Add(field);
                }
            }
            
            sections.Add(section);
        }
    }
    
    var reportData = new ReportData
    {
        Id = $"report-data-{Guid.NewGuid()}",
        ReportId = report.Id.ToString(),
        VersionId = version.Id.ToString(),
        VersionNumber = version.VersionNumber,
        Sections = sections,
        Metadata = new ReportDataMetadata
        {
            CreatedAt = DateTime.UtcNow,
            CreatedBy = version.CreatorId?.ToString() ?? "system",
            LastModifiedAt = DateTime.UtcNow,
            LastModifiedBy = version.CreatorId?.ToString() ?? "system"
        }
    };
    
    await _cosmosRepository.CreateReportDataAsync(reportData, cancellationToken);
    
    return reportData;
}
```

### Blob Storage Migration

The migration service will extract component definitions from SQL and store them in Blob Storage.

```csharp
private async Task<string> MigrateComponentsToBlobStorageAsync(
    Report report, 
    ReportVersion version, 
    CancellationToken cancellationToken)
{
    var components = new List<ComponentDefinition>();
    
    foreach (var componentDef in version.ComponentDefinitions)
    {
        var component = new ComponentDefinition
        {
            Id = componentDef.Id.ToString(),
            Name = componentDef.Name ?? $"Component{componentDef.Id}",
            SectionId = componentDef.SectionId,
            Code = componentDef.Code ?? string.Empty,
            Imports = JsonSerializer.Deserialize<List<string>>(componentDef.ImportsJson ?? "[]") ?? new List<string>(),
            Props = ExtractProps(componentDef.Code ?? string.Empty)
        };
        
        components.Add(component);
    }
    
    return await _blobRepository.SaveComponentsAsync(report.Id, version.Id, components, cancellationToken);
}

private List<string> ExtractProps(string componentCode)
{
    var props = new List<string>();
    
    if (string.IsNullOrEmpty(componentCode))
    {
        return props;
    }
    
    // Simple regex to extract props from component code
    var propsRegex = new Regex(@"interface\s+Props\s*{([^}]*)}", RegexOptions.Singleline);
    var match = propsRegex.Match(componentCode);
    
    if (match.Success && match.Groups.Count > 1)
    {
        var propsBlock = match.Groups[1].Value;
        var propLines = propsBlock.Split('\n');
        
        foreach (var line in propLines)
        {
            var trimmedLine = line.Trim();
            if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("//"))
            {
                continue;
            }
            
            var propMatch = Regex.Match(trimmedLine, @"(\w+)\s*:");
            if (propMatch.Success && propMatch.Groups.Count > 1)
            {
                props.Add(propMatch.Groups[1].Value);
            }
        }
    }
    
    return props;
}
```

### SQL Update

The migration service will update the SQL entities with references to the new storage locations.

```csharp
private async Task UpdateSqlReferencesAsync(
    Report report, 
    ReportVersion version, 
    string documentId, 
    string blobId, 
    CancellationToken cancellationToken)
{
    report.DataDocumentId = documentId;
    report.ComponentsBlobId = blobId;
    
    version.DataDocumentId = documentId;
    version.ComponentsBlobId = blobId;
    
    // Update all versions of this report
    foreach (var otherVersion in report.Versions.Where(v => v.Id != version.Id))
    {
        // For non-current versions, we'll migrate them on-demand
        // Just update the flag to indicate they need migration
        otherVersion.NeedsMigration = true;
    }
    
    await _sqlContext.SaveChangesAsync(cancellationToken);
}
```

## Migration Validation

### Data Integrity Validation

The migration service will validate the integrity of the migrated data by comparing the original SQL data with the new data in Cosmos DB and Blob Storage.

```csharp
public async Task<bool> ValidateMigrationAsync(Guid reportId, CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Validating migration for report {ReportId}", reportId);
    
    // Get report from SQL
    var report = await _sqlContext.Reports
        .Include(r => r.Versions.Where(v => v.IsCurrent))
        .ThenInclude(v => v.ComponentDefinitions)
        .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
        
    if (report == null)
    {
        _logger.LogWarning("Report {ReportId} not found", reportId);
        return false;
    }
    
    var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
    if (currentVersion == null)
    {
        _logger.LogWarning("Report {ReportId} has no current version", reportId);
        return false;
    }
    
    // Validate Cosmos DB data
    var isCosmosValid = await ValidateCosmosDbDataAsync(report, currentVersion, cancellationToken);
    
    // Validate Blob Storage data
    var isBlobValid = await ValidateBlobStorageDataAsync(report, currentVersion, cancellationToken);
    
    return isCosmosValid && isBlobValid;
}

private async Task<bool> ValidateCosmosDbDataAsync(
    Report report, 
    ReportVersion version, 
    CancellationToken cancellationToken)
{
    if (string.IsNullOrEmpty(report.DataDocumentId))
    {
        _logger.LogWarning("Report {ReportId} has no data document ID", report.Id);
        return false;
    }
    
    // Get data from Cosmos DB
    var reportData = await _cosmosRepository.GetReportDataAsync(report.DataDocumentId, cancellationToken);
    if (reportData == null)
    {
        _logger.LogWarning("Report data document {DocumentId} not found", report.DataDocumentId);
        return false;
    }
    
    // Validate basic properties
    if (reportData.ReportId != report.Id.ToString() || reportData.VersionId != version.Id.ToString())
    {
        _logger.LogWarning("Report data document {DocumentId} has incorrect report or version ID", report.DataDocumentId);
        return false;
    }
    
    // Extract original sections and fields from JSON
    var jsonData = version.JsonData;
    if (string.IsNullOrEmpty(jsonData))
    {
        // If original data was empty, just check that we have an empty document
        return reportData.Sections.Count == 0;
    }
    
    var reportContent = JsonSerializer.Deserialize<ReportContent>(jsonData, 
        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        
    // Validate section count
    if (reportContent?.Sections?.Count != reportData.Sections.Count)
    {
        _logger.LogWarning("Report data document {DocumentId} has incorrect section count", report.DataDocumentId);
        return false;
    }
    
    // Validate sections and fields
    // This is a simplified validation - in a real implementation, you would do a more thorough comparison
    foreach (var originalSection in reportContent.Sections)
    {
        var migratedSection = reportData.Sections.FirstOrDefault(s => s.Title == originalSection.Title);
        if (migratedSection == null)
        {
            _logger.LogWarning("Section {SectionTitle} not found in migrated data", originalSection.Title);
            return false;
        }
        
        if (originalSection.Fields?.Count != migratedSection.Fields.Count)
        {
            _logger.LogWarning("Section {SectionTitle} has incorrect field count", originalSection.Title);
            return false;
        }
    }
    
    return true;
}

private async Task<bool> ValidateBlobStorageDataAsync(
    Report report, 
    ReportVersion version, 
    CancellationToken cancellationToken)
{
    if (string.IsNullOrEmpty(report.ComponentsBlobId))
    {
        _logger.LogWarning("Report {ReportId} has no components blob ID", report.Id);
        return false;
    }
    
    // Get metadata from Blob Storage
    var componentsMetadata = await _blobRepository.GetComponentsMetadataAsync(report.ComponentsBlobId, cancellationToken);
    if (componentsMetadata == null)
    {
        _logger.LogWarning("Components metadata not found for blob ID {BlobId}", report.ComponentsBlobId);
        return false;
    }
    
    // Validate basic properties
    if (componentsMetadata.ReportId != report.Id.ToString() || componentsMetadata.VersionId != version.Id.ToString())
    {
        _logger.LogWarning("Components metadata has incorrect report or version ID", report.ComponentsBlobId);
        return false;
    }
    
    // Validate component count
    if (version.ComponentDefinitions.Count != componentsMetadata.Components.Count)
    {
        _logger.LogWarning("Components metadata has incorrect component count", report.ComponentsBlobId);
        return false;
    }
    
    // Validate each component
    foreach (var originalComponent in version.ComponentDefinitions)
    {
        var componentName = originalComponent.Name ?? $"Component{originalComponent.Id}";
        var migratedComponentMetadata = componentsMetadata.Components.FirstOrDefault(c => c.Name == componentName);
        if (migratedComponentMetadata == null)
        {
            _logger.LogWarning("Component {ComponentName} not found in migrated data", componentName);
            return false;
        }
        
        // Get component code from Blob Storage
        var migratedComponent = await _blobRepository.GetComponentAsync(report.ComponentsBlobId, componentName, cancellationToken);
        if (migratedComponent == null)
        {
            _logger.LogWarning("Component {ComponentName} code not found", componentName);
            return false;
        }
        
        // Validate component code
        if (migratedComponent.Code != originalComponent.Code)
        {
            _logger.LogWarning("Component {ComponentName} code does not match", componentName);
            return false;
        }
    }
    
    return true;
}
```

### Application Functionality Validation

The migration service will validate the functionality of the application with the migrated data by testing key operations.

```csharp
public async Task<bool> ValidateApplicationFunctionalityAsync(Guid reportId, CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Validating application functionality for report {ReportId}", reportId);
    
    try
    {
        // Test report retrieval
        var report = await _reportService.GetReportAsync(reportId, cancellationToken);
        if (report == null)
        {
            _logger.LogWarning("Failed to retrieve report {ReportId}", reportId);
            return false;
        }
        
        // Test report data retrieval
        var reportData = await _reportDataService.GetReportDataAsync(reportId, report.CurrentVersionId, cancellationToken);
        if (reportData == null)
        {
            _logger.LogWarning("Failed to retrieve report data for report {ReportId}", reportId);
            return false;
        }
        
        // Test report rendering
        var renderedHtml = await _reportRenderingService.RenderReportAsync(reportId, report.CurrentVersionId, cancellationToken);
        if (string.IsNullOrEmpty(renderedHtml))
        {
            _logger.LogWarning("Failed to render report {ReportId}", reportId);
            return false;
        }
        
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error validating application functionality for report {ReportId}", reportId);
        return false;
    }
}
```

## Rollback Strategy

### Rollback Criteria

The migration will be rolled back if any of the following conditions are met:

1. Data integrity validation fails for a significant number of reports
2. Application functionality validation fails
3. Performance degradation exceeds acceptable thresholds
4. Critical errors occur during migration
5. Business stakeholders decide to abort the migration

### Rollback Process

```csharp
public async Task<bool> RollbackMigrationAsync(Guid reportId, CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Rolling back migration for report {ReportId}", reportId);
    
    try
    {
        // Get report from SQL
        var report = await _sqlContext.Reports
            .Include(r => r.Versions)
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
            
        if (report == null)
        {
            _logger.LogWarning("Report {ReportId} not found", reportId);
            return false;
        }
        
        // Clear references to external storage
        report.DataDocumentId = null;
        report.ComponentsBlobId = null;
        
        foreach (var version in report.Versions)
        {
            version.DataDocumentId = null;
            version.ComponentsBlobId = null;
            version.NeedsMigration = false;
        }
        
        await _sqlContext.SaveChangesAsync(cancellationToken);
        
        // Delete data from Cosmos DB
        if (!string.IsNullOrEmpty(report.DataDocumentId))
        {
            await _cosmosRepository.DeleteReportDataAsync(report.DataDocumentId, cancellationToken);
        }
        
        // Delete components from Blob Storage
        if (!string.IsNullOrEmpty(report.ComponentsBlobId))
        {
            await _blobRepository.DeleteComponentsAsync(report.ComponentsBlobId, cancellationToken);
        }
        
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error rolling back migration for report {ReportId}", reportId);
        return false;
    }
}

public async Task<int> RollbackAllMigrationsAsync(CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Rolling back all migrations");
    
    try
    {
        // Get all reports with external storage references
        var reports = await _sqlContext.Reports
            .Where(r => r.DataDocumentId != null || r.ComponentsBlobId != null)
            .Include(r => r.Versions)
            .ToListAsync(cancellationToken);
            
        _logger.LogInformation("Found {ReportCount} reports to roll back", reports.Count);
        
        int rolledBackCount = 0;
        
        foreach (var report in reports)
        {
            try
            {
                // Clear references to external storage
                var dataDocumentId = report.DataDocumentId;
                var componentsBlobId = report.ComponentsBlobId;
                
                report.DataDocumentId = null;
                report.ComponentsBlobId = null;
                
                foreach (var version in report.Versions)
                {
                    version.DataDocumentId = null;
                    version.ComponentsBlobId = null;
                    version.NeedsMigration = false;
                }
                
                await _sqlContext.SaveChangesAsync(cancellationToken);
                
                // Delete data from Cosmos DB
                if (!string.IsNullOrEmpty(dataDocumentId))
                {
                    await _cosmosRepository.DeleteReportDataAsync(dataDocumentId, cancellationToken);
                }
                
                // Delete components from Blob Storage
                if (!string.IsNullOrEmpty(componentsBlobId))
                {
                    await _blobRepository.DeleteComponentsAsync(componentsBlobId, cancellationToken);
                }
                
                rolledBackCount++;
                _logger.LogInformation("Successfully rolled back report {ReportId}", report.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rolling back report {ReportId}", report.Id);
            }
        }
        
        _logger.LogInformation("Completed rollback. Rolled back {RolledBackCount} of {TotalCount} reports", 
            rolledBackCount, reports.Count);
            
        return rolledBackCount;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error rolling back all migrations");
        throw;
    }
}
```

## Post-Migration Activities

### 1. Performance Monitoring

1. **Monitor System Performance**
   - Track response times for key operations
   - Track resource utilization
   - Track error rates
   - Compare with pre-migration baseline

2. **Identify and Address Issues**
   - Analyze performance bottlenecks
   - Implement optimizations
   - Adjust resource allocation
   - Update monitoring thresholds

### 2. User Support

1. **Provide User Guidance**
   - Communicate changes to users
   - Provide documentation for new features
   - Offer training sessions if needed
   - Address user questions and concerns

2. **Monitor User Feedback**
   - Collect feedback on the new system
   - Address usability issues
   - Implement quick fixes for critical issues
   - Plan improvements based on feedback

### 3. Documentation Update

1. **Update Technical Documentation**
   - Document new architecture
   - Document migration process
   - Document lessons learned
   - Update system diagrams

2. **Update User Documentation**
   - Update user guides
   - Update FAQs
   - Update training materials
   - Update support procedures

### 4. Legacy Data Cleanup

1. **Archive Legacy Data**
   - Create archive of legacy JSON data
   - Store archive in secure location
   - Document archive structure
   - Implement archive access procedures

2. **Update Database Schema**
   - Remove unused columns
   - Optimize indexes
   - Update constraints
   - Generate new database diagram

## Migration Metrics and Reporting

### 1. Migration Progress Metrics

- Number of reports migrated
- Percentage of total reports migrated
- Migration rate (reports per hour)
- Error rate during migration
- Validation success rate

### 2. Performance Metrics

- Response time for key operations before and after migration
- Resource utilization before and after migration
- Error rate before and after migration
- User satisfaction before and after migration

### 3. Migration Reports

- Daily progress reports during migration
- Final migration report
- Performance comparison report
- Lessons learned report

## Conclusion

This migration strategy provides a comprehensive plan for migrating from the current SQL-only approach to the new multi-storage architecture. By following this strategy, the team can minimize risk, ensure data integrity, and minimize disruption to users.

The phased approach allows for incremental migration and validation, reducing the risk of data loss or system downtime. The detailed validation and rollback procedures provide a safety net in case issues arise during migration.

The post-migration activities ensure that the new system is properly monitored, documented, and supported, setting the stage for successful operation in the new architecture.