"use client";

import { Navigation } from "../navigation/navigation";
import { FooterTemplate } from "../navigation/footer-template";
import { NavigationProvider, useNavigation } from "@/hooks/use-navigation";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

function MainContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isClientOrAdmin = pathname.startsWith("/admin") || pathname.startsWith("/client");
  const isReportEditorPage = pathname.startsWith("/client/reports/editor");
  
  return (
    <main className={cn(
      "flex-1 transition-all duration-300 ease-in-out mt-16", // Always add top padding for header
      "min-h-[calc(100vh-4rem)]", // Ensure minimum height for content (100vh - header height)
      isClientOrAdmin && !isReportEditorPage ? "ml-20" : "" // Always use expanded sidebar margin
    )}>
      {children}
    </main>
  );
}

interface BaseLayoutProps {
  children: React.ReactNode;
}

export function BaseLayout({ children }: BaseLayoutProps) {
  // const pathname = usePathname();
  // const isClientOrAdmin = pathname.startsWith("/admin") || pathname.startsWith("/client");
  
  return (
    <NavigationProvider>
      <div className={cn(
        "test flex min-h-screen flex-col"
      )}>
        <Navigation />
        <MainContent>{children}</MainContent>
        <FooterTemplate />
      </div>
    </NavigationProvider>
  );
}
