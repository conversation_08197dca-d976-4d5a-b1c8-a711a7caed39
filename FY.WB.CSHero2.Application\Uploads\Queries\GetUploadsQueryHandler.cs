using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Uploads.Queries
{
    public class GetUploadsQueryHandler : IRequestHandler<GetUploadsQuery, PagedResult<UploadDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetUploadsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<PagedResult<UploadDto>> Handle(GetUploadsQuery request, CancellationToken cancellationToken)
        {
            var queryParameters = request.Parameters;
            var page = queryParameters.Page ?? 1;
            var limit = queryParameters.Limit ?? 10;

            IQueryable<Upload> query = _context.Uploads.AsNoTracking();

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(queryParameters.Search))
            {
                var searchTerm = queryParameters.Search.ToLower().Trim();
                query = query.Where(u => u.Filename != null && u.Filename.ToLower().Contains(searchTerm));
            }

            if (!string.IsNullOrWhiteSpace(queryParameters.ContentType))
            {
                query = query.Where(u => u.ContentType != null && u.ContentType.ToLower() == queryParameters.ContentType.ToLower());
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(queryParameters.SortBy))
            {
                var sortBy = queryParameters.SortBy.ToLower();
                var isDescending = queryParameters.SortOrder?.ToLower() == "desc";

                Expression<Func<Upload, object>> keySelector = sortBy switch
                {
                    "filename" => u => u.Filename,
                    "size" => u => u.Size,
                    "contenttype" => u => u.ContentType,
                    "createdat" => u => u.CreationTime,
                    _ => u => u.CreationTime // Default sort
                };

                query = isDescending ? query.OrderByDescending(keySelector) : query.OrderBy(keySelector);
            }
            else
            {
                query = query.OrderByDescending(u => u.CreationTime); // Default sort
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var uploadEntities = await query
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync(cancellationToken);

            var uploadDtos = uploadEntities.Select(upload => new UploadDto
            {
                Id = upload.Id,
                Filename = upload.Filename,
                Size = upload.Size,
                ContentType = upload.ContentType,
                StoragePath = upload.StoragePath,
                StorageProvider = upload.StorageProvider,
                ExternalUrl = upload.ExternalUrl,
                Checksum = upload.Checksum,
                CreatedAt = upload.CreationTime,
                UpdatedAt = upload.LastModificationTime,
                TenantId = upload.TenantId,
                IsImage = upload.IsImage(),
                IsDocument = upload.IsDocument()
            }).ToList();

            return new PagedResult<UploadDto>(uploadDtos, page, limit, totalCount);
        }
    }
}
