'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileDown, Loader2, AlertTriangle } from 'lucide-react';
import { renderReport, exportReportToPdf, exportReportToWord, exportReportToPowerPoint } from '@/lib/api';
import { Progress } from '@/components/ui/progress'; // Assuming you have a Progress component

// Helper to sanitize HTML (basic example, consider a more robust library if needed)
const sanitizeHtml = (htmlString: string): string => {
  // This is a very basic sanitizer. For production, use a library like DOMPurify.
  const clean = htmlString.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  return clean;
};

interface ExportProgress {
  format: 'pdf' | 'word' | 'powerpoint';
  progress: number;
  exporting: boolean;
  error: string | null;
}

export default function ReportViewPage() {
  const router = useRouter();
  const params = useParams();
  const reportId = params.reportId as string;

  const [reportHtml, setReportHtml] = useState<string | null>(null);
  const [reportName, setReportName] = useState<string>('Report'); // Placeholder, fetch actual name if needed
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [exportProgress, setExportProgress] = useState<Record<string, ExportProgress>>({
    pdf: { format: 'pdf', progress: 0, exporting: false, error: null },
    word: { format: 'word', progress: 0, exporting: false, error: null },
    powerpoint: { format: 'powerpoint', progress: 0, exporting: false, error: null },
  });

  const fetchReportHtml = useCallback(async () => {
    if (!reportId) return;
    setIsLoading(true);
    setError(null);
    try {
      const html = await renderReport(reportId);
      setReportHtml(sanitizeHtml(html));
      // Consider fetching report metadata here to get the actual reportName
      // For now, using placeholder or reportId
      // const reportDetails = await getReportById(reportId); // If you need client/report name for UI
      // setReportName(reportDetails.reportName);
    } catch (err) {
      console.error('Failed to load report:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      setReportHtml(null);
    } finally {
      setIsLoading(false);
    }
  }, [reportId]);

  useEffect(() => {
    fetchReportHtml();
  }, [fetchReportHtml]);

  const handleExport = async (format: 'pdf' | 'word' | 'powerpoint') => {
    setExportProgress(prev => ({
      ...prev,
      [format]: { ...prev[format], exporting: true, progress: 0, error: null },
    }));

    try {
      let exportFunc;
      if (format === 'pdf') exportFunc = exportReportToPdf;
      else if (format === 'word') exportFunc = exportReportToWord;
      else exportFunc = exportReportToPowerPoint;

      const { blob, filename } = await exportFunc(reportId, (progress) => {
        setExportProgress(prev => ({
          ...prev,
          [format]: { ...prev[format], progress },
        }));
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename; // Filename comes from BFF/backend
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setExportProgress(prev => ({
        ...prev,
        [format]: { ...prev[format], exporting: false, progress: 100 },
      }));
      // Add success notification if desired
    } catch (err) {
      console.error(`Failed to export to ${format}:`, err);
      setExportProgress(prev => ({
        ...prev,
        [format]: { 
          ...prev[format], 
          exporting: false, 
          error: err instanceof Error ? err.message : `Failed to export to ${format}` 
        },
      }));
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <header className="mb-6 flex items-center justify-between">
        <Button variant="outline" onClick={() => router.back()} size="sm">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <h1 className="text-2xl font-semibold">{reportName} - View</h1>
        <div className="flex space-x-2">
          {(['pdf', 'word', 'powerpoint'] as const).map((format) => (
            <Button
              key={format}
              variant="outline"
              size="sm"
              onClick={() => handleExport(format)}
              disabled={exportProgress[format].exporting || isLoading || !!error}
            >
              {exportProgress[format].exporting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <FileDown className="mr-2 h-4 w-4" />
              )}
              Export {format.toUpperCase()}
            </Button>
          ))}
        </div>
      </header>

      {Object.values(exportProgress).map(exp => exp.exporting && (
        <div key={exp.format} className="mb-4">
          <p className="text-sm text-muted-foreground">Exporting to {exp.format.toUpperCase()}... ({exp.progress}%)</p>
          <Progress value={exp.progress} className="w-full h-2" />
          {exp.error && <p className="text-xs text-red-500 mt-1">{exp.error}</p>}
        </div>
      ))}

      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="ml-4 text-lg">Loading report...</p>
        </div>
      )}

      {error && !isLoading && (
        <div className="text-center p-8 border border-destructive/50 bg-destructive/10 rounded-md">
          <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold text-destructive mb-2">Failed to load report</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchReportHtml}>
            Try Again
          </Button>
        </div>
      )}

      {!isLoading && !error && reportHtml && (
        <div className="prose max-w-none border rounded-md p-6 bg-background shadow">
          {/* Render the sanitized HTML. Ensure your CSS handles styling within this div. */}
          {/* Add a specific class for report content if needed for scoping styles */}
          <div dangerouslySetInnerHTML={{ __html: reportHtml }} />
        </div>
      )}
      
      {!isLoading && !error && !reportHtml && (
         <div className="text-center p-8 border rounded-md">
            <p className="text-muted-foreground">No report content to display.</p>
         </div>
      )}
    </div>
  );
}
