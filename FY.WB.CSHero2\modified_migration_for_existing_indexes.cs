using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Persistence.Migrations
{
    /// <summary>
    /// Migration to add NOT NULL constraints to TenantId columns and foreign key relationships
    /// Modified to handle existing indexes
    /// </summary>
    public partial class AddTenantIdConstraintsAndIndexes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 1. Add NOT NULL constraint to TenantId columns
            // This ensures that every new record MUST have a TenantId.
            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Forms",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Invoices",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Clients",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            // 2. Add Foreign Key constraints
            // This links TenantId in Forms, Invoices, and Clients to Id in TenantProfiles,
            // ensuring referential integrity.
            migrationBuilder.AddForeignKey(
                name: "FK_Forms_TenantProfiles_TenantId",
                table: "Forms",
                column: "TenantId",
                principalTable: "TenantProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_TenantProfiles_TenantId",
                table: "Invoices",
                column: "TenantId",
                principalTable: "TenantProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Clients_TenantProfiles_TenantId",
                table: "Clients",
                column: "TenantId",
                principalTable: "TenantProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            // 3. Add indexes on the foreign key columns - COMMENTED OUT because they already exist
            // This significantly improves performance for queries that join on these columns
            // and helps with referential integrity checks by the database engine.
            
            // COMMENTED OUT: These indexes already exist in the database
            // migrationBuilder.CreateIndex(
            //     name: "IX_Forms_TenantId",
            //     table: "Forms",
            //     column: "TenantId");
            // 
            // migrationBuilder.CreateIndex(
            //     name: "IX_Invoices_TenantId",
            //     table: "Invoices",
            //     column: "TenantId");

            // Only create the Client index if it doesn't exist
            // You may need to comment this out too if it already exists
            migrationBuilder.CreateIndex(
                name: "IX_Clients_TenantId",
                table: "Clients",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove indexes - COMMENTED OUT for indexes that already existed
            // migrationBuilder.DropIndex(
            //     name: "IX_Forms_TenantId",
            //     table: "Forms");
            // 
            // migrationBuilder.DropIndex(
            //     name: "IX_Invoices_TenantId",
            //     table: "Invoices");

            // Only drop the Client index if we created it
            migrationBuilder.DropIndex(
                name: "IX_Clients_TenantId",
                table: "Clients");

            // Remove foreign key constraints
            migrationBuilder.DropForeignKey(
                name: "FK_Forms_TenantProfiles_TenantId",
                table: "Forms");

            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_TenantProfiles_TenantId",
                table: "Invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_Clients_TenantProfiles_TenantId",
                table: "Clients");

            // Revert NOT NULL constraints
            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Forms",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Invoices",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<Guid>(
                name: "TenantId",
                table: "Clients",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");
        }
    }
}
