using System;

namespace FY.WB.CSHero2.Application.Forms.Dtos
{
    public class UpdateFormRequestDto
    {
        public Guid Id { get; set; } // Id of the Form to update
        public string Title { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        // Date is typically not updated, but can be included if needed.
        // For now, assuming Date is set at creation and not modified.
    }
}
