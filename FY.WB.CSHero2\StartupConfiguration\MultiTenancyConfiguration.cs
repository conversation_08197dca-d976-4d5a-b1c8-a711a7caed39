using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Finbuckle.MultiTenant;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.StartupConfiguration
{
    public static class MultiTenancyConfiguration
    {
        public static IServiceCollection AddMultiTenancyConfiguration(this IServiceCollection services)
        {
            services.AddMultiTenant<AppTenantInfo>()
                .WithClaimStrategy("tenant_id")
                .WithRouteStrategy()
                .WithInMemoryStore();


            return services;
        }

        public static IApplicationBuilder UseMultiTenancyConfiguration(this IApplicationBuilder app)
        {
            app.UseMultiTenant();
            return app;
        }
    }
}
