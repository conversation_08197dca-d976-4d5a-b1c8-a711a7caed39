import { FieldConfig, SectionConfig, StyleSectionConfig } from './styleFieldConfig';

// Color Palette Fields
const colorPaletteFields: FieldConfig[] = [
  {
    name: "primary",
    label: "Primary Color",
    type: "color",
    defaultValue: "",
    placeholder: "#000000"
  },
  {
    name: "secondary",
    label: "Secondary Color",
    type: "color",
    defaultValue: "",
    placeholder: "#000000"
  },
  {
    name: "neutralBackground",
    label: "Neutral Background",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "white", label: "White" },
      { value: "off-white", label: "Off-white" },
      { value: "light-gray", label: "Light gray" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customNeutralBackground",
    label: "Custom Background Color",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom background color",
    showWhen: (values) => values.neutralBackground === "custom"
  },
  {
    name: "neutralText",
    label: "Neutral Text",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "black", label: "Black" },
      { value: "dark-gray", label: "Dark gray" },
      { value: "dark-blue", label: "Dark blue" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customNeutralText",
    label: "Custom Text Color",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom text color",
    showWhen: (values) => values.neutralText === "custom"
  },
  {
    name: "scheme",
    label: "Color Scheme",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "let-ai-decide", label: "Let AI decide" },
      { value: "monochromatic", label: "Monochromatic" },
      { value: "complementary", label: "Complementary" },
      { value: "analogous", label: "Analogous" },
      { value: "triadic", label: "Triadic" },
      { value: "corporate", label: "Corporate/brand colors" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customScheme",
    label: "Custom Scheme Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.scheme === "custom"
  }
];

// Chart Style Fields
const chartStyleFields: FieldConfig[] = [
  {
    name: "lineThickness",
    label: "Line Thickness",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "thin", label: "Thin" },
      { value: "medium", label: "Medium" },
      { value: "thick", label: "Thick" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customLineThickness",
    label: "Custom Line Thickness",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom line thickness",
    showWhen: (values) => values.lineThickness === "custom"
  },
  {
    name: "colors",
    label: "Colors",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "match-document", label: "Match document palette" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customColors",
    label: "Custom Colors Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe palette and what (if any) data it corresponds to",
    showWhen: (values) => values.colors === "custom"
  },
  {
    name: "fonts",
    label: "Fonts",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "match-document", label: "Match document fonts" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customFonts",
    label: "Custom Fonts Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe fonts for charts and what (if any) elements they apply to",
    showWhen: (values) => values.fonts === "custom"
  },
  {
    name: "gridLines",
    label: "Grid Lines",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "light", label: "Light" },
      { value: "dark", label: "Dark" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customGridLines",
    label: "Custom Grid Lines Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.gridLines === "custom"
  },
  {
    name: "dataLabels",
    label: "Data Labels",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "endpoints", label: "Endpoints only" },
      { value: "all", label: "All points" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customDataLabels",
    label: "Custom Data Labels Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.dataLabels === "custom"
  },
  {
    name: "legend",
    label: "Legend",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "right", label: "Right" },
      { value: "bottom", label: "Bottom" },
      { value: "custom", label: "Custom position" }
    ]
  },
  {
    name: "customLegend",
    label: "Custom Legend Position",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom legend position",
    showWhen: (values) => values.legend === "custom"
  }
];

// Image Placement Fields
const imagePlacementFields: FieldConfig[] = [
  {
    name: "defaultPosition",
    label: "Default Position",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "centered", label: "Centered" },
      { value: "inline", label: "Inline" },
      { value: "text-wrapped", label: "Text-wrapped" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customDefaultPosition",
    label: "Custom Position Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.defaultPosition === "custom"
  },
  {
    name: "textWrapping",
    label: "Text Wrapping",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "square", label: "Square" },
      { value: "tight", label: "Tight" },
      { value: "through", label: "Through" }
    ]
  },
  {
    name: "sizePresets",
    label: "Size Presets",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "small", label: "Small" },
      { value: "medium", label: "Medium" },
      { value: "large", label: "Large" },
      { value: "full-width", label: "Full-width" }
    ]
  },
  {
    name: "border",
    label: "Border",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "thin-line", label: "Thin line" },
      { value: "frame", label: "Frame" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customBorder",
    label: "Custom Border Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.border === "custom"
  },
  {
    name: "spacing",
    label: "Spacing",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "tight", label: "Tight" },
      { value: "standard", label: "Standard" },
      { value: "generous", label: "Generous" }
    ]
  }
];

// Caption Style Fields
const captionStyleFields: FieldConfig[] = [
  {
    name: "position",
    label: "Position",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "below", label: "Below image" },
      { value: "above", label: "Above image" },
      { value: "side", label: "Side" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customPosition",
    label: "Custom Position Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.position === "custom"
  },
  {
    name: "font",
    label: "Font",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "same-as-body", label: "Same as body" },
      { value: "smaller", label: "Smaller than body" },
      { value: "italicized", label: "Italicized" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customFont",
    label: "Custom Font Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.font === "custom"
  },
  {
    name: "numbering",
    label: "Numbering",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "figure-x", label: "Figure X" }
    ]
  },
  {
    name: "alignment",
    label: "Alignment",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "left", label: "Left" },
      { value: "centered", label: "Centered" },
      { value: "right", label: "Right" }
    ]
  }
];

// Branding Elements Fields
const logoFields: FieldConfig[] = [
  {
    name: "placement",
    label: "Default Placement",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "cover-only", label: "Cover only" },
      { value: "every-page", label: "Every page (header)" },
      { value: "custom", label: "Custom rule" }
    ]
  },
  {
    name: "customPlacement",
    label: "Custom Placement Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.placement === "custom"
  },
  {
    name: "size",
    label: "Size",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "small", label: "Small" },
      { value: "medium", label: "Medium" },
      { value: "large", label: "Large" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customSize",
    label: "Custom Size",
    type: "number",
    defaultValue: "",
    placeholder: "Size in px",
    showWhen: (values) => values.size === "custom"
  }
];

const watermarksFields: FieldConfig[] = [
  {
    name: "type",
    label: "Type",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "text", label: "Text" },
      { value: "logo", label: "Logo" },
      { value: "pattern", label: "Pattern" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customType",
    label: "Custom Type Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.type === "custom"
  },
  {
    name: "opacity",
    label: "Opacity",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "light", label: "Light (10%)" },
      { value: "medium", label: "Medium (20%)" },
      { value: "dark", label: "Dark (30%)" },
      { value: "custom", label: "Custom" }
    ],
    showWhen: (values) => values.type && values.type !== "none"
  },
  {
    name: "customOpacity",
    label: "Custom Opacity",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom opacity",
    showWhen: (values) => values.type && values.type !== "none" && values.opacity === "custom"
  }
];

const brandingElementsFields: FieldConfig[] = [
  {
    name: "brandTypography",
    label: "Brand Typography",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "follow-guidelines", label: "Follow brand guidelines" },
      { value: "document-overrides", label: "Document overrides" },
      { value: "custom", label: "Custom approach" }
    ]
  },
  {
    name: "customBrandTypography",
    label: "Custom Brand Typography Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.brandTypography === "custom"
  },
  {
    name: "brandColors",
    label: "Brand Colors",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "strict", label: "Strict adherence" },
      { value: "modified", label: "Modified for document" },
      { value: "custom", label: "Custom approach" }
    ]
  },
  {
    name: "customBrandColors",
    label: "Custom Brand Colors Description",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.brandColors === "custom"
  }
];

// Define sections
const sections: SectionConfig[] = [
  {
    id: "colorPalette",
    title: "Color Palette",
    fields: colorPaletteFields,
    defaultExpanded: true
  },
  {
    id: "chartStyle",
    title: "Chart/Graph Style",
    fields: chartStyleFields
  },
  {
    id: "imagePlacement",
    title: "Image Placement",
    fields: imagePlacementFields
  },
  {
    id: "captionStyle",
    title: "Caption Style",
    fields: captionStyleFields
  },
  {
    id: "logo",
    title: "Logo",
    fields: logoFields
  },
  {
    id: "watermarks",
    title: "Watermarks",
    fields: watermarksFields
  },
  {
    id: "brandingElements",
    title: "Branding Elements",
    fields: brandingElementsFields
  }
];

// Export the complete configuration
export const visualElementsConfig: StyleSectionConfig = {
  sections
};
