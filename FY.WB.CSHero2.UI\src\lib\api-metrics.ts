import { NEXT_API_BASE_URL } from '@/lib/constants';

// Types for metrics data
export interface MetricData {
  current: number;
  previousPeriod: number;
  trend: number[];
}

export interface Metrics {
  totalCustomers: MetricData;
  newCustomers: MetricData;
  reportsCreated: MetricData;
  revenue: MetricData;
}

export interface MetricsResponse {
  metrics: Metrics;
  timeframe: string;
}

/**
 * Fetch metrics data for the specified timeframe
 * @param timeframe The timeframe to fetch metrics for ('today', 'wtd', 'mtd', 'ytd')
 * @returns Metrics data for the specified timeframe
 */
export async function getMetrics(timeframe: string): Promise<MetricsResponse> {
  const response = await fetch(`${NEXT_API_BASE_URL}/v1/admin/metrics?timeframe=${timeframe}`, {
    credentials: 'include', // Include cookies for authentication
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Error fetching metrics: ${response.status} ${errorText}`);
    throw new Error(`Failed to fetch metrics: ${response.status} ${errorText}`);
  }

  const data = await response.json();
  
  // Convert single trend values to arrays if needed
  if (data.metrics) {
    Object.keys(data.metrics).forEach(key => {
      const metric = data.metrics[key];
      if (metric && typeof metric.trend === 'number') {
        // Generate a simple trend array based on the single value
        const trendValue = metric.trend;
        metric.trend = [
          trendValue * 0.7,
          trendValue * 0.8,
          trendValue * 0.9,
          trendValue,
          trendValue * 1.1
        ];
      }
    });
  }

  return data;
}

/**
 * Fetch metrics data for today
 * @returns Metrics data for today
 */
export async function getTodayMetrics(): Promise<MetricsResponse> {
  return getMetrics('today');
}

/**
 * Fetch metrics data for week to date
 * @returns Metrics data for week to date
 */
export async function getWTDMetrics(): Promise<MetricsResponse> {
  return getMetrics('wtd');
}

/**
 * Fetch metrics data for month to date
 * @returns Metrics data for month to date
 */
export async function getMTDMetrics(): Promise<MetricsResponse> {
  return getMetrics('mtd');
}

/**
 * Fetch metrics data for year to date
 * @returns Metrics data for year to date
 */
export async function getYTDMetrics(): Promise<MetricsResponse> {
  return getMetrics('ytd');
}
