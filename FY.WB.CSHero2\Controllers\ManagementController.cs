using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FY.WB.CSHero2.Infrastructure.Persistence.Migrations;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Controllers
{
    /// <summary>
    /// Controller for administrative and management operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Ensure only authorized users can access these endpoints
    public class ManagementController : ControllerBase
    {
        private readonly ReportDataMigrationService _migrationService;
        private readonly ILogger<ManagementController> _logger;

        public ManagementController(
            ReportDataMigrationService migrationService,
            ILogger<ManagementController> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        /// <summary>
        /// Migrates existing report JSON data to the new structured format
        /// </summary>
        /// <returns>Number of reports migrated</returns>
        [HttpPost("migrate-report-data")]
        public async Task<IActionResult> MigrateReportData()
        {
            try
            {
                _logger.LogInformation("Starting report data migration via API");
                
                var migratedCount = await _migrationService.MigrateReportDataAsync();
                
                _logger.LogInformation("Report data migration completed. Migrated {Count} reports", migratedCount);
                
                return Ok(new
                {
                    Success = true,
                    Message = $"Successfully migrated {migratedCount} reports",
                    MigratedCount = migratedCount
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Error during report data migration: {Message}", ex.Message);
                
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "An error occurred during migration",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Gets migration status and statistics
        /// </summary>
        /// <returns>Migration status information</returns>
        [HttpGet("migration-status")]
        public async Task<IActionResult> GetMigrationStatus()
        {
            try
            {
                // This could be expanded to provide more detailed status information
                return Ok(new
                {
                    Success = true,
                    Message = "Migration status endpoint is available",
                    Timestamp = System.DateTime.UtcNow
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status: {Message}", ex.Message);
                
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "An error occurred getting migration status",
                    Error = ex.Message
                });
            }
        }
    }
}
