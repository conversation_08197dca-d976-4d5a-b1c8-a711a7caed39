using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Form : FullAuditedMultiTenantEntity<Guid>
    {
        // Navigation property to TenantProfile
        public virtual TenantProfile TenantProfile { get; set; } = null!;
        
        public string Title { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // e.g., "feedback", "complaint"
        public string Priority { get; set; } = string.Empty; // e.g., "high", "medium", "low"
        public string Description { get; set; } = string.Empty;
        public DateTime Date { get; set; }

        public Form() : base() { }

        public Form(
            Guid id,
            string title,
            string customerName,
            string email,
            string category,
            string priority,
            string description,
            DateTime date)
            : base(id)
        {
            Title = title;
            CustomerName = customerName;
            Email = email;
            Category = category;
            Priority = priority;
            Description = description;
            Date = date;
        }

        public void UpdateDetails(
            string title,
            string customerName,
            string email,
            string category,
            string priority,
            string description)
        {
            Title = title;
            CustomerName = customerName;
            Email = email;
            Category = category;
            Priority = priority;
            Description = description;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetPriority(string priority)
        {
            Priority = priority;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetCategory(string category)
        {
            Category = category;
            // LastModificationTime and LastModifierId will be set by DbContext
        }
    }
}
