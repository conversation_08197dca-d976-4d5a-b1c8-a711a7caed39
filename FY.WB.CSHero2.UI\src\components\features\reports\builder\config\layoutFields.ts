import { FieldConfig, SectionConfig, StyleSectionConfig } from './styleFieldConfig';

// Page Setup Fields
const pageSetupFields: FieldConfig[] = [
  {
    name: "pageSize",
    label: "Page Size",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "letter", label: "US Letter (8.5\" × 11\")" },
      { value: "a4", label: "A4 (210 × 297mm)" },
      { value: "legal", label: "Legal (8.5\" × 14\")" },
      { value: "tabloid", label: "Tabloid (11\" × 17\")" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "pageSizeDimensions",
    label: "Custom Page Size",
    type: "dimension-input",
    defaultValue: { width: "", height: "" },
    placeholder: "Enter dimensions",
    showWhen: (values) => values.pageSize === "custom"
  },
  {
    name: "orientation",
    label: "Orientation",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "portrait", label: "Portrait" },
      { value: "landscape", label: "Landscape" }
    ]
  },
  {
    name: "marginsPreset",
    label: "Margins",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "standard", label: "Standard (1\" all sides)" },
      { value: "narrow", label: "Narrow (0.5\" all sides)" },
      { value: "moderate", label: "Moderate (1\" top/bottom, 0.75\" left/right)" },
      { value: "wide", label: "Wide (1.25\" all sides)" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "marginTop",
    label: "Top Margin",
    type: "text",
    defaultValue: "",
    placeholder: "e.g., 1in",
    showWhen: (values) => values.marginsPreset === "custom"
  },
  {
    name: "marginBottom",
    label: "Bottom Margin",
    type: "text",
    defaultValue: "",
    placeholder: "e.g., 1in",
    showWhen: (values) => values.marginsPreset === "custom"
  },
  {
    name: "marginLeft",
    label: "Left Margin",
    type: "text",
    defaultValue: "",
    placeholder: "e.g., 1in",
    showWhen: (values) => values.marginsPreset === "custom"
  },
  {
    name: "marginRight",
    label: "Right Margin",
    type: "text",
    defaultValue: "",
    placeholder: "e.g., 1in",
    showWhen: (values) => values.marginsPreset === "custom"
  },
  {
    name: "bleed",
    label: "Bleed",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "0.125", label: "0.125\"" },
      { value: "0.25", label: "0.25\"" }
    ]
  }
];

// Columns Fields
const columnsFields: FieldConfig[] = [
  {
    name: "count",
    label: "Number of Columns",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "1", label: "1" },
      { value: "2", label: "2" },
      { value: "3", label: "3" },
      { value: "4", label: "4" }
    ]
  },
  {
    name: "widths",
    label: "Column Width",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "equal", label: "Equal" },
      { value: "custom", label: "Custom widths" }
    ]
  },
  {
    name: "gutterWidth",
    label: "Gutter Width",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "0.25", label: "0.25\"" },
      { value: "0.5", label: "0.5\"" },
      { value: "0.75", label: "0.75\"" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customGutterWidth",
    label: "Custom Gutter Width",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom gutter width",
    showWhen: (values) => values.gutterWidth === "custom"
  },
  {
    name: "separator",
    label: "Column Separator",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "line", label: "Line" }
    ]
  }
];

// Cover Design Fields
const coverDesignFields: FieldConfig[] = [
  {
    name: "layoutStyle",
    label: "Layout Style",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "minimal", label: "Minimal" },
      { value: "full-bleed", label: "Full-bleed image" },
      { value: "split", label: "Split (image/text)" },
      { value: "typography", label: "Typography-focused" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customLayoutStyle",
    label: "Custom Layout Style",
    type: "textarea",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.layoutStyle === "custom"
  },
  {
    name: "background",
    label: "Background",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "solid", label: "Solid color" },
      { value: "gradient", label: "Gradient" },
      { value: "image", label: "Image" },
      { value: "pattern", label: "Pattern" },
      { value: "none", label: "None" }
    ]
  },
  {
    name: "titlePosition",
    label: "Title Position",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "centered", label: "Centered" },
      { value: "left", label: "Left-aligned" },
      { value: "right", label: "Right-aligned" },
      { value: "top", label: "Top" },
      { value: "bottom", label: "Bottom" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "customTitlePosition",
    label: "Custom Title Position",
    type: "text",
    defaultValue: "",
    placeholder: "Describe for AI",
    showWhen: (values) => values.titlePosition === "custom"
  },
  {
    name: "visualElements",
    label: "Visual Elements",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "logo-only", label: "Logo only" },
      { value: "logo-with-graphics", label: "Logo with graphics" },
      { value: "image", label: "Image focused" },
      { value: "abstract", label: "Abstract elements" }
    ]
  }
];

// Title Page Fields
const titlePageFields: FieldConfig[] = [
  {
    name: "titleFont",
    label: "Title Font",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "Arial", label: "Arial" },
      { value: "Times New Roman", label: "Times New Roman" },
      { value: "Helvetica", label: "Helvetica" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "titleSize",
    label: "Title Size",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "18pt", label: "18pt" },
      { value: "24pt", label: "24pt" },
      { value: "36pt", label: "36pt" },
      { value: "48pt", label: "48pt" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "titleAlignment",
    label: "Title Alignment",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "left", label: "Left" },
      { value: "center", label: "Center" },
      { value: "right", label: "Right" }
    ]
  },
  {
    name: "subtitleShow",
    label: "Show Subtitle",
    type: "checkbox",
    defaultValue: false
  },
  {
    name: "subtitleFont",
    label: "Subtitle Font",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "Arial", label: "Arial" },
      { value: "Times New Roman", label: "Times New Roman" },
      { value: "Helvetica", label: "Helvetica" },
      { value: "custom", label: "Custom" }
    ],
    showWhen: (values) => values.subtitleShow
  },
  {
    name: "subtitleSize",
    label: "Subtitle Size",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "12pt", label: "12pt" },
      { value: "14pt", label: "14pt" },
      { value: "16pt", label: "16pt" },
      { value: "18pt", label: "18pt" },
      { value: "custom", label: "Custom" }
    ],
    showWhen: (values) => values.subtitleShow
  },
  {
    name: "authorShow",
    label: "Show Author/Contributors",
    type: "checkbox",
    defaultValue: false
  },
  {
    name: "authorFormat",
    label: "Author Format",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "list", label: "List" },
      { value: "grid", label: "Grid" }
    ],
    showWhen: (values) => values.authorShow
  },
  {
    name: "dateShow",
    label: "Show Date",
    type: "checkbox",
    defaultValue: false
  },
  {
    name: "dateFormat",
    label: "Date Format",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "month-year", label: "Month Year" },
      { value: "mm-dd-yyyy", label: "MM/DD/YYYY" },
      { value: "custom", label: "Custom" }
    ],
    showWhen: (values) => values.dateShow
  },
  {
    name: "customDateFormat",
    label: "Custom Date Format",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom date format",
    showWhen: (values) => values.dateShow && values.dateFormat === "custom"
  }
];

// Header/Footer Fields
const headerFooterFields: FieldConfig[] = [
  {
    name: "headerContent",
    label: "Header Content",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "title", label: "Title" },
      { value: "section", label: "Section title" },
      { value: "custom", label: "Custom text" }
    ]
  },
  {
    name: "customHeaderContent",
    label: "Custom Header Text",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom header text",
    showWhen: (values) => values.headerContent === "custom"
  },
  {
    name: "footerContent",
    label: "Footer Content",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "none", label: "None" },
      { value: "page-number", label: "Page number" },
      { value: "date", label: "Date" },
      { value: "author", label: "Author" },
      { value: "custom", label: "Custom text" }
    ]
  },
  {
    name: "customFooterContent",
    label: "Custom Footer Text",
    type: "text",
    defaultValue: "",
    placeholder: "Enter custom footer text",
    showWhen: (values) => values.footerContent === "custom"
  },
  {
    name: "pageNumberingStyle",
    label: "Page Numbering Style",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "1", label: "1, 2, 3..." },
      { value: "i", label: "i, ii, iii..." },
      { value: "I", label: "I, II, III..." },
      { value: "a", label: "a, b, c..." },
      { value: "A", label: "A, B, C..." },
      { value: "none", label: "None" }
    ]
  },
  {
    name: "pageNumberingPosition",
    label: "Page Numbering Position",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "bottom-center", label: "Bottom center" },
      { value: "bottom-right", label: "Bottom right" },
      { value: "bottom-left", label: "Bottom left" },
      { value: "top-right", label: "Top right" }
    ]
  },
  {
    name: "pageNumberingFormat",
    label: "Page Numbering Format",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "page-x", label: "Page X" },
      { value: "x-of-y", label: "X of Y" },
      { value: "x", label: "X" }
    ]
  },
  {
    name: "firstPage",
    label: "First Page",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "same", label: "Same as others" },
      { value: "different", label: "Different" },
      { value: "none", label: "No header/footer" }
    ]
  },
  {
    name: "sectionBreaks",
    label: "Section Breaks",
    type: "select",
    defaultValue: "",
    options: [
      { value: "", label: "Not specified" },
      { value: "continue", label: "Continue numbering" },
      { value: "restart", label: "Restart numbering" }
    ]
  }
];

// Define sections
const sections: SectionConfig[] = [
  {
    id: "pageSetup",
    title: "Page Size, Orientation, and Margins",
    fields: pageSetupFields,
    defaultExpanded: true
  },
  {
    id: "columns",
    title: "Columns",
    fields: columnsFields
  },
  {
    id: "coverDesign",
    title: "Cover Design",
    fields: coverDesignFields
  },
  {
    id: "titlePage",
    title: "Title Page Elements",
    fields: titlePageFields
  },
  {
    id: "headerFooter",
    title: "Header/Footer Details",
    fields: headerFooterFields
  }
];

// Export the complete configuration
export const documentLayoutConfig: StyleSectionConfig = {
  sections
};
