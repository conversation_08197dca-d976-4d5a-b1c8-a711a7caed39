"use client";

import { useState } from "react";
import { 
  FileText, 
  Layers, 
  Layout, 
  Palette, 
  ChevronRight, 
  ChevronLeft, 
  ArrowLeft,
  Upload
} from "lucide-react";
import { SidebarProps } from "../../types";

export function Sidebar({
  activeItem,
  onMenuClick,
  onSectionClick,
  onReturnToReports,
  onSaveReport,
  onRenderReport,
  reportTitle
}: SidebarProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [sections] = useState([
    { id: '1', title: 'Executive Summary' },
    { id: '2', title: 'Performance Metrics' },
    { id: '3', title: 'Customer Feedback' }
  ]);

  return (
    <>
      <aside className="bg-black text-white w-64 min-h-screen fixed left-0 top-0 z-20">
        {/* Header with return button */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          {isExpanded ? (
            <>
              <button
                onClick={onReturnToReports}
                className="flex items-center text-sm hover:text-blue-400 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Reports
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-400 hover:text-white"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsExpanded(true)}
              className="text-gray-400 hover:text-white mx-auto"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          )}
        </div>
        <div className="px-4 pt-4 pb-2">
          <button
            onClick={onReturnToReports}
            className="w-full py-1.5 text-white rounded-md hover:border-b-2 hover:border-primary transition-colors flex items-center justify-start text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {isExpanded ? "Return to Reports" : ""}
          </button>
        </div>

        {/* Save and Render buttons */}
        <div className="px-4 pb-4 border-b border-gray-700 space-y-2">
          <button
            onClick={onSaveReport}
            className="w-full py-2 bg-primary text-white rounded-md hover:bg-primary/70 transition-colors flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
              <polyline points="17 21 17 13 7 13 7 21"/>
              <polyline points="7 3 7 8 15 8"/>
            </svg>
            {isExpanded ? "Save Report" : ""}
          </button>
          <button
            onClick={onRenderReport}
            className="w-full py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <circle cx="12" cy="12" r="10"/>
              <polygon points="10 8 16 12 10 16 10 8"/>
            </svg>
            {isExpanded ? "Render Report" : ""}
          </button>
        </div>

        {/* Navigation tabs */}
        <nav className="flex-grow">
          <ul className="py-2">
            <li>
              <button
                className={`w-full text-left py-3 px-4 flex items-center ${
                  activeItem === 'Report Info'
                    ? 'bg-primary text-white'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
                onClick={() => onMenuClick('Report Info')}
              >
                <FileText className="h-5 w-5 mr-3" />
                {isExpanded && <span>Report Info</span>}
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left py-3 px-4 flex items-center ${
                  activeItem === 'Sections'
                    ? 'bg-primary text-white'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
                onClick={() => onMenuClick('Sections')}
              >
                <Layers className="h-5 w-5 mr-3" />
                {isExpanded && <span>Sections</span>}
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left py-3 px-4 flex items-center ${
                  activeItem === 'Template'
                    ? 'bg-primary text-white'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
                onClick={() => onMenuClick('Template')}
              >
                <Layout className="h-5 w-5 mr-3" />
                {isExpanded && <span>Style Template</span>}
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left py-3 px-4 flex items-center ${
                  activeItem === 'Style'
                    ? 'bg-primary text-white'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
                onClick={() => onMenuClick('Style')}
              >
                <Palette className="h-5 w-5 mr-3" />
                {isExpanded && <span>Customize Style</span>}
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left py-3 px-4 flex items-center ${
                  activeItem === 'Import Report'
                    ? 'bg-primary text-white'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
                onClick={() => onMenuClick('Import Report')}
              >
                <Upload className="h-5 w-5 mr-3" />
                {isExpanded && <span>Import Report</span>}
              </button>
            </li>
          </ul>
        </nav>

        {/* Footer with report title */}
        <div className="p-4 border-t border-gray-700">
          {isExpanded ? (
            <div className="truncate text-sm">
              <div className="text-gray-400 mb-1">Current Report:</div>
              <div className="font-medium truncate">{reportTitle}</div>
            </div>
          ) : (
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center mx-auto">
              <span className="text-xs font-bold">{reportTitle.charAt(0)}</span>
            </div>
          )}
        </div>
      </aside>
    </>
  );
}
