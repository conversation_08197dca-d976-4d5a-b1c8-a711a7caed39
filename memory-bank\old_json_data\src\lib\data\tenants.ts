import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';
import { Tenant } from '@/types/tenant';

/**
 * Get all tenants with optional filtering, sorting, and pagination
 * @param options Query options for filtering, sorting, and pagination
 * @returns Array of tenants
 */
export async function getTenants(options: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
} = {}): Promise<Tenant[]> {
  const { query, limit, page, sortBy, order } = options;
  
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants) {
    data.tenants = [];
    await writeJsonFile('db.json', data);
  }
  
  return processArray(data.tenants, {
    query,
    sortBy,
    order,
    page: page ? parseInt(page.toString()) : undefined,
    limit: limit ? parseInt(limit.toString()) : undefined
  });
}

/**
 * Get a tenant by ID
 * @param id Tenant ID
 * @returns Tenant object or null if not found
 */
export async function getTenantById(id: string): Promise<Tenant | null> {
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants) {
    data.tenants = [];
    await writeJsonFile('db.json', data);
    return null;
  }
  
  const tenant = data.tenants.find((t: Tenant) => t.id === id);
  return tenant || null;
}

/**
 * Create a new tenant
 * @param tenant Tenant data
 * @returns Created tenant
 */
export async function createTenant(tenant: Omit<Tenant, 'id' | 'createdAt' | 'updatedAt'>): Promise<Tenant> {
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants) {
    data.tenants = [];
  }
  
  const newTenant: Tenant = {
    ...tenant,
    id: generateId(data.tenants),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastLogin: undefined
  };
  
  data.tenants.push(newTenant);
  
  await writeJsonFile('db.json', data);
  
  return newTenant;
}

/**
 * Update a tenant
 * @param id Tenant ID
 * @param updates Tenant data to update
 * @returns Updated tenant or null if not found
 */
export async function updateTenant(
  id: string,
  updates: Partial<Omit<Tenant, 'id' | 'createdAt'>>
): Promise<Tenant | null> {
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants) {
    data.tenants = [];
    await writeJsonFile('db.json', data);
    return null;
  }
  
  const tenantIndex = data.tenants.findIndex((t: Tenant) => t.id === id);
  if (tenantIndex === -1) return null;
  
  const updatedTenant: Tenant = {
    ...data.tenants[tenantIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  data.tenants[tenantIndex] = updatedTenant;
  
  await writeJsonFile('db.json', data);
  
  return updatedTenant;
}

/**
 * Delete a tenant
 * @param id Tenant ID
 * @returns True if tenant was deleted, false if not found
 */
export async function deleteTenant(id: string): Promise<boolean> {
  const data = await readJsonFile<any>();
  
  // Initialize tenants array if it doesn't exist
  if (!data.tenants) {
    data.tenants = [];
    await writeJsonFile('db.json', data);
    return false;
  }
  
  const tenantIndex = data.tenants.findIndex((t: Tenant) => t.id === id);
  if (tenantIndex === -1) return false;
  
  data.tenants.splice(tenantIndex, 1);
  
  await writeJsonFile('db.json', data);
  
  return true;
}

/**
 * Archive a tenant (set status to inactive)
 * @param id Tenant ID
 * @returns Updated tenant or null if not found
 */
export async function archiveTenant(id: string): Promise<Tenant | null> {
  return updateTenant(id, { status: 'suspended' });
}
