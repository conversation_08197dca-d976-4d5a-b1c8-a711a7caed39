import { NextRequest, NextResponse } from 'next/server';
import { getPopularSubscriptions } from '@/lib/data/subscriptions';

/**
 * GET /api/v1/subscriptions/popular
 * Get popular subscriptions
 */
export async function GET(request: NextRequest) {
  try {
    const subscriptions = await getPopularSubscriptions();
    
    return NextResponse.json(subscriptions);
  } catch (error) {
    console.error('Error fetching popular subscriptions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular subscriptions' },
      { status: 500 }
    );
  }
}
