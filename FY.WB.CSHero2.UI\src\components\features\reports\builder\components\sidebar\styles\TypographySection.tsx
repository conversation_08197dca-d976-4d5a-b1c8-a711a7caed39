"use client";

import { TypographyOptions } from "../../../types";
import { typographyConfig } from "../../../config/typographyFields";
import { FormSection } from "../../ui/FormSection";
import { FormField } from "../../ui/FormField";
import { getSectionValues } from "../../../utils/formUtils";

interface TypographySectionProps {
  typography?: TypographyOptions;
  onUpdate: (field: string, value: any) => void;
}

export function TypographySection({ typography = {}, onUpdate }: TypographySectionProps) {
  const handleNestedUpdate = (category: string, field: string, value: any): void => {
    // Special handling for font sizes headings
    if (category === "fontSizes" && field.endsWith("Size")) {
      const headingLevel = field.replace("Size", "");
      const currentHeadings = typography.fontSizes?.headings || {};
      
      onUpdate("fontSizes", {
        ...typography.fontSizes,
        headings: {
          ...currentHeadings,
          [headingLevel]: value
        }
      });
      return;
    }
    
    // Default handling for other fields
    const currentCategory = typography[category as keyof TypographyOptions] as Record<string, any> || {};
    const updatedCategory = {
      ...currentCategory,
      [field]: value
    };
    
    onUpdate(category, updatedCategory);
  };

  // Helper function to get values for a specific section
  const getValuesForSection = (sectionId: string): Record<string, any> => {
    if (sectionId === "fontSizes") {
      // Combine font sizes with heading sizes
      return {
        body: typography.fontSizes?.body,
        h1Size: typography.fontSizes?.headings?.h1,
        h2Size: typography.fontSizes?.headings?.h2,
        h3Size: typography.fontSizes?.headings?.h3,
        h4Size: typography.fontSizes?.headings?.h4,
        footnotes: typography.fontSizes?.footnotes,
        captions: typography.fontSizes?.captions,
        scaleType: typography.fontSizes?.scaleType
      };
    }
    
    // For other sections, get values directly
    return getSectionValues(typography, sectionId);
  };

  return (
    <div className="space-y-4">
      {typographyConfig.sections.map(section => {
        const sectionValues = getValuesForSection(section.id);
        
        return (
          <FormSection 
            key={section.id}
            title={section.title}
            defaultExpanded={section.defaultExpanded}
          >
            {section.fields.map(field => (
              <FormField
                key={field.name}
                field={field}
                value={sectionValues[field.name]}
                onChange={(value) => handleNestedUpdate(section.id, field.name, value)}
                parentValues={sectionValues}
              />
            ))}
          </FormSection>
        );
      })}
    </div>
  );
}
