import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/auth/me
 * Returns the currently authenticated user by validating the auth token with the backend
 */
export async function GET(request: NextRequest) {
  try {
    // Get the auth token from the cookie
    const authToken = request.cookies.get('authToken')?.value;

    if (!authToken) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Call the backend API to validate the token and get user info
    // In a real implementation, you would have a dedicated endpoint for this
    // For now, we'll use a mock implementation
    try {
      // This would be a real API call in production
      // const response = await fetch(`${API_BASE_URL}/api/Auth/me`, {
      //   headers: {
      //     'Authorization': `Bearer ${authToken}`
      //   }
      // });

      // For now, we'll decode the JWT to get basic user info
      // This is not secure for production as it doesn't validate the token
      const tokenParts = authToken.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid token format');
      }

      const payload = JSON.parse(
        Buffer.from(tokenParts[1], 'base64').toString()
      );

      // Get the tenant ID from the cookie if it exists
      const tenantId = request.cookies.get('tenantId')?.value;

      // Get the isAdmin flag from the cookie
      const isAdmin = request.cookies.get('isAdmin')?.value === 'true';

      return NextResponse.json({
        userId: payload.nameid || payload.sub,
        email: payload.email,
        tenantId: tenantId || payload.tenant_id,
        isAdmin: isAdmin, // Include isAdmin flag
        authenticated: true
      });

    } catch (error) {
      console.error('Error validating token:', error);
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error fetching current user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user information' },
      { status: 500 }
    );
  }
}
