using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class TenantProfileConfiguration : IEntityTypeConfiguration<TenantProfile>
    {
        public void Configure(EntityTypeBuilder<TenantProfile> builder)
        {
            builder.HasKey(t => t.Id);

            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.Email)
                .IsRequired()
                .HasMaxLength(100);

            builder.HasIndex(t => t.Email)
                .IsUnique();

            builder.Property(t => t.Status)
                .IsRequired()
                .HasMaxLength(20); // e.g., "active", "suspended"

            builder.Property(t => t.Phone)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(t => t.Company)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.Subscription)
                .IsRequired()
                .HasMaxLength(50); // e.g., "professional", "enterprise"

            builder.Property(t => t.LastLoginTime)
                .IsRequired();

            builder.Property(t => t.BillingCycle)
                .IsRequired()
                .HasMaxLength(20); // e.g., "monthly", "annual", "quarterly"

            builder.Property(t => t.NextBillingDate)
                .IsRequired();

            builder.Property(t => t.SubscriptionStatus)
                .IsRequired()
                .HasMaxLength(20); // e.g., "active", "overdue"

            // JSON storage for payment method
            builder.Property(t => t.PaymentMethod)
                .IsRequired()
                .HasColumnType("nvarchar(max)") // Store as JSON string
                .HasDefaultValue("{}");

            // JSON storage for billing address
            builder.Property(t => t.BillingAddress)
                .IsRequired()
                .HasColumnType("nvarchar(max)") // Store as JSON string
                .HasDefaultValue("{}");

            // Create indexes for common query patterns
            builder.HasIndex(t => t.Status);
            builder.HasIndex(t => t.Subscription);
            builder.HasIndex(t => t.SubscriptionStatus);
            builder.HasIndex(t => t.Company);

            // Create composite indexes for common filtering scenarios
            builder.HasIndex(t => new { t.Status, t.Subscription });
            builder.HasIndex(t => new { t.Company, t.SubscriptionStatus });

            // Audit properties are handled by base entity configuration
            builder.Property(t => t.CreationTime)
                .IsRequired();

            builder.Property(t => t.LastModificationTime)
                .IsRequired(false);
        }
    }
}
