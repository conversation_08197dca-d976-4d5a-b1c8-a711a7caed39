using System;

namespace FY.WB.CSHero2.Application.Uploads.Dtos
{
    public class UploadDto
    {
        public Guid Id { get; set; }
        public string Filename { get; set; } = string.Empty;
        public long Size { get; set; } // In bytes
        public string ContentType { get; set; } = string.Empty; // MIME type
        public string StoragePath { get; set; } = string.Empty; // Path where the file is stored
        
        // Optional fields from entity
        public string? StorageProvider { get; set; }
        public string? ExternalUrl { get; set; }
        public string? Checksum { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? TenantId { get; set; }

        // Helper properties from entity methods, if desired in DTO
        public bool IsImage { get; set; }
        public bool IsDocument { get; set; }
    }
}
