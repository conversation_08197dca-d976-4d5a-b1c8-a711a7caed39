"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Search,
  FileText,
  Copy,
  Star,
  Calendar,
  Building,
  Tag,
  FileOutput,
  Download
} from "lucide-react";

// Temporary mock data - will be replaced with API calls
const savedReports = [
  {
    id: 1,
    name: "2023 Year-End Performance Review",
    client: "Acme Corporation",
    description: "Annual review of customer service team performance and achievements",
    tags: ["annual", "performance", "review"],
    createdDate: "2023-12-31",
    isFavorite: true,
  },
  {
    id: 2,
    name: "Q4 2023 Customer Satisfaction Report",
    client: "TechStart Inc",
    description: "Quarterly analysis of customer satisfaction metrics and feedback",
    tags: ["quarterly", "satisfaction", "analysis"],
    createdDate: "2023-12-15",
    isFavorite: false,
  },
  {
    id: 3,
    name: "Support Team Efficiency Analysis",
    client: "Global Solutions",
    description: "Detailed analysis of support team response times and resolution rates",
    tags: ["efficiency", "support", "metrics"],
    createdDate: "2023-11-30",
    isFavorite: true,
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function LibraryPage() {
  return (
    <div className="container mx-auto py-12 max-w-inner px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Report Library</h1>
            <p className="text-xl text-gray-600">
              Access and reuse your previously created reports
            </p>
          </div>
        </div>

        <div className="flex gap-4 mb-8">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
            <Input
              placeholder="Search saved reports..."
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export All
          </Button>
        </div>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-4"
      >
        {savedReports.map((report) => (
          <motion.div key={report.id} variants={cardVariants}>
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start gap-6">
                <div className="w-12 h-12 rounded-lg gradient-primary-secondary flex items-center justify-center flex-shrink-0">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                
                <div className="flex-grow">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-start gap-3">
                      <div>
                        <h3 className="text-xl font-semibold mb-2 flex items-center gap-2">
                          {report.name}
                          {report.isFavorite && (
                            <Star className="w-5 h-5 text-yellow-400 fill-current" />
                          )}
                        </h3>
                        <p className="text-gray-600">{report.description}</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Copy className="w-4 h-4 mr-1" />
                        Use as Template
                      </Button>
                      <Button variant="outline" size="sm">
                        <FileOutput className="w-4 h-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <Building className="w-4 h-4" />
                        Client
                      </div>
                      <div className="text-sm font-medium">{report.client}</div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <Calendar className="w-4 h-4" />
                        Created
                      </div>
                      <div className="text-sm">
                        {new Date(report.createdDate).toLocaleDateString()}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <Tag className="w-4 h-4" />
                        Tags
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {report.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
