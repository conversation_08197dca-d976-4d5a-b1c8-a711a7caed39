using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Forms.Commands
{
    public class DeleteFormCommandHandler : IRequestHandler<DeleteFormCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public DeleteFormCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(DeleteFormCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.Forms
                .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Form), request.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to delete this form.");
            }

            _context.Forms.Remove(entity);
            // For soft delete, you would do:
            // entity.IsDeleted = true;
            // entity.DeletionTime = DateTime.UtcNow;
            // entity.DeleterId = _currentUserService.UserId; // Assuming UserId is available in ICurrentUserService
            // _context.Forms.Update(entity);


            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
