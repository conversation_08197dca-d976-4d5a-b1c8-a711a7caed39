# Report Rendering Engine Implementation Plan

## Overview
This document outlines the implementation plan for the Report Rendering Engine, a component that leverages Large Language Models (LLMs) to generate HTML templates based on structured data and metadata.

## Architecture Components

### Domain Layer
- **Interfaces**
  - [x] `IDatabaseService` - Interface for database access to retrieve templates, data, and prompts
  - [x] `ILlmClient` - Interface for LLM client implementations
  - [x] `IHtmlValidator` - Interface for HTML validation

- **Models**
  - [x] `LlmMetrics` - Metrics for monitoring LLM performance
  - [x] `ValidationResult` - Result of HTML validation

- **Entities**
  - [x] `DocumentTemplateMetadata` - Metadata defining the structure of a document template
  - [x] `ReportStyle` - Style information for the report
  - [x] `ReportSection` - Represents a section in the document template
  - [x] `ReportField` - Represents a field in the document template

### Application Layer
- **Models**
  - [x] `CDataWrapper` - Special wrapper to ensure HTML content is properly included as CDATA in XML
  - [x] `RenderRequest` - Main request object that will be serialized to XML for the LLM
  - [x] `ReportStyle` - Application-specific style information
  - [x] `RenderSection` - Represents a section in the render request
  - [x] `RenderField` - Represents a field in the render request

- **Services**
  - [x] `PromptBuilder` - Static helper for building prompt objects from template data and metadata
  - [x] `XmlPromptSerializer` - Static helper for serializing RenderRequest objects to XML
  - [x] `ReportRenderer` - Main orchestrator for the report rendering process

### Infrastructure Layer
- **Models**
  - [x] `LlmConfig` - Configuration for LLM clients

- **Services**
  - [x] `LlmClientFactory` - Factory for creating different types of LLM clients
  - [x] `OpenAiLlmClient` - Implementation of ILlmClient for OpenAI
  - [x] `AnthropicLlmClient` - Implementation of ILlmClient for Anthropic Claude

## Implementation Flow

1. The `ReportRenderer` is the main entry point and orchestrates the entire process:
   - Retrieves template metadata, data, and existing HTML (if any) in parallel
   - Gets instruction prompt from cache or database
   - Builds a structured prompt object using `PromptBuilder`
   - Serializes the prompt to XML using `XmlPromptSerializer`
   - Sends the XML to the LLM using the configured `ILlmClient`
   - Optionally validates the generated HTML
   - Returns the HTML result

2. The `PromptBuilder` takes the template metadata, data, existing HTML, and instructions to create a structured `RenderRequest` object.

3. The `XmlPromptSerializer` converts the `RenderRequest` into a well-formed XML document that the LLM can understand.

4. The `LlmClientFactory` creates appropriate LLM clients (OpenAI or Anthropic) based on configuration.

5. The LLM clients (`OpenAiLlmClient` or `AnthropicLlmClient`) handle the communication with the respective LLM APIs.

## Key Features

- **Parallel Data Loading**: Template data, metadata, and existing HTML are loaded in parallel for efficiency.
- **Prompt Caching**: Frequently used prompts can be cached to improve performance.
- **Performance Monitoring**: Each step is timed, and metrics are logged for monitoring.
- **Flexible LLM Support**: The system supports multiple LLM providers through a factory pattern.
- **XML-Based Prompting**: Structured XML prompts provide clear instructions and data to the LLM.
- **HTML Validation**: Optional validation of the generated HTML for quality assurance.
- **Error Handling**: Comprehensive error handling with custom exceptions and logging.

## Code Structure

```
FY.WB.CSHero2.ReportRenderingEngine.Domain/
├── Interfaces/
│   ├── IDatabaseService.cs
│   ├── ILlmClient.cs
│   └── IHtmlValidator.cs
├── Models/
│   ├── LlmMetrics.cs
│   └── ValidationResult.cs
└── Entities/
    ├── DocumentTemplateMetadata.cs
    ├── ReportStyle.cs
    ├── ReportSection.cs
    └── ReportField.cs

FY.WB.CSHero2.ReportRenderingEngine.Application/
├── Common/
│   └── Models/
│       ├── CDataWrapper.cs
│       ├── RenderRequest.cs
│       ├── ReportStyle.cs
│       ├── RenderSection.cs
│       └── RenderField.cs
└── Services/
    ├── PromptBuilder.cs
    ├── XmlPromptSerializer.cs
    └── ReportRenderer.cs

FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/
├── Models/
│   └── LlmConfig.cs
└── Services/
    ├── LlmClientFactory.cs
    ├── OpenAiLlmClient.cs
    └── AnthropicLlmClient.cs
```

## Next Steps

- [ ] Implement database integration for retrieving templates, data, and prompts
- [ ] Add integration with actual LLM APIs (OpenAI and Anthropic)
- [ ] Implement HTML validation logic
- [ ] Create service registration and dependency injection setup
- [ ] Add configuration loading from app settings
- [ ] Develop controller/API endpoints to expose the rendering functionality
- [ ] Create integration tests
