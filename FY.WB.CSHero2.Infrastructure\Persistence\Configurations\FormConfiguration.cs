using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class FormConfiguration : IEntityTypeConfiguration<Form>
    {
        public void Configure(EntityTypeBuilder<Form> builder)
        {
            builder.HasKey(f => f.Id);

            builder.Property(f => f.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(f => f.CustomerName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.Email)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.Category)
                .IsRequired()
                .HasMaxLength(50); // e.g., "feedback", "complaint"

            builder.Property(f => f.Priority)
                .IsRequired()
                .HasMaxLength(20); // e.g., "high", "medium", "low"

            builder.Property(f => f.Description)
                .IsRequired()
                .HasMaxLength(2000); // Allow for longer descriptions

            builder.Property(f => f.Date)
                .IsRequired();

            // Create indexes for common query patterns
            builder.HasIndex(f => f.Category);
            builder.HasIndex(f => f.Priority);
            builder.HasIndex(f => f.Date);
            builder.HasIndex(f => f.Email);

            // Create a composite index for common filtering scenarios
            builder.HasIndex(f => new { f.Category, f.Priority, f.Date });

            // Make TenantId required and add foreign key relationship
            builder.Property(f => f.TenantId)
                .IsRequired();

            builder.HasOne(f => f.TenantProfile)
                .WithMany(tp => tp.Forms)
                .HasForeignKey(f => f.TenantId)
                .OnDelete(DeleteBehavior.Restrict);

            // Add index on TenantId for better query performance
            builder.HasIndex(f => f.TenantId);

            // Audit properties are handled by base entity configuration
            builder.Property(f => f.CreationTime)
                .IsRequired();

            builder.Property(f => f.LastModificationTime)
                .IsRequired(false);
        }
    }
}
