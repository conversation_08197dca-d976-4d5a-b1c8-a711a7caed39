/**
 * API Configuration Constants
 * 
 * This file centralizes all API URL and port configuration to avoid inconsistencies
 * across different parts of the application.
 */

// The default port for the ASP.NET Core backend
// This should match the port in Properties/launchSettings.json
export const DEFAULT_API_PORT = 5056;

// The base URL for the ASP.NET Core backend
// Uses environment variable if available, otherwise falls back to localhost with default port
export const API_BASE_URL = process.env.ASPNET_API_URL || `http://localhost:${DEFAULT_API_PORT}`;

// The base URL for API routes within the Next.js app
// Uses environment variable if available, otherwise falls back to relative path
export const NEXT_API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

// For health checks, we try multiple ports to find the running backend
export const HEALTH_CHECK_PORTS = [7104, 5000, 5001, 5056, 5057, 44300, 44301];
