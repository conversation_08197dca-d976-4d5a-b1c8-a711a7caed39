import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';
import { Subscription } from '@/types/subscription';

// Define the subscription data
const subscriptionData: Subscription[] = [
  // Basic tier
  {
    id: 'basic-monthly',
    name: 'Basic Monthly',
    description: 'Essential features for small businesses',
    type: 'basic',
    billingCycle: 'monthly',
    price: 19.99,
    features: [
      '5 User Accounts',
      'Basic Report Templates',
      'Email Support',
      '1GB Storage'
    ],
    isPopular: false,
    trialDays: 14
  },
  {
    id: 'basic-quarterly',
    name: 'Basic Quarterly',
    description: 'Essential features for small businesses',
    type: 'basic',
    billingCycle: 'quarterly',
    price: 53.97,
    features: [
      '5 User Accounts',
      'Basic Report Templates',
      'Email Support',
      '1GB Storage'
    ],
    isPopular: false,
    trialDays: 14,
    discountPercentage: 10
  },
  {
    id: 'basic-annual',
    name: 'Basic Annual',
    description: 'Essential features for small businesses',
    type: 'basic',
    billingCycle: 'annual',
    price: 239.88,
    features: [
      '5 User Accounts',
      'Basic Report Templates',
      'Email Support',
      '1GB Storage'
    ],
    isPopular: true,
    trialDays: 14,
    discountPercentage: 15
  },

  // Professional tier
  {
    id: 'professional-monthly',
    name: 'Professional Monthly',
    description: 'Advanced features for growing businesses',
    type: 'professional',
    billingCycle: 'monthly',
    price: 39.99,
    features: [
      '15 User Accounts',
      'Advanced Report Templates',
      'Priority Email Support',
      '5GB Storage',
      'Custom Branding',
      'API Access'
    ],
    isPopular: false,
    trialDays: 14
  },
  {
    id: 'professional-quarterly',
    name: 'Professional Quarterly',
    description: 'Advanced features for growing businesses',
    type: 'professional',
    billingCycle: 'quarterly',
    price: 107.97,
    features: [
      '15 User Accounts',
      'Advanced Report Templates',
      'Priority Email Support',
      '5GB Storage',
      'Custom Branding',
      'API Access'
    ],
    isPopular: false,
    trialDays: 14,
    discountPercentage: 10
  },
  {
    id: 'professional-annual',
    name: 'Professional Annual',
    description: 'Advanced features for growing businesses',
    type: 'professional',
    billingCycle: 'annual',
    price: 479.88,
    features: [
      '15 User Accounts',
      'Advanced Report Templates',
      'Priority Email Support',
      '5GB Storage',
      'Custom Branding',
      'API Access'
    ],
    isPopular: true,
    trialDays: 14,
    discountPercentage: 15
  },

  // Enterprise tier
  {
    id: 'enterprise-monthly',
    name: 'Enterprise Monthly',
    description: 'Complete solution for large organizations',
    type: 'enterprise',
    billingCycle: 'monthly',
    price: 79.99,
    features: [
      'Unlimited User Accounts',
      'All Report Templates',
      '24/7 Phone & Email Support',
      '25GB Storage',
      'Custom Branding',
      'Advanced API Access',
      'Dedicated Account Manager',
      'Custom Report Development',
      'SSO Integration'
    ],
    isPopular: false,
    trialDays: 30
  },
  {
    id: 'enterprise-quarterly',
    name: 'Enterprise Quarterly',
    description: 'Complete solution for large organizations',
    type: 'enterprise',
    billingCycle: 'quarterly',
    price: 215.97,
    features: [
      'Unlimited User Accounts',
      'All Report Templates',
      '24/7 Phone & Email Support',
      '25GB Storage',
      'Custom Branding',
      'Advanced API Access',
      'Dedicated Account Manager',
      'Custom Report Development',
      'SSO Integration'
    ],
    isPopular: false,
    trialDays: 30,
    discountPercentage: 10
  },
  {
    id: 'enterprise-annual',
    name: 'Enterprise Annual',
    description: 'Complete solution for large organizations',
    type: 'enterprise',
    billingCycle: 'annual',
    price: 959.88,
    features: [
      'Unlimited User Accounts',
      'All Report Templates',
      '24/7 Phone & Email Support',
      '25GB Storage',
      'Custom Branding',
      'Advanced API Access',
      'Dedicated Account Manager',
      'Custom Report Development',
      'SSO Integration'
    ],
    isPopular: true,
    trialDays: 30,
    discountPercentage: 15
  }
];

/**
 * Initialize the subscriptions data in the database
 */
async function initializeSubscriptions(): Promise<void> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    data.subscriptions = subscriptionData;
    await writeJsonFile('db.json', data);
  }
}

/**
 * Get all subscriptions with optional filtering, sorting, and pagination
 * @param options Query options for filtering, sorting, and pagination
 * @returns Array of subscriptions
 */
export async function getSubscriptions(options: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
  type?: 'basic' | 'professional' | 'enterprise';
  billingCycle?: 'monthly' | 'quarterly' | 'annual';
} = {}): Promise<Subscription[]> {
  const { query, limit, page, sortBy, order, type, billingCycle } = options;
  
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  // Apply type filter if provided
  let filteredSubscriptions = data.subscriptions;
  if (type) {
    filteredSubscriptions = filteredSubscriptions.filter((sub: Subscription) => sub.type === type);
  }
  
  // Apply billing cycle filter if provided
  if (billingCycle) {
    filteredSubscriptions = filteredSubscriptions.filter((sub: Subscription) => sub.billingCycle === billingCycle);
  }
  
  return processArray(filteredSubscriptions, {
    query,
    sortBy,
    order,
    page: page ? parseInt(page.toString()) : undefined,
    limit: limit ? parseInt(limit.toString()) : undefined
  });
}

/**
 * Get a subscription by ID
 * @param id Subscription ID
 * @returns Subscription object or null if not found
 */
export async function getSubscriptionById(id: string): Promise<Subscription | null> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  const subscription = data.subscriptions.find((s: Subscription) => s.id === id);
  return subscription || null;
}

/**
 * Get subscriptions by type
 * @param type Subscription type (basic, professional, enterprise)
 * @returns Array of subscriptions of the specified type
 */
export async function getSubscriptionsByType(type: 'basic' | 'professional' | 'enterprise'): Promise<Subscription[]> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  return data.subscriptions.filter((s: Subscription) => s.type === type);
}

/**
 * Get subscriptions by billing cycle
 * @param billingCycle Billing cycle (monthly, quarterly, annual)
 * @returns Array of subscriptions with the specified billing cycle
 */
export async function getSubscriptionsByBillingCycle(billingCycle: 'monthly' | 'quarterly' | 'annual'): Promise<Subscription[]> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  return data.subscriptions.filter((s: Subscription) => s.billingCycle === billingCycle);
}

/**
 * Get subscription by type and billing cycle
 * @param type Subscription type (basic, professional, enterprise)
 * @param billingCycle Billing cycle (monthly, quarterly, annual)
 * @returns Subscription object or null if not found
 */
export async function getSubscriptionByTypeAndBillingCycle(
  type: 'basic' | 'professional' | 'enterprise',
  billingCycle: 'monthly' | 'quarterly' | 'annual'
): Promise<Subscription | null> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  const subscription = data.subscriptions.find(
    (s: Subscription) => s.type === type && s.billingCycle === billingCycle
  );
  
  return subscription || null;
}

/**
 * Get popular subscriptions
 * @returns Array of subscriptions marked as popular
 */
export async function getPopularSubscriptions(): Promise<Subscription[]> {
  const data = await readJsonFile<any>();
  
  // Initialize subscriptions array if it doesn't exist
  if (!data.subscriptions) {
    await initializeSubscriptions();
    data.subscriptions = subscriptionData;
  }
  
  return data.subscriptions.filter((s: Subscription) => s.isPopular);
}

// Initialize subscriptions when the module is imported
initializeSubscriptions().catch(console.error);
