using FY.WB.CSHero2.Application.Clients.Commands;
using FY.WB.CSHero2.Application.Clients.Dtos;
using FY.WB.CSHero2.Application.Clients.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Controllers
{
    [Authorize] // Assuming client operations require authentication
    [ApiController]
    [Route("api/[controller]")]
    public class ClientsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ClientsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResult<ClientDto>>> GetClients([FromQuery] ClientQueryParametersDto queryParameters)
        {
            var query = new GetClientsQuery(queryParameters);
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ClientDto>> GetClientById(string id)
        {
            var query = new GetClientByIdQuery(id);
            var client = await _mediator.Send(query);
            return client != null ? Ok(client) : NotFound();
        }

        [HttpPost]
        public async Task<ActionResult<ClientDto>> CreateClient([FromBody] CreateClientRequestDto clientDto)
        {
            var command = new CreateClientCommand(clientDto);
            var createdClient = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetClientById), new { id = createdClient.Id }, createdClient);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<ClientDto>> UpdateClient(string id, [FromBody] UpdateClientRequestDto clientDto)
        {
            var command = new UpdateClientCommand(id, clientDto);
            var updatedClient = await _mediator.Send(command);
            return updatedClient != null ? Ok(updatedClient) : NotFound();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClient(string id)
        {
            var command = new DeleteClientCommand(id);
            var result = await _mediator.Send(command);
            return result ? NoContent() : NotFound();
        }

        [HttpPost("{id}/archive")]
        public async Task<ActionResult<ClientDto>> ArchiveClient(string id)
        {
            var command = new ArchiveClientCommand(id, true); // true to archive
            var client = await _mediator.Send(command);
            return client != null ? Ok(client) : NotFound();
        }

        [HttpPost("{id}/unarchive")]
        public async Task<ActionResult<ClientDto>> UnarchiveClient(string id)
        {
            var command = new ArchiveClientCommand(id, false); // false to unarchive
            var client = await _mediator.Send(command);
            return client != null ? Ok(client) : NotFound();
        }
    }
}
