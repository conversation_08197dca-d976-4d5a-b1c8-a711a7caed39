"use client";

import { motion } from "framer-motion";

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-[rgb(var(--primary))]">
      {/* Background gradient */}
      <div className="absolute inset-0 gradient-primary-tertiary opacity-90" />

      {/* Content */}
      <div className="relative container md:px-16 mx-auto px-2 py-16 max-w-inner">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-3xl"
        >
          <h1 className="text-5xl font-bold text-white mb-6">
            Transform Your Customer Service Data Into Actionable Reports
          </h1>
          <p className="text-xl text-white/90 mb-8">
            Generate comprehensive, visually appealing reports with AI-powered insights. 
            Streamline your analysis and make data-driven decisions faster than ever.
          </p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <a
              href="/client/reports/create"
              className="inline-flex items-center px-6 py-3 rounded-lg bg-white text-[rgb(var(--primary))] font-semibold hover:bg-white/90 transition-colors"
            >
              Start Creating Reports
              <svg
                className="ml-2 w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                />
              </svg>
            </a>
          </motion.div>
        </motion.div>

        {/* Animated Report Preview */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="absolute top-1/2 right-4 transform -translate-y-1/2 hidden lg:block"
        >
          <div className="w-[600px] h-[400px] bg-white rounded-lg shadow-2xl p-6">
            {/* Mock report content - we'll enhance this later */}
            <div className="h-full rounded border-2 border-dashed border-gray-200 flex items-center justify-center">
              <p className="text-gray-500">Report Preview Animation Coming Soon</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
