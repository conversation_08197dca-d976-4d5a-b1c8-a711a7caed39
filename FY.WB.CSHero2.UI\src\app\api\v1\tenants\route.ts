import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/tenants
 * Returns a list of tenants with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Forward all original searchParams to the backend
    const queryString = searchParams.toString();

    const authToken = cookies().get('authToken')?.value;
    const requestHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      requestHeaders['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making request to backend: ${API_BASE_URL}/api/TenantProfiles${queryString ? `?${queryString}` : ''}`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/TenantProfiles${queryString ? `?${queryString}` : ''}`, {
      method: 'GET',
      headers: requestHeaders,
      cache: 'no-store', // Disable caching to ensure fresh data
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to fetch tenants from backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const tenantsData = await backendResponse.json();
    console.log(`Successfully fetched tenants from backend: ${JSON.stringify(tenantsData).substring(0, 100)}...`);
    
    // Set headers for pagination if available from backend
    const responseHeaders = new Headers();
    if (backendResponse.headers.has('X-Total-Count')) {
      const totalCount = backendResponse.headers.get('X-Total-Count');
      if (totalCount) {
        responseHeaders.set('X-Total-Count', totalCount);
      }
    } else if (Array.isArray(tenantsData)) {
      responseHeaders.set('X-Total-Count', tenantsData.length.toString());
    }

    return NextResponse.json(tenantsData, { headers: responseHeaders });
  } catch (error) {
    console.error('Error fetching tenants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tenants', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/tenants
 * Creates a new tenant
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const authToken = cookies().get('authToken')?.value;
    const requestHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      requestHeaders['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making POST request to backend: ${API_BASE_URL}/api/TenantProfiles`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/TenantProfiles`, {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to create tenant via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const newTenant = await backendResponse.json();
    console.log(`Successfully created tenant: ${JSON.stringify(newTenant).substring(0, 100)}...`);
    return NextResponse.json(newTenant, { status: backendResponse.status }); // Use backend status, likely 201
  } catch (error) {
    console.error('Error creating tenant:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to create tenant: ${errorMessage}` },
      { status: 500 }
    );
  }
}
