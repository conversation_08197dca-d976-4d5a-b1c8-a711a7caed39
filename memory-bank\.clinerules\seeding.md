8. **Database Seeding**
   • Create `Infrastructure/Data/SeedData.cs` with an idempotent `SeedAsync(IServiceProvider)` method.
   • Seed demo tenants, users, and a minimal sample domain graph for each feature.
   • Source data from JSON files in `Tests/TestData/`, deserialized via `System.Text.Json`.

9. **E2E Test Fixtures**
   • Generate Playwright test scaffolding in `Tests/E2E/`.  
   • Tests must call an API that first invokes `SeedData.SeedAsync` to guarantee a known state.

User (Additions)
✓ Also create:  
  • `SeedData.cs` as per rule 8.  
  • At least one JSON file per entity type under `Tests/TestData/` (e.g., `invoices.json`, `clients.json`).  
  • A sample Playwright spec (`invoice.e2e.spec.ts`) showing login, seeded-invoice retrieval, and assertion.
