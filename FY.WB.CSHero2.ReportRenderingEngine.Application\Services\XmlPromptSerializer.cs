using System;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Static helper for serializing RenderRequest objects to XML
    /// </summary>
    public static class XmlPromptSerializer
    {
        /// <summary>
        /// Serializes a RenderRequest object to XML
        /// </summary>
        /// <param name="request">The request object to serialize</param>
        /// <returns>XML string representation</returns>
        public static string SerializeToXml(RenderRequest request)
        {
            if (request == null) throw new ArgumentNullException(nameof(request));

            var serializer = new XmlSerializer(typeof(RenderRequest));
            
            // Use custom settings for XML writer
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                OmitXmlDeclaration = false
            };

            using var stringWriter = new StringWriter();
            using var xmlWriter = XmlWriter.Create(stringWriter, settings);
            
            // Add XML namespace to prevent conflicts
            var ns = new XmlSerializerNamespaces();
            ns.Add(string.Empty, string.Empty); // Remove default namespace

            serializer.Serialize(xmlWriter, request, ns);
            return stringWriter.ToString();
        }
    }
}
