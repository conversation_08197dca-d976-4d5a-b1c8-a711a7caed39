"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  CreditCard,
  Lock,
  UserCircle,
  Settings2,
  Bell,
  Moon,
  BarChart,
  Globe,
} from "lucide-react";

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-16"
      >
        <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Settings</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Manage your account preferences and system settings
        </p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-8"
      >
        {/* Subscription Management */}
        <motion.div variants={cardVariants}>
          <Card className="p-8">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg gradient-primary-secondary flex items-center justify-center flex-shrink-0">
                <CreditCard className="w-6 h-6 text-white" />
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-semibold mb-6">Subscription Management</h2>
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium text-lg">Professional Plan</h3>
                        <p className="text-gray-600">Next billing date: March 20, 2024</p>
                      </div>
                      <Button size="lg">Change Plan</Button>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-medium text-lg mb-4">Payment Method</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-8 bg-gray-200 rounded"></div>
                        <span className="text-gray-600">•••• 4242</span>
                      </div>
                      <Button variant="outline">Update</Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Password Settings */}
        <motion.div variants={cardVariants}>
          <Card className="p-8">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg gradient-secondary-tertiary flex items-center justify-center flex-shrink-0">
                <Lock className="w-6 h-6 text-white" />
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-semibold mb-6">Password Settings</h2>
                <form className="space-y-4 max-w-md">
                  <div>
                    <Label htmlFor="current-password">Current Password</Label>
                    <Input type="password" id="current-password" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="new-password">New Password</Label>
                    <Input type="password" id="new-password" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input type="password" id="confirm-password" className="mt-1" />
                  </div>
                  <Button type="submit" size="lg" className="mt-2">Update Password</Button>
                </form>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Contact Information */}
        <motion.div variants={cardVariants}>
          <Card className="p-8">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg gradient-primary-tertiary flex items-center justify-center flex-shrink-0">
                <UserCircle className="w-6 h-6 text-white" />
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-semibold mb-6">Contact Information</h2>
                <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input type="text" id="name" defaultValue="John Doe" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input type="email" id="email" defaultValue="<EMAIL>" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input type="tel" id="phone" defaultValue="+****************" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="company">Company Name</Label>
                    <Input type="text" id="company" defaultValue="Acme Inc." className="mt-1" />
                  </div>
                  <div className="md:col-span-2">
                    <Button type="submit" size="lg" className="mt-2">Save Changes</Button>
                  </div>
                </form>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* General System Settings */}
        <motion.div variants={cardVariants}>
          <Card className="p-8">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg gradient-primary-secondary flex items-center justify-center flex-shrink-0">
                <Settings2 className="w-6 h-6 text-white" />
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-semibold mb-6">General System Settings</h2>
                <div className="space-y-8">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Bell className="w-5 h-5 text-gray-600" />
                      <div>
                        <Label htmlFor="notifications">Email Notifications</Label>
                        <p className="text-sm text-gray-600">Receive updates about your reports</p>
                      </div>
                    </div>
                    <Switch id="notifications" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Moon className="w-5 h-5 text-gray-600" />
                      <div>
                        <Label htmlFor="theme">Dark Mode</Label>
                        <p className="text-sm text-gray-600">Toggle dark mode theme</p>
                      </div>
                    </div>
                    <Switch id="theme" />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <BarChart className="w-5 h-5 text-gray-600" />
                      <div>
                        <Label htmlFor="analytics">Usage Analytics</Label>
                        <p className="text-sm text-gray-600">Help us improve by sharing usage data</p>
                      </div>
                    </div>
                    <Switch id="analytics" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Globe className="w-5 h-5 text-gray-600" />
                      <div>
                        <Label htmlFor="language">Language</Label>
                        <p className="text-sm text-gray-600">Choose your preferred language</p>
                      </div>
                    </div>
                    <select 
                      id="language" 
                      className="rounded-md border border-gray-300 p-2 bg-white"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  );
}
