﻿using System;
using System.Collections.Generic;

Console.WriteLine("🚀 Report Rendering Engine V2 - Quick Test");
Console.WriteLine(new string('=', 50));

try
{
    // Test basic model creation
    Console.WriteLine("📋 Testing Enhanced Models...");

    // Test VersionMetadata
    var metadata = new VersionMetadata
    {
        Description = "Test version",
        Tags = new List<string> { "test", "v2" },
        Priority = "High",
        VersionType = "Major"
    };

    Console.WriteLine($"✅ VersionMetadata: {metadata.Description}");
    Console.WriteLine($"   Tags: {string.Join(", ", metadata.Tags)}");

    // Test ExportOptions
    var exportOptions = new ExportOptions
    {
        Quality = "High",
        PaperSize = "A4",
        IncludeImages = true
    };

    Console.WriteLine($"✅ ExportOptions: {exportOptions.Quality} quality, {exportOptions.PaperSize}");

    Console.WriteLine("\n" + new string('=', 50));
    Console.WriteLine("✅ ALL BASIC TESTS PASSED!");
    Console.WriteLine("📊 Report Rendering Engine V2 models are working!");
    Console.WriteLine(new string('=', 50));
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Test failed: {ex.Message}");
}

// Simple model classes for testing
public class VersionMetadata
{
    public string Description { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new List<string>();
    public string Priority { get; set; } = string.Empty;
    public string VersionType { get; set; } = string.Empty;
}

public class ExportOptions
{
    public string Quality { get; set; } = string.Empty;
    public string PaperSize { get; set; } = string.Empty;
    public bool IncludeImages { get; set; }
}
