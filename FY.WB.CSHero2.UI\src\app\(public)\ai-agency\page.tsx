"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AIAgencyPage() {
  const router = useRouter();

  // Redirect to home page after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push("/");
    }, 100);
    
    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Redirecting...</h1>
        <p className="text-xl">Please wait while we redirect you to the homepage.</p>
      </div>
    </div>
  );
}
