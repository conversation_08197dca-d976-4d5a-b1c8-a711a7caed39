# Cosmos DB Containers Setup for Report Rendering Engine
# Creates database and containers in existing cshero-cosmosdb account

param(
    [string]$ResourceGroup = "rg_CSHero",
    [string]$CosmosAccount = "cshero-cosmosdb",
    [string]$DatabaseName = "ReportRenderingEngine"
)

Write-Host "=== Cosmos DB Containers Setup ===" -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Cyan
Write-Host "Cosmos Account: $CosmosAccount" -ForegroundColor Cyan
Write-Host "Database: $DatabaseName" -ForegroundColor Cyan
Write-Host ""

try {
    # Check if logged into Azure CLI
    $account = az account show 2>$null | ConvertFrom-Json
    if (-not $account) {
        Write-Host "Please login to Azure CLI first: az login" -ForegroundColor Red
        exit 1
    }

    Write-Host "Logged in as: $($account.user.name)" -ForegroundColor Green
    Write-Host ""

    # Create database (serverless)
    Write-Host "Creating database: $DatabaseName" -ForegroundColor Yellow
    az cosmosdb sql database create `
        --account-name $CosmosAccount `
        --resource-group $ResourceGroup `
        --name $DatabaseName

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Database may already exist, continuing..." -ForegroundColor Yellow
    }

    # Create report-styles container
    Write-Host "Creating container: report-styles" -ForegroundColor Yellow
    az cosmosdb sql container create `
        --account-name $CosmosAccount `
        --resource-group $ResourceGroup `
        --database-name $DatabaseName `
        --name "report-styles" `
        --partition-key-path "/tenantId"

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Container may already exist, continuing..." -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "✅ Cosmos DB containers setup complete!" -ForegroundColor Green
    Write-Host "Database: $DatabaseName" -ForegroundColor White
    Write-Host "Container: report-styles (partitioned by /tenantId)" -ForegroundColor White

} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
