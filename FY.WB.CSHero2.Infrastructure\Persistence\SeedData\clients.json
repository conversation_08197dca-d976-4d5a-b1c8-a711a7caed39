[{"Id": "00000000-0000-0000-0002-000000000001", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Tech Startup Inc", "CreatedAt": "2024-01-15T08:00:00Z", "UpdatedAt": "2024-02-20T15:30:00Z", "Phone": "+****************", "Address": "123 Innovation Street, Silicon Valley, CA", "CompanySize": "10-50", "Industry": "Software Development", "TenantId": "00000000-0000-0000-0003-000000000001"}, {"Id": "00000000-0000-0000-0002-000000000002", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "CloudTech Solutions", "CreatedAt": "2024-01-20T09:15:00Z", "UpdatedAt": "2024-02-19T14:20:00Z", "Phone": "+****************", "Address": "456 Cloud Ave, San Francisco, CA", "CompanySize": "50-100", "Industry": "Cloud Computing", "TenantId": "00000000-0000-0000-0003-000000000001"}, {"Id": "00000000-0000-0000-0002-000000000003", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "AI Research Labs", "CreatedAt": "2024-01-25T10:30:00Z", "UpdatedAt": "2024-02-18T16:45:00Z", "Phone": "+****************", "Address": "789 AI Blvd, San Jose, CA", "CompanySize": "100-500", "Industry": "Artificial Intelligence", "TenantId": "00000000-0000-0000-0003-000000000001"}, {"Id": "00000000-0000-0000-0002-000000000004", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "MedClinic Center", "CreatedAt": "2024-01-30T11:45:00Z", "UpdatedAt": "2024-02-17T13:15:00Z", "Phone": "+****************", "Address": "321 Healthcare Drive, Chicago, IL", "CompanySize": "100-250", "Industry": "Healthcare", "TenantId": "00000000-0000-0000-0003-000000000002"}, {"Id": "00000000-0000-0000-0002-000000000005", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Care Services Group", "CreatedAt": "2024-02-01T13:00:00Z", "UpdatedAt": "2024-02-16T17:30:00Z", "Phone": "+****************", "Address": "654 Care Street, Chicago, IL", "CompanySize": "250-500", "Industry": "Healthcare Services", "TenantId": "00000000-0000-0000-0003-000000000002"}, {"Id": "00000000-0000-0000-0002-000000000006", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Research Labs Co", "CreatedAt": "2024-02-05T14:15:00Z", "UpdatedAt": "2024-02-15T18:45:00Z", "Phone": "+****************", "Address": "987 Research Park, Otherville, NY", "CompanySize": "50-100", "Industry": "Research & Development", "TenantId": "00000000-0000-0000-0003-000000000004"}, {"Id": "00000000-0000-0000-0002-000000000007", "Name": "<PERSON>", "Email": "l.and<PERSON>@innovatetech.com", "Status": "Active", "CompanyName": "Innovate Technologies", "CreatedAt": "2024-02-10T15:30:00Z", "UpdatedAt": "2024-02-14T19:00:00Z", "Phone": "+****************", "Address": "753 Innovation Way, Otherville, NY", "CompanySize": "100-250", "Industry": "Technology Innovation", "TenantId": "00000000-0000-0000-0003-000000000004"}]