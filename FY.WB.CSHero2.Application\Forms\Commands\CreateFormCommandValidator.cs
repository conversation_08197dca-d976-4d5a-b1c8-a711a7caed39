using FluentValidation;
using FY.WB.CSHero2.Application.Forms.Dtos;
using System;

namespace FY.WB.CSHero2.Application.Forms.Commands
{
    public class CreateFormCommandValidator : AbstractValidator<CreateFormCommand>
    {
        public CreateFormCommandValidator()
        {
            RuleFor(v => v.Dto.Title)
                .NotEmpty().WithMessage("Title is required.")
                .MaximumLength(200).WithMessage("Title must not exceed 200 characters.");

            RuleFor(v => v.Dto.CustomerName)
                .NotEmpty().WithMessage("Customer Name is required.")
                .MaximumLength(200).WithMessage("Customer Name must not exceed 200 characters.");

            RuleFor(v => v.Dto.Email)
                .NotEmpty().WithMessage("Email is required.")
                .EmailAddress().WithMessage("Email is not a valid email address.")
                .MaximumLength(256).WithMessage("Email must not exceed 256 characters.");

            RuleFor(v => v.Dto.Category)
                .NotEmpty().WithMessage("Category is required.")
                .MaximumLength(100).WithMessage("Category must not exceed 100 characters.");

            RuleFor(v => v.Dto.Priority)
                .NotEmpty().WithMessage("Priority is required.")
                .MaximumLength(50).WithMessage("Priority must not exceed 50 characters.");

            RuleFor(v => v.Dto.Description)
                .NotEmpty().WithMessage("Description is required.")
                .MaximumLength(2000).WithMessage("Description must not exceed 2000 characters.");

            RuleFor(v => v.Dto.Date)
                .NotEmpty().WithMessage("Date is required.");
        }
    }
}
