/**
 * Configuration types for style form fields
 */

export interface FieldOption {
  value: string;
  label: string;
}

export interface FieldConfig {
  name: string;
  label: string;
  type: 'select' | 'text' | 'color' | 'number' | 'checkbox' | 'textarea' | 'icon-select' | 'dimension-input';
  defaultValue?: any;
  placeholder?: string;
  options?: FieldOption[];
  showWhen?: (values: any) => boolean;
  customProps?: Record<string, any>;
}

export interface SectionConfig {
  id: string;
  title: string;
  fields: FieldConfig[];
  defaultExpanded?: boolean;
}

export interface StyleSectionConfig {
  sections: SectionConfig[];
}
