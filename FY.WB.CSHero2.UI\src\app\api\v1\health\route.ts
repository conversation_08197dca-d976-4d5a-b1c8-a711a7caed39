import { NextResponse } from 'next/server';
import { HEALTH_CHECK_PORTS } from '@/lib/constants';

/**
 * GET /api/v1/health
 * Checks if the backend API is running by trying different endpoints
 */
export async function GET() {
  console.log("Health check API route called");
  const results = [];

  console.log(`Checking ports: ${HEALTH_CHECK_PORTS.join(', ')}`);
  for (const port of HEALTH_CHECK_PORTS) {
    console.log(`Checking port ${port}...`);
    const endpoints = [
      `/api/Health`,
      `/api/ping`
    ];

    let portResults = [];

    for (const endpoint of endpoints) {
        const baseHttp = `http://localhost:${port}${endpoint}`;
        const baseHttps = `https://localhost:${port}${endpoint}`;
        // Try HTTP first, then HTTPS
        const urls = [
          baseHttp,
          baseHttps
        ];

      let success = false;

      for (const testUrl of urls) {
        try {
          console.log(`Trying ${testUrl}...`);
          const fetchResponse = await fetch(testUrl, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            // Set a short timeout to avoid waiting too long
            signal: AbortSignal.timeout(2000)
          });

          if (fetchResponse.ok) {
            const data = await fetchResponse.json();
            portResults.push({
              endpoint,
              url: testUrl,
              status: 'available',
              data
            });
            success = true;
            break; // Found a working URL, no need to try others
          } else {
            console.log(`Got status ${fetchResponse.status} from ${testUrl}`);
          }
        } catch (error) {
          console.error(`Error checking ${testUrl}:`, error);
          // Continue to the next URL
        }
      }

      if (!success) {
        portResults.push({
          endpoint,
          status: 'unavailable',
          error: 'Failed to connect to endpoint via HTTP and HTTPS'
        });
      }
    }

    results.push({
      port,
      endpoints: portResults,
      status: portResults.some(r => r.status === 'available') ? 'available' : 'unavailable'
    });
  }

  return NextResponse.json({
    timestamp: new Date().toISOString(),
    backendStatus: results
  });
}
