"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Search,
  FileText,
  Layout,
  Copy,
  Tag,
  Clock,
  User,
  FileOutput
} from "lucide-react";

// Temporary mock data - will be replaced with API calls
const templates = [
  {
    id: 1,
    name: "Monthly Performance Review",
    description: "Comprehensive template for evaluating customer service team performance",
    segments: ["Overview", "Metrics", "Achievements", "Goals"],
    category: "Performance",
    lastUsed: "2024-01-15",
    author: "Admin Team",
  },
  {
    id: 2,
    name: "Customer Feedback Analysis",
    description: "Template for analyzing and presenting customer feedback and satisfaction metrics",
    segments: ["Summary", "Feedback Analysis", "Action Items"],
    category: "Analysis",
    lastUsed: "2024-01-20",
    author: "Quality Team",
  },
  {
    id: 3,
    name: "Quarterly Service Report",
    description: "Detailed quarterly report template for service quality and improvements",
    segments: ["Executive Summary", "KPIs", "Challenges", "Recommendations"],
    category: "Quarterly",
    lastUsed: "2024-01-25",
    author: "Management",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function TemplatesPage() {
  return (
    <div className="container mx-auto py-12 max-w-inner px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Report Templates</h1>
            <p className="text-xl text-gray-600">
              Choose from our collection of professional report templates
            </p>
          </div>
        </div>

        <div className="flex gap-4 mb-8">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
            <Input
              placeholder="Search templates..."
              className="pl-10"
            />
          </div>
        </div>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-4"
      >
        {templates.map((template) => (
          <motion.div key={template.id} variants={cardVariants}>
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start gap-6">
                <div className="w-12 h-12 rounded-lg gradient-primary-secondary flex items-center justify-center flex-shrink-0">
                  <Layout className="w-6 h-6 text-white" />
                </div>
                
                <div className="flex-grow">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">{template.name}</h3>
                      <p className="text-gray-600">{template.description}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Copy className="w-4 h-4 mr-1" />
                        Use Template
                      </Button>
                      <Button variant="outline" size="sm">
                        <FileOutput className="w-4 h-4 mr-1" />
                        Preview
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <Tag className="w-4 h-4" />
                        Category
                      </div>
                      <div className="px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-600 inline-block">
                        {template.category}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <Clock className="w-4 h-4" />
                        Last Used
                      </div>
                      <div className="text-sm">
                        {new Date(template.lastUsed).toLocaleDateString()}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center gap-2 text-gray-600 mb-1">
                        <User className="w-4 h-4" />
                        Created By
                      </div>
                      <div className="text-sm">{template.author}</div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex items-center gap-2 text-gray-600 mb-2">
                      <FileText className="w-4 h-4" />
                      Segments
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {template.segments.map((segment) => (
                        <span
                          key={segment}
                          className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600"
                        >
                          {segment}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
