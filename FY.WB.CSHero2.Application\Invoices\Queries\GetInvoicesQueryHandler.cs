using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Invoices.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic; // Added for List
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Invoices.Queries
{
    public class GetInvoicesQueryHandler : IRequestHandler<GetInvoicesQuery, PagedResult<InvoiceDto>>
    {
        private readonly IApplicationDbContext _context;
        // IMapper removed
        private readonly ICurrentUserService _currentUserService;

        public GetInvoicesQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService) // IMapper removed from constructor
        {
            _context = context;
            // _mapper assignment removed
            _currentUserService = currentUserService;
        }

        public async Task<PagedResult<InvoiceDto>> Handle(GetInvoicesQuery request, CancellationToken cancellationToken)
        {
            var queryParameters = request.Parameters;
            var page = queryParameters.Page ?? 1;
            var limit = queryParameters.Limit ?? 10;

            IQueryable<Invoice> query = _context.Invoices.AsNoTracking();

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(queryParameters.Search))
            {
                var searchTerm = queryParameters.Search.ToLower().Trim();
                query = query.Where(i =>
                    (i.OrderNumber != null && i.OrderNumber.ToLower().Contains(searchTerm)) ||
                    (i.Plans != null && i.Plans.ToLower().Contains(searchTerm)) ||
                    (i.TenantProfile != null && i.TenantProfile.Name != null && i.TenantProfile.Name.ToLower().Contains(searchTerm)) // Assuming TenantProfile.Name for search
                );
            }

            if (!string.IsNullOrWhiteSpace(queryParameters.Type))
            {
                query = query.Where(i => i.Type != null && i.Type.ToLower() == queryParameters.Type.ToLower());
            }

            if (!string.IsNullOrWhiteSpace(queryParameters.Status))
            {
                query = query.Where(i => i.Status != null && i.Status.ToLower() == queryParameters.Status.ToLower());
            }
            
            // Multi-tenancy is handled by global query filters in ApplicationDbContext
            // No explicit TenantId filter needed here for standard user access.
            // If _currentUserService.IsAdmin is true, global filter might be bypassed depending on its implementation.

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(queryParameters.SortBy))
            {
                var sortBy = queryParameters.SortBy.ToLower();
                var isDescending = queryParameters.SortOrder?.ToLower() == "desc";

                Expression<Func<Invoice, object>> keySelector = sortBy switch
                {
                    "ordernumber" => i => i.OrderNumber,
                    "type" => i => i.Type,
                    "plans" => i => i.Plans,
                    "amount" => i => i.Amount,
                    "status" => i => i.Status,
                    "date" => i => i.Date,
                    "createdat" => i => i.CreationTime,
                    _ => i => i.CreationTime // Default sort
                };

                query = isDescending ? query.OrderByDescending(keySelector) : query.OrderBy(keySelector);
            }
            else
            {
                query = query.OrderByDescending(i => i.CreationTime); // Default sort
            }

            var totalCount = await query.CountAsync(cancellationToken);
            var totalPages = (int)Math.Ceiling(totalCount / (double)limit); // totalPages is calculated by PagedResult

            var invoiceEntities = await query
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync(cancellationToken);

            var invoiceDtos = invoiceEntities.Select(invoice => new InvoiceDto
            {
                Id = invoice.Id,
                OrderNumber = invoice.OrderNumber,
                Type = invoice.Type,
                Plans = invoice.Plans,
                Amount = invoice.Amount,
                Status = invoice.Status,
                Date = invoice.Date,
                CreatedAt = invoice.CreationTime,
                UpdatedAt = invoice.LastModificationTime,
                TenantId = invoice.TenantId
                // If TenantProfile.Name is needed, ensure TenantProfile is included in the query:
                // query = _context.Invoices.Include(i => i.TenantProfile).AsNoTracking();
                // TenantName = invoice.TenantProfile?.Name 
            }).ToList();

            return new PagedResult<InvoiceDto>(invoiceDtos, page, limit, totalCount); // Corrected constructor
        }
    }
}
