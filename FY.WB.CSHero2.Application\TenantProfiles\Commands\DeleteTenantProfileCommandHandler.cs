using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.TenantProfiles.Commands
{
    public class DeleteTenantProfileCommandHandler : IRequestHandler<DeleteTenantProfileCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public DeleteTenantProfileCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task Handle(DeleteTenantProfileCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.TenantProfiles
                .FirstOrDefaultAsync(tp => tp.Id == request.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(TenantProfile), request.Id);
            }

            // Verify that the tenant profile belongs to the current user's tenant
            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                 // Or if an admin is allowed to delete any tenant profile, add role check here
                throw new ForbiddenAccessException("User is not authorized to delete this tenant profile.");
            }

            _context.TenantProfiles.Remove(entity);

            // If it's a soft delete, mark as deleted instead:
            // entity.IsDeleted = true;
            // entity.DeletionTime = DateTime.UtcNow;
            // entity.DeleterId = _currentUserService.UserId;
            // _context.TenantProfiles.Update(entity);


            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
