using Microsoft.Extensions.DependencyInjection;
using FY.WB.CSHero2.Infrastructure.Services;
using FY.WB.CSHero2.Application.Common.Interfaces;

namespace FY.WB.CSHero2.StartupConfiguration
{
    public static class ServiceConfiguration
    {
        public static IServiceCollection AddServiceConfiguration(this IServiceCollection services)
        {
            // Register HttpContextAccessor and CurrentUserService
            services.AddHttpContextAccessor();
            services.AddScoped<ICurrentUserService, CurrentUserService>();
            
            // Register CompanyProfileService
            services.AddScoped<ICompanyProfileService, CompanyProfileService>();

            return services;
        }
    }
}
