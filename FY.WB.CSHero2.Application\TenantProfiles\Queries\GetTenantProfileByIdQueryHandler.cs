using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.TenantProfiles.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.TenantProfiles.Queries
{
    public class GetTenantProfileByIdQueryHandler : IRequestHandler<GetTenantProfileByIdQuery, TenantProfileDto?>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetTenantProfileByIdQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<TenantProfileDto?> Handle(GetTenantProfileByIdQuery request, CancellationToken cancellationToken)
        {
            TenantProfile? tenantProfile;
            IQueryable<TenantProfile> query = _context.TenantProfiles.AsNoTracking();

            if (_currentUserService.IsAdmin)
            {
                // Admin can fetch any tenant profile by its direct ID, ignoring global tenant filters
                tenantProfile = await query
                    .IgnoreQueryFilters()
                    .FirstOrDefaultAsync(tp => tp.Id == request.Id, cancellationToken);
            }
            else
            {
                // Regular user: query is already filtered by ApplicationDbContext's global query filter
                // to only include TenantProfiles where TenantProfile.TenantId == _currentUserService.TenantId.
                // So, we just need to find the one with the matching primary key (request.Id).
                tenantProfile = await query
                    .FirstOrDefaultAsync(tp => tp.Id == request.Id, cancellationToken);
            }

            if (tenantProfile == null)
            {
                return null;
            }

            return MapToDto(tenantProfile);
        }

        private TenantProfileDto MapToDto(TenantProfile tenantProfile)
        {
            PaymentMethodInfoDto? paymentMethodDto = null;
            if (!string.IsNullOrWhiteSpace(tenantProfile.PaymentMethod) && tenantProfile.PaymentMethod != "{}")
            {
                try { paymentMethodDto = JsonSerializer.Deserialize<PaymentMethodInfoDto>(tenantProfile.PaymentMethod); }
                catch (JsonException) { /* Log error or handle as needed */ }
            }

            BillingAddressInfoDto? billingAddressDto = null;
            if (!string.IsNullOrWhiteSpace(tenantProfile.BillingAddress) && tenantProfile.BillingAddress != "{}")
            {
                try { billingAddressDto = JsonSerializer.Deserialize<BillingAddressInfoDto>(tenantProfile.BillingAddress); }
                catch (JsonException) { /* Log error or handle as needed */ }
            }

            return new TenantProfileDto
            {
                Id = tenantProfile.Id,
                TenantId = tenantProfile.TenantId,
                Name = tenantProfile.Name,
                Email = tenantProfile.Email,
                Status = tenantProfile.Status,
                Phone = tenantProfile.Phone,
                Company = tenantProfile.Company,
                Subscription = tenantProfile.Subscription,
                LastLoginTime = tenantProfile.LastLoginTime,
                BillingCycle = tenantProfile.BillingCycle,
                NextBillingDate = tenantProfile.NextBillingDate,
                SubscriptionStatus = tenantProfile.SubscriptionStatus,
                PaymentMethod = paymentMethodDto,
                BillingAddress = billingAddressDto,
                CreatedAt = tenantProfile.CreationTime,
                UpdatedAt = tenantProfile.LastModificationTime
            };
        }
    }
}
