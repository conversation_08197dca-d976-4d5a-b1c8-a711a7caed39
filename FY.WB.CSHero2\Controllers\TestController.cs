using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;

        public TestController(ILogger<TestController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IActionResult Get()
        {
            _logger.LogCritical("TEST CONTROLLER GET ENDPOINT CALLED");
            return Ok(new { message = "Test controller works!" });
        }

        [HttpPost]
        public IActionResult Post([FromBody] object data)
        {
            _logger.LogCritical("TEST CONTROLLER POST ENDPOINT CALLED");
            return Ok(new { message = "Test controller post works!", data });
        }
    }
}
