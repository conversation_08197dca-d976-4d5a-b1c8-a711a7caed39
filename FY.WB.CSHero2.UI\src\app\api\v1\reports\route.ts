import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers'; // To access cookies
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/reports
 * Returns a list of reports with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Forward all original searchParams to the backend
    const queryString = searchParams.toString();

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making request to backend: ${API_BASE_URL}/api/Reports${queryString ? `?${queryString}` : ''}`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/Reports${queryString ? `?${queryString}` : ''}`, {
      method: 'GET',
      headers,
      cache: 'no-store', // Disable caching to ensure fresh data
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to fetch reports from backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const reportsData = await backendResponse.json();
    console.log(`Successfully fetched reports from backend: ${JSON.stringify(reportsData).substring(0, 100)}...`);
    return NextResponse.json(reportsData);
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/reports
 * Creates a new report
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    console.log(`Making POST request to backend: ${API_BASE_URL}/api/Reports`);
    
    const backendResponse = await fetch(`${API_BASE_URL}/api/Reports`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to create report via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const newReport = await backendResponse.json();
    console.log(`Successfully created report: ${JSON.stringify(newReport).substring(0, 100)}...`);
    return NextResponse.json(newReport, { status: backendResponse.status }); // Use backend status, likely 201
  } catch (error) {
    console.error('Error creating report:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: `Failed to create report: ${errorMessage}` },
      { status: 500 }
    );
  }
}
