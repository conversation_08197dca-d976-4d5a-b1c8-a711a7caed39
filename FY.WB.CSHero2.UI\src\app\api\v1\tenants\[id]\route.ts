import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET /api/v1/tenants/[id]
 * Returns a single tenant by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;
    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    console.log(`Fetching tenant profile for ID: ${tenantId}`);

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Call the backend API
    const backendResponse = await fetch(`${API_BASE_URL}/api/TenantProfiles/${tenantId}`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend API error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to fetch tenant from backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const tenant = await backendResponse.json();
    console.log(`Successfully fetched tenant profile: ${JSON.stringify(tenant).substring(0, 100)}...`);
    return NextResponse.json(tenant);
  } catch (error) {
    console.error('Error fetching tenant:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tenant', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/tenants/[id]
 * Updates an existing tenant
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;
    const body = await request.json();

    console.log(`Updating tenant profile for ID: ${tenantId}`);

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Call the backend API
    const backendResponse = await fetch(`${API_BASE_URL}/api/TenantProfiles/${tenantId}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(body),
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend API error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to update tenant via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    const updatedTenant = await backendResponse.json();
    console.log(`Successfully updated tenant profile: ${JSON.stringify(updatedTenant).substring(0, 100)}...`);
    return NextResponse.json(updatedTenant);
  } catch (error) {
    console.error('Error updating tenant:', error);
    return NextResponse.json(
      { error: 'Failed to update tenant', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/tenants/[id]
 * Deletes a tenant
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;

    console.log(`Deleting tenant profile for ID: ${tenantId}`);

    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Call the backend API
    const backendResponse = await fetch(`${API_BASE_URL}/api/TenantProfiles/${tenantId}`, {
      method: 'DELETE',
      headers,
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      console.error(`Backend API error: ${backendResponse.status} ${errorBody}`);
      return NextResponse.json(
        { error: `Failed to delete tenant via backend: ${backendResponse.status} ${errorBody}` },
        { status: backendResponse.status }
      );
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting tenant:', error);
    return NextResponse.json(
      { error: 'Failed to delete tenant', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
