using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Migrations
{
    /// <summary>
    /// Service for migrating existing report JSON data to the new structured format
    /// </summary>
    public class ReportDataMigrationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ReportDataMigrationService> _logger;

        public ReportDataMigrationService(
            ApplicationDbContext context,
            ILogger<ReportDataMigrationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Migrates all existing report data from JSON format to structured entities
        /// </summary>
        public async Task<int> MigrateReportDataAsync()
        {
            _logger.LogInformation("Starting report data migration...");

            // Get all reports with their current versions
            var reports = await _context.Reports
                .Include(r => r.Versions.Where(v => v.IsCurrent))
                .Where(r => !r.IsDeleted)
                .ToListAsync();

            _logger.LogInformation("Found {ReportCount} reports to migrate", reports.Count);

            int migratedCount = 0;
            int skippedCount = 0;

            foreach (var report in reports)
            {
                try
                {
                    var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
                    if (currentVersion == null)
                    {
                        _logger.LogWarning("Report {ReportId} has no current version, skipping", report.Id);
                        skippedCount++;
                        continue;
                    }

                    // Check if report already has sections (already migrated)
                    var existingSections = await _context.ReportSections
                        .Where(s => s.ReportId == report.Id)
                        .CountAsync();

                    if (existingSections > 0)
                    {
                        _logger.LogInformation("Report {ReportId} already has {SectionCount} sections, skipping", 
                            report.Id, existingSections);
                        skippedCount++;
                        continue;
                    }

                    // Migrate the report data
                    var migrated = await MigrateReportAsync(report, currentVersion);
                    if (migrated)
                    {
                        migratedCount++;
                    }
                    else
                    {
                        skippedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error migrating report {ReportId}: {Message}", report.Id, ex.Message);
                    skippedCount++;
                }
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Completed report data migration. Migrated: {MigratedCount}, Skipped: {SkippedCount}, Total: {TotalCount}",
                migratedCount, skippedCount, reports.Count);

            return migratedCount;
        }

        /// <summary>
        /// Migrates a single report's data
        /// </summary>
        private async Task<bool> MigrateReportAsync(Report report, ReportVersion version)
        {
            var jsonData = version.JsonData;
            if (string.IsNullOrEmpty(jsonData))
            {
                _logger.LogWarning("Report {ReportId} has empty JSON data, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }

            try
            {
                // Try to parse as structured format first
                var structuredData = JsonSerializer.Deserialize<ReportContentModel>(jsonData,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (structuredData?.Sections != null && structuredData.Sections.Any())
                {
                    return await MigrateStructuredDataAsync(report, structuredData);
                }

                // Try to parse as flat format
                var flatData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonData);
                if (flatData != null && flatData.Any())
                {
                    return await MigrateFlatDataAsync(report, flatData);
                }

                _logger.LogWarning("Report {ReportId} has unrecognized JSON format, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to parse JSON for report {ReportId}, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }
        }

        /// <summary>
        /// Migrates structured JSON data (with sections array)
        /// </summary>
        private async Task<bool> MigrateStructuredDataAsync(Report report, ReportContentModel reportContent)
        {
            _logger.LogDebug("Migrating structured data for report {ReportId} with {SectionCount} sections",
                report.Id, reportContent.Sections!.Count);

            int sectionOrder = 0;
            foreach (var sectionData in reportContent.Sections!)
            {
                var section = new ReportSection(
                    Guid.NewGuid(),
                    report.Id,
                    sectionData.SectionTitle ?? "Untitled Section",
                    sectionData.Type ?? "text",
                    sectionOrder++);

                _context.ReportSections.Add(section);

                // Process section content
                if (sectionData.Content != null)
                {
                    await ProcessSectionContentAsync(section, sectionData.Content);
                }
            }

            return true;
        }

        /// <summary>
        /// Migrates flat JSON data (key-value pairs)
        /// </summary>
        private async Task<bool> MigrateFlatDataAsync(Report report, Dictionary<string, object> flatData)
        {
            _logger.LogDebug("Migrating flat data for report {ReportId} with {FieldCount} fields",
                report.Id, flatData.Count);

            // Group fields by section prefix (e.g., "header.title" -> "header" section)
            var sectionGroups = GroupFieldsBySection(flatData);

            int sectionOrder = 0;
            foreach (var sectionGroup in sectionGroups)
            {
                var section = new ReportSection(
                    Guid.NewGuid(),
                    report.Id,
                    CapitalizeFirstLetter(sectionGroup.Key),
                    DetermineSectionType(sectionGroup.Value),
                    sectionOrder++);

                _context.ReportSections.Add(section);

                // Create fields for this section
                int fieldOrder = 0;
                foreach (var field in sectionGroup.Value)
                {
                    var fieldName = field.Key.Contains('.') ? field.Key.Split('.').Last() : field.Key;
                    var sectionField = new ReportSectionField(
                        Guid.NewGuid(),
                        section.Id,
                        fieldName,
                        DetermineFieldType(field.Value),
                        field.Value?.ToString() ?? string.Empty,
                        fieldOrder++);

                    _context.ReportSectionFields.Add(sectionField);
                }
            }

            return true;
        }

        /// <summary>
        /// Processes content from a structured section
        /// </summary>
        private async Task ProcessSectionContentAsync(ReportSection section, object content)
        {
            var contentDict = ExtractFieldsFromContent(content);
            
            int fieldOrder = 0;
            foreach (var field in contentDict)
            {
                var sectionField = new ReportSectionField(
                    Guid.NewGuid(),
                    section.Id,
                    field.Key,
                    DetermineFieldType(field.Value),
                    field.Value?.ToString() ?? string.Empty,
                    fieldOrder++);

                _context.ReportSectionFields.Add(sectionField);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Creates a default section for reports with no data
        /// </summary>
        private async Task CreateDefaultSectionAsync(Report report)
        {
            var section = new ReportSection(
                Guid.NewGuid(),
                report.Id,
                "Content",
                "text",
                0);

            _context.ReportSections.Add(section);

            var field = new ReportSectionField(
                Guid.NewGuid(),
                section.Id,
                "content",
                "string",
                "No content available",
                0);

            _context.ReportSectionFields.Add(field);

            await Task.CompletedTask;
        }

        /// <summary>
        /// Groups flat fields by section prefix
        /// </summary>
        private Dictionary<string, Dictionary<string, object>> GroupFieldsBySection(Dictionary<string, object> flatData)
        {
            var groups = new Dictionary<string, Dictionary<string, object>>();

            foreach (var kvp in flatData)
            {
                var sectionName = "general";
                if (kvp.Key.Contains('.'))
                {
                    sectionName = kvp.Key.Split('.')[0];
                }

                if (!groups.ContainsKey(sectionName))
                {
                    groups[sectionName] = new Dictionary<string, object>();
                }

                groups[sectionName][kvp.Key] = kvp.Value;
            }

            return groups;
        }

        /// <summary>
        /// Extracts fields from content object
        /// </summary>
        private Dictionary<string, object> ExtractFieldsFromContent(object content)
        {
            var fields = new Dictionary<string, object>();

            if (content is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Object)
                {
                    foreach (var property in jsonElement.EnumerateObject())
                    {
                        fields[property.Name] = ExtractJsonElementValue(property.Value);
                    }
                }
                else
                {
                    fields["content"] = ExtractJsonElementValue(jsonElement);
                }
            }
            else
            {
                fields["content"] = content;
            }

            return fields;
        }

        /// <summary>
        /// Extracts value from JsonElement
        /// </summary>
        private object ExtractJsonElementValue(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString() ?? string.Empty,
                JsonValueKind.Number => element.GetDecimal(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => string.Empty,
                JsonValueKind.Object => element.GetRawText(),
                JsonValueKind.Array => element.GetRawText(),
                _ => element.GetRawText()
            };
        }

        /// <summary>
        /// Determines section type based on fields
        /// </summary>
        private string DetermineSectionType(Dictionary<string, object> fields)
        {
            var fieldNames = fields.Keys.Select(k => k.ToLower()).ToList();

            if (fieldNames.Any(f => f.Contains("chart") || f.Contains("graph")))
                return "chart";
            if (fieldNames.Any(f => f.Contains("table") || f.Contains("row") || f.Contains("column")))
                return "table";
            if (fieldNames.Any(f => f.Contains("list") || f.Contains("item")))
                return "list";
            if (fieldNames.Any(f => f.Contains("timeline") || f.Contains("date") || f.Contains("time")))
                return "timeline";

            return "text";
        }

        /// <summary>
        /// Determines field type based on value
        /// </summary>
        private string DetermineFieldType(object? value)
        {
            if (value == null) return "string";

            return value switch
            {
                bool => "boolean",
                int or long or decimal or double or float => "number",
                DateTime => "date",
                string str when DateTime.TryParse(str, out _) => "date",
                string str when decimal.TryParse(str, out _) => "number",
                string str when bool.TryParse(str, out _) => "boolean",
                string str when str.StartsWith("{") || str.StartsWith("[") => "json",
                _ => "string"
            };
        }

        /// <summary>
        /// Capitalizes the first letter of a string
        /// </summary>
        private string CapitalizeFirstLetter(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            return char.ToUpper(input[0]) + input.Substring(1);
        }
    }
}
