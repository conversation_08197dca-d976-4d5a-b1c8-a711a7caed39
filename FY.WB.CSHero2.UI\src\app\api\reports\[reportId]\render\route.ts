import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { API_BASE_URL } from '@/lib/constants'; // Backend API

export async function POST(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  const { reportId } = params;

  if (!reportId) {
    return NextResponse.json({ message: 'Report ID is required' }, { status: 400 });
  }

  const authToken = request.cookies.get('authToken')?.value;
  if (!authToken) {
    return NextResponse.json({ message: 'Authentication token is missing' }, { status: 401 });
  }

  try {
    // Adjust the backend endpoint as per your actual Report Rendering Engine API
    // This assumes the backend endpoint is something like /api/ReportRendering/render/{reportId}
    // and it's a POST request to initiate rendering.
    const backendResponse = await fetch(`${API_BASE_URL}/api/ReportRendering/render/${reportId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        // Add any other headers required by your backend rendering engine
        'Content-Type': 'application/json', // If your backend expects a body, even if empty
      },
      // body: JSON.stringify({}), // If your backend expects an empty JSON body for POST
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error(`Backend error rendering report ${reportId}: ${backendResponse.status} ${errorText}`);
      return NextResponse.json(
        { message: `Failed to render report: ${errorText || backendResponse.statusText}` },
        { status: backendResponse.status }
      );
    }

    const renderedHtml = await backendResponse.text();
    // Return the HTML content directly. The client-side will handle displaying it.
    return new NextResponse(renderedHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error(`Error in BFF while rendering report ${reportId}:`, error);
    let message = 'Internal server error';
    if (error instanceof Error) {
      message = error.message;
    }
    return NextResponse.json({ message }, { status: 500 });
  }
}
