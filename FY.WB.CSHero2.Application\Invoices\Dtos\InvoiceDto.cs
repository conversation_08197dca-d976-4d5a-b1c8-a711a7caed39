using System;

namespace FY.WB.CSHero2.Application.Invoices.Dtos
{
    public class InvoiceDto
    {
        public Guid Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "Invoice", "Payment", "Refund"
        public string Plans { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty; // e.g., "paid", "overdue"
        public DateTime Date { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? TenantId { get; set; }

        // Optional: If TenantProfile information is needed, add relevant properties here
        // public string? TenantName { get; set; } 
    }
}
