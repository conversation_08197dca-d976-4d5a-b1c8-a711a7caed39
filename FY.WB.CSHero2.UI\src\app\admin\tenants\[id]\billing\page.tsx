'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Save,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tenant } from '@/types/tenant';
import Link from 'next/link';
import { getTenantById, updateTenant } from '@/lib/api';
import { getInvoicesByTenantId } from '@/lib/api-invoices';
import { Invoice } from '@/types/invoice';
import { useToast } from '@/components/providers/toast-provider';
import {
  PaymentMethodCard,
  BillingAddressCard,
  SubscriptionDetailsCard,
  InvoiceHistoryCard
} from '@/components/features/tenants/billing';

export default function TenantBillingPage() {
  const params = useParams();
  const router = useRouter();
  const tenantId = params.id as string;
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    cardType: '',
    lastFourDigits: '',
    expirationDate: '',
    securityMethod: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    billingCycle: '',
    nextBillingDate: '',
    subscriptionStatus: '',
    subscription: ''
  });

  useEffect(() => {
    const fetchTenantData = async () => {
      try {
        setLoading(true);
        const tenantData = await getTenantById(tenantId);
        setTenant(tenantData);
        
        // Initialize form data from tenant
        if (tenantData) {
          setFormData({
            cardType: tenantData.paymentMethod?.cardType || '',
            lastFourDigits: tenantData.paymentMethod?.lastFourDigits || '',
            expirationDate: tenantData.paymentMethod?.expirationDate || '',
            securityMethod: tenantData.paymentMethod?.securityMethod || '',
            street: tenantData.billingAddress?.street || '',
            city: tenantData.billingAddress?.city || '',
            state: tenantData.billingAddress?.state || '',
            zipCode: tenantData.billingAddress?.zipCode || '',
            country: tenantData.billingAddress?.country || '',
            billingCycle: tenantData.billingCycle || 'monthly',
            nextBillingDate: tenantData.nextBillingDate ? new Date(tenantData.nextBillingDate).toISOString().split('T')[0] : '',
            subscriptionStatus: tenantData.subscriptionStatus || 'active',
            subscription: tenantData.subscription || 'basic'
          });
        }
        
        setError(null);
      } catch (err) {
        setError('Failed to fetch tenant data');
        toast({
          title: 'Error',
          description: 'Failed to fetch tenant data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTenantData();
    if (tenantId) {
      // fetchBillingHistory();
    }
  }, [tenantId, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      // Prepare the data for the API
      const updatedTenant: Partial<Tenant> = {
        paymentMethod: {
          cardType: formData.cardType,
          lastFourDigits: formData.lastFourDigits,
          expirationDate: formData.expirationDate,
          securityMethod: formData.securityMethod
        },
        billingAddress: {
          street: formData.street,
          city: formData.city,
          state: formData.state,
          zipCode: formData.zipCode,
          country: formData.country
        },
        subscription: formData.subscription as 'basic' | 'professional' | 'enterprise',
        billingCycle: formData.billingCycle as 'monthly' | 'quarterly' | 'annual',
        nextBillingDate: formData.nextBillingDate ? new Date(formData.nextBillingDate).toISOString() : undefined,
        subscriptionStatus: formData.subscriptionStatus as 'active' | 'overdue' | 'canceled'
      };
      
      // Update the tenant
      await updateTenant(tenantId, updatedTenant);
      
      setSuccess(true);
      toast({
        title: 'Success',
        description: 'Billing information updated successfully',
      });
      
      // Redirect back to tenant profile after a short delay
      setTimeout(() => {
        router.push(`/admin/tenants/${tenantId}`);
      }, 1500);
      
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to update billing information',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex items-center justify-center h-64">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex flex-col items-center justify-center h-64 gap-4">
          <p className="text-xl text-red-600">{error || 'Tenant not found'}</p>
          <Button asChild variant="outline">
            <Link href="/admin/tenants">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tenants
            </Link>
          </Button>
        </div>
      </div>
    );
  }


  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Button asChild variant="outline" size="sm" className="h-9">
              <Link href={`/admin/tenants/${tenantId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Tenant Profile
              </Link>
            </Button>
          </div>

          <h1 className="text-4xl font-bold mb-2 text-[rgb(var(--primary))]">Billing Information</h1>
          <p className="text-xl text-gray-600 mb-6">
            Update payment and billing details for {tenant.name}
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Payment Method Card */}
            <PaymentMethodCard 
              formData={formData} 
              setFormData={(data) => setFormData({ ...formData, ...data })} 
            />

            {/* Billing Address Card */}
            <BillingAddressCard 
              formData={formData} 
              setFormData={(data) => setFormData({ ...formData, ...data })} 
            />
          </div>

          {/* Subscription Details Card */}
          <SubscriptionDetailsCard 
            formData={formData} 
            setFormData={(data) => setFormData({ ...formData, ...data })} 
            tenant={tenant}
          />

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/admin/tenants/${tenantId}`)}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={saving || success}
              className="text-white"
            >
              {saving ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary/30 border-r-white"></div>
                  Saving...
                </>
              ) : success ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Saved
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
