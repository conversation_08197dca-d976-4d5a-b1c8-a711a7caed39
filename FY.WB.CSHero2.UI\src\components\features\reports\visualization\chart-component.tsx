"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell
} from "recharts";
import { Settings, RefreshCw, Download } from "lucide-react";

// Define chart data interface
export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

// Define chart configuration interface
export interface ChartConfig {
  type: "bar" | "line" | "pie" | "area" | "stacked-bar" | "donut" | "gauge" | "funnel" | "scatter" | "radar" | "map";
  title: string;
  description?: string;
  data: ChartData[];
  xAxisKey?: string;
  yAxisKey?: string;
  series?: {
    key: string;
    name: string;
    color?: string;
  }[];
  colors?: string[];
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  stacked?: boolean;
  aspectRatio?: number;
  options?: any;
}

// Default color palette
const DEFAULT_COLORS = [
  "#2563eb", // blue-600
  "#16a34a", // green-600
  "#ea580c", // orange-600
  "#9333ea", // purple-600
  "#e11d48", // rose-600
  "#0891b2", // cyan-600
  "#4f46e5", // indigo-600
  "#0d9488", // teal-600
  "#b91c1c", // red-600
  "#7c3aed", // violet-600
];

interface ChartComponentProps {
  config: ChartConfig;
  height?: number | string;
  width?: number | string;
  className?: string;
  onSettingsClick?: () => void;
  onRefreshClick?: () => void;
  onDownloadClick?: () => void;
}

export function ChartComponent({
  config,
  height = 400,
  width = "100%",
  className = "",
  onSettingsClick,
  onRefreshClick,
  onDownloadClick
}: ChartComponentProps) {
  const [chartData, setChartData] = useState<ChartData[]>(config.data);
  const [isLoading, setIsLoading] = useState(false);

  // Set up colors for the chart
  const colors = config.colors || DEFAULT_COLORS;

  // Handle refresh click
  const handleRefresh = () => {
    setIsLoading(true);
    
    // Simulate data refresh
    setTimeout(() => {
      // In a real implementation, this would fetch new data from an API
      setChartData(config.data.map(item => ({
        ...item,
        value: Math.floor(Math.random() * 1000)
      })));
      setIsLoading(false);
      
      if (onRefreshClick) {
        onRefreshClick();
      }
    }, 800);
  };

  // Handle download click
  const handleDownload = () => {
    // In a real implementation, this would generate and download a chart image
    alert("Chart download functionality would be implemented here");
    
    if (onDownloadClick) {
      onDownloadClick();
    }
  };

  // Render the appropriate chart based on the type
  const renderChart = () => {
    switch (config.type) {
      case "bar":
        return (
          <ResponsiveContainer width={width} height={height}>
            <BarChart data={chartData}>
              {config.showGrid !== false && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={config.xAxisKey || "name"} />
              <YAxis />
              {config.showTooltip !== false && <Tooltip />}
              {config.showLegend !== false && <Legend />}
              {config.series ? (
                config.series.map((series, index) => (
                  <Bar
                    key={series.key}
                    dataKey={series.key}
                    name={series.name}
                    fill={series.color || colors[index % colors.length]}
                    stackId={config.stacked ? "stack" : undefined}
                  />
                ))
              ) : (
                <Bar
                  dataKey={config.yAxisKey || "value"}
                  fill={colors[0]}
                  name={config.title}
                />
              )}
            </BarChart>
          </ResponsiveContainer>
        );

      case "line":
        return (
          <ResponsiveContainer width={width} height={height}>
            <LineChart data={chartData}>
              {config.showGrid !== false && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={config.xAxisKey || "name"} />
              <YAxis />
              {config.showTooltip !== false && <Tooltip />}
              {config.showLegend !== false && <Legend />}
              {config.series ? (
                config.series.map((series, index) => (
                  <Line
                    key={series.key}
                    type="monotone"
                    dataKey={series.key}
                    name={series.name}
                    stroke={series.color || colors[index % colors.length]}
                    activeDot={{ r: 8 }}
                  />
                ))
              ) : (
                <Line
                  type="monotone"
                  dataKey={config.yAxisKey || "value"}
                  stroke={colors[0]}
                  name={config.title}
                  activeDot={{ r: 8 }}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        );

      case "area":
        return (
          <ResponsiveContainer width={width} height={height}>
            <AreaChart data={chartData}>
              {config.showGrid !== false && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={config.xAxisKey || "name"} />
              <YAxis />
              {config.showTooltip !== false && <Tooltip />}
              {config.showLegend !== false && <Legend />}
              {config.series ? (
                config.series.map((series, index) => (
                  <Area
                    key={series.key}
                    type="monotone"
                    dataKey={series.key}
                    name={series.name}
                    fill={series.color || colors[index % colors.length]}
                    stroke={series.color || colors[index % colors.length]}
                    fillOpacity={0.3}
                    stackId={config.stacked ? "stack" : undefined}
                  />
                ))
              ) : (
                <Area
                  type="monotone"
                  dataKey={config.yAxisKey || "value"}
                  fill={colors[0]}
                  stroke={colors[0]}
                  fillOpacity={0.3}
                  name={config.title}
                />
              )}
            </AreaChart>
          </ResponsiveContainer>
        );

      case "pie":
      case "donut":
        return (
          <ResponsiveContainer width={width} height={height}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={config.type === "donut" ? 100 : 80}
                innerRadius={config.type === "donut" ? 60 : 0}
                fill="#8884d8"
                dataKey={config.yAxisKey || "value"}
                nameKey={config.xAxisKey || "name"}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              {config.showTooltip !== false && <Tooltip />}
              {config.showLegend !== false && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        );

      case "gauge":
        // Simple gauge implementation using a pie chart
        const gaugeValue = chartData[0]?.value || 0;
        const gaugeTotal = 100; // Assuming percentage-based gauge
        const gaugeFill = gaugeValue / gaugeTotal;
        const gaugeEmpty = 1 - gaugeFill;
        const gaugeData = [
          { name: "Value", value: gaugeValue },
          { name: "Empty", value: gaugeTotal - gaugeValue }
        ];
        
        return (
          <div className="relative">
            <ResponsiveContainer width={width} height={height}>
              <PieChart>
                <Pie
                  data={gaugeData}
                  cx="50%"
                  cy="50%"
                  startAngle={180}
                  endAngle={0}
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={0}
                  dataKey="value"
                >
                  <Cell fill={colors[0]} />
                  <Cell fill="#e5e7eb" /> {/* gray-200 */}
                </Pie>
                {config.showTooltip !== false && <Tooltip />}
              </PieChart>
            </ResponsiveContainer>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-3xl font-bold">{gaugeValue}%</div>
                <div className="text-sm text-gray-500">{config.title}</div>
              </div>
            </div>
          </div>
        );

      case "funnel":
        // Simple funnel chart implementation
        const sortedData = [...chartData].sort((a, b) => b.value - a.value);
        const maxValue = Math.max(...sortedData.map(item => item.value));
        
        return (
          <div className="w-full" style={{ height }}>
            <div className="space-y-2">
              {sortedData.map((item, index) => {
                const width = (item.value / maxValue) * 100;
                return (
                  <div key={item.name} className="relative">
                    <div
                      className="h-12 rounded-sm"
                      style={{
                        width: `${width}%`,
                        backgroundColor: colors[index % colors.length]
                      }}
                    >
                      <div className="absolute inset-y-0 left-2 flex items-center text-white">
                        {item.name}
                      </div>
                      <div className="absolute inset-y-0 right-2 flex items-center text-white">
                        {item.value}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );

      // For other chart types, show a placeholder
      default:
        return (
          <div className="flex items-center justify-center h-full border border-dashed border-gray-300 rounded-lg bg-gray-50">
            <div className="text-center p-6">
              <p className="text-gray-500">Chart type "{config.type}" is not implemented yet.</p>
              <p className="text-sm text-gray-400 mt-1">This would be a {config.type} chart in the full implementation.</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`chart-component ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium">{config.title}</h3>
          {config.description && (
            <p className="text-sm text-gray-500 mt-1">{config.description}</p>
          )}
        </div>
        <div className="flex space-x-2">
          {onSettingsClick && (
            <button
              onClick={onSettingsClick}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              title="Chart Settings"
            >
              <Settings className="h-5 w-5" />
            </button>
          )}
          {onRefreshClick && (
            <button
              onClick={handleRefresh}
              className={`p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 ${
                isLoading ? "animate-spin text-primary" : ""
              }`}
              title="Refresh Data"
              disabled={isLoading}
            >
              <RefreshCw className="h-5 w-5" />
            </button>
          )}
          {onDownloadClick && (
            <button
              onClick={handleDownload}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              title="Download Chart"
            >
              <Download className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      <div className={`chart-container ${isLoading ? "opacity-50" : ""}`}>
        {renderChart()}
      </div>
    </div>
  );
}
