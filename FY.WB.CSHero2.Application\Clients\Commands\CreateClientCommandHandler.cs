using FY.WB.CSHero2.Application.Clients.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class CreateClientCommandHandler : IRequestHandler<CreateClientCommand, ClientDto>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateClientCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<ClientDto> Handle(CreateClientCommand request, CancellationToken cancellationToken)
        {
            // Get the tenant ID from the current user
            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("Cannot create a client without a tenant ID. The current user does not have a tenant ID.");
            }

            var tenantId = _currentUserService.TenantId.Value;

            // Verify that the tenant profile exists
            var tenantProfile = await _context.TenantProfiles.FindAsync(new object[] { tenantId }, cancellationToken);
            if (tenantProfile == null)
            {
                throw new InvalidOperationException($"Cannot create a client for tenant ID {tenantId} because the tenant profile does not exist.");
            }

            var clientEntity = new Client(
                Guid.NewGuid(), // Add the required id parameter
                request.ClientDto.Name,
                request.ClientDto.Email,
                request.ClientDto.Status,
                request.ClientDto.CompanyName,
                request.ClientDto.Phone,
                request.ClientDto.Address,
                request.ClientDto.CompanySize,
                request.ClientDto.Industry
            );

            // Set the tenant ID and tenant profile
            clientEntity.TenantId = tenantId;
            clientEntity.TenantProfile = tenantProfile;

            _context.Clients.Add(clientEntity);
            await _context.SaveChangesAsync(cancellationToken);

            return new ClientDto
            {
                Id = clientEntity.Id.ToString(),
                Name = clientEntity.Name,
                Email = clientEntity.Email,
                Status = clientEntity.Status,
                CompanyName = clientEntity.CompanyName,
                CreatedAt = clientEntity.CreationTime,
                UpdatedAt = clientEntity.LastModificationTime ?? clientEntity.CreationTime,
                Phone = clientEntity.Phone,
                Address = clientEntity.Address,
                CompanySize = clientEntity.CompanySize,
                Industry = clientEntity.Industry
            };
        }
    }
}
