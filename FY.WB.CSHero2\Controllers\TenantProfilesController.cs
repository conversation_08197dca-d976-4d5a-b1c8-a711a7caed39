using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using MediatR; // Added for IMediator
using Microsoft.Extensions.Logging; // Added for ILogger
using FY.WB.CSHero2.Application.TenantProfiles.Dtos; // Added for TenantProfile DTOs
using FY.WB.CSHero2.Application.TenantProfiles.Queries; // Added for TenantProfile Queries
using FY.WB.CSHero2.Application.TenantProfiles.Commands; // Added for TenantProfile Commands
using FY.WB.CSHero2.Application.Common.Dtos; // Added for PagedResult
using FY.WB.CSHero2.Application.Common.Exceptions; // Added for custom exceptions
using FluentValidation; // Added for ValidationException

namespace FY.WB.CSHero2.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class TenantProfilesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<TenantProfilesController> _logger;

        public TenantProfilesController(IMediator mediator, ILogger<TenantProfilesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get tenant profiles. Admins can see all (paged), regular users see their own.
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResult<TenantProfileDto>), 200)] // For admin
        [ProducesResponseType(typeof(TenantProfileDto), 200)] // For regular user (single profile)
        public async Task<IActionResult> GetTenantProfiles([FromQuery] TenantProfileQueryParametersDto parameters)
        {
            _logger.LogInformation("Attempting to get tenant profiles with parameters: Search={Search}, Status={Status}, Subscription={Subscription}, Page={Page}, Limit={Limit}",
                parameters.Search, parameters.Status, parameters.Subscription, parameters.Page, parameters.Limit);
            
            // The query handler will differentiate between admin and regular user
            var query = new GetTenantProfilesQuery(parameters);
            var result = await _mediator.Send(query); // Handler needs to return appropriate type based on user role

            // This controller action might need to be smarter or split if the return type truly varies.
            // For now, assuming handler returns PagedResult and for non-admin it's a PagedResult of 1 item.
            return Ok(result);
        }

        /// <summary>
        /// Get a specific tenant profile by its ID.
        /// Admins can get any profile. Regular users can only get their own.
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(TenantProfileDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<TenantProfileDto?>> GetTenantProfile(Guid id)
        {
            _logger.LogInformation("Attempting to get tenant profile with ID: {TenantProfileId}", id);
            try
            {
                var query = new GetTenantProfileByIdQuery(id);
                var result = await _mediator.Send(query);

                if (result == null)
                {
                    _logger.LogWarning("TenantProfile with ID {TenantProfileId} not found or not accessible.", id);
                    return NotFound(new { message = $"TenantProfile with ID {id} not found or not accessible." });
                }
                
                _logger.LogInformation("Successfully retrieved tenant profile with ID: {TenantProfileId}", id);
                return Ok(result);
            }
            catch (FormatException)
            {
                _logger.LogWarning("Invalid ID format for TenantProfile: {TenantProfileId}", id);
                return BadRequest(new { message = "Invalid ID format. Please provide a valid GUID."});
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tenant profile with ID {TenantProfileId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while retrieving the tenant profile.", details = ex.Message });
            }
        }

        /// <summary>
        /// Creates a new tenant profile.
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), 201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateTenantProfile([FromBody] CreateTenantProfileRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new tenant profile with name: {TenantProfileName}", dto.Name);
            try
            {
                var command = new CreateTenantProfileCommand(dto);
                var tenantProfileId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created tenant profile with ID: {TenantProfileId}", tenantProfileId);
                return CreatedAtAction(nameof(GetTenantProfile), new { id = tenantProfileId }, new { id = tenantProfileId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating tenant profile.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating tenant profile: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tenant profile: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the tenant profile.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing tenant profile.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateTenantProfile(Guid id, [FromBody] UpdateTenantProfileRequestDto dto)
        {
            _logger.LogInformation("Attempting to update tenant profile with ID: {TenantProfileId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateTenantProfileCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated tenant profile with ID: {TenantProfileId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating tenant profile with ID: {TenantProfileId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "TenantProfile with ID {TenantProfileId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update tenant profile with ID: {TenantProfileId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tenant profile with ID {TenantProfileId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the tenant profile.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a tenant profile by its ID.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteTenantProfile(Guid id)
        {
            _logger.LogInformation("Attempting to delete tenant profile with ID: {TenantProfileId}", id);
            try
            {
                var command = new DeleteTenantProfileCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted tenant profile with ID: {TenantProfileId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "TenantProfile with ID {TenantProfileId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete tenant profile with ID: {TenantProfileId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tenant profile with ID {TenantProfileId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the tenant profile.", details = ex.Message });
            }
        }
    }
}
