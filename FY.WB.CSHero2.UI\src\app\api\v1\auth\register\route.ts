import { NextRequest, NextResponse } from 'next/server';
import { API_BASE_URL } from '@/lib/constants';

/**
 * POST /api/v1/auth/register
 * Handles user registration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Ensure required fields are present
    if (!body.email || !body.password || !body.companyName) {
      return NextResponse.json(
        { error: 'Email, password, and company name are required' },
        { status: 400 }
      );
    }

    // Log the URL we're trying to connect to
    console.log(`Attempting to connect to backend at: ${API_BASE_URL}/api/Auth/register`);

    // Construct the backend URL safely
    const backendUrl = new URL('/api/Auth/register', API_BASE_URL);
    console.log(`Constructed URL: ${backendUrl.toString()}`);
    console.log('API_BASE_URL value:', API_BASE_URL);
    console.log('Request body:', JSON.stringify({
      email: body.email,
      password: body.password,
      companyName: body.companyName,
    }));

    // Forward the registration request to the backend API
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const backendResponse = await fetch(backendUrl.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // Remove confirmPassword field before sending to backend
        body: JSON.stringify({
          email: body.email,
          password: body.password,
          companyName: body.companyName,
          // Exclude confirmPassword
        }),
        signal: controller.signal,
        // Disable caching
        cache: 'no-store',
        // Add this to prevent Node.js internal error from TLS/SSL
        keepalive: true,
        // Explicitly specify creds to avoid cookies being sent
        credentials: 'omit'
      });

      clearTimeout(timeoutId);

      // Log the response status
      console.log(`Backend response status: ${backendResponse.status}`);

      // Get the response data
      const responseData = await backendResponse.json();
      console.log('Backend response data:', responseData);

      // If the backend returns an error
      if (!backendResponse.ok) {
        console.error('Backend registration error:', responseData);
        return NextResponse.json(
          { error: responseData.message || responseData.error || 'Registration failed on backend' },
          { status: backendResponse.status }
        );
      }

      // Return the successful response
      return NextResponse.json(responseData);
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Fetch error during registration:', fetchError);
      return NextResponse.json(
        { error: fetchError instanceof Error ? fetchError.message : 'Connection failed to backend server' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error during registration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Registration failed' },
      { status: 500 }
    );
  }
}
