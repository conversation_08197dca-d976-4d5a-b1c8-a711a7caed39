'use client';

import { CreditCard } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface PaymentMethodCardProps {
  formData: {
    cardType: string;
    lastFourDigits: string;
    expirationDate: string;
    securityMethod: string;
  };
  setFormData: (formData: any) => void;
}

export function PaymentMethodCard({ formData, setFormData }: PaymentMethodCardProps) {
  return (
    <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
      <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Payment Method</h2>
      <div className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="cardType" className="text-sm font-medium text-muted-foreground">
            Card Type
          </label>
          <Select
            value={formData.cardType}
            onValueChange={(value) => setFormData({ ...formData, cardType: value })}
          >
            <SelectTrigger id="cardType">
              <SelectValue placeholder="Select card type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Visa">Visa</SelectItem>
              <SelectItem value="Mastercard">Mastercard</SelectItem>
              <SelectItem value="American Express">American Express</SelectItem>
              <SelectItem value="Discover">Discover</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label htmlFor="lastFourDigits" className="text-sm font-medium text-muted-foreground">
            Last 4 Digits
          </label>
          <Input
            id="lastFourDigits"
            value={formData.lastFourDigits}
            onChange={(e) => setFormData({ ...formData, lastFourDigits: e.target.value })}
            placeholder="Last 4 digits of card"
            maxLength={4}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="expirationDate" className="text-sm font-medium text-muted-foreground">
            Expiration Date
          </label>
          <Input
            id="expirationDate"
            value={formData.expirationDate}
            onChange={(e) => setFormData({ ...formData, expirationDate: e.target.value })}
            placeholder="MM/YY"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="securityMethod" className="text-sm font-medium text-muted-foreground">
            Security Method
          </label>
          <Select
            value={formData.securityMethod}
            onValueChange={(value) => setFormData({ ...formData, securityMethod: value })}
          >
            <SelectTrigger id="securityMethod">
              <SelectValue placeholder="Select security method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3D Secure">3D Secure</SelectItem>
              <SelectItem value="SMS Verification">SMS Verification</SelectItem>
              <SelectItem value="PIN Verification">PIN Verification</SelectItem>
              <SelectItem value="None">None</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </Card>
  );
}
