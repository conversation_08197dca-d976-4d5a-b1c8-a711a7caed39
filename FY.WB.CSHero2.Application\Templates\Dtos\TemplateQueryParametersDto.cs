namespace FY.WB.CSHero2.Application.Templates.Dtos
{
    public class TemplateQueryParametersDto
    {
        public string? Search { get; set; } // Search by Name, Description, Category, Tags
        public string? Category { get; set; } // Filter by a specific category
        public string? SortBy { get; set; } // e.g., "Name", "Category", "CreatedAt"
        public string? SortOrder { get; set; } // "asc" or "desc"
        public int? Page { get; set; } = 1;
        public int? Limit { get; set; } = 10;
    }
}
