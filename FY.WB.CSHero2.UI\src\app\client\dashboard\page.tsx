"use client";

import { <PERSON><PERSON><PERSON>ard } from "@/components/features/dashboard/metric-card";
import { ReportTable } from "@/components/features/reports/report-table";
import { Card } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { getClients, getReports } from "@/lib/api";
import { Report } from "@/types/report";
import { Client } from "@/types/client";
import { PagedResult } from "@/types/pagedResult"; // Import PagedResult
import { motion } from 'framer-motion';
import { useEffect } from "react";

export default function ClientDashboardPage() {
  // Fetch clients and reports data
  const { data: clientsPagedResult, isLoading: clientsLoading, error: clientsError } = useQuery<PagedResult<Client>>({
    queryKey: ['clients'],
    queryFn: async () => {
      try {
        console.log('Fetching clients data...');
        const response = await getClients(); // getClients now returns { data: PagedResult, headers }
        console.log('Clients data fetched successfully:', response.data);
        return response.data; // This should be PagedResult<ClientDto>
      } catch (error) {
        console.error('Error fetching clients:', error);
        throw error; // Re-throw to let React Query handle it
      }
    },
    retry: 1, // Retry once if the request fails
  });

  const { data: reportsQueryResult, isLoading: reportsLoading, error: reportsError } = useQuery({ // Changed data variable name and removed explicit PagedResult type
    queryKey: ['reports'],
    queryFn: async () => {
      try {
        console.log('Fetching reports data...');
        const response = await getReports(); // getReports now returns { items, totalCount, totalPages, currentPage, headers }
        console.log('Reports data fetched successfully:', response);
        return response; // Return the entire response object
      } catch (error) {
        console.error('Error fetching reports:', error);
        throw error; // Re-throw to let React Query handle it
      }
    },
    retry: 1, // Retry once if the request fails
  });

  // Log errors to console for debugging
  useEffect(() => {
    if (clientsError) {
      console.error('Clients query error:', clientsError);
    }
    if (reportsError) {
      console.error('Reports query error:', reportsError);
    }
  }, [clientsError, reportsError]);

  const clientsData = clientsPagedResult?.Items || []; // Assuming PagedResult from backend has 'Items'
  const reportsData = reportsQueryResult?.items || []; // reportsQueryResult now holds the object from getReports, access 'items'

  // Calculate metrics
  const metrics = {
    totalClients: clientsPagedResult?.TotalCount || 0, // Assuming PagedResult from backend has 'TotalCount'
    totalReports: reportsQueryResult?.totalCount || 0, // Access 'totalCount' from reportsQueryResult
    activeReports: reportsData.filter((r: Report) => r.status === 'In Progress').length || 0,
    pendingReports: reportsData.filter((r: Report) => r.status === 'Under Review').length || 0
  };

  // Mock trend data (you might want to replace this with real data)
  const mockTrend = [95, 96, 97, 98, 99, 100];

  const isLoading = clientsLoading || reportsLoading;
  const hasError = clientsError || reportsError;

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-16"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">Dashboard</h1>
            <p className="text-xl text-gray-600">
              Overview of your clients and reports
            </p>
          </div>
        </div>

        {/* Error Display */}
        {hasError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
            <p className="font-bold">Error loading data</p>
            <p>{clientsError ? `Clients: ${clientsError instanceof Error ? clientsError.message : 'Unknown error'}` : ''}</p>
            <p>{reportsError ? `Reports: ${reportsError instanceof Error ? reportsError.message : 'Unknown error'}` : ''}</p>
            <p className="mt-2">Please try refreshing the page or contact support if the issue persists.</p>
          </div>
        )}

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {isLoading ? (
            // Loading skeleton for metrics
            [...Array(4)].map((_, i) => (
              <Card key={i} className="p-6 space-y-4">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                <div className="h-8 bg-gray-200 rounded animate-pulse w-3/4"></div>
                <div className="h-16 bg-gray-200 rounded animate-pulse"></div>
              </Card>
            ))
          ) : (
            <>
              <MetricCard
                title="Total Clients"
                value={metrics.totalClients}
                previousValue={metrics.totalClients - 1}
                trend={mockTrend}
                format={(v) => v.toString()}
              />
              <MetricCard
                title="Total Reports"
                value={metrics.totalReports}
                previousValue={metrics.totalReports - 2}
                trend={mockTrend}
                format={(v) => v.toString()}
              />
              <MetricCard
                title="Active Reports"
                value={metrics.activeReports}
                previousValue={metrics.activeReports}
                trend={mockTrend}
                format={(v) => v.toString()}
              />
              <MetricCard
                title="Pending Reports"
                value={metrics.pendingReports}
                previousValue={metrics.pendingReports - 1}
                trend={mockTrend}
                format={(v) => v.toString()}
              />
            </>
          )}
        </div>
      </motion.div>

      {/* Recent Reports */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-semibold text-[rgb(var(--primary))]">Recent Reports</h2>
        </div>
        <Card className="overflow-hidden">
          <ReportTable searchQuery="" />
        </Card>
      </div>
    </div>
  );
}
