using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using MediatR; // Added for IMediator
using Microsoft.Extensions.Logging; // Added for ILogger
using FY.WB.CSHero2.Application.Templates.Dtos; // Added for Template DTOs
using FY.WB.CSHero2.Application.Templates.Queries; // Added for Template Queries
using FY.WB.CSHero2.Application.Templates.Commands; // Added for Template Commands
using FY.WB.CSHero2.Application.Common.Dtos; // Added for PagedResult
using FY.WB.CSHero2.Application.Common.Exceptions; // Added for custom exceptions
using FluentValidation; // Added for ValidationException

namespace FY.WB.CSHero2.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ApiExplorerSettings(GroupName = "v1")]
    public class TemplatesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<TemplatesController> _logger;

        public TemplatesController(IMediator mediator, ILogger<TemplatesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all templates with optional filtering, sorting, and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResult<TemplateDto>), 200)]
        public async Task<ActionResult<PagedResult<TemplateDto>>> GetTemplates(
            [FromQuery] string? search,
            [FromQuery] string? category,
            [FromQuery] string? sortBy,
            [FromQuery] string? sortOrder,
            [FromQuery] int? page,
            [FromQuery] int? limit)
        {
            _logger.LogInformation("Attempting to get templates with parameters: Search={Search}, Category={Category}, SortBy={SortBy}, SortOrder={SortOrder}, Page={Page}, Limit={Limit}",
                search, category, sortBy, sortOrder, page, limit);

            var parameters = new TemplateQueryParametersDto
            {
                Search = search,
                Category = category,
                SortBy = sortBy,
                SortOrder = sortOrder,
                Page = page,
                Limit = limit
            };

            var query = new GetTemplatesQuery(parameters);
            var result = await _mediator.Send(query);
            
            _logger.LogInformation("Successfully retrieved {Count} templates for page {Page}", result.Items.Count, result.Page);
            return Ok(result);
        }

        /// <summary>
        /// Get a template by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(TemplateDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<TemplateDto?>> GetTemplate(Guid id) // Changed id to Guid
        {
            _logger.LogInformation("Attempting to get template with ID: {TemplateId}", id);
            try
            {
                var query = new GetTemplateByIdQuery(id);
                var result = await _mediator.Send(query);

                if (result == null)
                {
                    _logger.LogWarning("Template with ID {TemplateId} not found", id);
                    return NotFound(new { message = $"Template with ID {id} not found." });
                }
                
                _logger.LogInformation("Successfully retrieved template with ID: {TemplateId}", id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                // Log detailed error. In a real app, you might not expose ex.Message directly.
                _logger.LogError(ex, "Error retrieving template with ID {TemplateId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while retrieving the template.", details = ex.Message });
            }
        }

        /// <summary>
        /// Creates a new template.
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), 201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateTemplate([FromBody] CreateTemplateRequestDto dto)
        {
            _logger.LogInformation("Attempting to create a new template with name: {TemplateName}", dto.Name);
            try
            {
                var command = new CreateTemplateCommand(dto);
                var templateId = await _mediator.Send(command);
                _logger.LogInformation("Successfully created template with ID: {TemplateId}", templateId);
                return CreatedAtAction(nameof(GetTemplate), new { id = templateId }, new { id = templateId });
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while creating template.");
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (InvalidOperationException ex) // Catch specific exception from handler
            {
                _logger.LogWarning(ex, "Invalid operation while creating template: {ErrorMessage}", ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating template: {ErrorMessage}", ex.Message);
                return StatusCode(500, new { message = "An error occurred while creating the template.", details = ex.Message });
            }
        }

        /// <summary>
        /// Updates an existing template.
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> UpdateTemplate(Guid id, [FromBody] UpdateTemplateRequestDto dto)
        {
            _logger.LogInformation("Attempting to update template with ID: {TemplateId}", id);
            if (id != dto.Id)
            {
                _logger.LogWarning("Mismatched ID in route and body for template update. Route ID: {RouteId}, Body ID: {BodyId}", id, dto.Id);
                return BadRequest(new { message = "Mismatched ID in route and body." });
            }

            try
            {
                var command = new UpdateTemplateCommand(dto);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully updated template with ID: {TemplateId}", id);
                return NoContent();
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning(ex, "Validation failed while updating template with ID: {TemplateId}", id);
                return BadRequest(new { message = "Validation failed.", errors = ex.Errors });
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Template with ID {TemplateId} not found for update.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to update template with ID: {TemplateId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating template with ID {TemplateId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while updating the template.", details = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a template by its ID.
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DeleteTemplate(Guid id)
        {
            _logger.LogInformation("Attempting to delete template with ID: {TemplateId}", id);
            try
            {
                var command = new DeleteTemplateCommand(id);
                await _mediator.Send(command);
                _logger.LogInformation("Successfully deleted template with ID: {TemplateId}", id);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                _logger.LogWarning(ex, "Template with ID {TemplateId} not found for deletion.", id);
                return NotFound(new { message = ex.Message });
            }
            catch (ForbiddenAccessException ex)
            {
                _logger.LogWarning(ex, "User not authorized to delete template with ID: {TemplateId}", id);
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting template with ID {TemplateId}: {ErrorMessage}", id, ex.Message);
                return StatusCode(500, new { message = "An error occurred while deleting the template.", details = ex.Message });
            }
        }
    }
}
