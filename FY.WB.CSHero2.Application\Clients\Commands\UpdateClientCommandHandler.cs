using FY.WB.CSHero2.Application.Clients.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;
using System; // Required for Guid

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class UpdateClientCommandHandler : IRequestHandler<UpdateClientCommand, ClientDto?>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public UpdateClientCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<ClientDto?> Handle(UpdateClientCommand request, CancellationToken cancellationToken)
        {
            if (!Guid.TryParse(request.Id, out var clientId))
            {
                return null; // Or throw an ArgumentException
            }

            var query = _context.Clients.AsQueryable();

            // Filter by tenant ID if available
            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(c => c.TenantId == _currentUserService.TenantId);
            }

            var clientEntity = await query.FirstOrDefaultAsync(c => c.Id == clientId, cancellationToken);

            if (clientEntity is null)
            {
                return null; // Client not found
            }

            // Update properties from DTO if they are provided
            clientEntity.Name = request.ClientDto.Name ?? clientEntity.Name;
            clientEntity.Email = request.ClientDto.Email ?? clientEntity.Email;
            clientEntity.Status = request.ClientDto.Status ?? clientEntity.Status;
            clientEntity.CompanyName = request.ClientDto.CompanyName ?? clientEntity.CompanyName;
            clientEntity.Phone = request.ClientDto.Phone ?? clientEntity.Phone;
            clientEntity.Address = request.ClientDto.Address ?? clientEntity.Address;
            clientEntity.CompanySize = request.ClientDto.CompanySize ?? clientEntity.CompanySize;
            clientEntity.Industry = request.ClientDto.Industry ?? clientEntity.Industry;
            // RecordModification is no longer needed - LastModificationTime will be updated automatically by DbContext

            await _context.SaveChangesAsync(cancellationToken);

            return new ClientDto
            {
                Id = clientEntity.Id.ToString(),
                Name = clientEntity.Name,
                Email = clientEntity.Email,
                Status = clientEntity.Status,
                CompanyName = clientEntity.CompanyName,
                CreatedAt = clientEntity.CreationTime,
                UpdatedAt = clientEntity.LastModificationTime ?? clientEntity.CreationTime,
                Phone = clientEntity.Phone,
                Address = clientEntity.Address,
                CompanySize = clientEntity.CompanySize,
                Industry = clientEntity.Industry
            };
        }
    }
}
