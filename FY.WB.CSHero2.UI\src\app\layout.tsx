import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toast } from "@/components/ui/toast";
import { ToastProvider } from "@/components/providers/toast-provider";
import { QueryProvider } from "@/components/providers/query-provider";
import { AuthProvider } from "@/components/providers/auth-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CS-HERO | Review. Align. Achieve ",
  description: "A simple tool for creating reports like Business Reviews and Success Plans. It helps teams stay aligned, informed, and empowered, making it useful for any role in any industry.",
  viewport: "width=device-width, initial-scale=1.0"
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} min-h-screen antialiased bg-background text-foreground`}>
        <QueryProvider>
          <AuthProvider>
            <ToastProvider>
              {children}
              <Toast />
            </ToastProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
