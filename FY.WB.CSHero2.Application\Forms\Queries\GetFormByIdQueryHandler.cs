using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Forms.Dtos;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Forms.Queries
{
    public class GetFormByIdQueryHandler : IRequestHandler<GetFormByIdQuery, FormDto>
    {
        private readonly IApplicationDbContext _context;

        public GetFormByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<FormDto> Handle(GetFormByIdQuery request, CancellationToken cancellationToken)
        {
            var form = await _context.Forms
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

            if (form == null)
            {
                throw new Exception($"Form with ID {request.Id} not found");
            }

            return new FormDto
            {
                Id = form.Id,
                Title = form.Title,
                CustomerName = form.CustomerName,
                Email = form.Email,
                Category = form.Category,
                Priority = form.Priority,
                Description = form.Description,
                Date = form.Date,
                TenantId = form.TenantId,
                CreationTime = form.CreationTime,
                LastModificationTime = form.LastModificationTime
            };
        }
    }
}
