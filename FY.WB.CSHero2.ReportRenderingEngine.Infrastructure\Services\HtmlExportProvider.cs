using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// HTML export provider for creating static HTML reports
    /// </summary>
    public class HtmlExportProvider : IExportProvider
    {
        private readonly ILogger<HtmlExportProvider> _logger;

        public string Format => "HTML";

        public HtmlExportProvider(ILogger<HtmlExportProvider> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Exports report data to HTML format
        /// </summary>
        public async Task<byte[]> ExportAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Generating HTML export for report {ReportId}", data.Report.Id);

                var htmlContent = await GenerateHtmlReportAsync(data, options, cancellationToken);
                var htmlBytes = Encoding.UTF8.GetBytes(htmlContent);

                _logger.LogInformation("Successfully generated HTML export ({Size} bytes) for report {ReportId}", 
                    htmlBytes.Length, data.Report.Id);

                return htmlBytes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating HTML export for report {ReportId}", data.Report.Id);
                throw;
            }
        }

        /// <summary>
        /// Gets HTML export capabilities
        /// </summary>
        public async Task<ExportCapabilities> GetCapabilitiesAsync()
        {
            return await Task.FromResult(new ExportCapabilities
            {
                Format = Format,
                SupportsImages = true,
                SupportsCharts = true,
                SupportsInteractivity = true,
                SupportsPasswordProtection = false,
                SupportsWatermarks = false,
                SupportsCustomStyling = true,
                SupportedPaperSizes = new List<string>(), // Not applicable for HTML
                SupportedOrientations = new List<string>(), // Not applicable for HTML
                SupportedQualityLevels = new List<string> { "High", "Medium", "Low" },
                MaxFileSize = 50 * 1024 * 1024, // 50MB
                AdditionalCapabilities = new Dictionary<string, object>
                {
                    { "SupportsResponsiveDesign", true },
                    { "SupportsJavaScript", true },
                    { "SupportsCss", true },
                    { "SupportsEmbeddedAssets", true }
                }
            });
        }

        /// <summary>
        /// Generates a preview of the HTML export
        /// </summary>
        public async Task<ExportPreview> PreviewAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating HTML preview for report {ReportId}", data.Report.Id);

                var estimatedFileSize = EstimateFileSize(data, options);
                var includedSections = GetIncludedSections(data, options);
                var warnings = GenerateWarnings(data, options);

                var preview = new ExportPreview
                {
                    Format = Format,
                    EstimatedPageCount = 1, // HTML is a single page
                    EstimatedFileSize = estimatedFileSize,
                    EstimatedProcessingTime = TimeSpan.FromSeconds(5), // Fast generation
                    IncludedSections = includedSections,
                    Warnings = warnings,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        { "SupportsInteractivity", true },
                        { "ResponsiveDesign", true },
                        { "IncludeNavigation", true },
                        { "EmbeddedAssets", options.IncludeImages }
                    }
                };

                _logger.LogDebug("Generated HTML preview for report {ReportId}: {FileSize} bytes estimated", 
                    data.Report.Id, estimatedFileSize);

                return await Task.FromResult(preview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating HTML preview for report {ReportId}", data.Report.Id);
                throw;
            }
        }

        #region Private Helper Methods

        private async Task<string> GenerateHtmlReportAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken)
        {
            var htmlBuilder = new StringBuilder();

            // Generate HTML document structure
            htmlBuilder.AppendLine("<!DOCTYPE html>");
            htmlBuilder.AppendLine("<html lang=\"en\">");
            htmlBuilder.AppendLine("<head>");
            
            // Add metadata
            htmlBuilder.AppendLine(GenerateHtmlHead(data, options));
            
            htmlBuilder.AppendLine("</head>");
            htmlBuilder.AppendLine("<body>");

            // Add navigation if multiple sections
            if (data.ComponentData.Components.Count > 1)
            {
                htmlBuilder.AppendLine(GenerateNavigation(data, options));
            }

            // Add main content
            htmlBuilder.AppendLine("<main class=\"report-content\">");
            
            // Add report header
            htmlBuilder.AppendLine(GenerateReportHeader(data, options));
            
            // Add report sections
            htmlBuilder.AppendLine(await GenerateReportSectionsAsync(data, options, cancellationToken));
            
            htmlBuilder.AppendLine("</main>");

            // Add footer
            htmlBuilder.AppendLine(GenerateReportFooter(data, options));

            // Add JavaScript for interactivity
            htmlBuilder.AppendLine(GenerateJavaScript(data, options));

            htmlBuilder.AppendLine("</body>");
            htmlBuilder.AppendLine("</html>");

            return htmlBuilder.ToString();
        }

        private string GenerateHtmlHead(ExportData data, ExportOptions options)
        {
            var headBuilder = new StringBuilder();
            
            // Basic meta tags
            headBuilder.AppendLine("<meta charset=\"UTF-8\">");
            headBuilder.AppendLine("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            headBuilder.AppendLine($"<title>{data.Report.Name}</title>");
            
            // Add metadata
            foreach (var metadata in options.Metadata)
            {
                headBuilder.AppendLine($"<meta name=\"{metadata.Key}\" content=\"{metadata.Value}\">");
            }
            
            // Add CSS styles
            headBuilder.AppendLine(GenerateCssStyles(options));
            
            return headBuilder.ToString();
        }

        private string GenerateCssStyles(ExportOptions options)
        {
            var cssBuilder = new StringBuilder();
            cssBuilder.AppendLine("<style>");
            
            // Base styles
            cssBuilder.AppendLine(@"
                * { box-sizing: border-box; }
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }
                .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
                .report-content { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1, h2, h3, h4, h5, h6 { color: #2c3e50; margin-top: 0; }
                .section { margin-bottom: 40px; padding: 20px 0; border-bottom: 1px solid #eee; }
                .section:last-child { border-bottom: none; }
                .navigation { 
                    background: #34495e; 
                    color: white; 
                    padding: 15px 0; 
                    position: sticky; 
                    top: 0; 
                    z-index: 100;
                }
                .nav-list { 
                    list-style: none; 
                    margin: 0; 
                    padding: 0; 
                    display: flex; 
                    flex-wrap: wrap; 
                    gap: 20px;
                }
                .nav-item a { 
                    color: white; 
                    text-decoration: none; 
                    padding: 8px 16px; 
                    border-radius: 4px; 
                    transition: background-color 0.3s;
                }
                .nav-item a:hover { background-color: rgba(255,255,255,0.1); }
                .report-header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #3498db; }
                .report-title { font-size: 2.5em; margin-bottom: 10px; color: #2c3e50; }
                .report-meta { color: #7f8c8d; font-size: 1.1em; }
                .component-container { margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #7f8c8d; font-size: 0.9em; }
                
                /* Responsive design */
                @media (max-width: 768px) {
                    .container { padding: 10px; }
                    .report-content { padding: 20px; }
                    .nav-list { flex-direction: column; }
                    .report-title { font-size: 2em; }
                }
                
                /* Print styles */
                @media print {
                    body { background: white; }
                    .navigation { display: none; }
                    .report-content { box-shadow: none; }
                    .section { page-break-inside: avoid; }
                }
            ");

            // Custom styling from options
            foreach (var style in options.CustomStyling)
            {
                cssBuilder.AppendLine($".{style.Key} {{ {style.Value} }}");
            }

            // Quality-based styles
            if (options.Quality == "High")
            {
                cssBuilder.AppendLine("img { max-width: 100%; height: auto; }");
            }
            else if (options.Quality == "Low")
            {
                cssBuilder.AppendLine("img { max-width: 100%; height: auto; filter: contrast(0.8); }");
            }

            cssBuilder.AppendLine("</style>");
            return cssBuilder.ToString();
        }

        private string GenerateNavigation(ExportData data, ExportOptions options)
        {
            var navBuilder = new StringBuilder();
            navBuilder.AppendLine("<nav class=\"navigation\">");
            navBuilder.AppendLine("<div class=\"container\">");
            navBuilder.AppendLine("<ul class=\"nav-list\">");
            
            foreach (var component in data.ComponentData.Components)
            {
                if (ShouldIncludeSection(component.SectionId, options))
                {
                    var sectionId = $"section-{component.SectionId}";
                    navBuilder.AppendLine($"<li class=\"nav-item\"><a href=\"#{sectionId}\">{component.SectionName}</a></li>");
                }
            }
            
            navBuilder.AppendLine("</ul>");
            navBuilder.AppendLine("</div>");
            navBuilder.AppendLine("</nav>");
            
            return navBuilder.ToString();
        }

        private string GenerateReportHeader(ExportData data, ExportOptions options)
        {
            var headerBuilder = new StringBuilder();
            headerBuilder.AppendLine("<div class=\"report-header\">");
            headerBuilder.AppendLine($"<h1 class=\"report-title\">{data.Report.Name}</h1>");
            headerBuilder.AppendLine("<div class=\"report-meta\">");
            
            if (data.Report.Client != null)
            {
                headerBuilder.AppendLine($"<p>Client: <strong>{data.Report.Client.Name}</strong></p>");
            }
            
            headerBuilder.AppendLine($"<p>Generated: <strong>{DateTime.Now:yyyy-MM-dd HH:mm:ss}</strong></p>");
            headerBuilder.AppendLine($"<p>Version: <strong>{data.Version.VersionNumber}</strong></p>");
            
            if (!string.IsNullOrEmpty(data.Version.Description))
            {
                headerBuilder.AppendLine($"<p>Description: <em>{data.Version.Description}</em></p>");
            }
            
            headerBuilder.AppendLine("</div>");
            headerBuilder.AppendLine("</div>");
            
            return headerBuilder.ToString();
        }

        private async Task<string> GenerateReportSectionsAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken)
        {
            var sectionsBuilder = new StringBuilder();
            
            foreach (var component in data.ComponentData.Components)
            {
                if (ShouldIncludeSection(component.SectionId, options))
                {
                    var sectionId = $"section-{component.SectionId}";
                    sectionsBuilder.AppendLine($"<section id=\"{sectionId}\" class=\"section\">");
                    sectionsBuilder.AppendLine($"<h2>{component.SectionName}</h2>");
                    
                    // Convert React component to HTML
                    var componentHtml = await ConvertComponentToHtmlAsync(component, cancellationToken);
                    sectionsBuilder.AppendLine("<div class=\"component-container\">");
                    sectionsBuilder.AppendLine(componentHtml);
                    sectionsBuilder.AppendLine("</div>");
                    
                    sectionsBuilder.AppendLine("</section>");
                }
            }
            
            return sectionsBuilder.ToString();
        }

        private async Task<string> ConvertComponentToHtmlAsync(SectionComponent component, CancellationToken cancellationToken)
        {
            // In a full implementation, this would render React components to HTML
            // This could use server-side rendering with Node.js or similar
            
            var htmlBuilder = new StringBuilder();
            htmlBuilder.AppendLine($"<!-- Component: {component.SectionId} -->");
            htmlBuilder.AppendLine("<div class=\"react-component\">");
            
            // Placeholder for actual component rendering
            htmlBuilder.AppendLine("<div class=\"component-placeholder\">");
            htmlBuilder.AppendLine($"<p><strong>Component:</strong> {component.SectionName}</p>");
            htmlBuilder.AppendLine("<p>This would contain the rendered React component content.</p>");
            
            if (!string.IsNullOrEmpty(component.ComponentCode))
            {
                // Show a preview of the component code
                var codePreview = component.ComponentCode.Length > 200 
                    ? component.ComponentCode.Substring(0, 200) + "..."
                    : component.ComponentCode;
                
                htmlBuilder.AppendLine("<details>");
                htmlBuilder.AppendLine("<summary>View Component Code</summary>");
                htmlBuilder.AppendLine($"<pre><code>{System.Web.HttpUtility.HtmlEncode(codePreview)}</code></pre>");
                htmlBuilder.AppendLine("</details>");
            }
            
            htmlBuilder.AppendLine("</div>");
            htmlBuilder.AppendLine("</div>");
            
            return await Task.FromResult(htmlBuilder.ToString());
        }

        private string GenerateReportFooter(ExportData data, ExportOptions options)
        {
            var footerBuilder = new StringBuilder();
            footerBuilder.AppendLine("<footer class=\"footer\">");
            footerBuilder.AppendLine("<div class=\"container\">");
            footerBuilder.AppendLine($"<p>Report generated on {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            footerBuilder.AppendLine($"<p>Report ID: {data.Report.Id}</p>");
            footerBuilder.AppendLine($"<p>Version: {data.Version.VersionNumber}</p>");
            footerBuilder.AppendLine("</div>");
            footerBuilder.AppendLine("</footer>");
            
            return footerBuilder.ToString();
        }

        private string GenerateJavaScript(ExportData data, ExportOptions options)
        {
            var jsBuilder = new StringBuilder();
            jsBuilder.AppendLine("<script>");
            
            // Smooth scrolling for navigation
            jsBuilder.AppendLine(@"
                document.addEventListener('DOMContentLoaded', function() {
                    // Smooth scrolling for navigation links
                    document.querySelectorAll('a[href^=""#""]').forEach(anchor => {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const target = document.querySelector(this.getAttribute('href'));
                            if (target) {
                                target.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        });
                    });
                    
                    // Add print functionality
                    const printButton = document.createElement('button');
                    printButton.textContent = 'Print Report';
                    printButton.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 1000; padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;';
                    printButton.onclick = () => window.print();
                    document.body.appendChild(printButton);
                    
                    // Hide print button when printing
                    window.addEventListener('beforeprint', () => printButton.style.display = 'none');
                    window.addEventListener('afterprint', () => printButton.style.display = 'block');
                });
            ");
            
            jsBuilder.AppendLine("</script>");
            return jsBuilder.ToString();
        }

        private bool ShouldIncludeSection(string sectionId, ExportOptions options)
        {
            if (options.ExcludeSections.Contains(sectionId))
            {
                return false;
            }

            if (options.IncludeSections.Any())
            {
                return options.IncludeSections.Contains(sectionId);
            }

            return true;
        }

        private long EstimateFileSize(ExportData data, ExportOptions options)
        {
            var baseSize = 50 * 1024; // 50KB base HTML
            var sizePerComponent = 10 * 1024; // 10KB per component
            var includedComponents = data.ComponentData.Components.Count(c => ShouldIncludeSection(c.SectionId, options));
            
            var estimatedSize = baseSize + (includedComponents * sizePerComponent);
            
            // Add size for images if included
            if (options.IncludeImages)
            {
                var imageCount = EstimateImageCount(data);
                estimatedSize += imageCount * 100 * 1024; // 100KB per image
            }
            
            return estimatedSize;
        }

        private int EstimateImageCount(ExportData data)
        {
            return data.ComponentData.Components.Count / 3; // Assume 1 image per 3 components
        }

        private List<string> GetIncludedSections(ExportData data, ExportOptions options)
        {
            return data.ComponentData.Components
                .Where(c => ShouldIncludeSection(c.SectionId, options))
                .Select(c => c.SectionName)
                .ToList();
        }

        private List<string> GenerateWarnings(ExportData data, ExportOptions options)
        {
            var warnings = new List<string>();

            if (!data.ComponentData.Components.Any())
            {
                warnings.Add("No components found in report. Export may be empty.");
            }

            if (options.IncludeImages && EstimateImageCount(data) > 20)
            {
                warnings.Add("Large number of images detected. File size may be significant.");
            }

            return warnings;
        }

        #endregion
    }
}
