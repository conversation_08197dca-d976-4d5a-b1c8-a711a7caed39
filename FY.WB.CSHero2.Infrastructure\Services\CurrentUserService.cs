using FY.WB.CSHero2.Application.Common.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging; // Added for logging
using System;
using System.Linq; // Added for logging claims
using System.Security.Claims;

namespace FY.WB.CSHero2.Infrastructure.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<CurrentUserService> _logger; // Added for logging

        public CurrentUserService(IHttpContextAccessor httpContextAccessor, ILogger<CurrentUserService> logger) // Added logger
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger; // Added logger
        }

        public string? UserId
        {
            get
            {
                var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
                _logger.LogInformation("[CurrentUserService] UserId accessed. Value: {UserId}", userId ?? "null");
                return userId;
            }
        }

        public Guid? TenantId
        {
            get
            {
                var user = _httpContextAccessor.HttpContext?.User;
                if (user == null)
                {
                    _logger.LogWarning("[CurrentUserService] HttpContext.User is null when accessing TenantId.");
                    return null;
                }

                var allClaims = user.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
                _logger.LogInformation("[CurrentUserService] All claims for TenantId access: {AllClaims}", string.Join("; ", allClaims));

                var tenantIdClaim = user.FindFirstValue("tenant_id");
                _logger.LogInformation("[CurrentUserService] TenantId claim ('tenant_id') accessed. Raw value: {TenantIdClaim}", tenantIdClaim ?? "null");
                if (Guid.TryParse(tenantIdClaim, out Guid tenantId))
                {
                    _logger.LogInformation("[CurrentUserService] TenantId parsed successfully: {TenantId}", tenantId);
                    return tenantId;
                }
                _logger.LogWarning("[CurrentUserService] Failed to parse TenantId claim value: {TenantIdClaim}", tenantIdClaim ?? "null");
                return null;
            }
        }

        public bool IsAuthenticated
        {
            get
            {
                var isAuthenticated = _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
                _logger.LogInformation("[CurrentUserService] IsAuthenticated accessed. Value: {IsAuthenticated}", isAuthenticated);
                return isAuthenticated;
            }
        }

        public bool IsAdmin
        {
            get
            {
                var user = _httpContextAccessor.HttpContext?.User;
                if (user == null)
                {
                    _logger.LogWarning("[CurrentUserService] HttpContext.User is null when accessing IsAdmin.");
                    return false;
                }
                
                var isAdminClaim = user.FindFirstValue("IsAdmin");
                _logger.LogInformation("[CurrentUserService] IsAdmin claim ('IsAdmin') accessed. Raw value: {IsAdminClaim}", isAdminClaim ?? "null");
                var isAdminResult = isAdminClaim != null && bool.TryParse(isAdminClaim, out bool isAdminValue) && isAdminValue;
                _logger.LogInformation("[CurrentUserService] IsAdmin evaluated to: {IsAdminResult}", isAdminResult);
                return isAdminResult;
            }
        }
    }
}
