using System.Collections.Generic;
using System.Xml.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models
{
    /// <summary>
    /// Main request object that will be serialized to XML for the LLM
    /// </summary>
    [XmlRoot("RenderRequest")]
    public class RenderRequest
    {
        /// <summary>
        /// Style information for the document
        /// </summary>
        public ReportStyle Style { get; set; } = new ReportStyle();

        /// <summary>
        /// Sections containing data to be rendered
        /// </summary>
        [XmlArray("Sections")]
        [XmlArrayItem("Section")]
        public List<RenderSection> Sections { get; set; } = new List<RenderSection>();

        /// <summary>
        /// Existing HTML content to be preserved, wrapped in CDATA
        /// </summary>
        public CDataWrapper ExistingHtml { get; set; } = new CDataWrapper(string.Empty);
        
        /// <summary>
        /// User instruction for guiding the rendering
        /// </summary>
        public string UserInstruction { get; set; } = string.Empty;
    }
}
