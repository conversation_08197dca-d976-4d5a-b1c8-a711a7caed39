namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities
{
    /// <summary>
    /// Represents a field in the document template metadata
    /// </summary>
    public class ReportField
    {
        /// <summary>
        /// Unique identifier for the field
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the field
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the field
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The data type of the field (e.g., text, date, number, boolean)
        /// </summary>
        public string Type { get; set; } = "text";
    }
}
