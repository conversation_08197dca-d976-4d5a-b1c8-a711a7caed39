[{"id": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "sectionId": "11111111-1111-1111-1111-111111111111", "name": "heading", "type": "string", "content": "Executive Summary", "order": 0, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb", "sectionId": "11111111-1111-1111-1111-111111111111", "name": "content", "type": "string", "content": "This report provides a comprehensive analysis of the company's financial performance for Q4 2023. Key highlights include revenue growth of 15% and improved operational efficiency.", "order": 1, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "cccccccc-cccc-cccc-cccc-cccccccccccc", "sectionId": "22222222-2222-2222-2222-222222222222", "name": "chartType", "type": "string", "content": "bar", "order": 0, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "dddddddd-dddd-dddd-dddd-dddddddddddd", "sectionId": "22222222-2222-2222-2222-222222222222", "name": "data", "type": "json", "content": "{\"labels\": [\"Q1\", \"Q2\", \"Q3\", \"Q4\"], \"values\": [1200000, 1350000, 1400000, 1550000]}", "order": 1, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee", "sectionId": "33333333-3333-3333-3333-333333333333", "name": "headers", "type": "json", "content": "[\"Metric\", \"Q3 2023\", \"Q4 2023\", \"Change\"]", "order": 0, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "ffffffff-ffff-ffff-ffff-ffffffffffff", "sectionId": "33333333-3333-3333-3333-333333333333", "name": "rows", "type": "json", "content": "[[\"Revenue\", \"$1.4M\", \"$1.55M\", \"+10.7%\"], [\"Profit Margin\", \"18.5%\", \"21.2%\", \"+2.7%\"], [\"Customer Count\", \"1,250\", \"1,380\", \"+10.4%\"]]", "order": 1, "creationTime": "2024-01-15T10:00:00Z", "lastModificationTime": "2024-01-15T10:00:00Z"}, {"id": "10101010-1010-1010-1010-101010101010", "sectionId": "44444444-4444-4444-4444-444444444444", "name": "heading", "type": "string", "content": "Project Overview", "order": 0, "creationTime": "2024-01-16T09:30:00Z", "lastModificationTime": "2024-01-16T09:30:00Z"}, {"id": "20202020-2020-2020-2020-202020202020", "sectionId": "44444444-4444-4444-4444-444444444444", "name": "description", "type": "string", "content": "This project aims to modernize the company's digital infrastructure and improve customer experience through enhanced online services.", "order": 1, "creationTime": "2024-01-16T09:30:00Z", "lastModificationTime": "2024-01-16T09:30:00Z"}, {"id": "30303030-3030-3030-3030-303030303030", "sectionId": "55555555-5555-5555-5555-555555555555", "name": "milestones", "type": "json", "content": "[{\"date\": \"2024-01-01\", \"title\": \"Project Kickoff\", \"status\": \"completed\"}, {\"date\": \"2024-03-15\", \"title\": \"Phase 1 Completion\", \"status\": \"in-progress\"}, {\"date\": \"2024-06-30\", \"title\": \"Final Delivery\", \"status\": \"planned\"}]", "order": 0, "creationTime": "2024-01-16T09:30:00Z", "lastModificationTime": "2024-01-16T09:30:00Z"}]