using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Identity;
using System.Threading.Tasks;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Infrastructure.Persistence.Seeders;

namespace FY.WB.CSHero2.StartupConfiguration
{
    // Non-static class for logging
    public class DbMigrationLogger {}
    
    public static class DatabaseMigrationConfiguration
    {
        public static async Task MigrateAndSeedDatabase(this IApplicationBuilder app)
        {
            using (var scope = app.ApplicationServices.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<DbMigrationLogger>>();
                
                // Migrate database
                try
                {
                    var context = services.GetRequiredService<ApplicationDbContext>();
                    if (context.Database.IsSqlServer())
                    {
                        context.Database.Migrate();
                        logger.LogInformation("Database migration completed successfully");
                    }
                    else
                    {
                        logger.LogInformation("Database is not SQL Server, skipping migrations");
                    }
                }
                catch (System.Exception ex)
                {
                    logger.LogError(ex, "An error occurred while migrating the database.");
                }
                
                // Seed database
                try 
                {
                    await DataSeeder.SeedAsync(services);
                    logger.LogInformation("Database seeding completed successfully");
                }
                catch (System.Exception ex)
                {
                    logger.LogError(ex, "An error occurred while seeding the database.");
                }
            }
        }
    }
}
