using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Forms.Commands
{
    public class CreateFormCommandHandler : IRequestHandler<CreateFormCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public CreateFormCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<Guid> Handle(CreateFormCommand request, CancellationToken cancellationToken)
        {
            var entity = new Form
            {
                Title = request.Dto.Title,
                CustomerName = request.Dto.CustomerName,
                Email = request.Dto.Email,
                Category = request.Dto.Category,
                Priority = request.Dto.Priority,
                Description = request.Dto.Description,
                Date = request.Dto.Date,
                // TenantId will be set by the DbContext interceptor or ApplyMultiTenancyAndAuditInfo
            };
            
            if (!_currentUserService.TenantId.HasValue)
            {
                // This should ideally not happen for a tenant-specific entity
                throw new InvalidOperationException("TenantId is required to create a Form.");
            }
            entity.TenantId = _currentUserService.TenantId.Value;


            _context.Forms.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);

            return entity.Id;
        }
    }
}
