import { NextRequest, NextResponse } from 'next/server';
import { getReports, createReport } from '@/lib/api'; // Corrected import path

/**
 * GET /api/reports
 * Returns a list of reports with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const limit = searchParams.get('_limit');
    const page = searchParams.get('_page');
    const sortBy = searchParams.get('_sort') || undefined;
    const order = searchParams.get('_order') as 'asc' | 'desc' | undefined; // This will be mapped to sortOrder
    const search = searchParams.get('q') || undefined; // For 'search' parameter
    const status = searchParams.get('status') || undefined;
    const category = searchParams.get('category') || undefined;
    const clientId = searchParams.get('clientId') || undefined;
    
    // Collect any other potential query parameters dynamically, though getReports has a fixed set
    // For now, we'll stick to the known parameters of getReports.
    // If dynamic query parameters are needed, getReports signature would need to change.

    const reports = await getReports({
      search,
      status,
      category,
      clientId,
      limit: limit ? parseInt(limit) : undefined,
      page: page ? parseInt(page) : undefined,
      sortBy,
      sortOrder: order // Map 'order' to 'sortOrder'
    });
    
    return NextResponse.json(reports);
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports
 * Creates a new report
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newReport = await createReport(body);
    
    return NextResponse.json(newReport, { status: 201 });
  } catch (error) {
    console.error('Error creating report:', error);
    return NextResponse.json(
      { error: 'Failed to create report' },
      { status: 500 }
    );
  }
}
