import { NextResponse } from 'next/server';
import { getTodayMetrics } from '@/lib/data/metrics';

/**
 * GET /api/today/metrics
 * Returns today's metrics data
 */
export async function GET() {
  try {
    const metrics = await getTodayMetrics();
    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching today\'s metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}
