using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Forms.Dtos;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Forms.Queries
{
    public class GetFormsQueryHandler : IRequestHandler<GetFormsQuery, List<FormDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetFormsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<FormDto>> Handle(GetFormsQuery request, CancellationToken cancellationToken)
        {
            var parameters = request.Parameters;
            
            // Start with a query that will respect multi-tenancy (handled by DbContext global filters)
            var query = _context.Forms.AsNoTracking();

            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(parameters.Search))
            {
                var searchTerm = parameters.Search.ToLower();
                query = query.Where(f => 
                    f.Title.ToLower().Contains(searchTerm) ||
                    f.CustomerName.ToLower().Contains(searchTerm) ||
                    f.Email.ToLower().Contains(searchTerm) ||
                    f.Description.ToLower().Contains(searchTerm));
            }

            // Apply category filter if provided
            if (!string.IsNullOrWhiteSpace(parameters.Category))
            {
                query = query.Where(f => f.Category.ToLower() == parameters.Category.ToLower());
            }

            // Apply priority filter if provided
            if (!string.IsNullOrWhiteSpace(parameters.Priority))
            {
                query = query.Where(f => f.Priority.ToLower() == parameters.Priority.ToLower());
            }

            // Apply tenant filter if provided (and user has permission to see cross-tenant data)
            if (parameters.TenantId.HasValue)
            {
                query = query.Where(f => f.TenantId == parameters.TenantId);
            }

            // Apply sorting
            query = ApplySorting(query, parameters.SortBy, parameters.SortOrder);

            // Apply pagination
            var pageSize = parameters.Limit ?? 10;
            var pageNumber = parameters.Page ?? 1;
            var skip = (pageNumber - 1) * pageSize;

            // Execute the query and map to DTOs
            var forms = await query
                .Skip(skip)
                .Take(pageSize)
                .Select(f => new FormDto
                {
                    Id = f.Id,
                    Title = f.Title,
                    CustomerName = f.CustomerName,
                    Email = f.Email,
                    Category = f.Category,
                    Priority = f.Priority,
                    Description = f.Description,
                    Date = f.Date,
                    TenantId = f.TenantId,
                    CreationTime = f.CreationTime,
                    LastModificationTime = f.LastModificationTime
                })
                .ToListAsync(cancellationToken);

            return forms;
        }

        private static IQueryable<Domain.Entities.Form> ApplySorting(
            IQueryable<Domain.Entities.Form> query, 
            string? sortBy, 
            string? sortOrder)
        {
            var isDescending = sortOrder?.ToLower() == "desc";

            return sortBy?.ToLower() switch
            {
                "title" => isDescending 
                    ? query.OrderByDescending(f => f.Title) 
                    : query.OrderBy(f => f.Title),
                "customername" => isDescending 
                    ? query.OrderByDescending(f => f.CustomerName) 
                    : query.OrderBy(f => f.CustomerName),
                "email" => isDescending 
                    ? query.OrderByDescending(f => f.Email) 
                    : query.OrderBy(f => f.Email),
                "category" => isDescending 
                    ? query.OrderByDescending(f => f.Category) 
                    : query.OrderBy(f => f.Category),
                "priority" => isDescending 
                    ? query.OrderByDescending(f => f.Priority) 
                    : query.OrderBy(f => f.Priority),
                "date" => isDescending 
                    ? query.OrderByDescending(f => f.Date) 
                    : query.OrderBy(f => f.Date),
                _ => isDescending 
                    ? query.OrderByDescending(f => f.CreationTime) 
                    : query.OrderBy(f => f.CreationTime)
            };
        }
    }
}
