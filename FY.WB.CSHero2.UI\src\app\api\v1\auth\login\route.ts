import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { API_BASE_URL } from '@/lib/constants';

/**
 * POST /api/v1/auth/login
 * Handles user login by proxying to the backend API and setting an HttpOnly cookie
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Log the URL we're trying to connect to
    console.log(`Attempting to connect to backend at: ${API_BASE_URL}/api/Auth/login`);

    // Construct the backend URL safely
    const backendUrl = new URL('/api/Auth/login', API_BASE_URL);
    console.log(`Constructed URL: ${backendUrl.toString()}`);

    // Call the backend API with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(backendUrl.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    const data = await response.json();

    // If the backend returns an error
    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || 'Authentication failed' },
        { status: response.status }
      );
    }

    // Extract token and user information
    const { token, expiration, userId, email: userEmail, isAdmin } = data;

    // Create a response with user data (excluding the token)
    const clientResponse = NextResponse.json({
      userId,
      email: userEmail,
      isAdmin, // Include isAdmin in the response
      authenticated: true
    });

    // Set the token as an HttpOnly cookie
    clientResponse.cookies.set('authToken', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // Only use HTTPS in production
      sameSite: 'lax',
      expires: new Date(expiration),
      path: '/',
    });

    // Add tenant ID to the cookie if available
    if (data.tenantId) {
      clientResponse.cookies.set('tenantId', data.tenantId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        expires: new Date(expiration),
        path: '/',
      });
    }

    // Set isAdmin cookie - this one is not httpOnly so it can be read by client-side JavaScript
    clientResponse.cookies.set('isAdmin', isAdmin.toString(), {
      httpOnly: false, // Allow JavaScript to read this cookie
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: new Date(expiration),
      path: '/',
    });

    return clientResponse;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
