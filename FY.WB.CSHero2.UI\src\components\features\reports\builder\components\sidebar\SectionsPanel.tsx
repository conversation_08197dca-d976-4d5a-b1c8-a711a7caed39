"use client";

import { useState } from "react";
import { GripVertical, Check, Pencil, ChevronDown, ChevronUp, Wand2 } from "lucide-react";
import { Section } from "../../types";
import { Reorder } from "framer-motion";

interface SectionsPanelProps {
  sections: Section[];
  onAddSection: () => void;
  onSectionClick?: (id: string, title: string) => void;
  onUpdateSection?: (section: Section) => void;
}

export function SectionsPanel({ 
  sections, 
  onAddSection, 
  onSectionClick,
  onUpdateSection
}: SectionsPanelProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [expandedSectionId, setExpandedSectionId] = useState<string | null>(null);
  const [descriptionValue, setDescriptionValue] = useState<string>('');
  
  const handleEditClick = (section: Section, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingId(section.id);
    setEditValue(section.title);
  };

  const handleSave = () => {
    if (editingId && sections && onUpdateSection) {
      const section = sections.find(s => s.id === editingId);
      if (section) {
        const updatedSection = { 
          ...section, 
          title: editValue 
        };
        onUpdateSection(updatedSection);
      }
      setEditingId(null);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setEditingId(null);
    }
  };

  const toggleDescription = (section: Section, e: React.MouseEvent) => {
    e.stopPropagation();
    if (expandedSectionId === section.id) {
      setExpandedSectionId(null);
    } else {
      setExpandedSectionId(section.id);
      setDescriptionValue(section.description || '');
    }
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescriptionValue(e.target.value);
  };

  const saveDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (expandedSectionId && sections && onUpdateSection) {
      const section = sections.find(s => s.id === expandedSectionId);
      if (section) {
        const updatedSection = {
          ...section,
          description: descriptionValue
        };
        onUpdateSection(updatedSection);
      }
    }
    setExpandedSectionId(null);
  };

  const cancelDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedSectionId(null);
  };

  const enhanceDescription = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Here we'd typically call an AI enhancement function
    // For now, just add some placeholder text if empty
    if (!descriptionValue) {
      setDescriptionValue("This section will provide key insights about the report data.");
    }
  };

  return (
    <div className="space-y-4 h-full overflow-hidden">
      <div className="border rounded-lg p-4 overflow-y-auto max-h-[calc(100vh-200px)]">
        <h3 className="font-medium mb-2">Report Sections</h3>
        <p className="text-sm text-gray-600 mb-4">Double-click a title to edit it or drag to reorder sections.</p>

        <div className="space-y-3">
          <Reorder.Group axis="y" values={sections} onReorder={() => {}} className="space-y-3">
            {sections.map((section) => (
              <Reorder.Item
                key={section.id}
                value={section}
                className="flex items-center justify-between p-3 bg-gray-50 rounded border cursor-move"
                whileDrag={{
                  scale: 1.02,
                  boxShadow: "0 5px 10px rgba(0, 0, 0, 0.1)",
                  backgroundColor: "#f9fafb"
                }}
                onClick={() => onSectionClick && onSectionClick(section.id, section.title)}
              >
                <div className="flex flex-col w-full">
                  <div className="flex items-center w-full">
                    <GripVertical className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" />
                    {editingId === section.id ? (
                      <div className="flex items-center flex-1" onClick={(e) => e.stopPropagation()}>
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          onKeyDown={handleKeyDown}
                          onBlur={handleSave}
                          className="flex-1 p-1 border rounded mr-2"
                          autoFocus
                        />
                        <button
                          className="p-1 rounded hover:bg-gray-200"
                          onClick={handleSave}
                        >
                          <Check className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between flex-1">
                        <span className="font-medium">
                          {section.title}
                        </span>
                        <div className="flex items-center space-x-1">
                          <button 
                            className="p-1 rounded hover:bg-gray-200 text-gray-500"
                            onClick={(e) => handleEditClick(section, e)}
                          >
                            <Pencil className="h-4 w-4" />
                          </button>
                          <button 
                            className="p-1 rounded hover:bg-gray-200 text-gray-500"
                            onClick={(e) => toggleDescription(section, e)}
                          >
                            {expandedSectionId === section.id ? 
                              <ChevronUp className="h-4 w-4" /> : 
                              <ChevronDown className="h-4 w-4" />
                            }
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {expandedSectionId === section.id && (
                    <div 
                      className="mt-2 p-2 bg-white rounded border" 
                      onClick={(e) => e.stopPropagation()}
                    >
                      <label className="block text-sm text-gray-600 mb-1">
                        Description for AI
                      </label>
                      <textarea 
                        className="w-full p-2 border rounded text-sm mb-2"
                        rows={3}
                        value={descriptionValue}
                        onChange={handleDescriptionChange}
                        placeholder="Describe this section for the AI that will generate the report..."
                      />
                      <div className="flex justify-end space-x-2">
                        <button
                          className="px-2 py-1 text-xs border rounded hover:bg-gray-50"
                          onClick={cancelDescription}
                        >
                          Cancel
                        </button>
                        <button
                          className="px-2 py-1 text-xs border rounded hover:bg-gray-50 flex items-center"
                          onClick={enhanceDescription}
                        >
                          <Wand2 className="h-3 w-3 mr-1" />
                          Enhance
                        </button>
                        <button
                          className="px-2 py-1 text-xs bg-primary text-white rounded"
                          onClick={saveDescription}
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </Reorder.Item>
            ))}
          </Reorder.Group>

          <button 
            className="w-full mt-2 py-2 bg-primary text-white rounded-md primary/70"
            onClick={onAddSection}
          >
            Add New Section
          </button>
        </div>
      </div>
    </div>
  );
}
