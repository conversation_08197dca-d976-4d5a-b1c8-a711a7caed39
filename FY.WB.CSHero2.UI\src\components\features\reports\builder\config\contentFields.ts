import { FieldConfig, SectionConfig, StyleSectionConfig } from './styleFieldConfig';

// Body Text Style Fields
const bodyTextStyleFields: FieldConfig[] = [
  {
    name: "paragraphIndentation",
    label: "Paragraph Indentation",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "first-line", label: "First line (0.25\"-0.5\")" },
      { value: "hanging", label: "Hanging" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "spacingBetweenParagraphs",
    label: "Spacing Between Paragraphs",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "4pt", label: "4pt" },
      { value: "8pt", label: "8pt" },
      { value: "12pt", label: "12pt" },
      { value: "full-line", label: "Full line" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "textJustification",
    label: "Text Justification",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "justified", label: "Justified" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "hyphenation",
    label: "Hyphenation",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "automatic", label: "Automatic" },
      { value: "custom", label: "Custom rules" }
    ]
  },
  {
    name: "dropCaps",
    label: "Drop Caps",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "first-paragraph", label: "First paragraph" },
      { value: "custom", label: "Custom rule" }
    ]
  }
];

// Lists Fields
const listsFields: FieldConfig[] = [
  {
    name: "bulletStyleLevel1",
    label: "Bullet Style - Level 1",
    type: "select",
    defaultValue: "filled-circle",
    options: [
      { value: "filled-circle", label: "Filled circle" },
      { value: "dash", label: "Dash" },
      { value: "square", label: "Square" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "bulletStyleLevel2",
    label: "Bullet Style - Level 2",
    type: "select",
    defaultValue: "open-circle",
    options: [
      { value: "open-circle", label: "Open circle" },
      { value: "dash", label: "Dash" },
      { value: "square", label: "Square" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "numberedListStyleLevel1",
    label: "Numbered List Style - Level 1",
    type: "select",
    defaultValue: "1-2-3",
    options: [
      { value: "1-2-3", label: "1., 2., 3." },
      { value: "a-b-c", label: "a., b., c." },
      { value: "i-ii-iii", label: "i., ii., iii." },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "numberedListStyleLevel2",
    label: "Numbered List Style - Level 2",
    type: "select",
    defaultValue: "a-b-c",
    options: [
      { value: "1-2-3", label: "1., 2., 3." },
      { value: "a-b-c", label: "a., b., c." },
      { value: "i-ii-iii", label: "i., ii., iii." },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "indentation",
    label: "Indentation",
    type: "select",
    defaultValue: "bullet-aligned",
    options: [
      { value: "bullet-aligned", label: "Bullet aligned" },
      { value: "hanging", label: "Hanging indent" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "spacingBetweenItems",
    label: "Spacing Between Items",
    type: "select",
    defaultValue: "4pt",
    options: [
      { value: "0pt", label: "None" },
      { value: "4pt", label: "4pt" },
      { value: "8pt", label: "8pt" },
      { value: "12pt", label: "12pt" }
    ]
  },
  {
    name: "spacingBeforeAfterLists",
    label: "Spacing Before/After Lists",
    type: "select",
    defaultValue: "8pt",
    options: [
      { value: "0pt", label: "None" },
      { value: "8pt", label: "8pt" },
      { value: "12pt", label: "12pt" },
      { value: "16pt", label: "16pt" }
    ]
  }
];

// Block Quotes Fields
const blockQuotesFields: FieldConfig[] = [
  {
    name: "indentation",
    label: "Indentation",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left" },
      { value: "right", label: "Right" },
      { value: "both", label: "Both sides" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "fontStyle",
    label: "Font Style",
    type: "select",
    defaultValue: "same-as-body",
    options: [
      { value: "same-as-body", label: "Same as body" },
      { value: "italicized", label: "Italicized" },
      { value: "smaller", label: "Smaller size" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "bordersBackground",
    label: "Borders/Background",
    type: "select",
    defaultValue: "left-border",
    options: [
      { value: "none", label: "None" },
      { value: "left-border", label: "Left border" },
      { value: "background", label: "Background shading" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "attributionStyle",
    label: "Attribution Style",
    type: "select",
    defaultValue: "right-aligned",
    options: [
      { value: "none", label: "None" },
      { value: "right-aligned", label: "Right-aligned" },
      { value: "below-with-dash", label: "Below with dash" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Tables Fields
const tablesFields: FieldConfig[] = [
  {
    name: "borderStyle",
    label: "Border Style",
    type: "select",
    defaultValue: "all",
    options: [
      { value: "none", label: "None" },
      { value: "all", label: "All borders" },
      { value: "outside", label: "Outside only" },
      { value: "custom", label: "Custom rule" }
    ]
  },
  {
    name: "headerRowStyle",
    label: "Header Row Style",
    type: "select",
    defaultValue: "both",
    options: [
      { value: "bold", label: "Bold" },
      { value: "background", label: "Background color" },
      { value: "both", label: "Both" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "rowStriping",
    label: "Row Striping",
    type: "select",
    defaultValue: "alternating",
    options: [
      { value: "none", label: "None" },
      { value: "alternating", label: "Alternating colors" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "cellPadding",
    label: "Cell Padding",
    type: "select",
    defaultValue: "moderate",
    options: [
      { value: "minimal", label: "Minimal (2-4pt)" },
      { value: "moderate", label: "Moderate (4-8pt)" },
      { value: "spacious", label: "Spacious (8-12pt)" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "textAlignment",
    label: "Text Alignment",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left" },
      { value: "centered", label: "Centered" },
      { value: "context-dependent", label: "Context-dependent" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "captionPosition",
    label: "Caption Position",
    type: "select",
    defaultValue: "above",
    options: [
      { value: "above", label: "Above" },
      { value: "below", label: "Below" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// References Fields
const referencesFields: FieldConfig[] = [
  {
    name: "styleGuide",
    label: "Style Guide",
    type: "select",
    defaultValue: "apa",
    options: [
      { value: "apa", label: "APA" },
      { value: "mla", label: "MLA" },
      { value: "chicago", label: "Chicago" },
      { value: "ieee", label: "IEEE" },
      { value: "harvard", label: "Harvard" },
      { value: "vancouver", label: "Vancouver" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "bibliographyLayout",
    label: "Bibliography Layout",
    type: "select",
    defaultValue: "hanging-indent",
    options: [
      { value: "hanging-indent", label: "Hanging indent" },
      { value: "block", label: "Block style" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "citationFormat",
    label: "Citation Format",
    type: "select",
    defaultValue: "in-text",
    options: [
      { value: "in-text", label: "In-text" },
      { value: "footnotes", label: "Footnotes" },
      { value: "endnotes", label: "Endnotes" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "footnotePosition",
    label: "Footnote Placement",
    type: "select",
    defaultValue: "bottom-of-page",
    options: [
      { value: "bottom-of-page", label: "Bottom of page" },
      { value: "end-of-section", label: "End of section" },
      { value: "end-of-document", label: "End of document" }
    ]
  },
  {
    name: "numberingStyle",
    label: "Numbering Style",
    type: "select",
    defaultValue: "continuous",
    options: [
      { value: "continuous", label: "Continuous" },
      { value: "restart", label: "Restart each page/section" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Define sections
const sections: SectionConfig[] = [
  {
    id: "bodyTextStyle",
    title: "Body Text Style",
    fields: bodyTextStyleFields,
    defaultExpanded: true
  },
  {
    id: "lists",
    title: "Lists and Bullet Points",
    fields: listsFields
  },
  {
    id: "blockQuotes",
    title: "Block Quotes",
    fields: blockQuotesFields
  },
  {
    id: "tables",
    title: "Tables",
    fields: tablesFields
  },
  {
    id: "references",
    title: "Reference & Citation",
    fields: referencesFields
  }
];

// Export the complete configuration
export const contentFormattingConfig: StyleSectionConfig = {
  sections
};
