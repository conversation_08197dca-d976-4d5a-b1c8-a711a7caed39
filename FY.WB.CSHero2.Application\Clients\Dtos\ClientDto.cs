namespace FY.WB.CSHero2.Application.Clients.Dtos
{
    public class ClientDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // e.g., "active", "inactive"
        public string CompanyName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? CompanySize { get; set; } // e.g., "1-10", "100-500"
        public string? Industry { get; set; }
    }
}
