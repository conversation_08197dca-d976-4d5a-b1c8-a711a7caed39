/**
 * Utility functions for working with form data
 */

/**
 * Safely get a nested value from an object
 * @param obj The object to get the value from
 * @param path The path to the value, e.g. "colorPalette.primary"
 * @param defaultValue The default value to return if the path doesn't exist
 */
export function getNestedValue(obj: any, path: string, defaultValue: any = undefined): any {
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === undefined || result === null || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result === undefined ? defaultValue : result;
}

/**
 * Set a nested value in an object
 * @param obj The object to set the value in
 * @param path The path to the value, e.g. "colorPalette.primary"
 * @param value The value to set
 */
export function setNestedValue(obj: any, path: string, value: any): any {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  let current = obj;
  
  // Create the nested structure if it doesn't exist
  for (const key of keys) {
    if (current[key] === undefined || current[key] === null) {
      current[key] = {};
    }
    current = current[key];
  }
  
  // Set the value
  current[lastKey] = value;
  
  return obj;
}

/**
 * Get all values for a section from a nested object
 * @param obj The object to get the values from
 * @param sectionId The section ID
 */
export function getSectionValues(obj: any, sectionId: string): Record<string, any> {
  return obj[sectionId] || {};
}

/**
 * Update a nested value in a section
 * @param obj The object to update
 * @param sectionId The section ID
 * @param fieldName The field name
 * @param value The new value
 */
export function updateSectionField(
  obj: any, 
  sectionId: string, 
  fieldName: string, 
  value: any
): any {
  const section = obj[sectionId] || {};
  
  return {
    ...obj,
    [sectionId]: {
      ...section,
      [fieldName]: value
    }
  };
}
