# User-specific files
*.DS_Store
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# ASP.NET Core specific
Properties/launchSettings.json
**/appsettings.Development.json
**/secrets.json

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Rider
.idea/
*.iml

# Visual Studio
.vs/

# Next.js / Node.js
node_modules/
.next/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
*.env.local
*.env.*.local
.pnp/
.pnp.js
.pnp.cjs
coverage/
build/
dist/

# Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.VisualState.xml
TestResult.xml
*.DotSettings
*.sqlite
*.sqlite-journal
