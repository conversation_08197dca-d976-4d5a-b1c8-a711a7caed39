using MediatR;
using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Uploads.Dtos;

namespace FY.WB.CSHero2.Application.Uploads.Queries
{
    public class GetUploadsQuery : IRequest<PagedResult<UploadDto>>
    {
        public UploadQueryParametersDto Parameters { get; }

        public GetUploadsQuery(UploadQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
