using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Interface for LLM client implementations supporting both HTML and React component generation
    /// </summary>
    public interface ILlmClient
    {
        /// <summary>
        /// Generates HTML template content based on the provided prompt
        /// </summary>
        /// <param name="prompt">The XML prompt containing instructions and data</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The generated HTML template</returns>
        Task<string> GenerateHtmlTemplateAsync(string prompt, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates React component code based on the provided prompt and context
        /// </summary>
        /// <param name="prompt">The prompt containing instructions for component generation</param>
        /// <param name="context">Additional context data for component generation</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The generated React component code</returns>
        Task<string> GenerateReactComponentAsync(string prompt, ComponentGenerationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates React component code and provides feedback
        /// </summary>
        /// <param name="componentCode">The React component code to validate</param>
        /// <param name="framework">The target framework (NextJS, React, etc.)</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Validation result with errors, warnings, and suggestions</returns>
        Task<ComponentValidationResult> ValidateComponentAsync(string componentCode, string framework = "NextJS", CancellationToken cancellationToken = default);

        /// <summary>
        /// Optimizes React component code for performance, accessibility, or other criteria
        /// </summary>
        /// <param name="componentCode">The React component code to optimize</param>
        /// <param name="optimizationType">The type of optimization (performance, accessibility, bundle-size, etc.)</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The optimized component code</returns>
        Task<string> OptimizeComponentAsync(string componentCode, string optimizationType = "performance", CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves metrics about the LLM client usage
        /// </summary>
        /// <returns>LLM usage metrics</returns>
        Task<LlmMetrics> GetMetricsAsync();
    }
}
