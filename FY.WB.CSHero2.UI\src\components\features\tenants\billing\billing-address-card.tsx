'use client';

import { Building } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

interface BillingAddressCardProps {
  formData: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  setFormData: (formData: any) => void;
}

export function BillingAddressCard({ formData, setFormData }: BillingAddressCardProps) {
  return (
    <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
      <h2 className="text-xl font-semibold mb-4 text-[rgb(var(--primary))]">Billing Address</h2>
      <div className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="street" className="text-sm font-medium text-muted-foreground">
            Street Address
          </label>
          <Input
            id="street"
            value={formData.street}
            onChange={(e) => setFormData({ ...formData, street: e.target.value })}
            placeholder="Street address"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="city" className="text-sm font-medium text-muted-foreground">
            City
          </label>
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => setFormData({ ...formData, city: e.target.value })}
            placeholder="City"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="state" className="text-sm font-medium text-muted-foreground">
              State/Province
            </label>
            <Input
              id="state"
              value={formData.state}
              onChange={(e) => setFormData({ ...formData, state: e.target.value })}
              placeholder="State/Province"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="zipCode" className="text-sm font-medium text-muted-foreground">
              ZIP/Postal Code
            </label>
            <Input
              id="zipCode"
              value={formData.zipCode}
              onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
              placeholder="ZIP/Postal Code"
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="country" className="text-sm font-medium text-muted-foreground">
            Country
          </label>
          <Input
            id="country"
            value={formData.country}
            onChange={(e) => setFormData({ ...formData, country: e.target.value })}
            placeholder="Country"
          />
        </div>
      </div>
    </Card>
  );
}
