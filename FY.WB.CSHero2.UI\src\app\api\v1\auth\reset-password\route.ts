import { NextRequest, NextResponse } from 'next/server';
import { sendPasswordResetEmail } from '@/lib/data/users';

/**
 * POST /api/v1/auth/reset-password
 * Sends a password reset email to the specified email address
 */
export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Send password reset email
    const success = await sendPasswordResetEmail(email);
    
    if (!success) {
      return NextResponse.json(
        { error: 'User with this email not found' },
        { status: 404 }
      );
    }

    // Return success response
    return NextResponse.json(
      { 
        success: true, 
        message: 'Password reset email sent successfully' 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return NextResponse.json(
      { error: 'Failed to send password reset email' },
      { status: 500 }
    );
  }
}
