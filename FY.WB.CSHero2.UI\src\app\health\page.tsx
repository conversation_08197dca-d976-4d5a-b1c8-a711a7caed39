"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";

interface HealthCheckResult {
  timestamp: string;
  backendStatus: Array<{
    port: number;
    status: string;
    data?: any;
    statusCode?: number;
    error?: string;
  }>;
}

export default function HealthPage() {
  const [healthData, setHealthData] = useState<HealthCheckResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkHealth = async () => {
      try {
        setLoading(true);
        const response = await fetch("/api/v1/health");
        if (!response.ok) {
          throw new Error(`Health check failed with status: ${response.status}`);
        }
        const data = await response.json();
        setHealthData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error occurred");
        console.error("Health check error:", err);
      } finally {
        setLoading(false);
      }
    };

    checkHealth();
  }, []);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="w-full max-w-2xl p-6">
        <h1 className="mb-6 text-2xl font-bold">Backend API Health Check</h1>
        
        {loading && <p className="text-gray-500">Checking backend API status...</p>}
        
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-4 text-red-700">
            <p className="font-medium">Error:</p>
            <p>{error}</p>
          </div>
        )}
        
        {healthData && (
          <div>
            <p className="mb-2 text-sm text-gray-500">
              Last checked: {new Date(healthData.timestamp).toLocaleString()}
            </p>
            
            <div className="mt-4 space-y-4">
              <h2 className="text-xl font-semibold">Backend Status:</h2>
              
              {healthData.backendStatus.map((result) => (
                <div 
                  key={result.port}
                  className={`rounded-md p-4 ${
                    result.status === 'available' 
                      ? 'bg-green-50 text-green-700' 
                      : 'bg-red-50 text-red-700'
                  }`}
                >
                  <p className="font-medium">Port {result.port}: {result.status}</p>
                  
                  {result.status === 'available' && result.data && (
                    <pre className="mt-2 overflow-auto rounded bg-black/5 p-2 text-sm">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                  
                  {result.status === 'error' && (
                    <p>Status code: {result.statusCode}</p>
                  )}
                  
                  {result.status === 'unavailable' && result.error && (
                    <p>{result.error}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}