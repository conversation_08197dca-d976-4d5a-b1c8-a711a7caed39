using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Templates.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Templates.Queries
{
    public class GetTemplateByIdQueryHandler : IRequestHandler<GetTemplateByIdQuery, TemplateDto?>
    {
        private readonly IApplicationDbContext _context;

        public GetTemplateByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<TemplateDto?> Handle(GetTemplateByIdQuery request, CancellationToken cancellationToken)
        {
            var template = await _context.Templates
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if (template == null)
            {
                return null;
            }

            // Deserialize JSON properties
            var tags = JsonSerializer.Deserialize<List<string>>(template.Tags ?? "[]") ?? new List<string>();
            
            var sectionsDto = new List<TemplateSectionDto>();
            if (!string.IsNullOrWhiteSpace(template.Sections) && template.Sections != "[]")
            {
                var sections = JsonSerializer.Deserialize<List<TemplateSection>>(template.Sections);
                if (sections != null)
                {
                    sectionsDto = sections.Select(s => new TemplateSectionDto { Id = s.Id, Title = s.Title, Type = s.Type }).ToList();
                }
            }

            var fieldsDto = new List<TemplateFieldDto>();
            if (!string.IsNullOrWhiteSpace(template.Fields) && template.Fields != "[]")
            {
                var fields = JsonSerializer.Deserialize<List<TemplateField>>(template.Fields);
                if (fields != null)
                {
                     fieldsDto = fields.Select(f => new TemplateFieldDto { 
                        Id = f.Id, 
                        Name = f.Name, 
                        Type = f.Type, 
                        Content = f.Content,
                        Config = f.Config?.RootElement.Clone() // Clone JsonElement for DTO stability
                    }).ToList();
                }
            }

            return new TemplateDto
            {
                Id = template.Id,
                Name = template.Name,
                Description = template.Description,
                Category = template.Category,
                ThumbnailUrl = template.ThumbnailUrl,
                Tags = tags,
                Sections = sectionsDto,
                Fields = fieldsDto,
                CreatedAt = template.CreationTime,
                UpdatedAt = template.LastModificationTime,
                TenantId = template.TenantId
            };
        }
    }
}
