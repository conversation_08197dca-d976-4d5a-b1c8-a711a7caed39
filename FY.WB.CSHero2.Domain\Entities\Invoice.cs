using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Invoice : FullAuditedMultiTenantEntity<Guid>
    {
        // Navigation property to TenantProfile
        public virtual TenantProfile TenantProfile { get; set; } = null!;
        
        public string OrderNumber { get; set; } = string.Empty; // e.g., "INV-20250315-001"
        public string Type { get; set; } = string.Empty; // "Invoice", "Payment", "Refund"
        public string Plans { get; set; } = string.Empty; // e.g., "Professional Plan - Monthly"
        public decimal Amount { get; set; } // Store as decimal, not string like in JSON
        public string Status { get; set; } = string.Empty; // e.g., "paid", "overdue"
        public DateTime Date { get; set; } // The invoice/payment/refund date

        public Invoice() : base() { }

        public Invoice(
            Guid id,
            string orderNumber,
            string type,
            string plans,
            decimal amount,
            string status,
            DateTime date)
            : base(id)
        {
            OrderNumber = orderNumber;
            Type = type;
            Plans = plans;
            Amount = amount;
            Status = status;
            Date = date;
        }

        public void UpdateDetails(
            string plans,
            decimal amount,
            string status)
        {
            Plans = plans;
            Amount = amount;
            Status = status;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetStatus(string status)
        {
            Status = status;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        // Helper method to generate order number based on type
        public static string GenerateOrderNumber(string type, DateTime date)
        {
            string prefix = type switch
            {
                "Invoice" => "INV",
                "Payment" => "PMT",
                "Refund" => "REF",
                _ => throw new ArgumentException($"Invalid invoice type: {type}")
            };

            return $"{prefix}-{date:yyyyMMdd}-{Guid.NewGuid().ToString()[..4]}";
        }
    }
}
