# Technical Specification: Multi-Storage Report Structure

## 1. Introduction

This technical specification provides detailed information about the domain models, interfaces, and implementation details for the multi-storage approach to the report structure. The new architecture will distribute report data across three storage systems:

1. **SQL Database**: Store report metadata and style selections
2. **Azure Blob Storage**: Store rendered Next.js components with HTML and CSS
3. **Azure Cosmos DB**: Store report data (sections and fields) as JSON

## 2. Domain Models

### 2.1. SQL Database Models

#### 2.1.1. Report

```csharp
public class Report
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public Guid? TenantId { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public Guid? LastModifiedById { get; set; }
    public bool IsPublished { get; set; }
    public bool IsArchived { get; set; }
    
    // New properties for multi-storage
    public string DataDocumentId { get; set; }
    public string ComponentsBlobId { get; set; }
    
    // Navigation properties
    public virtual ICollection<ReportVersion> Versions { get; set; }
    public virtual ReportStyle Style { get; set; }
    public virtual TenantProfile Tenant { get; set; }
    public virtual ApplicationUser Creator { get; set; }
    public virtual ApplicationUser LastModifiedBy { get; set; }
}
```

#### 2.1.2. ReportVersion

```csharp
public class ReportVersion
{
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    public string VersionName { get; set; }
    public Guid? CreatorId { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsCurrent { get; set; }
    public bool IsPublished { get; set; }
    
    // New properties for multi-storage
    public string DataDocumentId { get; set; }
    public string ComponentsBlobId { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; }
    public virtual ApplicationUser Creator { get; set; }
}
```

#### 2.1.3. ReportStyle

```csharp
public class ReportStyle
{
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public string Theme { get; set; }
    public string ColorScheme { get; set; }
    public string Typography { get; set; }
    public string Spacing { get; set; }
    
    // JSON serialized options
    public string LayoutOptionsJson { get; set; }
    public string TypographyOptionsJson { get; set; }
    public string StructureOptionsJson { get; set; }
    public string ContentOptionsJson { get; set; }
    public string VisualOptionsJson { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; }
}
```

### 2.2. Cosmos DB Models

#### 2.2.1. ReportData

```csharp
public class ReportData
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("reportId")]
    public string ReportId { get; set; }
    
    [JsonPropertyName("versionId")]
    public string VersionId { get; set; }
    
    [JsonPropertyName("versionNumber")]
    public int VersionNumber { get; set; }
    
    [JsonPropertyName("sections")]
    public List<ReportSection> Sections { get; set; }
    
    [JsonPropertyName("metadata")]
    public ReportDataMetadata Metadata { get; set; }
}
```

#### 2.2.2. ReportSection

```csharp
public class ReportSection
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("title")]
    public string Title { get; set; }
    
    [JsonPropertyName("type")]
    public string Type { get; set; }
    
    [JsonPropertyName("order")]
    public int Order { get; set; }
    
    [JsonPropertyName("fields")]
    public List<ReportSectionField> Fields { get; set; }
    
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; }
}
```

#### 2.2.3. ReportSectionField

```csharp
public class ReportSectionField
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    [JsonPropertyName("type")]
    public string Type { get; set; }
    
    [JsonPropertyName("content")]
    public string Content { get; set; }
    
    [JsonPropertyName("order")]
    public int Order { get; set; }
    
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; }
}
```

#### 2.2.4. ReportDataMetadata

```csharp
public class ReportDataMetadata
{
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; }
    
    [JsonPropertyName("lastModifiedAt")]
    public DateTime LastModifiedAt { get; set; }
    
    [JsonPropertyName("lastModifiedBy")]
    public string LastModifiedBy { get; set; }
}
```

### 2.3. Blob Storage Models

#### 2.3.1. ComponentsMetadata

```csharp
public class ComponentsMetadata
{
    [JsonPropertyName("reportId")]
    public string ReportId { get; set; }
    
    [JsonPropertyName("versionId")]
    public string VersionId { get; set; }
    
    [JsonPropertyName("components")]
    public List<ComponentMetadata> Components { get; set; }
    
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; }
}
```

#### 2.3.2. ComponentMetadata

```csharp
public class ComponentMetadata
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    [JsonPropertyName("sectionId")]
    public string SectionId { get; set; }
    
    [JsonPropertyName("fileName")]
    public string FileName { get; set; }
    
    [JsonPropertyName("imports")]
    public List<string> Imports { get; set; }
    
    [JsonPropertyName("props")]
    public List<string> Props { get; set; }
}
```

#### 2.3.3. ComponentDefinition

```csharp
public class ComponentDefinition
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string SectionId { get; set; }
    public string Code { get; set; }
    public List<string> Imports { get; set; }
    public List<string> Props { get; set; }
}
```

## 3. Repository Interfaces

### 3.1. SQL Repository Interfaces

#### 3.1.1. IReportMetadataRepository

```csharp
public interface IReportMetadataRepository
{
    Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Report>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<ReportVersion> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportVersion>> GetReportVersionsAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
    Task UpdateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
    Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    
    Task<ReportStyle> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default);
    Task UpdateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default);
}
```

### 3.2. Cosmos DB Repository Interfaces

#### 3.2.1. IReportDataRepository

```csharp
public interface IReportDataRepository
{
    Task<ReportData> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    Task<ReportData> GetReportDataByReportIdAsync(string reportId, string versionId, CancellationToken cancellationToken = default);
    Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task DeleteReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    
    Task<ReportSection> GetReportSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
    Task AddReportSectionAsync(string documentId, ReportSection section, CancellationToken cancellationToken = default);
    Task UpdateReportSectionAsync(string documentId, ReportSection section, CancellationToken cancellationToken = default);
    Task DeleteReportSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
    
    Task<ReportSectionField> GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
    Task AddReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field, CancellationToken cancellationToken = default);
    Task UpdateReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field, CancellationToken cancellationToken = default);
    Task DeleteReportSectionFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
}
```

### 3.3. Blob Storage Repository Interfaces

#### 3.3.1. IReportComponentsRepository

```csharp
public interface IReportComponentsRepository
{
    Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<ComponentDefinition> components, CancellationToken cancellationToken = default);
    Task<ComponentsMetadata> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
    Task<ComponentDefinition> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
    Task<IEnumerable<ComponentDefinition>> GetAllComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);
}
```

## 4. Service Interfaces

### 4.1. IReportService

```csharp
public interface IReportService
{
    Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportDto>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(CreateReportDto createReportDto, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(UpdateReportDto updateReportDto, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<ReportVersionDto> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportVersionDto>> GetReportVersionsAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportVersionAsync(CreateReportVersionDto createVersionDto, CancellationToken cancellationToken = default);
    Task UpdateReportVersionAsync(UpdateReportVersionDto updateVersionDto, CancellationToken cancellationToken = default);
    Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    
    Task<ReportStyleDto> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportStyleAsync(CreateReportStyleDto createStyleDto, CancellationToken cancellationToken = default);
    Task UpdateReportStyleAsync(UpdateReportStyleDto updateStyleDto, CancellationToken cancellationToken = default);
}
```

### 4.2. IReportDataService

```csharp
public interface IReportDataService
{
    Task<ReportDataDto> GetReportDataAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    Task<string> CreateReportDataAsync(CreateReportDataDto createDataDto, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(UpdateReportDataDto updateDataDto, CancellationToken cancellationToken = default);
    Task DeleteReportDataAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    
    Task<ReportSectionDto> GetReportSectionAsync(Guid reportId, Guid versionId, string sectionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportSectionDto>> GetReportSectionsAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    Task<string> AddReportSectionAsync(Guid reportId, Guid versionId, CreateReportSectionDto createSectionDto, CancellationToken cancellationToken = default);
    Task UpdateReportSectionAsync(Guid reportId, Guid versionId, UpdateReportSectionDto updateSectionDto, CancellationToken cancellationToken = default);
    Task DeleteReportSectionAsync(Guid reportId, Guid versionId, string sectionId, CancellationToken cancellationToken = default);
    
    Task<ReportSectionFieldDto> GetReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportSectionFieldDto>> GetReportSectionFieldsAsync(Guid reportId, Guid versionId, string sectionId, CancellationToken cancellationToken = default);
    Task<string> AddReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, CreateReportSectionFieldDto createFieldDto, CancellationToken cancellationToken = default);
    Task UpdateReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, UpdateReportSectionFieldDto updateFieldDto, CancellationToken cancellationToken = default);
    Task DeleteReportSectionFieldAsync(Guid reportId, Guid versionId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
}
```

### 4.3. IReportRenderingService

```csharp
public interface IReportRenderingService
{
    Task<string> RenderReportAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ComponentDefinitionDto>> GetReportComponentsAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    Task<ComponentDefinitionDto> GetReportComponentAsync(Guid reportId, Guid versionId, string componentName, CancellationToken cancellationToken = default);
    Task<byte[]> ExportReportAsPdfAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
    Task<byte[]> ExportReportAsHtmlAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
}
```

### 4.4. IReportDataMigrationService

```csharp
public interface IReportDataMigrationService
{
    Task<int> MigrateReportDataAsync(CancellationToken cancellationToken = default);
    Task<MigrationStatusDto> GetMigrationStatusAsync(CancellationToken cancellationToken = default);
    Task<bool> ValidateMigrationAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<bool> RollbackMigrationAsync(Guid reportId, CancellationToken cancellationToken = default);
}
```

## 5. Implementation Details

### 5.1. SQL Repository Implementation

#### 5.1.1. ReportMetadataRepository

```csharp
public class ReportMetadataRepository : IReportMetadataRepository
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<ReportMetadataRepository> _logger;
    
    public ReportMetadataRepository(
        IApplicationDbContext dbContext,
        ILogger<ReportMetadataRepository> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }
    
    public async Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Reports
            .Include(r => r.Style)
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
    }
    
    public async Task<IEnumerable<Report>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbContext.Reports
            .Include(r => r.Style)
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .AsQueryable();
            
        if (tenantId.HasValue)
        {
            query = query.Where(r => r.TenantId == tenantId);
        }
        
        return await query.ToListAsync(cancellationToken);
    }
    
    public async Task<Guid> CreateReportAsync(Report report, CancellationToken cancellationToken = default)
    {
        _dbContext.Reports.Add(report);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return report.Id;
    }
    
    public async Task UpdateReportAsync(Report report, CancellationToken cancellationToken = default)
    {
        _dbContext.Reports.Update(report);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
    
    public async Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        var report = await _dbContext.Reports.FindAsync(new object[] { reportId }, cancellationToken);
        if (report != null)
        {
            _dbContext.Reports.Remove(report);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
    }
    
    // Other methods implementation...
}
```

### 5.2. Cosmos DB Repository Implementation

#### 5.2.1. ReportDataRepository

```csharp
public class ReportDataRepository : IReportDataRepository
{
    private readonly CosmosClient _cosmosClient;
    private readonly Container _container;
    private readonly ILogger<ReportDataRepository> _logger;
    
    public ReportDataRepository(
        CosmosClient cosmosClient, 
        IConfiguration configuration,
        ILogger<ReportDataRepository> logger)
    {
        _cosmosClient = cosmosClient;
        _logger = logger;
        
        var databaseName = configuration["CosmosDb:DatabaseName"];
        var containerName = configuration["CosmosDb:ContainerName"];
        _container = _cosmosClient.GetContainer(databaseName, containerName);
    }
    
    public async Task<ReportData> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _container.ReadItemAsync<ReportData>(
                documentId, 
                new PartitionKey(documentId),
                cancellationToken: cancellationToken);
                
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            _logger.LogWarning("Report data document {DocumentId} not found", documentId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving report data document {DocumentId}", documentId);
            throw;
        }
    }
    
    public async Task<ReportData> GetReportDataByReportIdAsync(string reportId, string versionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new QueryDefinition(
                "SELECT * FROM c WHERE c.reportId = @reportId AND c.versionId = @versionId")
                .WithParameter("@reportId", reportId)
                .WithParameter("@versionId", versionId);
                
            var iterator = _container.GetItemQueryIterator<ReportData>(query);
            var results = new List<ReportData>();
            
            while (iterator.HasMoreResults)
            {
                var response = await iterator.ReadNextAsync(cancellationToken);
                results.AddRange(response.ToList());
            }
            
            return results.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving report data for report {ReportId}, version {VersionId}", 
                reportId, versionId);
            throw;
        }
    }
    
    public async Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _container.CreateItemAsync(
                data,
                new PartitionKey(data.Id),
                cancellationToken: cancellationToken);
                
            _logger.LogInformation("Created report data document {DocumentId}", data.Id);
            return data.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report data document {DocumentId}", data.Id);
            throw;
        }
    }
    
    // Other methods implementation...
}
```

### 5.3. Blob Storage Repository Implementation

#### 5.3.1. ReportComponentsRepository

```csharp
public class ReportComponentsRepository : IReportComponentsRepository
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    private readonly ILogger<ReportComponentsRepository> _logger;
    
    public ReportComponentsRepository(
        BlobServiceClient blobServiceClient, 
        IConfiguration configuration,
        ILogger<ReportComponentsRepository> logger)
    {
        _blobServiceClient = blobServiceClient;
        _logger = logger;
        _containerName = configuration["BlobStorage:ContainerName"];
    }
    
    public async Task<string> SaveComponentsAsync(
        Guid reportId, 
        Guid versionId, 
        IEnumerable<ComponentDefinition> components, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var blobId = $"reports/{reportId}/{versionId}";
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Ensure container exists
            await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
            
            // Create metadata
            var metadata = new ComponentsMetadata
            {
                ReportId = reportId.ToString(),
                VersionId = versionId.ToString(),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "system", // This should be replaced with the actual user ID
                Components = components.Select(c => new ComponentMetadata
                {
                    Id = c.Id,
                    Name = c.Name,
                    SectionId = c.SectionId,
                    FileName = $"{c.Name}.tsx",
                    Imports = c.Imports,
                    Props = c.Props
                }).ToList()
            };
            
            // Save metadata
            var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            
            var metadataBlobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");
            using var metadataStream = new MemoryStream(Encoding.UTF8.GetBytes(metadataJson));
            await metadataBlobClient.UploadAsync(metadataStream, overwrite: true, cancellationToken: cancellationToken);
            
            // Save components
            foreach (var component in components)
            {
                var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                await componentBlobClient.UploadAsync(componentStream, overwrite: true, cancellationToken: cancellationToken);
            }
            
            _logger.LogInformation("Saved {ComponentCount} components for report {ReportId}, version {VersionId}", 
                components.Count(), reportId, versionId);
                
            return blobId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving components for report {ReportId}, version {VersionId}", 
                reportId, versionId);
            throw;
        }
    }
    
    // Other methods implementation...
}
```

### 5.4. Service Implementation

#### 5.4.1. ReportService

```csharp
public class ReportService : IReportService
{
    private readonly IReportMetadataRepository _metadataRepository;
    private readonly IReportDataRepository _dataRepository;
    private readonly IReportComponentsRepository _componentsRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ReportService> _logger;
    
    public ReportService(
        IReportMetadataRepository metadataRepository,
        IReportDataRepository dataRepository,
        IReportComponentsRepository componentsRepository,
        IMapper mapper,
        ILogger<ReportService> logger)
    {
        _metadataRepository = metadataRepository;
        _dataRepository = dataRepository;
        _componentsRepository = componentsRepository;
        _mapper = mapper;
        _logger = logger;
    }
    
    public async Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        var report = await _metadataRepository.GetReportAsync(reportId, cancellationToken);
        if (report == null)
        {
            return null;
        }
        
        return _mapper.Map<ReportDto>(report);
    }
    
    public async Task<IEnumerable<ReportDto>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default)
    {
        var reports = await _metadataRepository.GetReportsAsync(tenantId, cancellationToken);
        return _mapper.Map<IEnumerable<ReportDto>>(reports);
    }
    
    public async Task<Guid> CreateReportAsync(CreateReportDto createReportDto, CancellationToken cancellationToken = default)
    {
        var report = _mapper.Map<Report>(createReportDto);
        report.Id = Guid.NewGuid();
        report.CreatedAt = DateTime.UtcNow;
        
        // Create initial version
        var version = new ReportVersion
        {
            Id = Guid.NewGuid(),
            ReportId = report.Id,
            VersionNumber = 1,
            VersionName = "Initial Version",
            CreatorId = report.CreatorId,
            CreatedAt = DateTime.UtcNow,
            IsCurrent = true,
            IsPublished = false
        };
        
        // Create initial style
        var style = new ReportStyle
        {
            Id = Guid.NewGuid(),
            ReportId = report.Id,
            Theme = createReportDto.Theme ?? "default",
            ColorScheme = createReportDto.ColorScheme ?? "default",
            Typography = createReportDto.Typography ?? "default",
            Spacing = createReportDto.Spacing ?? "default"
        };
        
        // Create initial report data in Cosmos DB
        var reportData = new ReportData
        {
            Id = $"report-data-{Guid.NewGuid()}",
            ReportId = report.Id.ToString(),
            VersionId = version.Id.ToString(),
            VersionNumber = version.VersionNumber,
            Sections = new List<ReportSection>(),
            Metadata = new ReportDataMetadata
            {
                CreatedAt = DateTime.UtcNow,
                CreatedBy = report.CreatorId?.ToString() ?? "system",
                LastModifiedAt = DateTime.UtcNow,
                LastModifiedBy = report.CreatorId?.ToString() ?? "system"
            }
        };
        
        var documentId = await _dataRepository.CreateReportDataAsync(reportData, cancellationToken);
        
        // Update references
        report.DataDocumentId = documentId;
        version.DataDocumentId = documentId;
        
        // Save to SQL
        report.Versions = new List<ReportVersion> { version };
        report.Style = style;
        
        await _metadataRepository.CreateReportAsync(report, cancellationToken);
        
        return report.Id;
    }
    
    // Other methods implementation...
}
```

#### 5.4.2. ReportDataService

```csharp
public class ReportDataService : IReportDataService
{
    private readonly IReportMetadataRepository _metadataRepository;
    private readonly IReportDataRepository _dataRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ReportDataService> _logger;
    
    public ReportDataService(
        IReportMetadataRepository metadataRepository,
        IReportDataRepository dataRepository,
        IMapper mapper,
        ILogger<ReportDataService> logger)
    {
        _metadataRepository = metadataRepository;
        _data