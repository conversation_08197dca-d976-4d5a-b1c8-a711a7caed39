'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Calendar, 
  Building, 
  Phone, 
  Mail, 
  User, 
  Clock, 
  Shield, 
  CreditCard,
  KeyRound,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tenant } from '@/types/tenant';
import Link from 'next/link';
import { getTenantById } from '@/lib/api';
import { getInvoicesByTenantId } from '@/lib/api-invoices';
import { Invoice } from '@/types/invoice';
import { useToast } from '@/components/providers/toast-provider';
import { InvoiceHistoryCard, InvoiceGeneratorButton } from '@/components/features/tenants/billing';

// Import API functions
import { sendPasswordReset } from '@/lib/api';

export default function TenantProfilePage() {
  const params = useParams();
  const router = useRouter();
  const tenantId = params.id as string;
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resetSent, setResetSent] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);
  const [billingHistory, setBillingHistory] = useState<Invoice[]>([]);
  const [billingHistoryLoading, setBillingHistoryLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchTenantData = async () => {
      try {
        setLoading(true);
        const tenantData = await getTenantById(tenantId);
        setTenant(tenantData);
        setError(null);
      } catch (err) {
        setError('Failed to fetch tenant data');
        toast({
          title: 'Error',
          description: 'Failed to fetch tenant data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    const fetchBillingHistory = async () => {
      try {
        setBillingHistoryLoading(true);
        const invoices = await getInvoicesByTenantId(tenantId);
        setBillingHistory(invoices);
      } catch (err) {
        console.error('Failed to fetch billing history:', err);
        // Use mock data as fallback
        setBillingHistory([
          {
            id: '1',
            tenantId,
            date: 'Mar 13, 2025',
            type: 'Invoice',
            orderNumber: 'AB04350700160EUS',
            plans: 'Creative Cloud All Apps 100GB',
            amount: 'US$17.47',
            status: 'paid',
            createdAt: new Date().toISOString()
          },
          {
            id: '2',
            tenantId,
            date: 'Feb 13, 2025',
            type: 'Invoice',
            orderNumber: 'AB04350700160EUS',
            plans: 'Creative Cloud All Apps 100GB',
            amount: 'US$17.47',
            status: 'paid',
            createdAt: new Date().toISOString()
          }
        ]);
      } finally {
        setBillingHistoryLoading(false);
      }
    };

    fetchTenantData();
    if (tenantId) {
      fetchBillingHistory();
    }
  }, [tenantId, toast]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handlePasswordReset = async () => {
    if (!tenant?.email) return;
    
    try {
      setResetLoading(true);
      await sendPasswordReset(tenant.email);
      setResetSent(true);
      toast({
        title: 'Success',
        description: 'Password reset email sent successfully',
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to send password reset email',
        variant: 'destructive',
      });
    } finally {
      setResetLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex items-center justify-center h-64">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="container mx-auto py-12 max-w-inner md:px-16">
        <div className="flex flex-col items-center justify-center h-64 gap-4">
          <p className="text-xl text-red-600">{error || 'Tenant not found'}</p>
          <Button asChild variant="outline">
            <Link href="/admin/tenants">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tenants
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'active':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100/50 text-emerald-700 ring-1 ring-emerald-600/20';
      case 'suspended':
        return 'bg-gradient-to-r from-red-50 to-red-100/50 text-red-700 ring-1 ring-red-600/20';
      default:
        return 'bg-gradient-to-r from-slate-50 to-slate-100/50 text-slate-700 ring-1 ring-slate-600/20';
    }
  };

  return (
    <div className="container mx-auto py-12 max-w-inner md:px-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Button asChild variant="outline" size="sm" className="h-9">
              <Link href="/admin/tenants">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Tenants
              </Link>
            </Button>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(tenant.status)}`}>
              {tenant.status}
            </span>
          </div>

          <h1 className="text-4xl font-bold mb-2 text-primary">{tenant.name}</h1>
          <p className="text-xl text-gray-600 mb-6">
            Tenant profile and subscription details
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-primary">Contact Information</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Full Name</p>
                  <p className="font-medium">{tenant.name}</p>
                </div>
              </div>

              {tenant.company && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Building className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Company</p>
                    <p className="font-medium">{tenant.company}</p>
                  </div>
                </div>
              )}

              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Mail className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{tenant.email}</p>
                </div>
              </div>

              {tenant.phone && (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">{tenant.phone}</p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-primary">Billing Details</h2>
            <div className="space-y-4">
              {/* Payment Method */}
              {tenant.paymentMethod ? (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <CreditCard className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Payment Method</p>
                    <p className="font-medium">
                      {tenant.paymentMethod.cardType} •••• {tenant.paymentMethod.lastFourDigits || '****'}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Expires: {tenant.paymentMethod.expirationDate || 'MM/YY'}
                    </p>
                    {tenant.paymentMethod.securityMethod && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Security: {tenant.paymentMethod.securityMethod}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <CreditCard className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Payment Method</p>
                    <p className="font-medium text-muted-foreground">No payment method on file</p>
                  </div>
                </div>
              )}

              {/* Billing Address */}
              {tenant.billingAddress ? (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Building className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Billing Address</p>
                    <p className="font-medium">{tenant.billingAddress.street}</p>
                    <p className="font-medium">
                      {tenant.billingAddress.city}, {tenant.billingAddress.state} {tenant.billingAddress.zipCode}
                    </p>
                    <p className="font-medium">{tenant.billingAddress.country}</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Building className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Billing Address</p>
                    <p className="font-medium text-muted-foreground">No billing address on file</p>
                  </div>
                </div>
              )}

              {/* Subscription Status */}
              <div className="flex items-center justify-between bg-primary/10 p-2">
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 p-2 rounded-md">
                    <Shield className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Subscription</p>
                    <p className="font-medium text-lg">{tenant.subscription ? tenant.subscription.charAt(0).toUpperCase() + tenant.subscription.slice(1) : 'Basic'}</p>
                  </div>
                </div>
                <span className={`inline-flex items-center border-2 border-white px-3 py-1 rounded-full text-xs font-medium ${
                  tenant.subscriptionStatus === 'active' || !tenant.subscriptionStatus
                    ? 'bg-emerald-100/80 text-emerald-700' 
                    : tenant.subscriptionStatus === 'overdue'
                    ? 'bg-amber-100/80 text-amber-700'
                    : tenant.subscriptionStatus === 'canceled'
                    ? 'bg-red-100/80 text-red-700'
                    : 'bg-emerald-100/80 text-emerald-700'
                }`}>
                  {tenant.subscriptionStatus ? tenant.subscriptionStatus.replace('_', ' ') : 'active'}
                </span>
              </div>

              <div className="flex justify-between rounded">
                {/* Billing Cycle */}
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Calendar className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Billing Cycle</p>
                    <p className="font-medium capitalize">{tenant.billingCycle || 'Monthly'}</p>
                  </div>
                </div>

                {/* Next Billing Date */}
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Next Billing Date</p>
                    <p className="font-medium">
                      {tenant.nextBillingDate 
                        ? formatDate(tenant.nextBillingDate)
                        : 'Not available'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Billing Action Buttons */}
              <div className="pt-4 space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push(`/admin/tenants/${tenantId}/billing`)}
                >
                  Update Payment Information
                </Button>
                
                {tenant && <InvoiceGeneratorButton 
                  tenant={tenant} 
                  onSuccess={() => {
                    toast({
                      title: 'Success',
                      description: 'Invoice generated successfully',
                    });
                  }}
                />}
              </div>
            </div>
          </Card>

          <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
            <h2 className="text-xl font-semibold mb-4 text-primary">Account Management</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-0.5 bg-primary/10 p-2 rounded-md">
                  <Shield className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Account Status</p>
                  <p className="font-medium capitalize">{tenant.status}</p>
                </div>
              </div>

              <div className="pt-4">
                <h3 className="text-md font-semibold mb-3">Password Management</h3>
                {resetSent ? (
                  <div className="flex items-center gap-2 text-emerald-600 mb-4">
                    <CheckCircle className="h-5 w-5" />
                    <span>Password reset email sent</span>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground mb-4">
                    Send a password reset link to the tenant's email address.
                  </p>
                )}
                <Button 
                  onClick={handlePasswordReset} 
                  disabled={resetLoading || resetSent}
                  className="w-full"
                >
                  <KeyRound className="mr-2 h-4 w-4" />
                  {resetLoading ? 'Sending...' : resetSent ? 'Email Sent' : 'Send Password Reset'}
                </Button>
              </div>

              <div className="pt-4">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push(`/admin/tenants/edit/${tenantId}`)}
                >
                  Edit Tenant Details
                </Button>
              </div>
            </div>
          </Card>

        </div>

        <div className="grid grid-cols-1 lg:grid-cols-1 gap-8 mb-12">
          {/* Invoice and Billing History Card */}
          {billingHistoryLoading ? (
            <Card className="col-span-1 p-6 bg-gradient-to-b from-card to-card/95 shadow-sm hover:shadow-md transition-all duration-200">
              <h2 className="text-xl font-semibold mb-4 text-primary">Invoice & Billing History</h2>
              <div className="flex items-center justify-center h-32">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary/30 border-r-primary"></div>
              </div>
            </Card>
          ) : (
            <InvoiceHistoryCard billingHistory={billingHistory} />
          )}
        </div>
      </motion.div>
    </div>
  );
}
