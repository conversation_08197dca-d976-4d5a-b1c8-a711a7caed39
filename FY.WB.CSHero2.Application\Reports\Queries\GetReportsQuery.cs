using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Reports.Dtos;
using MediatR;

namespace FY.WB.CSHero2.Application.Reports.Queries
{
    public class GetReportsQuery : IRequest<PagedResult<ReportDto>>
    {
        public ReportQueryParametersDto Parameters { get; }

        public GetReportsQuery(ReportQueryParametersDto parameters)
        {
            Parameters = parameters;
        }
    }
}
