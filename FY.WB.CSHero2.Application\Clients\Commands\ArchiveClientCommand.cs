using FY.WB.CSHero2.Application.Clients.Dtos;
using MediatR;

namespace FY.WB.CSHero2.Application.Clients.Commands
{
    public class ArchiveClientCommand : IRequest<ClientDto?>
    {
        public string Id { get; }
        public bool SetArchived { get; } // true to archive, false to unarchive

        public ArchiveClientCommand(string id, bool setArchived = true)
        {
            Id = id;
            SetArchived = setArchived;
        }
    }
}
