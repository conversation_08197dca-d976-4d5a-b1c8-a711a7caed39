import { Tenant } from '@/types/tenant';
import { Invoice } from '@/types/invoice';
import { CompanyProfile } from '@/types/company';
import html2pdf from 'html2pdf.js';

export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unitPrice: number;
  startDate?: string;
  endDate?: string;
}

export interface InvoiceDiscount {
  code: string;
  percentage: number;
}

export interface GeneratedInvoice {
  html: string;
}

/**
 * Generate an invoice HTML document based on tenant and company data
 * @param tenant The tenant to generate the invoice for
 * @param companyProfile The company profile with billing information
 * @param invoiceNumber The invoice number
 * @param issueDate The date the invoice was issued
 * @param dueDate The date the invoice is due
 * @param lineItems The line items for the invoice
 * @param discount Optional discount to apply
 * @returns HTML string for the invoice
 */
export function generateInvoice({
  tenant,
  companyProfile,
  invoiceNumber,
  issueDate,
  dueDate,
  lineItems,
  discount
}: {
  tenant: Tenant;
  companyProfile: CompanyProfile;
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  lineItems: InvoiceLineItem[];
  discount?: InvoiceDiscount;
}): GeneratedInvoice {
  // Calculate subtotal
  const subtotal = lineItems.reduce((sum, item) => {
    return sum + (item.quantity * item.unitPrice);
  }, 0);

  // Calculate discount amount if applicable
  const discountAmount = discount ? (subtotal * discount.percentage / 100) : 0;
  
  // Calculate total
  const total = subtotal - discountAmount;

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Generate company address HTML
  const companyAddressHtml = `
    <div>
      ${companyProfile.name}
      ${companyProfile.addressLine1 ? `<br>${companyProfile.addressLine1}` : ''}
      ${companyProfile.addressLine2 ? `<br>${companyProfile.addressLine2}` : ''}
      ${companyProfile.city && companyProfile.state ? 
        `<br>${companyProfile.city}, ${companyProfile.state} ${companyProfile.zipCode || ''}` : ''}
      ${companyProfile.country ? `<br>${companyProfile.country}` : ''}
      ${companyProfile.email ? `<br>${companyProfile.email}` : ''}
    </div>
  `;

  // Generate customer address HTML
  const customerAddressHtml = `
    <div>
      ${tenant.name}
      ${tenant.company ? `<br>${tenant.company}` : ''}
      ${tenant.billingAddress?.street ? `<br>${tenant.billingAddress.street}` : ''}
      ${tenant.billingAddress?.city && tenant.billingAddress?.state ? 
        `<br>${tenant.billingAddress.city}, ${tenant.billingAddress.state} ${tenant.billingAddress.zipCode || ''}` : ''}
      ${tenant.billingAddress?.country ? `<br>${tenant.billingAddress.country}` : ''}
      ${tenant.email ? `<br>${tenant.email}` : ''}
    </div>
  `;

  // Generate line items HTML
  const lineItemsHtml = lineItems.map(item => {
    const dateRange = item.startDate && item.endDate ? 
      `<br>${formatDate(item.startDate)} – ${formatDate(item.endDate)}` : '';
    
    return `
      <tr>
        <td>
          ${item.description}
          ${dateRange}
        </td>
        <td>${item.quantity}</td>
        <td>${formatCurrency(item.unitPrice)}</td>
        <td>${formatCurrency(item.quantity * item.unitPrice)}</td>
      </tr>
    `;
  }).join('');

  // Generate the full invoice HTML
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoiceNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 20px;
        }
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
        }
        .invoice-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 40px;
        }
        .invoice-title {
          font-size: 28px;
          font-weight: bold;
        }
        .company-name {
          font-size: 24px;
          color: #666;
          text-align: right;
        }
        .invoice-details {
          margin-bottom: 30px;
        }
        .invoice-details-row {
          display: flex;
          margin-bottom: 8px;
        }
        .invoice-details-label {
          font-weight: bold;
          width: 120px;
        }
        .addresses {
          display: flex;
          justify-content: space-between;
          margin-bottom: 40px;
        }
        .address-block {
          width: 48%;
        }
        .address-title {
          font-weight: bold;
          margin-bottom: 8px;
        }
        .total-due {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 30px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        th {
          background-color: #f2f2f2;
          text-align: left;
          padding: 10px;
          border-bottom: 1px solid #ddd;
        }
        td {
          padding: 10px;
          border-bottom: 1px solid #ddd;
        }
        .amount-column {
          text-align: right;
        }
        .totals-table {
          width: 350px;
          margin-left: auto;
        }
        .totals-table td {
          padding: 5px 10px;
        }
        .totals-table .total-row {
          font-weight: bold;
          border-top: 2px solid #ddd;
        }
        .totals-table .discount-row {
          color: #d9534f;
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <div class="invoice-header">
          <div class="invoice-title">Invoice</div>
          <div class="company-name">${companyProfile.name}</div>
        </div>
        
        <div class="invoice-details">
          <div class="invoice-details-row">
            <div class="invoice-details-label">Invoice number</div>
            <div>${invoiceNumber}</div>
          </div>
          <div class="invoice-details-row">
            <div class="invoice-details-label">Date of issue</div>
            <div>${formatDate(issueDate)}</div>
          </div>
          <div class="invoice-details-row">
            <div class="invoice-details-label">Date due</div>
            <div>${formatDate(dueDate)}</div>
          </div>
        </div>
        
        <div class="addresses">
          <div class="address-block">
            <div class="address-title">${companyProfile.name}</div>
            ${companyAddressHtml}
          </div>
          <div class="address-block">
            <div class="address-title">Bill to</div>
            ${customerAddressHtml}
          </div>
        </div>
        
        <div class="total-due">
          ${formatCurrency(total)} USD due ${formatDate(dueDate)}
        </div>
        
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th>Qty</th>
              <th>Unit price</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${lineItemsHtml}
          </tbody>
        </table>
        
        <table class="totals-table">
          <tr>
            <td>Subtotal</td>
            <td class="amount-column">${formatCurrency(subtotal)}</td>
          </tr>
          ${discount ? `
          <tr class="discount-row">
            <td>${discount.code} (${discount.percentage}% off)</td>
            <td class="amount-column">-${formatCurrency(discountAmount)}</td>
          </tr>
          ` : ''}
          <tr class="total-row">
            <td>Total</td>
            <td class="amount-column">${formatCurrency(total)}</td>
          </tr>
          <tr>
            <td>Amount due</td>
            <td class="amount-column">${formatCurrency(total)} USD</td>
          </tr>
        </table>
      </div>
    </body>
    </html>
  `;

  return { html };
}

/**
 * Generate a unique invoice number
 * @param prefix Optional prefix for the invoice number
 * @returns A unique invoice number
 */
export function generateInvoiceNumber(prefix: string = 'INV'): string {
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * Create a downloadable invoice PDF from HTML
 * @param html The invoice HTML
 * @param filename The filename for the PDF (without extension)
 */
export function downloadInvoice(html: string, filename: string): void {
  // Make sure the filename has a .pdf extension
  const pdfFilename = filename.endsWith('.pdf') ? filename : `${filename.replace(/\.html$/, '')}.pdf`;
  
  // Create a temporary container for the HTML content
  const container = document.createElement('div');
  container.innerHTML = html;
  document.body.appendChild(container);
  
  // Configure html2pdf options
  const options = {
    margin: 10,
    filename: pdfFilename,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2, useCORS: true },
    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
  };
  
  // Generate and download the PDF
  html2pdf()
    .from(container)
    .set(options)
    .save()
    .then(() => {
      // Clean up the temporary container
      document.body.removeChild(container);
    })
    .catch(error => {
      console.error('Error generating PDF:', error);
      
      // Fallback to HTML download if PDF generation fails
      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename.replace(/\.pdf$/, '.html');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // Clean up the temporary container
      document.body.removeChild(container);
    });
}
