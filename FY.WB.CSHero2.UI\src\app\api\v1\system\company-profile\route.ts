import { NextRequest, NextResponse } from 'next/server';
import { API_BASE_URL } from '@/lib/constants';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const authToken = cookies().get('authToken')?.value; // Or your specific auth cookie name
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const backendResponse = await fetch(`${API_BASE_URL}/api/v1/system/company-profile`, {
      method: 'GET',
      headers,
      cache: 'no-store',
    });

    if (!backendResponse.ok) {
      const errorBody = await backendResponse.text();
      return NextResponse.json({ error: `Failed to fetch company profile from backend: ${errorBody}` }, { status: backendResponse.status });
    }
    const data = await backendResponse.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in Next.js company-profile route:', error);
    return NextResponse.json({ error: 'Internal server error in Next.js route' }, { status: 500 });
  }
}
