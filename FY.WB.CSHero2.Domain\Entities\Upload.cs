using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Upload : FullAuditedMultiTenantEntity<Guid>
    {
        public string Filename { get; set; } = string.Empty;
        public long Size { get; set; } // In bytes
        public string ContentType { get; set; } = string.Empty; // MIME type (e.g., "application/pdf")
        public string StoragePath { get; set; } = string.Empty; // Path where the file is stored
        public string? StorageProvider { get; set; } // e.g., "LocalStorage", "AzureBlob" - for future use
        public string? ExternalUrl { get; set; } // URL if stored externally - for future use
        public string? Checksum { get; set; } // For file integrity verification - for future use

        public Upload() : base() { }

        public Upload(
            Guid id,
            string filename,
            long size,
            string contentType,
            string storagePath,
            string? storageProvider = null,
            string? externalUrl = null,
            string? checksum = null)
            : base(id)
        {
            Filename = filename;
            Size = size;
            ContentType = contentType;
            StoragePath = storagePath;
            StorageProvider = storageProvider;
            ExternalUrl = externalUrl;
            Checksum = checksum;
        }

        public void UpdateMetadata(
            string filename,
            string contentType)
        {
            Filename = filename;
            ContentType = contentType;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void UpdateStorageDetails(
            string storagePath,
            string? storageProvider = null,
            string? externalUrl = null)
        {
            StoragePath = storagePath;
            StorageProvider = storageProvider;
            ExternalUrl = externalUrl;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetChecksum(string checksum)
        {
            Checksum = checksum;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        // Helper method to determine if file is an image
        public bool IsImage()
        {
            return ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
        }

        // Helper method to determine if file is a document
        public bool IsDocument()
        {
            return ContentType switch
            {
                "application/pdf" => true,
                "application/msword" => true,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => true,
                "application/vnd.ms-excel" => true,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => true,
                _ => false
            };
        }
    }
}
