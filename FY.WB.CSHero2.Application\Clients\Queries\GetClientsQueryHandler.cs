using FY.WB.CSHero2.Application.Clients.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Linq.Expressions; // Required for Expression
using System; // Required for Math

namespace FY.WB.CSHero2.Application.Clients.Queries
{
    public class GetClientsQueryHandler : IRequestHandler<GetClientsQuery, PagedResult<ClientDto>>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetClientsQueryHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<PagedResult<ClientDto>> Handle(GetClientsQuery request, CancellationToken cancellationToken)
        {
            var query = _context.Clients.AsQueryable();

            // Filter by tenant ID if available
            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(c => c.TenantId == _currentUserService.TenantId);
            }

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(request.Parameters.SearchTerm))
            {
                var term = request.Parameters.SearchTerm.ToLower().Trim();
                query = query.Where(c => 
                    (c.Name != null && c.Name.ToLower().Contains(term)) ||
                    (c.Email != null && c.Email.ToLower().Contains(term)) ||
                    (c.CompanyName != null && c.CompanyName.ToLower().Contains(term))
                );
            }

            if (!string.IsNullOrWhiteSpace(request.Parameters.Status))
            {
                query = query.Where(c => c.Status.ToLower() == request.Parameters.Status.ToLower());
            }

            if (!string.IsNullOrWhiteSpace(request.Parameters.Industry))
            {
                query = query.Where(c => c.Industry != null && c.Industry.ToLower() == request.Parameters.Industry.ToLower());
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(request.Parameters.SortBy))
            {
                // Basic dynamic sorting. For more complex scenarios, consider a library or more robust implementation.
                var propertyInfo = typeof(Client).GetProperty(request.Parameters.SortBy, System.Reflection.BindingFlags.IgnoreCase | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                if (propertyInfo != null)
                {
                    var parameter = Expression.Parameter(typeof(Client), "c");
                    var propertyAccess = Expression.MakeMemberAccess(parameter, propertyInfo);
                    var orderByExp = Expression.Lambda(propertyAccess, parameter);
                    
                    string methodName = (request.Parameters.SortOrder?.ToLower() == "desc") ? "OrderByDescending" : "OrderBy";
                    
                    MethodCallExpression resultExpression = Expression.Call(
                        typeof(Queryable), 
                        methodName, 
                        new Type[] { typeof(Client), propertyInfo.PropertyType }, 
                        query.Expression, 
                        Expression.Quote(orderByExp));
                    
                    query = query.Provider.CreateQuery<Client>(resultExpression);
                }
            }
            else
            {
                query = query.OrderByDescending(c => c.CreationTime); // Default sort
            }

            // Get total count for pagination before applying skip/take
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination
            query = query.Skip((request.Parameters.Page - 1) * request.Parameters.PageSize)
                         .Take(request.Parameters.PageSize);

            var clients = await query
                .Select(c => new ClientDto
                {
                    Id = c.Id.ToString(),
                    Name = c.Name,
                    Email = c.Email,
                    Status = c.Status,
                    CompanyName = c.CompanyName,
                    CreatedAt = c.CreationTime,
                    UpdatedAt = c.LastModificationTime ?? c.CreationTime,
                    Phone = c.Phone,
                    Address = c.Address,
                    CompanySize = c.CompanySize,
                    Industry = c.Industry
                })
                .ToListAsync(cancellationToken);

            return new PagedResult<ClientDto>(clients, request.Parameters.Page, request.Parameters.PageSize, totalCount);
        }
    }
}
