import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers'; // Import cookies
import { API_BASE_URL } from '@/lib/constants'; // Import C# API Base URL
import type { InvoiceCreateInput } from '@/types/invoice'; // Keep for POST

/**
 * GET /api/v1/invoices
 * Proxies to the C# backend to get a list of invoices.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url); // Keep existing query param parsing
    
    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      // Construct URL for C# backend, forwarding query parameters
      const backendUrl = new URL(`${API_BASE_URL}/api/Invoices`);
      searchParams.forEach((value, key) => {
        backendUrl.searchParams.append(key, value);
      });

      console.log(`Fetching from backend: ${backendUrl.toString()}`);

      const backendResponse = await fetch(backendUrl.toString(), {
        method: 'GET',
        headers,
        cache: 'no-store', // Or your preferred caching strategy
        signal: controller.signal, // Add the abort signal
      });

      if (!backendResponse.ok) {
        const errorBody = await backendResponse.text();
        console.error(`Backend API error (GET /api/Invoices): ${backendResponse.status} ${errorBody}`);
        return NextResponse.json(
          { error: `Failed to fetch invoices from backend: ${backendResponse.statusText}` },
          { status: backendResponse.status }
        );
      }

      const invoices = await backendResponse.json();
      // If your C# backend returns pagination headers (like X-Pagination), you might want to forward them
      // For now, just returning the data
      return NextResponse.json(invoices);
    } finally {
      clearTimeout(timeoutId); // Clear the timeout to prevent memory leaks
    }
  } catch (error) {
    // Check if it's an abort error (timeout)
    if (error instanceof Error && error.name === 'AbortError') {
      console.error('Fetch timeout after 10 seconds in GET /api/v1/invoices route');
      return NextResponse.json(
        { error: 'Request timed out after 10 seconds' },
        { status: 504 } // Gateway Timeout
      );
    }

    console.error('Error in Next.js GET /api/v1/invoices route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoices', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/invoices
 * Proxies to the C# backend to create a new invoice.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as InvoiceCreateInput;
    
    const authToken = cookies().get('authToken')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const backendResponse = await fetch(`${API_BASE_URL}/api/Invoices`, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal, // Add the abort signal
      });

      if (!backendResponse.ok) {
        const errorBody = await backendResponse.text();
        console.error(`Backend API error (POST /api/Invoices): ${backendResponse.status} ${errorBody}`);
        return NextResponse.json(
          { error: `Failed to create invoice via backend: ${backendResponse.statusText}` },
          { status: backendResponse.status }
        );
      }
      
      const newInvoice = await backendResponse.json();
      return NextResponse.json(newInvoice, { status: backendResponse.status }); // Use backend status, likely 201
    } finally {
      clearTimeout(timeoutId); // Clear the timeout to prevent memory leaks
    }
  } catch (error) {
    // Check if it's an abort error (timeout)
    if (error instanceof Error && error.name === 'AbortError') {
      console.error('Fetch timeout after 10 seconds in POST /api/v1/invoices route');
      return NextResponse.json(
        { error: 'Request timed out after 10 seconds' },
        { status: 504 } // Gateway Timeout
      );
    }

    console.error('Error in Next.js POST /api/v1/invoices route:', error);
    return NextResponse.json(
      { error: 'Failed to create invoice', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
