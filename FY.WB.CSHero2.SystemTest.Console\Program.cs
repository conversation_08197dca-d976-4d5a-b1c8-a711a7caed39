using System;
using System.Threading.Tasks;
using FY.WB.CSHero2;

namespace FY.WB.CSHero2.SystemTest.ConsoleApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            System.Console.WriteLine("🚀 Report Rendering Engine V2 - Standalone System Test");
            System.Console.WriteLine(new string('=', 60));

            try
            {
                await SimpleSystemTest.RunSimpleTestAsync();

                System.Console.WriteLine("\n" + new string('=', 60));
                System.Console.WriteLine("✅ ALL TESTS PASSED! Report Rendering Engine V2 is working correctly!");
                System.Console.WriteLine(new string('=', 60));
            }
            catch (Exception ex)
            {
                System.Console.WriteLine("\n" + new string('=', 60));
                System.Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
                System.Console.WriteLine(new string('=', 60));
                Environment.Exit(1);
            }

            System.Console.WriteLine("\nPress any key to exit...");
            System.Console.ReadKey();
        }
    }
}
