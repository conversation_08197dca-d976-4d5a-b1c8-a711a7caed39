using System;

namespace FY.WB.CSHero2.Domain.Entities.Core
{
    public abstract class AuditedEntity<TId> : Entity<TId>, ICreationAuditedObject, IModificationAuditedObject
    {
        public virtual DateTime CreationTime { get; set; }
        public virtual Guid? CreatorId { get; set; }
        public virtual DateTime? LastModificationTime { get; set; }
        public virtual Guid? LastModifierId { get; set; }

        protected AuditedEntity()
        {
            // CreationTime can be set here or by a higher-level service/interceptor.
            // For now, we'll let services handle it to align with ABP's typical approach.
        }

        protected AuditedEntity(TId id) : base(id)
        {
        }
    }
}