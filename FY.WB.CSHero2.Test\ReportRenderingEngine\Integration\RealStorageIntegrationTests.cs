using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Xunit;
using Xunit.Abstractions;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// Integration tests for real CosmosDB and Azure Blob Storage implementations
    /// </summary>
    public class RealStorageIntegrationTests : ReportRenderingIntegrationTestBase
    {
        public RealStorageIntegrationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task StorageInitialization_ShouldCreateRequiredContainers()
        {
            // Arrange
            var initService = ServiceProvider.GetRequiredService<StorageInitializationService>();

            // Act
            await initService.InitializeAsync();
            var healthStatus = await initService.ValidateAsync();

            // Assert
            Assert.Equal(HealthStatus.Healthy, healthStatus.OverallStatus);
            Assert.True(healthStatus.CosmosDbStatus.IsHealthy);
            Assert.True(healthStatus.BlobStorageStatus.IsHealthy);
            
            Logger.LogInformation("Storage initialization test passed - all systems healthy");
        }

        [Fact]
        public async Task CosmosDbReportStyleService_FullCrudOperations_ShouldWork()
        {
            // Arrange
            var styleService = ServiceProvider.GetRequiredService<IReportStyleService>();
            var tenantId = Guid.NewGuid();
            var reportVersionId = Guid.NewGuid();
            var userId = Guid.NewGuid();

            var testStyle = new ReportStyleDocument
            {
                Id = ReportStyleDocument.CreateReportStyleId(reportVersionId),
                PartitionKey = tenantId.ToString(),
                ReportVersionId = reportVersionId,
                TenantId = tenantId,
                StyleType = "report",
                HtmlContent = "<div class='test-report'>Test Content</div>",
                CssStyles = ".test-report { color: blue; font-size: 16px; }",
                InlineStyles = new Dictionary<string, object>
                {
                    ["backgroundColor"] = "#f0f0f0",
                    ["padding"] = "20px"
                },
                ComponentStyles = new Dictionary<string, ComponentStyle>
                {
                    ["header"] = new ComponentStyle
                    {
                        ComponentId = "header",
                        ComponentType = "header",
                        CssClasses = new List<string> { "text-xl", "font-bold", "mb-4" },
                        InlineStyles = new Dictionary<string, string>
                        {
                            ["color"] = "#333333"
                        },
                        Layout = new ComponentLayout
                        {
                            Width = "100%",
                            Height = "auto",
                            Display = "block"
                        }
                    }
                },
                Metadata = new StyleMetadata
                {
                    Framework = "TailwindCSS",
                    FrameworkVersion = "3.0",
                    Theme = "modern",
                    ColorScheme = "light",
                    Tags = new List<string> { "test", "integration", "report" }
                },
                CreatedBy = userId,
                LastModifiedBy = userId
            };

            try
            {
                // Act & Assert - Create
                var savedId = await styleService.SaveStyleAsync(testStyle);
                Assert.Equal(testStyle.Id, savedId);
                Logger.LogInformation("Successfully created style document: {StyleId}", savedId);

                // Act & Assert - Read
                var retrievedStyle = await styleService.GetStyleAsync(testStyle.Id, tenantId);
                Assert.NotNull(retrievedStyle);
                Assert.Equal(testStyle.Id, retrievedStyle.Id);
                Assert.Equal(testStyle.HtmlContent, retrievedStyle.HtmlContent);
                Assert.Equal(testStyle.CssStyles, retrievedStyle.CssStyles);
                Assert.Equal(testStyle.Metadata.Framework, retrievedStyle.Metadata.Framework);
                Assert.Contains("test", retrievedStyle.Metadata.Tags);
                Logger.LogInformation("Successfully retrieved style document: {StyleId}", retrievedStyle.Id);

                // Act & Assert - Update
                retrievedStyle.HtmlContent = "<div class='updated-report'>Updated Content</div>";
                retrievedStyle.Metadata.Tags.Add("updated");
                retrievedStyle.UpdateModified(userId);

                var updateResult = await styleService.UpdateStyleAsync(retrievedStyle);
                Assert.True(updateResult);
                Logger.LogInformation("Successfully updated style document: {StyleId}", retrievedStyle.Id);

                // Verify update
                var updatedStyle = await styleService.GetStyleAsync(testStyle.Id, tenantId);
                Assert.NotNull(updatedStyle);
                Assert.Contains("Updated Content", updatedStyle.HtmlContent);
                Assert.Contains("updated", updatedStyle.Metadata.Tags);
                Assert.True(updatedStyle.Version > testStyle.Version);

                // Act & Assert - Search
                var searchResults = await styleService.SearchStylesAsync(tenantId, "test");
                Assert.Contains(searchResults, s => s.Id == testStyle.Id);
                Logger.LogInformation("Successfully found style in search results");

                // Act & Assert - Get by Framework
                var frameworkResults = await styleService.GetStylesByFrameworkAsync(tenantId, "TailwindCSS");
                Assert.Contains(frameworkResults, s => s.Id == testStyle.Id);
                Logger.LogInformation("Successfully found style by framework");

                // Act & Assert - Copy for new report version
                var newReportVersionId = Guid.NewGuid();
                var copiedStyleId = await styleService.CopyStyleForReportVersionAsync(
                    testStyle.Id, newReportVersionId, tenantId, userId);
                
                var copiedStyle = await styleService.GetStyleAsync(copiedStyleId, tenantId);
                Assert.NotNull(copiedStyle);
                Assert.Equal(newReportVersionId, copiedStyle.ReportVersionId);
                Assert.Equal(testStyle.HtmlContent, copiedStyle.HtmlContent); // Original content, not updated
                Logger.LogInformation("Successfully copied style for new report version: {CopiedStyleId}", copiedStyleId);

                // Cleanup copied style
                await styleService.DeleteStyleAsync(copiedStyleId, tenantId);
            }
            finally
            {
                // Cleanup - Delete
                var deleteResult = await styleService.DeleteStyleAsync(testStyle.Id, tenantId);
                Assert.True(deleteResult);
                Logger.LogInformation("Successfully deleted style document: {StyleId}", testStyle.Id);

                // Verify deletion
                var deletedStyle = await styleService.GetStyleAsync(testStyle.Id, tenantId);
                Assert.Null(deletedStyle);
            }
        }

        [Fact]
        public async Task AzureBlobReportDataService_FullCrudOperations_ShouldWork()
        {
            // Arrange
            var blobService = ServiceProvider.GetRequiredService<IReportDataBlobService>();
            var tenantId = Guid.NewGuid();
            var reportId = Guid.NewGuid();
            var versionId = Guid.NewGuid();

            var testData = new Dictionary<string, object>
            {
                ["reportTitle"] = "Integration Test Report",
                ["sections"] = new List<object>
                {
                    new Dictionary<string, object>
                    {
                        ["type"] = "header",
                        ["content"] = "Test Header",
                        ["level"] = 1
                    },
                    new Dictionary<string, object>
                    {
                        ["type"] = "paragraph",
                        ["content"] = "This is a test paragraph with some content."
                    },
                    new Dictionary<string, object>
                    {
                        ["type"] = "chart",
                        ["chartType"] = "bar",
                        ["data"] = new Dictionary<string, object>
                        {
                            ["labels"] = new[] { "Q1", "Q2", "Q3", "Q4" },
                            ["values"] = new[] { 100, 150, 200, 175 }
                        }
                    }
                },
                ["metadata"] = new Dictionary<string, object>
                {
                    ["createdAt"] = DateTime.UtcNow.ToString("O"),
                    ["version"] = "1.0",
                    ["tags"] = new[] { "test", "integration" }
                }
            };

            string? blobPath = null;

            try
            {
                // Act & Assert - Save
                blobPath = await blobService.SaveReportDataAsync(tenantId, reportId, versionId, testData);
                Assert.NotNull(blobPath);
                Assert.Contains(tenantId.ToString(), blobPath);
                Assert.Contains(reportId.ToString(), blobPath);
                Assert.Contains(versionId.ToString(), blobPath);
                Logger.LogInformation("Successfully saved report data to blob: {BlobPath}", blobPath);

                // Act & Assert - Exists
                var exists = await blobService.ExistsAsync(blobPath);
                Assert.True(exists);
                Logger.LogInformation("Confirmed blob exists: {BlobPath}", blobPath);

                // Act & Assert - Get Size
                var size = await blobService.GetSizeAsync(blobPath);
                Assert.NotNull(size);
                Assert.True(size > 0);
                Logger.LogInformation("Blob size: {Size} bytes", size);

                // Act & Assert - Get Metadata
                var metadata = await blobService.GetMetadataAsync(blobPath);
                Assert.NotNull(metadata);
                Assert.Equal(blobPath, metadata.BlobPath);
                Assert.True(metadata.Size > 0);
                Assert.Equal("application/json", metadata.ContentType);
                Logger.LogInformation("Retrieved blob metadata: {Size} bytes, {ContentType}", metadata.Size, metadata.ContentType);

                // Act & Assert - Read as Dictionary
                var retrievedData = await blobService.GetReportDataAsync(blobPath);
                Assert.NotNull(retrievedData);
                Assert.Equal("Integration Test Report", retrievedData["reportTitle"].ToString());
                Logger.LogInformation("Successfully retrieved report data as dictionary");

                // Act & Assert - Read as JSON
                var jsonData = await blobService.GetReportDataJsonAsync(blobPath);
                Assert.NotNull(jsonData);
                Assert.Contains("Integration Test Report", jsonData);
                Logger.LogInformation("Successfully retrieved report data as JSON");

                // Act & Assert - Read as Stream
                using var stream = await blobService.GetReportDataStreamAsync(blobPath);
                Assert.NotNull(stream);
                Assert.True(stream.CanRead);
                
                using var reader = new StreamReader(stream);
                var streamContent = await reader.ReadToEndAsync();
                Assert.Contains("Integration Test Report", streamContent);
                Logger.LogInformation("Successfully retrieved report data as stream");

                // Act & Assert - Update
                testData["reportTitle"] = "Updated Integration Test Report";
                testData["lastModified"] = DateTime.UtcNow.ToString("O");
                
                var updateResult = await blobService.UpdateReportDataAsync(blobPath, testData);
                Assert.True(updateResult);
                Logger.LogInformation("Successfully updated report data");

                // Verify update
                var updatedData = await blobService.GetReportDataAsync(blobPath);
                Assert.Equal("Updated Integration Test Report", updatedData["reportTitle"].ToString());
                Assert.True(updatedData.ContainsKey("lastModified"));

                // Act & Assert - Copy
                var newTenantId = Guid.NewGuid();
                var newReportId = Guid.NewGuid();
                var newVersionId = Guid.NewGuid();
                
                var copiedBlobPath = await blobService.CopyReportDataAsync(blobPath, newTenantId, newReportId, newVersionId);
                Assert.NotNull(copiedBlobPath);
                Assert.NotEqual(blobPath, copiedBlobPath);
                Logger.LogInformation("Successfully copied report data to: {CopiedBlobPath}", copiedBlobPath);

                // Verify copy
                var copiedData = await blobService.GetReportDataAsync(copiedBlobPath);
                Assert.Equal("Updated Integration Test Report", copiedData["reportTitle"].ToString());

                // Cleanup copied blob
                await blobService.DeleteReportDataAsync(copiedBlobPath);

                // Act & Assert - List operations
                var tenantBlobs = await blobService.ListReportDataAsync(tenantId);
                Assert.Contains(tenantBlobs, path => path == blobPath);
                Logger.LogInformation("Found {Count} blobs for tenant", tenantBlobs.Count);

                var reportBlobs = await blobService.ListReportVersionDataAsync(tenantId, reportId);
                Assert.Contains(reportBlobs, path => path == blobPath);
                Logger.LogInformation("Found {Count} version blobs for report", reportBlobs.Count);

                // Test asset operations
                var assetName = "test-chart.png";
                var assetData = Encoding.UTF8.GetBytes("fake-image-data-for-testing");
                using var assetStream = new MemoryStream(assetData);
                
                var assetPath = await blobService.SaveAssetAsync(tenantId, reportId, versionId, assetName, assetStream, "image/png");
                Assert.NotNull(assetPath);
                Logger.LogInformation("Successfully saved asset: {AssetPath}", assetPath);

                // Retrieve asset
                using var retrievedAssetStream = await blobService.GetAssetAsync(assetPath);
                using var assetReader = new MemoryStream();
                await retrievedAssetStream.CopyToAsync(assetReader);
                var retrievedAssetData = assetReader.ToArray();
                Assert.Equal(assetData, retrievedAssetData);
                Logger.LogInformation("Successfully retrieved asset data");

                // Cleanup asset
                await blobService.DeleteAssetAsync(assetPath);
            }
            finally
            {
                // Cleanup - Delete
                if (blobPath != null)
                {
                    var deleteResult = await blobService.DeleteReportDataAsync(blobPath);
                    Assert.True(deleteResult);
                    Logger.LogInformation("Successfully deleted report data blob: {BlobPath}", blobPath);

                    // Verify deletion
                    var existsAfterDelete = await blobService.ExistsAsync(blobPath);
                    Assert.False(existsAfterDelete);
                }
            }
        }

        [Fact]
        public async Task HybridStorage_EndToEndWorkflow_ShouldWork()
        {
            // Arrange
            var styleService = ServiceProvider.GetRequiredService<IReportStyleService>();
            var blobService = ServiceProvider.GetRequiredService<IReportDataBlobService>();
            
            var tenantId = Guid.NewGuid();
            var reportId = Guid.NewGuid();
            var versionId = Guid.NewGuid();
            var userId = Guid.NewGuid();

            // Create style document
            var styleDocument = new ReportStyleDocument
            {
                Id = ReportStyleDocument.CreateReportStyleId(versionId),
                PartitionKey = tenantId.ToString(),
                ReportVersionId = versionId,
                TenantId = tenantId,
                StyleType = "report",
                HtmlContent = "<div class='hybrid-test'>{{content}}</div>",
                CssStyles = ".hybrid-test { background: #fff; padding: 20px; }",
                Metadata = new StyleMetadata
                {
                    Framework = "TailwindCSS",
                    Theme = "hybrid-test",
                    Tags = new List<string> { "hybrid", "integration" }
                },
                CreatedBy = userId,
                LastModifiedBy = userId
            };

            // Create report data
            var reportData = new Dictionary<string, object>
            {
                ["title"] = "Hybrid Storage Test Report",
                ["content"] = "This report demonstrates hybrid storage across CosmosDB and Blob Storage",
                ["styleDocumentId"] = styleDocument.Id,
                ["sections"] = new List<object>
                {
                    new Dictionary<string, object>
                    {
                        ["type"] = "summary",
                        ["data"] = new Dictionary<string, object>
                        {
                            ["totalReports"] = 150,
                            ["activeUsers"] = 45,
                            ["storageUsed"] = "2.3 GB"
                        }
                    }
                }
            };

            string? blobPath = null;

            try
            {
                // Act - Save to both storage systems
                var styleId = await styleService.SaveStyleAsync(styleDocument);
                blobPath = await blobService.SaveReportDataAsync(tenantId, reportId, versionId, reportData);

                Logger.LogInformation("Saved to hybrid storage - Style: {StyleId}, Data: {BlobPath}", styleId, blobPath);

                // Act - Retrieve from both systems
                var retrievedStyle = await styleService.GetStyleAsync(styleId, tenantId);
                var retrievedData = await blobService.GetReportDataAsync(blobPath);

                // Assert - Verify data integrity
                Assert.NotNull(retrievedStyle);
                Assert.NotNull(retrievedData);
                Assert.Equal(styleDocument.HtmlContent, retrievedStyle.HtmlContent);
                Assert.Equal("Hybrid Storage Test Report", retrievedData["title"].ToString());
                Assert.Equal(styleId, retrievedData["styleDocumentId"].ToString());

                Logger.LogInformation("Successfully verified hybrid storage data integrity");

                // Act - Simulate report rendering workflow
                var htmlTemplate = retrievedStyle.HtmlContent;
                var reportTitle = retrievedData["title"].ToString();
                var renderedHtml = htmlTemplate?.Replace("{{content}}", reportTitle);

                Assert.Contains("Hybrid Storage Test Report", renderedHtml);
                Logger.LogInformation("Successfully simulated report rendering with hybrid data");

                // Act - Test cross-system operations
                var allTenantStyles = await styleService.GetStylesByTenantAsync(tenantId);
                var allTenantBlobs = await blobService.ListReportDataAsync(tenantId);

                Assert.Contains(allTenantStyles, s => s.Id == styleId);
                Assert.Contains(allTenantBlobs, b => b == blobPath);

                Logger.LogInformation("Successfully verified cross-system tenant isolation");
            }
            finally
            {
                // Cleanup both storage systems
                if (blobPath != null)
                {
                    await blobService.DeleteReportDataAsync(blobPath);
                }
                await styleService.DeleteStyleAsync(styleDocument.Id, tenantId);

                Logger.LogInformation("Successfully cleaned up hybrid storage test data");
            }
        }

        [Fact]
        public async Task ValidationAndErrorHandling_ShouldWorkCorrectly()
        {
            // Arrange
            var styleService = ServiceProvider.GetRequiredService<IReportStyleService>();
            var blobService = ServiceProvider.GetRequiredService<IReportDataBlobService>();

            // Test style validation
            var invalidStyle = new ReportStyleDocument(); // Missing required fields
            var validationResult = await styleService.ValidateStyleAsync(invalidStyle);
            
            Assert.False(validationResult.IsValid);
            Assert.Contains(validationResult.Errors, e => e.Contains("ID is required"));
            Assert.Contains(validationResult.Errors, e => e.Contains("Tenant ID is required"));
            Logger.LogInformation("Style validation correctly identified {ErrorCount} errors", validationResult.Errors.Count);

            // Test non-existent blob operations
            var nonExistentPath = "tenants/00000000-0000-0000-0000-000000000000/reports/test/versions/test/data.json";
            
            var exists = await blobService.ExistsAsync(nonExistentPath);
            Assert.False(exists);

            var size = await blobService.GetSizeAsync(nonExistentPath);
            Assert.Null(size);

            var metadata = await blobService.GetMetadataAsync(nonExistentPath);
            Assert.Null(metadata);

            var deleteResult = await blobService.DeleteReportDataAsync(nonExistentPath);
            Assert.False(deleteResult); // Should return false for non-existent blob

            Logger.LogInformation("Error handling tests completed successfully");
        }
    }
}