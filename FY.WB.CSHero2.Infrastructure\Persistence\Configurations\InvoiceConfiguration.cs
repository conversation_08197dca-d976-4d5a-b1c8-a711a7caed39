using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
    {
        public void Configure(EntityTypeBuilder<Invoice> builder)
        {
            builder.HasKey(i => i.Id);

            builder.Property(i => i.OrderNumber)
                .IsRequired()
                .HasMaxLength(20); // e.g., "INV-20250315-001"

            builder.HasIndex(i => i.OrderNumber)
                .IsUnique();

            builder.Property(i => i.Type)
                .IsRequired()
                .HasMaxLength(20); // "Invoice", "Payment", "Refund"

            builder.Property(i => i.Plans)
                .IsRequired()
                .HasMaxLength(200); // e.g., "Professional Plan - Monthly"

            builder.Property(i => i.Amount)
                .IsRequired()
                .HasPrecision(18, 2); // Support up to 16 digits before decimal, 2 after

            builder.Property(i => i.Status)
                .IsRequired()
                .HasMaxLength(20); // e.g., "paid", "overdue"

            builder.Property(i => i.Date)
                .IsRequired();

            // Make TenantId required and add foreign key relationship
            builder.Property(i => i.TenantId)
                .IsRequired();

            builder.HasOne(i => i.TenantProfile)
                .WithMany(tp => tp.Invoices)
                .HasForeignKey(i => i.TenantId)
                .OnDelete(DeleteBehavior.Restrict);

            // Add index on TenantId for better query performance
            builder.HasIndex(i => i.TenantId);

            // Audit properties are handled by base entity configuration
            builder.Property(i => i.CreationTime)
                .IsRequired();

            builder.Property(i => i.LastModificationTime)
                .IsRequired(false);
        }
    }
}
