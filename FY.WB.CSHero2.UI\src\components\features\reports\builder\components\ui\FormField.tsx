"use client";

import { FieldConfig } from "../../config/styleFieldConfig";

interface FormFieldProps {
  field: FieldConfig;
  value: any;
  onChange: (value: any) => void;
  parentValues?: Record<string, any>;
}

/**
 * A form field component that renders different field types based on the configuration
 */
export function FormField({ field, value, onChange, parentValues = {} }: FormFieldProps) {
  // Check if the field should be shown based on the showWhen condition
  if (field.showWhen && !field.showWhen(parentValues)) {
    return null;
  }

  // Render the appropriate field type
  switch (field.type) {
    case "select":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <select
            className="w-full p-2 border rounded"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
          >
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      );

    case "text":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            value={value || ""}
            placeholder={field.placeholder}
            onChange={(e) => onChange(e.target.value)}
          />
        </div>
      );

    case "color":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              className="w-10 h-10 rounded border"
              value={value || ""}
              onChange={(e) => onChange(e.target.value)}
            />
            <input
              type="text"
              className="flex-1 p-2 border rounded"
              value={value || ""}
              placeholder={field.placeholder}
              onChange={(e) => onChange(e.target.value)}
            />
          </div>
        </div>
      );

    case "number":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <input
            type="number"
            className="w-full p-2 border rounded"
            value={value || ""}
            placeholder={field.placeholder}
            onChange={(e) => onChange(e.target.value)}
          />
        </div>
      );

    case "checkbox":
      return (
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">{field.label}</label>
          <div className="flex items-center">
            <input
              type="checkbox"
              className="mr-2"
              checked={value || false}
              onChange={(e) => onChange(e.target.checked)}
            />
            <span className="text-sm">Show</span>
          </div>
        </div>
      );

    case "textarea":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <textarea
            className="w-full p-2 border rounded"
            rows={3}
            value={value || ""}
            placeholder={field.placeholder}
            onChange={(e) => onChange(e.target.value)}
          />
        </div>
      );
      
    case "icon-select":
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <div className="flex space-x-2">
            {field.options?.map(option => {
              const isSelected = value === option.value;
              return (
                <button
                  key={option.value}
                  type="button"
                  className={`p-2 border rounded flex items-center justify-center ${
                    isSelected ? 'bg-primary text-white border-primary' : 'bg-white border-gray-300'
                  }`}
                  onClick={() => onChange(option.value)}
                >
                  {/* Display column layout icons based on the value */}
                  <div className="w-10 h-10 flex items-center justify-center">
                    {option.value === "1" && (
                      <div className="w-6 h-6 bg-current"></div>
                    )}
                    {option.value === "2" && (
                      <div className="w-6 h-6 flex space-x-1">
                        <div className="w-1/2 bg-current"></div>
                        <div className="w-1/2 bg-current"></div>
                      </div>
                    )}
                    {option.value === "3" && (
                      <div className="w-6 h-6 flex space-x-1">
                        <div className="w-1/3 bg-current"></div>
                        <div className="w-1/3 bg-current"></div>
                        <div className="w-1/3 bg-current"></div>
                      </div>
                    )}
                    {option.value === "4" && (
                      <div className="w-6 h-6 flex space-x-1">
                        <div className="w-1/4 bg-current"></div>
                        <div className="w-1/4 bg-current"></div>
                        <div className="w-1/4 bg-current"></div>
                        <div className="w-1/4 bg-current"></div>
                      </div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      );
      
    case "dimension-input":
      // Parse the value which should be an object with width and height
      const dimensions = value ? (typeof value === 'object' ? value : { width: '', height: '' }) : { width: '', height: '' };
      
      return (
        <div>
          <label className="block text-sm font-medium mb-1">{field.label}</label>
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <label className="text-xs text-gray-500">W</label>
              <input
                type="text"
                className="w-full p-2 border rounded"
                value={dimensions.width || ''}
                placeholder="Width"
                onChange={(e) => onChange({ ...dimensions, width: e.target.value })}
              />
            </div>
            <span className="text-gray-500">×</span>
            <div className="flex-1">
              <label className="text-xs text-gray-500">H</label>
              <input
                type="text"
                className="w-full p-2 border rounded"
                value={dimensions.height || ''}
                placeholder="Height"
                onChange={(e) => onChange({ ...dimensions, height: e.target.value })}
              />
            </div>
          </div>
        </div>
      );

    default:
      return null;
  }
}
