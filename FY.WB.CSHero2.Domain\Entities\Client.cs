using System;
using FY.WB.CSHero2.Domain.Entities.Core; // Import the Core namespace

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Client : FullAuditedMultiTenantEntity<Guid> // Inherit from the new base class
    {
        // Navigation property to TenantProfile
        public virtual TenantProfile TenantProfile { get; set; } = null!;
        
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // e.g., "Active", "Inactive"
        public string CompanyName { get; set; } = string.Empty;
        
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? CompanySize { get; set; } // e.g., "1-10", "100-500"
        public string? Industry { get; set; }

        // Properties like Id, TenantId, CreationTime, CreatorId, LastModificationTime,
        // LastModifierId, IsDeleted, DeleterId, DeletionTime are now inherited.

        // Public constructor for EF Core and JSON deserialization
        public Client() : base() { }

        public Client(Guid id, string name, string email, string status, string companyName,
                      string? phone, string? address, string? companySize, string? industry)
            : base(id) // Pass Id to the base constructor
        {
            Name = name;
            Email = email;
            Status = status;
            CompanyName = companyName;
            Phone = phone;
            Address = address;
            CompanySize = companySize;
            Industry = industry;
            // CreationTime, CreatorId will be set by the application/DbContext
            // LastModificationTime, LastModifierId will be set by the application/DbContext
        }
        
        // Consider if a factory method is more appropriate than a public constructor
        // for setting initial audit properties if not handled by DbContext interceptors.
        // For example:
        // public static Client Create(...) { ... client.CreationTime = DateTime.UtcNow; ... }


        public void UpdateDetails(string name, string email, string status, string companyName,
                                  string? phone, string? address, string? companySize, string? industry)
        {
            Name = name;
            Email = email;
            Status = status;
            CompanyName = companyName;
            Phone = phone;
            Address = address;
            CompanySize = companySize;
            Industry = industry;
            // LastModificationTime, LastModifierId will be set by the application/DbContext
        }

        public void SetStatus(string status)
        {
            Status = status;
            // LastModificationTime, LastModifierId will be set by the application/DbContext
        }

        // The methods RecordModification() and the direct setting of CreatedAt/UpdatedAt
        // are removed as these will be handled by the auditing properties from the base class
        // and set by a higher-level mechanism (e.g., DbContext interceptors or application services).
    }
}
