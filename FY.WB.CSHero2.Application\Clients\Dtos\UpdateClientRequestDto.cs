using System.ComponentModel.DataAnnotations;

namespace FY.WB.CSHero2.Application.Clients.Dtos
{
    public class UpdateClientRequestDto
    {
        [StringLength(100)]
        public string? Name { get; set; }

        [EmailAddress]
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? Status { get; set; }

        [StringLength(100)]
        public string? CompanyName { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? CompanySize { get; set; }

        [StringLength(100)]
        public string? Industry { get; set; }
    }
}
