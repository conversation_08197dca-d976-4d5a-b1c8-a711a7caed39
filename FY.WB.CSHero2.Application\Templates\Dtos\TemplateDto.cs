using System;
using System.Collections.Generic;
using System.Text.Json; // Required for JsonDocument if used, or for deserialization logic if not directly exposing JsonDocument

namespace FY.WB.CSHero2.Application.Templates.Dtos
{
    // Mirroring structure from Domain.Entities.Template for DTO representation
    public class TemplateSectionDto
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }

    public class TemplateFieldDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public object? Config { get; set; } // Representing JsonDocument as object or string for DTO
    }

    public class TemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;
        
        public IEnumerable<string> Tags { get; set; } = new List<string>();
        public IEnumerable<TemplateSectionDto> Sections { get; set; } = new List<TemplateSectionDto>();
        public IEnumerable<TemplateFieldDto> Fields { get; set; } = new List<TemplateFieldDto>();

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? TenantId { get; set; }
    }
}
