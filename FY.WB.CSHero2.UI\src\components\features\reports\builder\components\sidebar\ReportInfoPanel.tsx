"use client";

import { ReportInfo } from "../../types";

interface ReportInfoPanelProps {
  reportInfo: ReportInfo;
  clients?: any[];
  onUpdateReportInfo: (field: string, value: any) => void;
  templateName?: string;
}

export function ReportInfoPanel({ 
  reportInfo, 
  clients, 
  onUpdateReportInfo, 
  templateName 
}: ReportInfoPanelProps) {
  
  const handleClientChange = (clientId: string) => {
    if (!onUpdateReportInfo || !clients) return;

    if (clientId) {
      const selectedClient = clients.find(client => client.id.toString() === clientId);

      if (selectedClient) {
        const companyName = selectedClient.companyName || selectedClient.name;
        onUpdateReportInfo('client', {
          'client-id': clientId,
          'company-name': companyName
        });
      }
    } else {
      onUpdateReportInfo('client', undefined);
    }
  };

  return (
    <div className="space-y-4">
      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-2">Report Details</h3>
        <p className="text-sm text-gray-600 mb-4">Configure the basic information for your report.</p>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1">Report Name</label>
            <input 
              type="text" 
              className="w-full p-2 border rounded" 
              placeholder="Q1 2025 Performance Report" 
              value={reportInfo.title}
              onChange={(e) => onUpdateReportInfo('title', e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea 
              className="w-full p-2 border rounded" 
              rows={3} 
              placeholder="Quarterly performance analysis..."
              value={reportInfo.description}
              onChange={(e) => onUpdateReportInfo('description', e.target.value)}
            ></textarea>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Client</label>
            <select
              className="w-full p-2 border rounded"
              value={reportInfo.client ? reportInfo.client['client-id'] : ''}
              onChange={(e) => handleClientChange(e.target.value)}
            >
              <option value="">Select a client</option>
              {clients && clients.map((client) => (
                <option key={client.id} value={client.id.toString()}>
                  {client.companyName || client.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-2">Time Period</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Start Date</label>
            <input 
              type="date" 
              className="w-full p-2 border rounded"
              value={reportInfo.startDate}
              onChange={(e) => onUpdateReportInfo('startDate', e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">End Date</label>
            <input 
              type="date" 
              className="w-full p-2 border rounded"
              value={reportInfo.endDate}
              onChange={(e) => onUpdateReportInfo('endDate', e.target.value)}
            />
          </div>
        </div>
      </div>

      {templateName && (
        <div className="border rounded-lg p-4 bg-blue-50">
          <h3 className="font-medium mb-2">Template</h3>
          <p className="text-sm">Using template: <span className="font-medium">{templateName}</span></p>
        </div>
      )}
    </div>
  );
}
