import { FieldConfig, SectionConfig, StyleSectionConfig } from './styleFieldConfig';

// Font Families Fields
const fontFamiliesFields: FieldConfig[] = [
  {
    name: "primary",
    label: "Primary Font",
    type: "select",
    defaultValue: "Aria<PERSON>",
    options: [
      { value: "Aria<PERSON>", label: "Aria<PERSON>" },
      { value: "Helvetica", label: "Helvetica" },
      { value: "Cal<PERSON>ri", label: "Calibri" },
      { value: "Roboto", label: "Roboto" },
      { value: "Times New Roman", label: "Times New Roman" },
      { value: "Georgia", label: "Georgia" },
      { value: "<PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON>" },
      { value: "Baskerville", label: "Baskerville" }
    ]
  },
  {
    name: "secondary",
    label: "Secondary Font",
    type: "select",
    defaultValue: "Georgia",
    options: [
      { value: "Arial", label: "Arial" },
      { value: "Helvetica", label: "Helvetica" },
      { value: "<PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON>" },
      { value: "Roboto", label: "Roboto" },
      { value: "Times New Roman", label: "Times New Roman" },
      { value: "Georgia", label: "Georgia" },
      { value: "<PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON>" },
      { value: "Baskerville", label: "Baskerville" }
    ]
  },
  {
    name: "monospace",
    label: "Monospace Font",
    type: "select",
    defaultValue: "Courier New",
    options: [
      { value: "Courier New", label: "Courier New" },
      { value: "Consolas", label: "Consolas" },
      { value: "Monaco", label: "Monaco" },
      { value: "Menlo", label: "Menlo" }
    ]
  },
  {
    name: "pairingPreset",
    label: "Font Pairing Preset",
    type: "select",
    defaultValue: "classic",
    options: [
      { value: "classic", label: "Classic" },
      { value: "modern", label: "Modern" },
      { value: "technical", label: "Technical" },
      { value: "creative", label: "Creative" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Font Sizes Fields
const fontSizesFields: FieldConfig[] = [
  {
    name: "body",
    label: "Body Text",
    type: "select",
    defaultValue: "11pt",
    options: [
      { value: "10pt", label: "10pt" },
      { value: "11pt", label: "11pt" },
      { value: "12pt", label: "12pt" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "h1Size",
    label: "H1 Size",
    type: "select",
    defaultValue: "24pt",
    options: [
      { value: "18pt", label: "18pt" },
      { value: "20pt", label: "20pt" },
      { value: "24pt", label: "24pt" },
      { value: "28pt", label: "28pt" },
      { value: "32pt", label: "32pt" }
    ]
  },
  {
    name: "h2Size",
    label: "H2 Size",
    type: "select",
    defaultValue: "20pt",
    options: [
      { value: "16pt", label: "16pt" },
      { value: "18pt", label: "18pt" },
      { value: "20pt", label: "20pt" },
      { value: "22pt", label: "22pt" },
      { value: "24pt", label: "24pt" }
    ]
  },
  {
    name: "h3Size",
    label: "H3 Size",
    type: "select",
    defaultValue: "16pt",
    options: [
      { value: "14pt", label: "14pt" },
      { value: "16pt", label: "16pt" },
      { value: "18pt", label: "18pt" },
      { value: "20pt", label: "20pt" }
    ]
  },
  {
    name: "h4Size",
    label: "H4 Size",
    type: "select",
    defaultValue: "14pt",
    options: [
      { value: "12pt", label: "12pt" },
      { value: "13pt", label: "13pt" },
      { value: "14pt", label: "14pt" },
      { value: "16pt", label: "16pt" }
    ]
  },
  {
    name: "footnotes",
    label: "Footnotes/Endnotes",
    type: "select",
    defaultValue: "9pt",
    options: [
      { value: "8pt", label: "8pt" },
      { value: "9pt", label: "9pt" },
      { value: "10pt", label: "10pt" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "captions",
    label: "Captions",
    type: "select",
    defaultValue: "9pt",
    options: [
      { value: "8pt", label: "8pt" },
      { value: "9pt", label: "9pt" },
      { value: "10pt", label: "10pt" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "scaleType",
    label: "Scale Type",
    type: "select",
    defaultValue: "fixed",
    options: [
      { value: "fixed", label: "Fixed sizes" },
      { value: "relative", label: "Relative (em-based)" },
      { value: "modular", label: "Modular scale" }
    ]
  }
];

// Line Spacing Fields
const lineSpacingFields: FieldConfig[] = [
  {
    name: "body",
    label: "Body Text",
    type: "select",
    defaultValue: "1.15",
    options: [
      { value: "1.0", label: "Single (1.0)" },
      { value: "1.15", label: "Comfortable (1.15)" },
      { value: "2.0", label: "Double (2.0)" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "headings",
    label: "Headings",
    type: "select",
    defaultValue: "1.0",
    options: [
      { value: "1.0", label: "Single (1.0)" },
      { value: "0.9", label: "Tight (0.9)" },
      { value: "1.2", label: "Loose (1.2)" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "lists",
    label: "Lists",
    type: "select",
    defaultValue: "same-as-body",
    options: [
      { value: "same-as-body", label: "Same as body" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "blockQuotes",
    label: "Block Quotes",
    type: "select",
    defaultValue: "same-as-body",
    options: [
      { value: "same-as-body", label: "Same as body" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "footnotes",
    label: "Footnotes/References",
    type: "select",
    defaultValue: "single",
    options: [
      { value: "single", label: "Single" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Alignment Fields
const alignmentFields: FieldConfig[] = [
  {
    name: "body",
    label: "Body Text",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "justified", label: "Justified" },
      { value: "center", label: "Centered" },
      { value: "right", label: "Right-aligned" }
    ]
  },
  {
    name: "headings",
    label: "Headings",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "center", label: "Centered" },
      { value: "right", label: "Right-aligned" }
    ]
  },
  {
    name: "lists",
    label: "Lists",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "captions",
    label: "Captions",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "center", label: "Centered" },
      { value: "right", label: "Right-aligned" }
    ]
  },
  {
    name: "tables",
    label: "Tables",
    type: "select",
    defaultValue: "left",
    options: [
      { value: "left", label: "Left-aligned" },
      { value: "center", label: "Centered" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Special Text Styles Fields
const specialTextStylesFields: FieldConfig[] = [
  {
    name: "boldUsage",
    label: "Bold Usage",
    type: "select",
    defaultValue: "headings-only",
    options: [
      { value: "headings-only", label: "Headings only" },
      { value: "key-terms", label: "Key terms" },
      { value: "custom", label: "Custom rules" }
    ]
  },
  {
    name: "italicsUsage",
    label: "Italics Usage",
    type: "select",
    defaultValue: "emphasis",
    options: [
      { value: "emphasis", label: "Emphasis" },
      { value: "terms", label: "Terms" },
      { value: "titles", label: "Titles" },
      { value: "custom", label: "Custom rules" }
    ]
  },
  {
    name: "underlineUsage",
    label: "Underline Usage",
    type: "select",
    defaultValue: "hyperlinks-only",
    options: [
      { value: "none", label: "None" },
      { value: "hyperlinks-only", label: "Hyperlinks only" },
      { value: "custom", label: "Custom rules" }
    ]
  },
  {
    name: "highlighting",
    label: "Highlighting",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "background", label: "Background color" },
      { value: "text-color", label: "Text color change" },
      { value: "custom", label: "Custom" }
    ]
  },
  {
    name: "textCase",
    label: "Text Case Options",
    type: "select",
    defaultValue: "sentence",
    options: [
      { value: "sentence", label: "Sentence case" },
      { value: "title", label: "Title Case" },
      { value: "uppercase", label: "UPPERCASE" },
      { value: "lowercase", label: "lowercase" }
    ]
  },
  {
    name: "smallCaps",
    label: "Small Caps",
    type: "select",
    defaultValue: "none",
    options: [
      { value: "none", label: "None" },
      { value: "headings-only", label: "Headings only" },
      { value: "custom", label: "Custom" }
    ]
  }
];

// Define sections
const sections: SectionConfig[] = [
  {
    id: "fontFamilies",
    title: "Font Families",
    fields: fontFamiliesFields,
    defaultExpanded: true
  },
  {
    id: "fontSizes",
    title: "Font Sizes",
    fields: fontSizesFields
  },
  {
    id: "lineSpacing",
    title: "Line Spacing",
    fields: lineSpacingFields
  },
  {
    id: "alignment",
    title: "Alignment",
    fields: alignmentFields
  },
  {
    id: "specialTextStyles",
    title: "Special Text Styles",
    fields: specialTextStylesFields
  }
];

// Export the complete configuration
export const typographyConfig: StyleSectionConfig = {
  sections
};
