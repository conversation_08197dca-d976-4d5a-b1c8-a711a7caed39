using System;

namespace FY.WB.CSHero2.Application.Uploads.Dtos
{
    public class UpdateUploadRequestDto
    {
        public Guid Id { get; set; } // Id of the Upload to update
        public string? Filename { get; set; } // Allow updating filename
        public string? ContentType { get; set; } // Allow updating content type
        
        // Storage details might be updated separately or together
        public string? StoragePath { get; set; }
        public string? StorageProvider { get; set; }
        public string? ExternalUrl { get; set; }
        public string? Checksum { get; set; } // Allow updating checksum
        // Size is typically not updated after initial upload.
    }
}
