import { readJsonFile, writeJsonFile, processArray, generateId } from './utils';

export interface TemplateField {
  id: string;
  name: string;
  type: string;
  content: string;
  config?: {
    rows?: number;
    columns?: number;
  };
}

export interface TemplateSection {
  id: string;
  title: string;
  type: string;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnailUrl: string;
  sections: TemplateSection[];
  fields: TemplateField[];
}

/**
 * Get all templates with optional filtering, sorting, and pagination
 * @param options Query options for filtering, sorting, and pagination
 * @returns Array of templates
 */
export async function getTemplates(options: {
  query?: Record<string, string | string[]>;
  limit?: number;
  page?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
} = {}): Promise<Template[]> {
  const { query, limit, page, sortBy, order } = options;
  
  const data = await readJsonFile<any>();
  
  return processArray(data.templates, {
    query,
    sortBy,
    order,
    page: page ? parseInt(page.toString()) : undefined,
    limit: limit ? parseInt(limit.toString()) : undefined
  });
}

/**
 * Get a template by ID
 * @param id Template ID
 * @returns Template object or null if not found
 */
export async function getTemplateById(id: string): Promise<Template | null> {
  const data = await readJsonFile<any>();
  const template = data.templates.find((t: Template) => t.id === id);
  return template || null;
}

/**
 * Create a new template
 * @param template Template data
 * @returns Created template
 */
export async function createTemplate(template: Omit<Template, 'id'>): Promise<Template> {
  const data = await readJsonFile<any>();
  
  const newTemplate: Template = {
    ...template,
    id: `template-${Date.now()}`
  };
  
  data.templates.push(newTemplate);
  
  await writeJsonFile('db.json', data);
  
  return newTemplate;
}

/**
 * Update a template
 * @param id Template ID
 * @param updates Template data to update
 * @returns Updated template or null if not found
 */
export async function updateTemplate(
  id: string,
  updates: Partial<Omit<Template, 'id'>>
): Promise<Template | null> {
  const data = await readJsonFile<any>();
  
  const templateIndex = data.templates.findIndex((t: Template) => t.id === id);
  if (templateIndex === -1) return null;
  
  const updatedTemplate: Template = {
    ...data.templates[templateIndex],
    ...updates
  };
  
  data.templates[templateIndex] = updatedTemplate;
  
  await writeJsonFile('db.json', data);
  
  return updatedTemplate;
}

/**
 * Delete a template
 * @param id Template ID
 * @returns True if template was deleted, false if not found
 */
export async function deleteTemplate(id: string): Promise<boolean> {
  const data = await readJsonFile<any>();
  
  const templateIndex = data.templates.findIndex((t: Template) => t.id === id);
  if (templateIndex === -1) return false;
  
  data.templates.splice(templateIndex, 1);
  
  await writeJsonFile('db.json', data);
  
  return true;
}
