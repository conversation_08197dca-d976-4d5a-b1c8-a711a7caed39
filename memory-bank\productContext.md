# Product Context: SaaS Template

**Version:** 0.1
**Date:** 2025-05-09
**Related Document:** `projectbrief.md`

## 1. Problem Space

Developing new SaaS applications often involves repetitive setup for core functionalities like user authentication, multi-tenancy, API structure, and a modern frontend framework. This boilerplate work consumes valuable development time that could be spent on unique business logic and features. Developers need a solid, well-structured starting point that incorporates best practices for these common SaaS components.

## 2. Solution: A Reusable SaaS Template

This project aims to provide a comprehensive template that accelerates the development of SaaS applications by providing a pre-configured, integrated solution for:
-   A secure ASP.NET Core Web API backend.
-   A responsive and modern Next.js frontend.
-   User identity management.
-   Multi-tenancy architecture.
-   JWT-based authentication.

## 3. Target Users

-   **Developers/Development Teams:** Primary users who will leverage this template to kickstart new SaaS projects.
-   **Startups:** Companies looking to rapidly prototype and build SaaS products.
-   **Enterprise Teams:** Groups needing a standardized, modern stack for new SaaS initiatives.

## 4. User Experience (UX) Goals (for the template's output)

While this project is a template for developers, the applications *built from* this template should aim for:
-   **Clear Authentication Flow:** Easy registration, login, and logout processes for end-users.
-   **Segregated User Areas:**
    -   Publicly accessible marketing/information pages.
    -   A secure "client" or "user" area for authenticated users to access general application features.
    -   A "tenant" specific area where user experience might be tailored or branded based on the tenant context.
-   **Responsive Design:** The Next.js frontend should be built with Tailwind CSS, enabling responsive layouts that work across various devices.
-   **Performant API:** The ASP.NET Core backend should be efficient and scalable.

## 5. How It Should Work (High-Level Flow)

1.  **Project Initialization:** A developer clones or uses this template as a starting point.
2.  **Configuration:** The developer configures database connection strings, JWT secrets, and potentially initial tenant information.
3.  **Backend Development:**
    -   The ASP.NET Core API provides endpoints for user registration and login.
    -   Custom user fields (e.g., `CompanyName`, `CompanyUrl`) are part of the `ApplicationUser` model.
    -   The API issues JWTs upon successful login.
    -   (Future) Finbuckle handles tenant resolution (e.g., via route, hostname) and provides `ITenantInfo` to the application, potentially isolating data via a multi-tenant DbContext.
4.  **Frontend Development:**
    -   The Next.js application provides UI for registration and login.
    -   It communicates with the ASP.NET Core API (via BFF API routes) for authentication.
    -   An `AuthContext` manages client-side authentication state (user, token).
    -   JWTs (ideally as HttpOnly cookies) are used to authenticate subsequent requests to the backend.
    -   Middleware protects routes, redirecting unauthenticated users.
    -   Distinct layouts (`PublicLayout`, `ClientLayout`, `TenantLayout`) render appropriate UI shells based on the route and authentication/tenant status.
5.  **End-User Interaction (Example):**
    -   A new user visits the public site and registers.
    -   The user logs in, receiving a JWT.
    -   The user is redirected to their client dashboard.
    -   If accessing a tenant-specific part of the application, the UI and data might reflect that tenant's context.

## 6. Key Differentiators (of the template)

-   **Integrated Stack:** Combines ASP.NET Core and Next.js, two popular and powerful frameworks.
-   **Multi-tenancy Foundation:** Includes Finbuckle for building multi-tenant SaaS applications from the ground up.
-   **Modern Frontend:** Leverages Next.js with TypeScript and Tailwind CSS for a modern development experience and UI.
-   **Best Practices:** Aims to incorporate good practices for security (JWTs, HttpOnly cookies), project structure, and API design.
