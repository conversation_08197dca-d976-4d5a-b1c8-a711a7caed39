<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Service Performance Report - Q1 2024</title>
    <style>
        :root {
            /* Primary Colors */
            --deep-teal: #1D3B4A;
            --slate-gray: #8599A5;
            --clean-white: #FFFFFF;
            /* Secondary Colors */
        --accent-blue: #3E7BFA;
        --success-green: #2AC769;
        --alert-orange: #FF7E42;
        --warning-yellow: #FFCB47;
        --light-gray: #F0F4F7;
        --dark-gray: #2A3B47;
        
        /* Spacing */
        --spacing-micro: 4px;
        --spacing-small: 8px;
        --spacing-medium: 16px;
        --spacing-large: 24px;
        --spacing-xlarge: 32px;
        --spacing-xxlarge: 48px;
    }
    
    body {
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        line-height: 1.5;
        color: var(--dark-gray);
        background-color: var(--light-gray);
        margin: 0;
        padding: 0;
    }
    
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px;
    }
    
    .page {
        background-color: var(--clean-white);
        border-radius: 12px;
        box-shadow: 0px 4px 12px rgba(0,0,0,0.08);
        margin-bottom: 60px;
        padding: var(--spacing-xlarge);
        width: 850px;
        margin-left: auto;
        margin-right: auto;
        box-sizing: border-box;
    }
    
    .cover-page {
        position: relative;
        background-color: var(--clean-white);
        color: #333;
        padding: 0;
        overflow: hidden;
        height: 950px;
        width: 850px;
        margin: 0 auto 40PX;
        aspect-ratio: 8.5/11;
    }
    
    .cover-diagonal {
        position: absolute;
        width: 100%;
        height: 100%;
        clip-path: polygon(0 0, 50% 0, 0 100%);
    }
    
    .cover-diagonal-overlay {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(29, 59, 74, 1);
        clip-path: polygon(0 0, 50% 0, 0 30%);
    }
    
    .cover-diagonal-2 {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #a5b1b9;
        clip-path: polygon(0 0, 75% 0, 0 50%);
        opacity: 0.7;
    }
    
    .cover-diagonal-3 {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(29, 59, 74, 0.9);
        clip-path: polygon(0 60%, 60% 100%, 0 100%);
        opacity: 0.8;
        z-index: 2;
    }
    
    .cover-diagonal-4 {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(29, 59, 74, 0.4);
        clip-path: polygon(0 30%, 110% 100%, 0 100%);
        opacity: 0.8;
        z-index: 2;
    }

    .cover-diagonal-5 {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(29, 59, 74, 0.4);
        clip-path: polygon(190% 0, 100% 100%, 70% 100%);
        opacity: 0.8;
        z-index: 2;
    }
    
    .cover-content {
        position: relative;
        z-index: 10;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 40px;
        box-sizing: border-box;
    }
    
    .cover-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .cover-logo {
        color: white;
        font-size: 24px;
    }
    
    .cover-company {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 16px;
        letter-spacing: 1px;
    }
    
    .cover-main {
        align-self: flex-end;
        text-align: right;
        margin: 0;
        padding: 0;
        position: absolute;
        top: 50%;
        right: 40px;
        transform: translateY(-50%);
    }
    
    .cover-date {
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 20px;
        text-align: right;
    }
    
    .cover-title {
        font-family: 'Poppins', sans-serif;
        font-size: 48px;
        font-weight: 700;
        color: #222;
        margin: 0;
        text-align: right;
    }
    
    .cover-footer {
        align-self: flex-start;
        color: white;
        margin-top: auto;
    }
    
    .cover-prepared-label {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 5px;
    }
    
    .cover-prepared-name {
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        opacity: 0.9;
    }
    
    h1, h2, h3, h4 {
        font-family: 'Poppins', sans-serif;
        color: var(--deep-teal);
        margin-top: 0;
    }
    
    h1 {
        font-size: 32px;
        font-weight: 700;
    }
    
    h2 {
        font-size: 24px;
        font-weight: 600;
        border-bottom: 2px solid var(--slate-gray);
        padding-bottom: var(--spacing-small);
        margin-bottom: var(--spacing-large);
    }
    
    h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: var(--spacing-medium);
    }
    
    .subtitle {
        font-size: 20px;
        color: var(--slate-gray);
        margin-bottom: var(--spacing-large);
    }
    
    .meta-info {
        font-size: 14px;
        color: var(--slate-gray);
        margin-bottom: var(--spacing-large);
    }
    
    .card {
        background: var(--clean-white);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: var(--spacing-large);
        box-shadow: 0px 4px 12px rgba(0,0,0,0.08);
    }
    
    .card-header {
        margin-bottom: var(--spacing-medium);
    }
    
    .card-title {
        font-family: 'Poppins', sans-serif;
        font-size: 20px;
        font-weight: 600;
        color: var(--deep-teal);
        margin: 0;
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-large);
    }
    
    .metric-box {
        background-color: var(--clean-white);
        border-radius: 12px;
        padding: var(--spacing-medium);
        box-shadow: 0px 4px 12px rgba(0,0,0,0.05);
    }
    
    .metric-title {
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: var(--slate-gray);
        margin-bottom: var(--spacing-micro);
    }
    
    .metric-value {
        font-family: 'Poppins', sans-serif;
        font-size: 24px;
        font-weight: 700;
        color: var(--deep-teal);
    }
    
    .metric-target {
        font-size: 12px;
        color: var(--slate-gray);
    }
    
    .metric-status {
        font-size: 12px;
        font-weight: 600;
        border-radius: 4px;
        padding: 2px 8px;
        display: inline-block;
        margin-top: var(--spacing-micro);
    }
    
    .status-achieved {
        background-color: rgba(42, 199, 105, 0.1);
        color: var(--success-green);
    }
    
    .status-partial {
        background-color: rgba(255, 203, 71, 0.1);
        color: var(--warning-yellow);
    }
    
    .status-not-achieved {
        background-color: rgba(255, 126, 66, 0.1);
        color: var(--alert-orange);
    }
    
    .table-container {
        overflow-x: auto;
        margin-bottom: var(--spacing-large);
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
    }
    
    thead {
        background-color: var(--deep-teal);
        color: var(--clean-white);
    }
    
    th {
        text-align: left;
        padding: var(--spacing-medium);
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 14px;
    }
    
    tr:nth-child(even) {
        background-color: #F7FAFC;
    }
    
    td {
        padding: var(--spacing-medium);
        font-size: 14px;
        border-bottom: 1px solid #E6EBF0;
    }
    
    .highlight {
        color: var(--accent-blue);
        font-weight: 500;
    }
    
    .achievement {
        margin-bottom: var(--spacing-medium);
        padding-bottom: var(--spacing-medium);
        border-bottom: 1px solid #E6EBF0;
    }
    
    .achievement:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .achievement-title {
        font-weight: 600;
        margin-bottom: var(--spacing-micro);
    }
    
    .achievement-date {
        font-size: 12px;
        color: var(--slate-gray);
    }
    
    .feedback-box {
        background-color: var(--light-gray);
        padding: var(--spacing-medium);
        border-radius: 8px;
        margin-top: var(--spacing-medium);
    }
    
    .feedback-author {
        font-weight: 600;
        margin-bottom: var(--spacing-micro);
    }
    
    .feedback-date {
        font-size: 12px;
        color: var(--slate-gray);
        margin-top: var(--spacing-small);
    }
    
    .two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-large);
    }
    
    .progress-bar {
        height: 8px;
        background-color: #E6EBF0;
        border-radius: 4px;
        margin-top: var(--spacing-small);
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        border-radius: 4px;
    }
    
    .progress-fill.good {
        background-color: var(--success-green);
    }
    
    .progress-fill.warning {
        background-color: var(--warning-yellow);
    }
    
    .progress-fill.alert {
        background-color: var(--alert-orange);
    }
    
    @media (max-width: 768px) {
        .container {
            padding: 20px;
        }
        
        .two-column, .metrics-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

</head>
<body>
    <div class="container">
        <!-- Cover Page -->
        <div class="page cover-page">
            <div class="cover-diagonal"></div>
            <div class="cover-diagonal-overlay"></div>
            <div class="cover-diagonal-2"></div>
            <div class="cover-diagonal-3"></div>
            <div class="cover-diagonal-4"></div>
            <div class="cover-diagonal-5"></div>
            <div class="cover-content">
                <div class="cover-header">
                    <div class="cover-logo">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="cover-company">BORGELLE</div>
                </div>
                <div class="cover-main">
                    <div class="cover-date">02/05/2021</div>
                    <h1 class="cover-title">Business<br>Report</h1>
                </div>
                <div class="cover-footer">
                    <div class="cover-prepared-label">PREPARED FOR:</div>
                    <div class="cover-prepared-name">Daniel Gallego</div>
                </div>
            </div>
        </div>
        <!-- Executive Summary -->
    <div class="page">
        <h2>Executive Summary</h2>
        <p>This report presents a comprehensive assessment of customer service performance for Apple's support team during Q1 2024. The analysis covers key performance metrics, individual achievements, and recommendations for continued improvement.</p>
        
        <h3>Performance Highlights</h3>
        <ul>
            <li>Representatives maintained strong customer satisfaction scores averaging 4.5/5</li>
            <li>Average call duration stayed below the target of 8 minutes</li>
            <li>Key Achievement: Jane Doe recognized as top performer with highest customer satisfaction score</li>
        </ul>
    </div>
    
    <!-- Performance Metrics -->
    <div class="page">
        <h2>Performance Metrics</h2>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Jane Doe</th>
                        <th>Mark Johnson</th>
                        <th>Team Target</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Total Calls Handled</td>
                        <td>1,420</td>
                        <td>1,350</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>Avg Call Duration</td>
                        <td class="highlight">7.5 min</td>
                        <td>7.9 min</td>
                        <td>&lt;8.0 min</td>
                    </tr>
                    <tr>
                        <td>First Call Resolution</td>
                        <td class="highlight">88.5%</td>
                        <td>85.0%</td>
                        <td>&gt;85%</td>
                    </tr>
                    <tr>
                        <td>Customer Satisfaction</td>
                        <td class="highlight">4.6/5</td>
                        <td>4.4/5</td>
                        <td>&gt;4.5/5</td>
                    </tr>
                    <tr>
                        <td>Escalation Rate</td>
                        <td class="highlight">2.8%</td>
                        <td>3.5%</td>
                        <td>&lt;3.0%</td>
                    </tr>
                    <tr>
                        <td>Attendance</td>
                        <td>98.0%</td>
                        <td>97.5%</td>
                        <td>&gt;97%</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h3>Key Metrics Visualization</h3>
        
        <div class="two-column">
            <div class="metric-box">
                <div class="metric-title">Customer Satisfaction Score</div>
                <div>
                    <p>Jane Doe: 4.6/5</p>
                    <div class="progress-bar">
                        <div class="progress-fill good" style="width: 92%"></div>
                    </div>
                    
                    <p>Mark Johnson: 4.4/5</p>
                    <div class="progress-bar">
                        <div class="progress-fill warning" style="width: 88%"></div>
                    </div>
                    
                    <p>Target: 4.5/5</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%; background-color: var(--slate-gray);"></div>
                    </div>
                </div>
            </div>
            
            <div class="metric-box">
                <div class="metric-title">Escalation Rate</div>
                <div>
                    <p>Jane Doe: 2.8%</p>
                    <div class="progress-bar">
                        <div class="progress-fill good" style="width: 28%"></div>
                    </div>
                    
                    <p>Mark Johnson: 3.5%</p>
                    <div class="progress-bar">
                        <div class="progress-fill alert" style="width: 35%"></div>
                    </div>
                    
                    <p>Target: 3.0%</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 30%; background-color: var(--slate-gray);"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Individual Performance -->
    <div class="page">
        <h2>Individual Performance Assessment</h2>
        
        <div class="two-column">
            <!-- Jane Doe -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Jane Doe</h3>
                    <div class="meta-info">Employee ID: CS12345 | Role: Customer Service Representative</div>
                </div>
                
                <h3>Goal Achievement</h3>
                <div class="metrics-grid">
                    <div class="metric-box">
                        <div class="metric-title">Customer Satisfaction Score</div>
                        <div class="metric-value">4.6/5</div>
                        <div class="metric-target">Target: 4.5/5</div>
                        <div class="metric-status status-achieved">Achieved</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-title">Escalation Rate</div>
                        <div class="metric-value">2.8%</div>
                        <div class="metric-target">Target: 3.0%</div>
                        <div class="metric-status status-achieved">Achieved</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-title">Average Call Duration</div>
                        <div class="metric-value">7.5 min</div>
                        <div class="metric-target">Target: 8.0 min</div>
                        <div class="metric-status status-achieved">Achieved</div>
                    </div>
                </div>
                
                <h3>Achievements</h3>
                <div class="achievement">
                    <div class="achievement-title">Top Performer Award</div>
                    <div>Recognized as top customer service representative for achieving highest Customer Satisfaction Score for Q1 2024.</div>
                    <div class="achievement-date">Awarded: March 28, 2024</div>
                </div>
                
                <div class="achievement">
                    <div class="achievement-title">Efficiency Excellence</div>
                    <div>Consistently maintained an average call duration significantly below the target.</div>
                    <div class="achievement-date">Awarded: March 15, 2024</div>
                </div>
                
                <h3>Supervisor Feedback</h3>
                <div class="feedback-box">
                    <div class="feedback-author">John Smith</div>
                    <p>Jane has consistently exceeded her performance targets this quarter. Her customer satisfaction ratings and call efficiency set an excellent standard for the team. Continued focus on proactive customer engagement is recommended.</p>
                    <div class="feedback-date">Date: March 30, 2024</div>
                </div>
            </div>
            
            <!-- Mark Johnson -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Mark Johnson</h3>
                    <div class="meta-info">Employee ID: CS67890 | Role: Customer Service Representative</div>
                </div>
                
                <h3>Goal Achievement</h3>
                <div class="metrics-grid">
                    <div class="metric-box">
                        <div class="metric-title">Customer Satisfaction Score</div>
                        <div class="metric-value">4.4/5</div>
                        <div class="metric-target">Target: 4.5/5</div>
                        <div class="metric-status status-partial">Partially Achieved</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-title">Escalation Rate</div>
                        <div class="metric-value">3.5%</div>
                        <div class="metric-target">Target: 3.0%</div>
                        <div class="metric-status status-not-achieved">Not Achieved</div>
                    </div>
                    
                    <div class="metric-box">
                        <div class="metric-title">Average Call Duration</div>
                        <div class="metric-value">7.9 min</div>
                        <div class="metric-target">Target: 8.0 min</div>
                        <div class="metric-status status-achieved">Achieved</div>
                    </div>
                </div>
                
                <h3>Achievements</h3>
                <div class="achievement">
                    <div class="achievement-title">Improvement Recognition</div>
                    <div>Significant improvement in call handling efficiency compared to last quarter.</div>
                    <div class="achievement-date">Awarded: March 20, 2024</div>
                </div>
                
                <h3>Supervisor Feedback</h3>
                <div class="feedback-box">
                    <div class="feedback-author">Emily Davis</div>
                    <p>Mark has shown notable improvement this quarter. Continued attention on customer satisfaction and reducing escalations will benefit his overall performance.</p>
                    <div class="feedback-date">Date: March 31, 2024</div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
