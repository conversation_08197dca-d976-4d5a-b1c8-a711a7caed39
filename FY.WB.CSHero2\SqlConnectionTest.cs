using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace FY.WB.CSHero2
{
    public class SqlConnectionTest
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("SQL Connection Test Starting...");
            
            // Load configuration from appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
            
            // Get the connection string
            string connectionString = configuration.GetConnectionString("DefaultConnection");
            Console.WriteLine($"Using connection string: {connectionString}");
            
            // Try to connect to SQL Server using raw ADO.NET
            try
            {
                await using (var connection = new SqlConnection(connectionString))
                {
                    Console.WriteLine("Opening connection...");
                    await connection.OpenAsync();
                    Console.WriteLine("Connection opened successfully!");
                    
                    // Try to execute a simple query
                    await using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT @@VERSION";
                        var result = await command.ExecuteScalarAsync();
                        Console.WriteLine($"SQL Server version: {result}");
                    }
                    
                    // Try to create database if it doesn't exist (manually)
                    await using (var command = connection.CreateCommand())
                    {
                        Console.WriteLine("Checking if database exists...");
                        // Get database name from connection string
                        var builder = new SqlConnectionStringBuilder(connectionString);
                        var dbName = builder.InitialCatalog;
                        
                        if (string.IsNullOrEmpty(dbName))
                        {
                            dbName = "UN_WB_TemplateDb"; // Default name if not in connection string
                        }
                        
                        command.CommandText = $@"
                            IF NOT EXISTS (SELECT name FROM master.dbo.sysdatabases WHERE name = '{dbName}')
                            BEGIN
                                CREATE DATABASE [{dbName}]
                                PRINT 'Database created successfully!'
                            END
                            ELSE
                            BEGIN
                                PRINT 'Database already exists.'
                            END";
                        
                        Console.WriteLine($"Executing: {command.CommandText}");
                        await command.ExecuteNonQueryAsync();
                        Console.WriteLine("Database check/creation command executed successfully");
                    }
                    
                    Console.WriteLine("Test completed successfully!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error connecting to database: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                // Suggestion for SQL Server connection issues
                Console.WriteLine("\nPossible solutions:");
                Console.WriteLine("1. Check if SQL Server is running");
                Console.WriteLine("2. Verify that the connection string is correct");
                Console.WriteLine("3. Ensure your account has sufficient permissions");
                Console.WriteLine("4. Check if SQL Server Authentication is enabled");
                Console.WriteLine("5. Verify TCP/IP protocol is enabled in SQL Server Network Configuration");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
