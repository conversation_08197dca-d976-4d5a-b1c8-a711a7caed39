# CUD Operations Test Procedure

## Overview

This document outlines the test procedure for validating the Create, Update, and Delete (CUD) operations implemented for the following entities:
- TenantProfile
- Form
- Invoice
- Report
- Template
- Upload

## Prerequisites

1. **API Running**: Ensure the FY.WB.CSHero2 API is running on `http://localhost:5056`
2. **Database Seeded**: The database should be seeded with test data (happens automatically on startup)
3. **Test Users Available**:
   - Admin: `<EMAIL>` / `AdminPass123!`
   - Tenant1: `<EMAIL>` / `Test123!`

## Test Execution

### 1. Run All Tests

```bash
cd FY.WB.CSHero2.Test
dotnet test
```

### 2. Run Specific Controller Tests

```bash
# Test only Upload CUD operations
dotnet test --filter "FullyQualifiedName~UploadsControllerTests"

# Test only Forms (existing GET operations)
dotnet test --filter "FullyQualifiedName~FormsControllerTests"
```

### 3. Run Tests with Detailed Output

```bash
dotnet test --logger "console;verbosity=detailed"
```

## Test Categories

### Upload Controller Tests (`UploadsControllerTests.cs`)

#### Create Tests
- ✅ `CreateUpload_ValidData_ReturnsCreated`
  - Tests successful creation with valid data
  - Verifies 201 Created status code
  - Confirms the upload can be retrieved after creation

- ✅ `CreateUpload_InvalidData_ReturnsBadRequest`
  - Tests validation with missing required fields
  - Verifies 400 Bad Request status code

#### Update Tests
- ✅ `UpdateUpload_ValidData_ReturnsNoContent`
  - Tests successful update of existing upload
  - Verifies 204 No Content status code
  - Confirms changes are persisted

- ✅ `UpdateUpload_NonExistentId_ReturnsNotFound`
  - Tests update attempt on non-existent entity
  - Verifies 404 Not Found status code

- ✅ `UpdateUpload_MismatchedId_ReturnsBadRequest`
  - Tests validation when route ID doesn't match body ID
  - Verifies 400 Bad Request status code

#### Delete Tests
- ✅ `DeleteUpload_ExistingId_ReturnsNoContent`
  - Tests successful deletion of existing upload
  - Verifies 204 No Content status code
  - Confirms entity is no longer retrievable

- ✅ `DeleteUpload_NonExistentId_ReturnsNotFound`
  - Tests deletion attempt on non-existent entity
  - Verifies 404 Not Found status code

#### Multi-Tenancy Tests
- ✅ `UpdateUpload_CrossTenant_ReturnsForbidden`
  - Tests that users cannot update entities from other tenants
  - Verifies 403 Forbidden status code

- ✅ `DeleteUpload_CrossTenant_ReturnsForbidden`
  - Tests that users cannot delete entities from other tenants
  - Verifies 403 Forbidden status code

## Expected Test Results

### Success Criteria
- All tests should pass
- No compilation errors
- Proper HTTP status codes returned
- Multi-tenant data isolation enforced
- Validation rules working correctly

### Common Issues and Troubleshooting

#### 1. API Not Running
**Error**: Connection refused or timeout
**Solution**: Start the API project:
```bash
cd FY.WB.CSHero2
dotnet run
```

#### 2. Authentication Failures
**Error**: 401 Unauthorized responses
**Solution**: 
- Verify test user credentials in `TestBase.cs`
- Check if users exist in the database
- Run `CreateTestUsers.cs` if needed

#### 3. Database Issues
**Error**: Entity not found or constraint violations
**Solution**:
- Restart the API to trigger fresh seeding
- Check database connection string
- Verify migrations are applied

#### 4. Validation Errors
**Error**: Unexpected 400 Bad Request responses
**Solution**:
- Check DTO property names match exactly
- Verify required fields are provided
- Review FluentValidation rules

## Manual Testing via Swagger

For additional verification, you can test the endpoints manually using Swagger UI:

1. Navigate to `http://localhost:5056/swagger`
2. Authenticate using the `/api/Auth/login` endpoint
3. Copy the returned JWT token
4. Click "Authorize" and enter: `Bearer {your-token}`
5. Test the CUD endpoints for each entity

### Example Test Flow in Swagger

1. **Login**: POST `/api/Auth/login`
2. **Create**: POST `/api/uploads` with valid data
3. **Read**: GET `/api/uploads/{id}` to verify creation
4. **Update**: PUT `/api/uploads/{id}` with modified data
5. **Read**: GET `/api/uploads/{id}` to verify update
6. **Delete**: DELETE `/api/uploads/{id}`
7. **Read**: GET `/api/uploads/{id}` should return 404

## Test Data Examples

### Valid Upload Creation Data
```json
{
  "filename": "test-document.pdf",
  "size": 1024000,
  "contentType": "application/pdf",
  "storagePath": "/uploads/test-document.pdf",
  "storageProvider": "LocalStorage",
  "externalUrl": "https://example.com/files/test-document.pdf",
  "checksum": "abc123def456"
}
```

### Valid Upload Update Data
```json
{
  "id": "existing-upload-id",
  "filename": "updated-document.pdf",
  "contentType": "application/pdf",
  "storagePath": "/uploads/updated-document.pdf",
  "checksum": "updated-checksum"
}
```

## Next Steps

1. **Extend Tests**: Create similar test classes for other entities (TenantProfile, Form, Invoice, Report, Template)
2. **Performance Tests**: Add tests for bulk operations and concurrent access
3. **Integration Tests**: Test complete workflows that span multiple entities
4. **Error Handling**: Add tests for edge cases and error scenarios
5. **Security Tests**: Verify authorization rules and input sanitization

## Notes

- Tests use NUnit framework
- All tests inherit from `TestBase` for common setup
- Tests are designed to be independent and can run in any order
- Each test cleans up after itself to avoid side effects
- Console output provides detailed information for debugging
