'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, Download } from 'lucide-react';
import { useToast } from '@/components/providers/toast-provider';
import { Tenant } from '@/types/tenant';
import { 
  generateInvoice, 
  generateInvoiceNumber, 
  downloadInvoice,
  InvoiceLineItem,
  InvoiceDiscount
} from '@/lib/generate-invoice';
import { getAdminTenant, getSubscriptionByTypeAndBillingCycle } from '@/lib/api';

interface InvoiceGeneratorButtonProps {
  tenant: Tenant;
  onSuccess?: () => void;
}

export function InvoiceGeneratorButton({ tenant, onSuccess }: InvoiceGeneratorButtonProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleGenerateInvoice = async () => {
    try {
      setLoading(true);

      console.log('Generating invoice for tenant:', tenant);
      
      // Fetch the admin tenant for company information
      const adminTenantResponse = await getAdminTenant();
      console.log('Admin tenant response:', adminTenantResponse);
      
      // Handle different response formats
      let adminTenant;
      if (adminTenantResponse && adminTenantResponse.data) {
        adminTenant = adminTenantResponse.data;
      } else if (adminTenantResponse) {
        // Handle case where the data might not be nested
        adminTenant = adminTenantResponse;
      }
      
      console.log('Processed admin tenant:', adminTenant);

      if (!adminTenant) {
        toast({
          title: 'Error',
          description: 'Admin tenant information not found',
          variant: 'destructive',
        });
        throw new Error('Admin tenant information not found');
      }

      // Generate invoice data
      const today = new Date();
      
      // Create subscription period dates
      const startDate = new Date();
      const endDate = new Date(startDate);
      
      // Set end date and due date based on billing cycle
      const tenantBillingCycle = tenant.billingCycle || 'annual';
      let dueDate = new Date(today);
      
      if (tenantBillingCycle === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1); // 1 month subscription
        dueDate.setMonth(dueDate.getMonth() + 1); // Due in 1 month
      } else if (tenantBillingCycle === 'quarterly') {
        endDate.setMonth(endDate.getMonth() + 3); // 3 month subscription
        dueDate.setMonth(dueDate.getMonth() + 3); // Due in 3 months
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1); // 1 year subscription
        dueDate.setFullYear(dueDate.getFullYear() + 1); // Due in 1 year
      }

      // Create line items based on tenant subscription
      const lineItems: InvoiceLineItem[] = [];
      
      // Get subscription data based on tenant's subscription type and billing cycle
      const subscriptionType = tenant.subscription || 'basic';
      
      // Use the subscription data from the API
      const subscriptionResponse = await getSubscriptionByTypeAndBillingCycle(
        subscriptionType as 'basic' | 'professional' | 'enterprise',
        tenantBillingCycle as 'monthly' | 'quarterly' | 'annual'
      );
      
      if (!subscriptionResponse) {
        throw new Error(`Subscription not found for type ${subscriptionType} and billing cycle ${tenantBillingCycle}`);
      }
      
      // Add subscription line item
      lineItems.push({
        description: subscriptionResponse.name,
        quantity: 1,
        unitPrice: subscriptionResponse.price,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });
      
      // Add tax line item (5% of subscription price)
      const taxAmount = lineItems[0].unitPrice * 0.05;
      lineItems.push({
        description: 'Tax',
        quantity: 1,
        unitPrice: taxAmount,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      // Create discount if applicable
      let discount: InvoiceDiscount | undefined;
      if (tenant.subscription === 'enterprise') {
        discount = {
          code: 'ENTERPRISE24',
          percentage: 50
        };
      }

      // Generate invoice number
      const invoiceNumber = generateInvoiceNumber('CS-HERO');

      // Generate the invoice HTML
      const { html } = generateInvoice({
        tenant,
        adminTenant,
        invoiceNumber,
        issueDate: today.toISOString(),
        dueDate: dueDate.toISOString(),
        lineItems,
        discount
      });

      // Download the invoice as PDF
      downloadInvoice(html, `invoice-${invoiceNumber}.pdf`);

      toast({
        title: 'Success',
        description: 'PDF Invoice generated and downloaded successfully',
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error generating invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF invoice',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleGenerateInvoice}
      disabled={loading}
      className="flex items-center gap-2"
    >
      {loading ? (
        <>Generating...</>
      ) : (
        <>
          <FileText className="h-4 w-4" />
          Generate PDF Invoice
        </>
      )}
    </Button>
  );
}
