using System.Xml;
using System.Xml.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models
{
    /// <summary>
    /// Special wrapper to ensure HTML content is properly included as CDATA in XML
    /// </summary>
    public class CDataWrapper
    {
        private string _value;

        /// <summary>
        /// Default constructor required for XML serialization
        /// </summary>
        public CDataWrapper() { }
        
        /// <summary>
        /// Constructor that accepts a string value
        /// </summary>
        /// <param name="value">The string value to wrap in CDATA</param>
        public CDataWrapper(string value) => _value = value ?? string.Empty;

        /// <summary>
        /// The XML representation of the value as a CDATA section
        /// </summary>
        [XmlText]
        public XmlNode[] CData
        {
            get
            {
                var doc = new XmlDocument();
                return new XmlNode[] { doc.CreateCDataSection(_value) };
            }
            set => _value = value?[0]?.Value ?? string.Empty;
        }

        /// <summary>
        /// Returns the string representation of the value
        /// </summary>
        /// <returns>The string value</returns>
        public override string ToString() => _value;
    }
}
