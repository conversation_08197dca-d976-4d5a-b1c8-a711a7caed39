using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Uploads.Queries
{
    public class GetUploadByIdQueryHandler : IRequestHandler<GetUploadByIdQuery, UploadDto?>
    {
        private readonly IApplicationDbContext _context;

        public GetUploadByIdQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<UploadDto?> Handle(GetUploadByIdQuery request, CancellationToken cancellationToken)
        {
            // Upload is FullAuditedMultiTenantEntity, so global query filter applies.
            var upload = await _context.Uploads
                .AsNoTracking()
                .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

            if (upload == null)
            {
                return null;
            }

            return new UploadDto
            {
                Id = upload.Id,
                Filename = upload.Filename,
                Size = upload.Size,
                ContentType = upload.ContentType,
                StoragePath = upload.StoragePath,
                StorageProvider = upload.StorageProvider,
                ExternalUrl = upload.ExternalUrl,
                Checksum = upload.Checksum,
                CreatedAt = upload.CreationTime,
                UpdatedAt = upload.LastModificationTime,
                TenantId = upload.TenantId,
                IsImage = upload.IsImage(),
                IsDocument = upload.IsDocument()
            };
        }
    }
}
