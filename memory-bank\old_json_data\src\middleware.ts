import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes that require authentication
const protectedRoutes = [
  '/client',
  '/client/',
  '/admin',
  '/admin/',
]

// Define protected path patterns
const protectedPathPatterns = [
  '/client/',
  '/admin/',
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if the path is protected
  const isProtectedPath = protectedRoutes.includes(pathname) ||
    protectedPathPatterns.some(pattern => pathname.startsWith(pattern))

  // Check if it's an admin path
  const isAdminPath = pathname === '/admin' || pathname === '/admin/' ||
    pathname.startsWith('/admin/')

  // Check for auth token
  const authToken = request.cookies.get('authToken')?.value

  // Check for admin status from cookies
  const isAdmin = request.cookies.get('isAdmin')?.value === 'true'

  // If it's a protected path and no auth token exists, redirect to login
  if (isProtectedPath && !authToken) {
    const loginUrl = new URL('/login', request.url)
    // Add a redirect parameter to return after login
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // If it's an admin path but user is not an admin, redirect to client dashboard
  if (isAdminPath && authToken && !isAdmin) {
    return NextResponse.redirect(new URL('/client/dashboard', request.url))
  }

  // Specific redirects
  // Redirect /client to /client/dashboard
  if (pathname === '/client' || pathname === '/client/') {
    return NextResponse.redirect(new URL('/client/dashboard', request.url))
  }

  // Redirect /admin to /admin/dashboard
  if (pathname === '/admin' || pathname === '/admin/') {
    return NextResponse.redirect(new URL('/admin/dashboard', request.url))
  }

  // Handle ai-agency redirects
  if (pathname === '/ai-agency' || pathname.startsWith('/ai-agency/')) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  // Allow all other requests to proceed
  return NextResponse.next()
}

export const config = {
  matcher: [
    // Match all client routes
    '/client',
    '/client/:path*',
    // Match all admin routes
    '/admin',
    '/admin/:path*',
    // Match ai-agency routes
    '/ai-agency',
    '/ai-agency/:path*',
  ]
}
