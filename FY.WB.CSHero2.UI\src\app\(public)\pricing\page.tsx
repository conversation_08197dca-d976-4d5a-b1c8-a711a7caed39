"use client";

import { motion } from "framer-motion";
import { Check } from "lucide-react";

const plans = [
  {
    name: "Basic",
    price: "$49",
    description: "Perfect for small teams getting started",
    features: [
      "Up to 50 reports/month",
      "5 team tenants",
      "Basic templates",
      "Email support",
      "Export to PDF",
    ],
    gradient: "gradient-primary-secondary",
  },
  {
    name: "Pro",
    price: "$99",
    description: "For growing teams with advanced needs",
    features: [
      "Unlimited reports",
      "25 team tenants",
      "Advanced templates",
      "Priority support",
      "Export to PDF & PPT",
      "Custom branding",
      "API access",
    ],
    gradient: "gradient-secondary-tertiary",
    popular: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    description: "Custom solutions for large organizations",
    features: [
      "Unlimited everything",
      "Unlimited team tenants",
      "Custom templates",
      "24/7 support",
      "All export formats",
      "Custom integrations",
      "SLA guarantee",
      "Dedicated manager",
    ],
    gradient: "gradient-primary-tertiary",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function PricingPage() {
  return (
    <div className="gradient-primary-secondary">
      <div className="container mx-auto px-4 py-24 md:px-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4 text-white">Pricing Plans</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Choose the perfect plan for your team's needs
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan) => (
            <motion.div
              key={plan.name}
              variants={itemVariants}
              className="relative"
            >
              <div className="h-full bg-white rounded-lg shadow-lg p-8">
                {plan.popular && (
                  <div className={`absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${plan.gradient} text-white text-sm font-semibold py-1 px-4 rounded-full`}>
                    Most Popular
                  </div>
                )}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  <div className={`text-4xl font-bold mb-2 ${plan.gradient} bg-clip-text text-transparent`}>
                    {plan.price}
                    {plan.price !== "Custom" && <span className="text-lg text-gray-600">/mo</span>}
                  </div>
                  <p className="text-gray-600">{plan.description}</p>
                </div>
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center text-gray-600">
                      <Check className="w-5 h-5 text-green-500 mr-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <a
                    href={plan.price === "Custom" ? "/contact" : "/signup"}
                    className={`block w-full py-3 px-6 rounded-lg text-center text-white ${plan.gradient} font-semibold hover:opacity-90 transition-opacity`}
                  >
                    {plan.price === "Custom" ? "Contact Sales" : "Get Started"}
                  </a>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 p-8 rounded-lg bg-white shadow-lg max-w-3xl mx-auto text-center"
        >
          <h2 className="text-2xl font-bold mb-4 text-[rgb(var(--primary))]">Have Questions?</h2>
          <p className="text-gray-600 mb-6">
            Check out our FAQ section or contact our sales team for detailed information about our plans.
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="/faq"
              className="px-6 py-3 rounded-lg text-[rgb(var(--primary))] border border-[rgb(var(--primary))] font-semibold hover:bg-[rgb(var(--primary))] hover:text-white transition-colors"
            >
              View FAQ
            </a>
            <a
              href="/contact"
              className="px-6 py-3 rounded-lg gradient-primary-tertiary text-white font-semibold hover:opacity-90 transition-opacity"
            >
              Contact Sales
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
