import { motion } from 'framer-motion';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus, Download, BarChart, LayoutTemplate, FileText, Users } from 'lucide-react';
import { cn } from "@/lib/utils";


interface PageHeaderProps {
  title: string;
  description: string;
  placeHolder?: string;
  searchQuery?: string;
  setSearchQuery?: (value: string) => void;
  btnUrl?: string;
  btnName?: string;
  onButtonClick?: () => void;
  renderButton?: () => React.ReactNode;
}

export function PageHeader({ 
  title,
  description, 
  btnUrl, 
  btnName, 
  placeHolder, 
  searchQuery,
  renderButton,
  onButtonClick, 
  setSearchQuery
}: PageHeaderProps) {
  return (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className='border rounded-xl pr-12 shadow-md flex'>
          <div className='bg-primary w-4 rounded-l-xl'></div>
          <div className='w-full py-8 pl-8'>
            {/* <div className="flex justify-between items-center mb-8"> */}
            <div className={cn(
              "flex justify-between items-center",
              placeHolder ? "mb-8" : ""
            )}>
              <div>
                <h1 className="text-4xl font-bold mb-4 text-[rgb(var(--primary))]">{ title }</h1>
                <p className="text-xl text-gray-600">
                  { description }
                </p>
              </div>
              {renderButton ? (
                renderButton()
              ) : btnUrl ? (
                <Link href={btnUrl}>
                  <Button size="lg" className="gradient-primary-secondary text-white">
                    <Plus className="w-5 h-5 mr-2" />
                    {btnName || "Add New"}
                  </Button>
                </Link>
              ) : onButtonClick ? (
                <Button 
                  onClick={onButtonClick}
                  size="lg" 
                  className="gradient-primary-secondary text-white"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {btnName || "Add New"}
                </Button>
              ) : null}
            </div>

            {placeHolder ? (
            <div className="flex gap-4">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
                <Input
                  placeholder={placeHolder}
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery && setSearchQuery(e.target.value)}
                  />
              </div>
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="w-5 h-5" />
                Export
              </Button>
            </div>
            ) : null }
          </div>
        </div>
      </motion.div>
  );
}
