namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Metrics for monitoring LLM performance
    /// </summary>
    public class LlmMetrics
    {
        /// <summary>
        /// The total number of requests made to the LLM
        /// </summary>
        public int TotalRequests { get; set; }
        
        /// <summary>
        /// The number of successfully completed requests
        /// </summary>
        public int SuccessfulRequests { get; set; }
        
        /// <summary>
        /// The number of failed requests
        /// </summary>
        public int FailedRequests { get; set; }
        
        /// <summary>
        /// The average latency in milliseconds for successful requests
        /// </summary>
        public double AverageLatencyMs { get; set; }
        
        /// <summary>
        /// The total number of tokens used across all requests
        /// </summary>
        public long TotalTokensUsed { get; set; }
    }
}
