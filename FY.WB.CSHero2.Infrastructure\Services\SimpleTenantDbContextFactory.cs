using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Infrastructure.Interfaces;
using FY.WB.CSHero2.Application.Common.Interfaces;
using System;

namespace FY.WB.CSHero2.Infrastructure.Services;

/// <summary>
/// Simplified tenant-aware DbContext factory that creates ApplicationDbContext instances
/// without depending on EF Core's IDbContextFactory to avoid service lifetime conflicts
/// </summary>
public class SimpleTenantDbContextFactory : ITenantDbContextFactory
{
    private readonly IConfiguration _configuration;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SimpleTenantDbContextFactory> _logger;

    public SimpleTenantDbContextFactory(
        IConfiguration configuration,
        IServiceProvider serviceProvider)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = serviceProvider.GetRequiredService<ILogger<SimpleTenantDbContextFactory>>();
    }

    /// <summary>
    /// Creates a new ApplicationDbContext instance with current tenant context
    /// </summary>
    public ApplicationDbContext CreateDbContext()
    {
        try
        {
            // Create a new scope to get fresh instances of scoped services
            using var scope = _serviceProvider.CreateScope();
            
            // Get the current user service and tenant info from the new scope
            var currentUserService = scope.ServiceProvider.GetRequiredService<ICurrentUserService>();
            var tenantAccessor = scope.ServiceProvider.GetService<IMultiTenantContextAccessor<AppTenantInfo>>();
            var tenantInfo = tenantAccessor?.MultiTenantContext?.TenantInfo;

            // Create DbContextOptions manually
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            optionsBuilder.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);

                sqlOptions.CommandTimeout(30);
            });

            // Enable sensitive data logging in development
            if (_configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                optionsBuilder.EnableSensitiveDataLogging();
            }

            // Enable detailed errors in development
            if (_configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                optionsBuilder.EnableDetailedErrors();
            }

            // Create the context with the manually configured options
            var context = new ApplicationDbContext(optionsBuilder.Options, currentUserService, tenantInfo);

            _logger.LogDebug("Created new DbContext instance for tenant: {TenantId}", 
                tenantInfo?.Id ?? "Unknown");

            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create DbContext instance");
            throw;
        }
    }

    /// <summary>
    /// Creates a new ApplicationDbContext instance for a specific tenant
    /// </summary>
    public ApplicationDbContext CreateDbContext(string tenantId)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            
            var currentUserService = scope.ServiceProvider.GetRequiredService<ICurrentUserService>();
            
            // Create a mock tenant info for the specified tenant
            var tenantInfo = new AppTenantInfo
            {
                Id = tenantId,
                Identifier = tenantId,
                Name = $"Tenant-{tenantId}"
            };

            // Create DbContextOptions manually
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            optionsBuilder.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);

                sqlOptions.CommandTimeout(30);
            });

            // Enable sensitive data logging in development
            if (_configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                optionsBuilder.EnableSensitiveDataLogging();
            }

            // Enable detailed errors in development
            if (_configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                optionsBuilder.EnableDetailedErrors();
            }

            // Create the context with the manually configured options
            var context = new ApplicationDbContext(optionsBuilder.Options, currentUserService, tenantInfo);

            _logger.LogDebug("Created new DbContext instance for specific tenant: {TenantId}", tenantId);

            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create DbContext instance for tenant: {TenantId}", tenantId);
            throw;
        }
    }
}

/// <summary>
/// Simple tenant info implementation for factory usage
/// </summary>
internal class TenantInfo : ITenantInfo
{
    public string? Id { get; set; }
    public string? Identifier { get; set; }
    public string? Name { get; set; }
    public string? ConnectionString { get; set; }
    public IDictionary<string, object?> Items { get; set; } = new Dictionary<string, object?>();
}
