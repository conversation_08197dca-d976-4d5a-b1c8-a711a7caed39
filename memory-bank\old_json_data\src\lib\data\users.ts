import { readJsonFile, writeJsonFile } from './utils';

export interface User {
  id: number;
  email: string;
  name: string;
  role: string;
  created_at: string;
  avatarUrl: string | null;
}

/**
 * Get a user by ID
 * @param id User ID
 * @returns User object or null if not found
 */
export async function getUserById(id: number): Promise<User | null> {
  const data = await readJsonFile<any>();
  const user = data.users.find((u: User) => u.id === id);
  return user || null;
}

/**
 * Get the current user (for auth)
 * @returns Current user or null
 */
export async function getCurrentUser(): Promise<User | null> {
  // In a real app, this would check the authentication state
  // For mock purposes, we're returning the admin user
  return getUserById(1);
}

/**
 * Send a password reset email to a user
 * @param email Email address of the user
 * @returns True if the email was sent, false if the user was not found
 */
export async function sendPasswordResetEmail(email: string): Promise<boolean> {
  const data = await readJsonFile<any>();
  
  // Check if the email exists in users
  const userExists = data.users.some((u: User) => u.email.toLowerCase() === email.toLowerCase());
  if (userExists) {
    // In a real app, this would send an actual email
    // For mock purposes, we're just returning success
    
    // You could log the reset request in the database
    if (!data.passwordResetRequests) {
      data.passwordResetRequests = [];
    }
    
    data.passwordResetRequests.push({
      email,
      requestedAt: new Date().toISOString(),
      token: `reset-token-${Date.now()}`, // In a real app, this would be a secure token
      used: false
    });
    
    await writeJsonFile('db.json', data);
    return true;
  }
  
  // Check if the email exists in tenants
  if (data.tenants) {
    const tenantExists = data.tenants.some((t: any) => t.email.toLowerCase() === email.toLowerCase());
    if (tenantExists) {
      // In a real app, this would send an actual email
      // For mock purposes, we're just returning success
      
      // You could log the reset request in the database
      if (!data.passwordResetRequests) {
        data.passwordResetRequests = [];
      }
      
      data.passwordResetRequests.push({
        email,
        requestedAt: new Date().toISOString(),
        token: `reset-token-${Date.now()}`, // In a real app, this would be a secure token
        used: false
      });
      
      await writeJsonFile('db.json', data);
      return true;
    }
  }
  
  return false;
}
