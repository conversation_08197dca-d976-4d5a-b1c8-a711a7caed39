# Report Structure Entity Relationships

## Overview

This document describes the entity relationships in the refactored report structure. The new structure follows a hierarchical model:

1. **Report** - Main container with metadata
2. **ReportSection** - Sections within a report (a list that can be infinite)
3. **ReportSectionField** - Fields within a section (a list of fields for each section)

## Entity Diagram

```mermaid
erDiagram
    Report ||--o{ ReportSection : contains
    ReportSection ||--o{ ReportSectionField : contains
    Report }|--|| Client : "belongs to"
    Report }o--o| Template : "based on"
    Report ||--o{ ReportVersion : "has versions"
    
    Report {
        Guid Id PK
        string ReportNumber
        Guid ClientId FK
        string ClientName
        string Name
        string Category
        int SlideCount
        string Status
        string Author
        Guid TenantId
        Guid TemplateId FK
        Guid CurrentVersionId FK
        string ReportType
        DateTime CreationTime
        DateTime LastModificationTime
    }
    
    ReportSection {
        Guid Id PK
        Guid ReportId FK
        string Title
        string Type
        int Order
        DateTime CreationTime
        DateTime LastModificationTime
    }
    
    ReportSectionField {
        Guid Id PK
        Guid SectionId FK
        string Name
        string Type
        string Content
        int Order
        DateTime CreationTime
        DateTime LastModificationTime
    }
    
    Client {
        Guid Id PK
        string Name
        Guid TenantId
    }
    
    Template {
        Guid Id PK
        string Name
        string Description
    }
    
    ReportVersion {
        Guid Id PK
        Guid ReportId FK
        int VersionNumber
        string Description
        bool IsCurrent
        string ComponentDataJson
        string JsonData
    }
```

## Entity Descriptions

### Report

The `Report` entity is the main container for a report. It contains metadata about the report, such as its name, category, and status. It also has relationships to other entities, such as the client it belongs to and the template it's based on.

**Key Properties:**
- `Id` (Guid): Primary key
- `ReportNumber` (string): Unique identifier for the report (e.g., "CSR-2025-001")
- `ClientId` (Guid): Foreign key to the client
- `Name` (string): Name of the report
- `Category` (string): Category of the report
- `Status` (string): Status of the report (e.g., "Draft", "In Progress", "Completed")
- `TenantId` (Guid): Tenant the report belongs to (for multi-tenancy)

**Relationships:**
- One-to-many with `ReportSection`: A report can have multiple sections
- Many-to-one with `Client`: A report belongs to a client
- Many-to-one with `Template`: A report can be based on a template
- One-to-many with `ReportVersion`: A report can have multiple versions

### ReportSection

The `ReportSection` entity represents a section within a report. Each section has a title, type, and order. Sections are ordered by the `Order` property.

**Key Properties:**
- `Id` (Guid): Primary key
- `ReportId` (Guid): Foreign key to the report
- `Title` (string): Title of the section
- `Type` (string): Type of the section (e.g., "text", "chart", "table")
- `Order` (int): Order of the section within the report

**Relationships:**
- Many-to-one with `Report`: A section belongs to a report
- One-to-many with `ReportSectionField`: A section can have multiple fields

### ReportSectionField

The `ReportSectionField` entity represents a field within a section. Each field has a name, type, and content. Fields are ordered by the `Order` property.

**Key Properties:**
- `Id` (Guid): Primary key
- `SectionId` (Guid): Foreign key to the section
- `Name` (string): Name of the field
- `Type` (string): Type of the field (e.g., "string", "number", "date")
- `Content` (string): Content of the field
- `Order` (int): Order of the field within the section

**Relationships:**
- Many-to-one with `ReportSection`: A field belongs to a section

## Database Schema

### Tables

#### Reports

| Column | Type | Constraints |
|--------|------|-------------|
| Id | uniqueidentifier | PRIMARY KEY |
| ReportNumber | nvarchar(20) | NOT NULL, UNIQUE |
| ClientId | uniqueidentifier | NOT NULL, FOREIGN KEY |
| ClientName | nvarchar(100) | NOT NULL |
| Name | nvarchar(200) | NOT NULL |
| Category | nvarchar(50) | NOT NULL |
| SlideCount | int | NOT NULL |
| Status | nvarchar(50) | NOT NULL |
| Author | nvarchar(100) | NOT NULL |
| TemplateId | uniqueidentifier | NULL, FOREIGN KEY |
| CurrentVersionId | uniqueidentifier | NULL |
| ReportType | nvarchar(50) | NOT NULL, DEFAULT 'Standard' |
| TenantId | uniqueidentifier | NOT NULL |
| CreationTime | datetime2 | NOT NULL |
| CreatorId | uniqueidentifier | NULL |
| LastModificationTime | datetime2 | NULL |
| LastModifierId | uniqueidentifier | NULL |
| IsDeleted | bit | NOT NULL, DEFAULT 0 |
| DeletionTime | datetime2 | NULL |
| DeleterId | uniqueidentifier | NULL |

#### ReportSections

| Column | Type | Constraints |
|--------|------|-------------|
| Id | uniqueidentifier | PRIMARY KEY |
| ReportId | uniqueidentifier | NOT NULL, FOREIGN KEY |
| Title | nvarchar(200) | NOT NULL |
| Type | nvarchar(50) | NOT NULL |
| Order | int | NOT NULL |
| CreationTime | datetime2 | NOT NULL |
| CreatorId | uniqueidentifier | NULL |
| LastModificationTime | datetime2 | NULL |
| LastModifierId | uniqueidentifier | NULL |

#### ReportSectionFields

| Column | Type | Constraints |
|--------|------|-------------|
| Id | uniqueidentifier | PRIMARY KEY |
| SectionId | uniqueidentifier | NOT NULL, FOREIGN KEY |
| Name | nvarchar(100) | NOT NULL |
| Type | nvarchar(50) | NOT NULL |
| Content | nvarchar(max) | NOT NULL |
| Order | int | NOT NULL |
| CreationTime | datetime2 | NOT NULL |
| CreatorId | uniqueidentifier | NULL |
| LastModificationTime | datetime2 | NULL |
| LastModifierId | uniqueidentifier | NULL |

### Indexes

#### Reports

| Index Name | Columns | Type |
|------------|---------|------|
| IX_Reports_ReportNumber | ReportNumber | UNIQUE |
| IX_Reports_ClientId | ClientId | NON-UNIQUE |
| IX_Reports_TemplateId | TemplateId | NON-UNIQUE |
| IX_Reports_CurrentVersionId | CurrentVersionId | NON-UNIQUE |
| IX_Reports_ReportType | ReportType | NON-UNIQUE |
| IX_Reports_TenantId | TenantId | NON-UNIQUE |

#### ReportSections

| Index Name | Columns | Type |
|------------|---------|------|
| IX_ReportSections_ReportId_Order | ReportId, Order | NON-UNIQUE |
| IX_ReportSections_ReportId | ReportId | NON-UNIQUE |

#### ReportSectionFields

| Index Name | Columns | Type |
|------------|---------|------|
| IX_ReportSectionFields_SectionId_Order | SectionId, Order | NON-UNIQUE |
| IX_ReportSectionFields_SectionId | SectionId | NON-UNIQUE |

## Migration from Old Structure

The old structure stored report content as JSON in the `ReportVersion.JsonData` field. The new structure extracts this JSON data into structured entities:

1. The `Report` entity remains largely unchanged
2. For each section in the JSON data, a new `ReportSection` entity is created
3. For each field in the section content, a new `ReportSectionField` entity is created

### Example

**Old Structure (JSON):**
```json
{
  "template": "standard",
  "sections": [
    {
      "section-title": "Introduction",
      "type": "text",
      "content": {
        "heading": "Introduction",
        "body": "This is the introduction section."
      }
    },
    {
      "section-title": "Data Analysis",
      "type": "chart",
      "content": {
        "heading": "Data Analysis",
        "chartType": "bar",
        "data": {
          "labels": ["Jan", "Feb", "Mar"],
          "values": [10, 20, 30]
        }
      }
    }
  ]
}
```

**New Structure (Entities):**

Report:
- Id: "00000000-0000-0000-0000-000000000101"
- Name: "Sample Report"
- ...

ReportSections:
1. Id: "00000000-0000-0000-0000-000000000201"
   - ReportId: "00000000-0000-0000-0000-000000000101"
   - Title: "Introduction"
   - Type: "text"
   - Order: 0

2. Id: "00000000-0000-0000-0000-000000000202"
   - ReportId: "00000000-0000-0000-0000-000000000101"
   - Title: "Data Analysis"
   - Type: "chart"
   - Order: 1

ReportSectionFields:
1. Id: "00000000-0000-0000-0000-000000000301"
   - SectionId: "00000000-0000-0000-0000-000000000201"
   - Name: "heading"
   - Type: "string"
   - Content: "Introduction"
   - Order: 0

2. Id: "00000000-0000-0000-0000-000000000302"
   - SectionId: "00000000-0000-0000-0000-000000000201"
   - Name: "body"
   - Type: "string"
   - Content: "This is the introduction section."
   - Order: 1

3. Id: "00000000-0000-0000-0000-000000000303"
   - SectionId: "00000000-0000-0000-0000-000000000202"
   - Name: "heading"
   - Type: "string"
   - Content: "Data Analysis"
   - Order: 0

4. Id: "00000000-0000-0000-0000-000000000304"
   - SectionId: "00000000-0000-0000-0000-000000000202"
   - Name: "chartType"
   - Type: "string"
   - Content: "bar"
   - Order: 1

5. Id: "00000000-0000-0000-0000-000000000305"
   - SectionId: "00000000-0000-0000-0000-000000000202"
   - Name: "data"
   - Type: "json"
   - Content: "{\"labels\":[\"Jan\",\"Feb\",\"Mar\"],\"values\":[10,20,30]}"
   - Order: 2

## Benefits of the New Structure

1. **Type Safety**: Strong typing for all report components
2. **Improved Querying**: Ability to query specific sections or fields
3. **Better Performance**: Optimized database access patterns
4. **Enhanced Maintainability**: Clearer code structure and better separation of concerns
5. **Scalability**: Support for an infinite number of sections and fields
6. **Versioning**: Better support for versioning and history tracking